package pocs

import (
	"bufio"
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"sync"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/hashicorp/golang-lru/v2/expirable"
	"golang.org/x/sync/semaphore"
	"gopkg.in/yaml.v3"

	"github.acme.red/intelli-sec/npoc/pocs/http/deserialize"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss"
	"github.acme.red/intelli-sec/npoc/utils/file"

	"github.acme.red/intelli-sec/common/rulecheck"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/loader/template"
	"github.acme.red/intelli-sec/npoc/pocs/http/dirsearch"
	"github.acme.red/intelli-sec/npoc/pocs/http/exchange"
	"github.acme.red/intelli-sec/npoc/pocs/http/fastjson"
	"github.acme.red/intelli-sec/npoc/pocs/http/fileupload"
	"github.acme.red/intelli-sec/npoc/pocs/http/fortigate"
	"github.acme.red/intelli-sec/npoc/pocs/http/gitlab"
	"github.acme.red/intelli-sec/npoc/pocs/http/lfr"
	"github.acme.red/intelli-sec/npoc/pocs/http/log4j"
	"github.acme.red/intelli-sec/npoc/pocs/http/loginbrute"
	"github.acme.red/intelli-sec/npoc/pocs/http/rce"
	"github.acme.red/intelli-sec/npoc/pocs/http/shiro"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli"
	"github.acme.red/intelli-sec/npoc/pocs/http/ssrf"
	"github.acme.red/intelli-sec/npoc/pocs/http/struts2"
	"github.acme.red/intelli-sec/npoc/pocs/http/thinkphp"
	"github.acme.red/intelli-sec/npoc/pocs/http/xstream"
	"github.acme.red/intelli-sec/npoc/pocs/http/xxe"
)

const (
	PocCacheKey          = "template"
	xssBrowserDefaultQPS = 1
)

var NeedDetectLanguage = []string{
	string(npoc.RCEType), string(npoc.FileReadType), string(npoc.DeserializeType),
}

var NeedDetectCMS = []string{
	string(npoc.DeserializeType),
}

var NeedDetectServer = []string{
	string(npoc.FileReadType),
}

var NeedDetectOS = []string{
	string(npoc.RCEType), string(npoc.FileReadType), string(npoc.XXEType),
}

var (
	SysCache = expirable.NewLRU[string, []npoc.PoC](1, nil, 0) //nolint:gochecknoglobals // 存放一个最新的系统poc版本

	CustomCache = expirable.NewLRU[string, npoc.PoC](10000, nil, 0) // 存放自定义的poc
	customMu    sync.Mutex
)

var GenVulnPocs = []npoc.PoC{
	&rce.PoC{},
	&log4j.PoC{},
	&lfr.PoC{},
	&dirsearch.PoC{},
	&fileupload.PoC{},
	&sqli.PoC{
		AllowTimeType:    true,
		AllowBoolType:    true,
		AllowCheckCookie: false,
	},
	&ssrf.PoC{},
	&xxe.PoC{},
	&xss.PoC{BrowserQPS: semaphore.NewWeighted(int64(xssBrowserDefaultQPS))},
	&fastjson.PoC{},
	&shiro.PoC{},
	&thinkphp.PoC{},
	&struts2.PoC{},
	&xstream.PoC{
		FullScan: false,
	},
	&loginbrute.PoC{},
	&exchange.PoC{},
	&gitlab.PoC{},
	&fortigate.PoC{},
	&deserialize.PoC{},
}

func getLoginbruteDict(dictBaseDir string) ([]string, []string, error) {
	var users, passwords []string

	userPath := filepath.Join(dictBaseDir, "web-user.txt")
	userStr, err := os.ReadFile(userPath)
	if err != nil {
		return nil, nil, err
	}
	userScanner := bufio.NewScanner(bytes.NewReader(userStr))
	for userScanner.Scan() {
		users = append(users, userScanner.Text())
	}

	passPath := filepath.Join(dictBaseDir, "web-password.txt")
	passStr, err := os.ReadFile(passPath)
	if err != nil {
		return nil, nil, err
	}
	passScanner := bufio.NewScanner(bytes.NewReader(passStr))
	for passScanner.Scan() {
		passwords = append(passwords, passScanner.Text())
	}
	if len(users) < 1 {
		return nil, nil, errors.New("login brute user dict is empty")
	}
	if len(passwords) < 1 {
		return nil, nil, errors.New("login brute password dict is empty")
	}
	return users, passwords, nil
}

func GetLoginBruteDict(dictBaseDir string) ([]string, []string, error) {
	return getLoginbruteDict(dictBaseDir)
}

// LoadCustomPoc 加载自定义的poc，如果poc之前加载过则复用之前的缓存
func LoadCustomPoc(ctx context.Context, content []byte) (npoc.PoC, error) {
	hash := md5.Sum(content)
	hashStr := hex.EncodeToString(hash[:])
	if poc, ok := CustomCache.Get(hashStr); ok {
		return poc, nil
	}
	// poc的加载依次运行，避免重复加载
	customMu.Lock()
	defer customMu.Unlock()
	// 这里会因为第一次任务并发加载的时候较多相同的poc任务从上面缓存中获取不到poc，进入等待解锁逻辑
	// 这里在等完锁后再判断一次等待期间是否有其他已经加载了poc的，就不再重复加载了
	if poc, ok := CustomCache.Get(hashStr); ok {
		return poc, nil
	}
	var tp = template.Template{}
	err := yaml.Unmarshal(content, &tp)
	if err != nil {
		return nil, fmt.Errorf("yaml template unmarshal fail, err: %w", err)
	}
	if err = tp.Compile(); err != nil {
		return nil, fmt.Errorf("yaml template compile fail, err: %w, poc_id: %s", err, tp.ID())
	}
	warning, err := rulecheck.CheckTemplateRule(content, false)
	if err != nil {
		return nil, fmt.Errorf("rule check exist error, err: %w, poc_id: %s", err, tp.ID())
	} else if warning != nil {
		slog.WarnContext(ctx, "poc check warning", "warning", warning, "poc_id", tp.ID())
	}
	if poc, ok := CustomCache.Get(hashStr); ok {
		return poc, nil
	}
	CustomCache.Add(hashStr, &tp)
	return &tp, nil
}

func LoadSystemPocs(ctx context.Context, directory string) error {
	var pocs []npoc.PoC
	files, err := file.GetYamlFilesInDir(directory)
	if err != nil {
		return fmt.Errorf("yaml template files get fail, error: %w", err)
	}
	for _, f := range files {
		b, _ := os.ReadFile(f)
		var tp template.Template
		err = yaml.Unmarshal(b, &tp)
		if err != nil {
			slog.ErrorContext(ctx, "yaml template unmarshal fail", slogext.Error(err), "file", f)
			continue
		}
		if err = tp.Compile(); err != nil {
			slog.ErrorContext(ctx, "yaml template compile fail", slogext.Error(err), "file", f)
			continue
		}
		warning, err := rulecheck.CheckTemplateRule(b, true)
		if err != nil {
			slog.ErrorContext(ctx, "rule check exist error", slogext.Error(err), "file", f)
			continue
		} else if warning != nil {
			slog.WarnContext(ctx, "poc check warning", "warning", warning, "file_name", f)
		}
		pocs = append(pocs, &tp)
	}
	SysCache.Add(PocCacheKey, pocs)
	return nil
}
