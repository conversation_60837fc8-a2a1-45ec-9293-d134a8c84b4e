package fastjson

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/hashicorp/golang-lru/v2/expirable"

	"github.acme.red/intelli-sec/npoc"
)

const ID = string(npoc.FastjsonType)

type PoC struct {
	FirstScaned     *expirable.LRU[string, bool]
	QueryJsonScaned *expirable.LRU[string, bool]
	BodyJsonScaned  *expirable.LRU[string, bool]
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "fastjson 反序列化漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.FastjsonType,
		Tags:           nil,
		Description:    "fastjson在解析json的过程中，支持使用autoType来实例化某一个具体的类，并调用该类的set/get方法来访问属性。通过查找代码中相关的方法，即可构造出一些恶意利用链，造成任意文件读取、命令执行、内网端口扫描、攻击内网网站、发起Dos攻击等危害。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	if !p.NeedScan(c) {
		return nil
	}
	p.desScan(c)
	return nil
}

func (p *PoC) NeedScan(c *npoc.HTTPContext) bool {
	// 这个插件一个 website 最多只应该跑三次，一次为首个任意请求， 一次为 GET 且 query 中包含 json 字样时，最后一次为 body 为 json
	if c.Task().Client.RdnsClient == nil {
		return false
	}
	if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_first_scaned", ID), struct{}{}); !ok {
		return true
	}
	rawReq := c.Task().Request

	if rawReq.Method == http.MethodGet && (strings.Contains(rawReq.URL.String(), "%7b") || strings.Contains(rawReq.URL.String(), "{")) {
		if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_query_scaned", ID), struct{}{}); !ok {
			return true
		}
	}

	if strings.Contains(strings.ToLower(rawReq.Header.Get("Content-Type")), "json") {
		if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_body_scaned", ID), struct{}{}); !ok {
			return true
		}
	}
	return false
}
