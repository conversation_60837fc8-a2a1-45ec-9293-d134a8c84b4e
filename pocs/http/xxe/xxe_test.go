package xxe

import (
	"sync"
	"testing"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/test"
)

func TestEchoXXEPayloads(t *testing.T) {
	testCases := []test.TestCase{
		test.CreateXXETestCase("Echo Linux Passwd", "http://**********:9014/?route=echo_linux_passwd", "ELnxPwd", httpv.MethodPost, []byte("xml=test")),
		test.CreateXXETestCase("Echo PHP Filter Passwd", "http://**********:9014/?route=echo_php_filter_passwd", "EPLPwdB64", httpv.MethodPost, []byte("xml=test")),
		test.CreateXXETestCase("Error PHP Data URI", "http://**********:9014/?route=error_php_data_uri", "ErrPLDuPwd", httpv.MethodPost, []byte("xml=test")),
		test.CreateXXETestCase("Error Java LDTD", "http://**********:9015/xxejavaerror/process", "ErrLLdtdPwd", httpv.MethodPost, []byte("xml_payload=test")),
		test.CreateXXETestCase("XInclude Linux Passwd", "http://**********:9016/", "XILPwd", httpv.MethodPost, []byte("xml_payload=test")),
		test.CreateXXETestCase("XInclude PHP Filter Passwd", "http://**********:9016/", "XIPLPwdB64", httpv.MethodPost, []byte("xml_payload=test")),
	}

	poc := &PoC{}
	test.ExecuteVulnerabilityTest(t, testCases, func(hc *npoc.HTTPContext) error {
		// 合并所有相关的载荷用于echo测试
		testPayloads := append(append([]Payload{}, echoPayloads...), errorPayloads...)
		testPayloads = append(testPayloads, xincludePayloads...)

		poc.echoXMLScan(hc, false, testPayloads)
		return nil
	})
}

func TestBlindXXEPayloads(t *testing.T) {
	testCases := []test.TestCase{
		test.CreateXXETestCase("Blind Parameter Entity", "http://**********:9014/?route=blind_param_entity", "BPE", httpv.MethodPost, []byte("xml=test")),
		test.CreateXXETestCase("Blind General Entity", "http://**********:9014/?route=blind_general_entity", "BGE", httpv.MethodPost, []byte("xml=test")),
		test.CreateXXETestCase("Blind Doctype System", "http://**********:9014/?route=blind_doctype_system", "BDS", httpv.MethodPost, []byte("xml=test")),
	}

	poc := &PoC{}
	var oobWg sync.WaitGroup

	test.ExecuteOOBTest(t, testCases, func(hc *npoc.HTTPContext) error {
		poc.blindXMLScan(hc, false, blindPayloads, &oobWg)
		return nil
	}, func() {
		oobWg.Wait()
	})
}

func TestDefaultXXE(t *testing.T) {
	testCases := []test.TestCase{
		test.NewTestCase("Default XXE JSON").
			WithURL("http://**********:9017/process_json").
			WithMethod(httpv.MethodPost).
			WithBody([]byte("{\"name\": \"test\", \"value\": 123}")).
			WithContentType("application/json").
			WithPayload("BPE", "xxe").
			WithExpectedCategory(npoc.XXEType).
			WithDescription("默认XXE JSON测试").
			Build(),

		test.NewTestCase("Default XXE Form").
			WithURL("http://**********:9017/process_params").
			WithMethod(httpv.MethodPost).
			WithBody([]byte("item=apple")).
			WithContentType("application/x-www-form-urlencoded").
			WithPayload("BPE", "xxe").
			WithExpectedCategory(npoc.XXEType).
			WithDescription("默认XXE表单测试").
			Build(),

		test.NewTestCase("Default XXE Text").
			WithURL("http://**********:9017/process_other").
			WithMethod(httpv.MethodPost).
			WithBody([]byte("Hello XXE Target!")).
			WithContentType("text/plain").
			WithPayload("BPE", "xxe").
			WithExpectedCategory(npoc.XXEType).
			WithDescription("默认XXE文本测试").
			Build(),
	}

	poc := &PoC{}
	var oobWg sync.WaitGroup

	test.ExecuteOOBTest(t, testCases, func(hc *npoc.HTTPContext) error {
		poc.defaultBlindXMLScan(hc, &oobWg)
		return nil
	}, func() {
		oobWg.Wait()
	})
}
