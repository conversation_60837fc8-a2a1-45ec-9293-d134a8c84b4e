package xxe

import (
	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

func (p *PoC) echoXMLScan(c *npoc.HTTPContext, isXMLReq bool, payloads []Payload) bool {
	rawReq := c.Task().Request
	success := false
	for key, param := range rawReq.Params() {
		if !param.NeedCheck || param.ParamType == httpv.ParamTypePath {
			continue
		}
		for _, payload := range payloads {
			select {
			case <-c.Context.Done():
				return false
			default:
			}
			scanReq, scanResp, err := c.Task().Client.SendNewRequest(c.Context, rawReq, key, payload.XMLContent)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
				continue
			}
			if payload.CheckerReg.Match(scanResp.Body) {
				extra := map[string]string{npoc.ExtraKeyCheckRule: payload.CheckerReg.String(), npoc.ExtraKeySubCategory: "xxe_echo_param"}
				p.reportVulnerability(c, scanReq, scanResp, param.Key, payload.XMLContent, extra, nil, nil)
				success = true
				break
			}
		}
	}
	if !isXMLReq {
		return success
	}
	for _, payload := range payloads {
		newReq := rawReq.BuildReqWithBody([]byte(payload.XMLContent))
		scanResp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			continue
		}
		if payload.CheckerReg.Match(scanResp.Body) {
			extra := map[string]string{npoc.ExtraKeyCheckRule: payload.CheckerReg.String(), npoc.ExtraKeySubCategory: "xxe_echo_body"}
			p.reportVulnerability(c, newReq, scanResp, "", payload.XMLContent, extra, nil, nil)
			success = true
			break
		}
	}

	return success
}
