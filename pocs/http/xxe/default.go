package xxe

import (
	"log/slog"
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

func (p *PoC) defaultBlindXMLScan(c *npoc.HTTPContext, oobWg *sync.WaitGroup) {
	rawReq := c.Task().Request
	bodyType := utils.DetectRequestBodyType(rawReq.Body, rawReq.Header)
	oobChecker, err := makeOOBChecker(c, blindPayloads[0].XMLContent)
	if err != nil {
		slog.ErrorContext(c.Context, "生成OOB payload失败", "error", err)
		return
	}

	var requestXMLBody string
	var conversionError bool

	switch bodyType {
	case utils.BodyTypeJSON:
		toXML, err := ConvertJSONToXML(rawReq.Body)
		if err != nil {
			slog.WarnContext(c.Context, "JSON转XML失败，将使用默认payload逻辑", "error", err)
			conversionError = true
			break
		}
		xmlo, err := MergePayloadAndDataXML(oobChecker.payload, toXML)
		if err != nil {
			slog.WarnContext(c.Context, "合并JSON转换的XML与payload失败，将使用默认payload逻辑", "error", err)
			conversionError = true
			break
		}
		requestXMLBody = xmlo
	case utils.BodyTypeParams:
		toXML, err := ConvertParamsToXML(rawReq.Body)
		if err != nil {
			slog.WarnContext(c.Context, "Params转XML失败，将使用默认payload逻辑", "error", err)
			conversionError = true
			break
		}
		xmlo, err := MergePayloadAndDataXML(oobChecker.payload, toXML)
		if err != nil {
			slog.WarnContext(c.Context, "合并Params转换的XML与payload失败，将使用默认payload逻辑", "error", err)
			conversionError = true
			break
		}
		requestXMLBody = xmlo
	default:
		conversionError = true
	}

	if conversionError {
		requestXMLBody = oobChecker.payload
	}

	newReq := rawReq.BuildReqWithBodyAndContentType([]byte(requestXMLBody), "application/xml")
	scanResp, err := c.Task().Client.Do(c.Context, newReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
	}
	oobWg.Add(1)
	go func(checker checker, req *httpv.Request, resp *httpv.Response) {
		defer utils.RecoverFun(c.Context)
		defer c.Task().Client.RdnsClient.RemoveURL(checker.oobURL)
		defer oobWg.Done()
		if oobutils.WaitForOOBTrigger(c.Context, checker.oobURL) {
			oobDetails := oobutils.ExtractOOBDetails(c.Context, checker.oobURL)
			extra := map[string]string{npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: checker.oobURL.URL(), npoc.ExtraKeySubCategory: "xxe_blind_param"}
			p.reportVulnerability(c, req, resp, "", checker.payload, extra, checker.oobURL, oobDetails)
		}
	}(oobChecker, newReq, scanResp)
}
