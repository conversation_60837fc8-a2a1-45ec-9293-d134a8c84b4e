package xxe

import (
	"sync"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/guess"
)

const ID = string(npoc.XXEType)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "XXE",
		PocType:        npoc.GenericPocType,
		Category:       npoc.XXEType,
		Tags:           nil,
		Description:    "XXE漏洞发生在应用程序解析XML输入时，没有禁止外部实体的加载，导致可加载恶意外部文件和代码，造成任意文件读取、命令执行、内网端口扫描、攻击内网网站、发起Dos攻击等危害。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	req := c.Task().Request
	params := req.Params()
	if len(params) == 0 {
		return nil
	}
	isXMLReq := guess.IsXMLRequest(req)
	ePayloads := append(append(echoPayloads, errorPayloads...), xincludePayloads...)
	var oobWg sync.WaitGroup
	if !p.echoXMLScan(c, isXMLReq, ePayloads) && c.Task().Client.RdnsClient != nil {
		p.blindXMLScan(c, isXMLReq, blindPayloads, &oobWg)
	}
	if !isXMLReq {
		p.defaultBlindXMLScan(c, &oobWg)
	}
	oobWg.Wait()
	return nil
}

func (p *PoC) reportVulnerability(
	c *npoc.HTTPContext,
	req *httpv.Request,
	resp *httpv.Response,
	param string,
	payload string,
	extra map[string]string,
	oobURL *client.URL,
	oobDetails []*npoc.OOBDetail,
) {
	metaData := p.Metadata()
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}
	vuln := &npoc.Vulnerability{
		Method:      req.Method,
		Category:    metaData.Category,
		PocType:     metaData.PocType,
		Severity:    metaData.Severity,
		Param:       param,
		Payload:     payload,
		URL:         c.Task().Request.URL.String(),
		PoC:         p.ID(),
		HTTP:        vHTTP,
		Name:        metaData.Name,
		Description: metaData.Description,
		Extra:       extra,
		OOBUrl:      oobURL,
		OOBDetails:  oobDetails,
		Confidence:  npoc.ConfidenceHigh,
	}

	c.OutputVulnerability(vuln)
}
