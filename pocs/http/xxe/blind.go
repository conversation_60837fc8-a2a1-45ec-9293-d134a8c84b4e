package xxe

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

func (p *PoC) blindXMLScan(c *npoc.HTTPContext, isXMLReq bool, payloads []Payload, oobWg *sync.WaitGroup) {
	rawReq := c.Task().Request

	// 第一个循环：参数注入
	for key, param := range rawReq.Params() {
		if !param.NeedCheck || param.ParamType == httpv.ParamTypePath {
			continue
		}
		for _, payload := range payloads {
			select {
			case <-c.Context.Done():
				return
			default:
			}
			oobChecker, err := makeOOBChecker(c, payload.XMLContent)
			if err != nil {
				continue
			}
			scanReq, scanResp, err := c.Task().Client.SendNewRequest(c.Context, rawReq, key, oobChecker.payload)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			}
			oobWg.Add(1)
			go func(checker checker, req *httpv.Request, resp *httpv.Response, pl Payload) {
				defer utils.RecoverFun(c.Context)
				defer c.Task().Client.RdnsClient.RemoveURL(checker.oobURL)
				defer oobWg.Done()
				if oobutils.WaitForOOBTrigger(c.Context, checker.oobURL) {
					oobDetails := oobutils.ExtractOOBDetails(c.Context, checker.oobURL)
					extra := map[string]string{
						npoc.ExtraKeyCheckRule:   npoc.ExtraValueOOBCheck,
						npoc.ExtraKeyOOBUrl:      checker.oobURL.URL(),
						npoc.ExtraKeySubCategory: "xxe_blind_param",
					}
					p.reportVulnerability(c, req, resp, "", pl.XMLContent, extra, checker.oobURL, oobDetails)
				}
			}(oobChecker, scanReq, scanResp, payload)
		}
	}

	if !isXMLReq {
		return
	}

	// 第二个循环：请求体注入
	for _, payload := range payloads {
		oobChecker, err := makeOOBChecker(c, payload.XMLContent)
		if err != nil {
			continue
		}
		newReq := rawReq.BuildReqWithBody([]byte(oobChecker.payload))
		scanResp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		}
		oobWg.Add(1)
		go func(checker checker, req *httpv.Request, resp *httpv.Response, pl Payload) {
			defer utils.RecoverFun(c.Context)
			defer c.Task().Client.RdnsClient.RemoveURL(checker.oobURL)
			defer oobWg.Done()
			if oobutils.WaitForOOBTrigger(c.Context, checker.oobURL) {
				oobDetails := oobutils.ExtractOOBDetails(c.Context, checker.oobURL)
				extra := map[string]string{
					npoc.ExtraKeyCheckRule:   npoc.ExtraValueOOBCheck,
					npoc.ExtraKeyOOBUrl:      checker.oobURL.URL(),
					npoc.ExtraKeySubCategory: "xxe_blind_body",
				}
				p.reportVulnerability(c, req, resp, "", pl.XMLContent, extra, checker.oobURL, oobDetails)
			}
		}(oobChecker, newReq, scanResp, payload)
	}
}
