package lfr

import (
	"net/url"
	"reflect"
	"testing"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

func TestGetPayloads_UnknownOS_Contexts(t *testing.T) {
	tests := []struct {
		name                  string
		detectedOS            string
		contextType           string
		originalValue         string
		expectedPayloadsCount int
	}{
		{
			name:                  "Unknown OS, ParamTypeQuery, empty originalValue",
			detectedOS:            detector.OSUnknown,
			contextType:           httpv.ParamTypeQuery,
			originalValue:         "",
			expectedPayloadsCount: 42,
		},
		{
			name:                  "Unknown OS, ParamTypePath, empty originalValue",
			detectedOS:            detector.OSUnknown,
			contextType:           httpv.ParamTypePath,
			originalValue:         "",
			expectedPayloadsCount: 87,
		},
		{
			name:                  "Unknown OS, ParamTypeQuery, originalValue with slash",
			detectedOS:            detector.OSUnknown,
			contextType:           httpv.ParamTypeQuery,
			originalValue:         "test/path",
			expectedPayloadsCount: 63,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			payloads := getPayloads(tt.detectedOS, tt.contextType, tt.originalValue)
			if len(payloads) != tt.expectedPayloadsCount {
				t.Errorf("getPayloads() for %s: expected %d payloads, got %d", tt.name, tt.expectedPayloadsCount, len(payloads))
				// 可选：打印生成的payload以进行调试
				for _, p := range payloads {
					t.Logf("%s", p.Value)
				}
			}
		})
	}
}

func TestExtractPathSegments(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected []PathSegment
	}{
		{
			name: "空路径",
			path: "",
			expected: []PathSegment{
				{Path: "/"},
			},
		},
		{
			name: "根路径",
			path: "/",
			expected: []PathSegment{
				{Path: "/"},
			},
		},
		{
			name: "单级路径（文件）",
			path: "/aa",
			expected: []PathSegment{
				{Path: "/"},
			},
		},
		{
			name: "单级路径（目录）",
			path: "/aa/",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
			},
		},
		{
			name: "双级路径（文件）",
			path: "/aa/bb",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
			},
		},
		{
			name: "双级路径（目录）",
			path: "/aa/bb/",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
			},
		},
		{
			name: "三级路径（文件）",
			path: "/aa/bb/cc",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
			},
		},
		{
			name: "三级路径（目录）",
			path: "/aa/bb/cc/",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
				{Path: "/aa/bb/cc/"},
			},
		},
		{
			name: "四级路径（文件）",
			path: "/aa/bb/cc/dd",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
				{Path: "/aa/bb/cc/"},
			},
		},
		{
			name: "四级路径（目录）",
			path: "/aa/bb/cc/dd/",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
				{Path: "/aa/bb/cc/"},
			},
		},
		{
			name: "带明确文件扩展名的路径",
			path: "/aa/bb/file.txt",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
			},
		},
		{
			name: "非规范路径（包含../）",
			path: "/aa/../bb/cc",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/bb/"},
			},
		},
		{
			name: "非规范路径（包含./）",
			path: "/aa/./bb/cc",
			expected: []PathSegment{
				{Path: "/"},
				{Path: "/aa/"},
				{Path: "/aa/bb/"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建一个模拟请求
			req := &httpv.Request{}
			req.URL = &url.URL{Path: tt.path}

			// 调用函数
			got := extractPathSegments(req)

			// 检查结果
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("extractPathSegments() = %v, want %v", got, tt.expected)
			}
		})
	}
}
