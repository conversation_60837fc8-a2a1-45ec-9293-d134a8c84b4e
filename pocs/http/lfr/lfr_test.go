package lfr

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/test"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

// TestCase 定义测试用例结构
type TestCase struct {
	Name        string
	URL         string
	Method      string
	Body        []byte
	ContentType string
	// 指纹信息配置
	OS     string // detector.OSLinux, detector.OSWindows, detector.OSUnknown
	Server string // detector.ServerApache, detector.ServerNginx, detector.ServerIIS, detector.ServerUnknown
	IsPHP  bool
	// 期望结果
	ExpectVuln  bool
	Description string
}

// 创建带指纹信息的测试上下文
func createTestHTTPContextWithFingerprint(tc TestCase) (*npoc.HTTPContext, error) {
	c, err := test.CreateTestHTTPContext(tc.URL, tc.Method, tc.Body, tc.ContentType)
	if err != nil {
		return nil, err
	}
	// 设置指纹信息
	var fingerprints []httpv.FingerprintInfo
	// 添加OS指纹
	if tc.OS != "" {
		fingerprints = append(fingerprints, httpv.FingerprintInfo{Name: tc.OS})
	}
	// 添加Server指纹
	if tc.Server != "" {
		fingerprints = append(fingerprints, httpv.FingerprintInfo{Name: tc.Server})
	}
	// 添加PHP指纹
	if tc.IsPHP {
		fingerprints = append(fingerprints, httpv.FingerprintInfo{Name: detector.LangPHP})
	}
	c.Task().FingerPrint = fingerprints
	return c, nil
}

// 测试结果结构体
type testResult struct {
	Name         string
	Duration     time.Duration
	VulnNum      int
	RequestCount int64 // 新增：请求包数量
	Error        error
}

func TestFuzzRequestParamsConcurrent(t *testing.T) {
	testCases := []TestCase{
		{
			Name:        "Linux_PHP_Query_Param",
			URL:         "http://**********:9022/vulnerable/wrapper.php?content=info",
			Method:      "GET",
			OS:          detector.OSLinux,
			IsPHP:       true,
			ExpectVuln:  true,
			Description: "Linux环境下PHP应用的查询参数测试",
		},
		{
			Name:        "Linux_NonPHP_Query_Param",
			URL:         "http://**********:9022/include.php?file=test-files/config.txt",
			Method:      "GET",
			OS:          detector.OSLinux,
			IsPHP:       false,
			ExpectVuln:  true,
			Description: "Linux环境下非PHP应用的查询参数测试",
		},
		{
			Name:        "基础绕过测试",
			URL:         "http://**********:9022/vulnerable/basic.php?page=home",
			Method:      "GET",
			OS:          detector.OSUnknown,
			IsPHP:       true,
			ExpectVuln:  true,
			Description: "对../做了基础的过滤",
		},
		{
			Name:        "高级绕过测试",
			URL:         "http://**********:9022/vulnerable/filtered.php?include=/var/log/apache2/access.log",
			Method:      "GET",
			OS:          detector.OSUnknown,
			IsPHP:       false,
			ExpectVuln:  true,
			Description: "过滤的更多的内容",
		},
		{
			Name:        "空字节绕过测试",
			URL:         "http://**********:9022/vulnerable/nullbyte.php?template=/",
			Method:      "GET",
			OS:          detector.OSUnknown,
			IsPHP:       false,
			ExpectVuln:  true,
			Description: "只有在有空字节的时候才能读取到内容",
		},
	}

	poc := &PoC{}
	vulnNum := len(testCases)
	trueVuln := 0
	var totalRequestCount int64 // 新增：总请求数统计
	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make([]testResult, 0, len(testCases))

	// 记录总开始时间
	totalStart := time.Now()

	for _, tc := range testCases {
		wg.Add(1)
		go func(tc TestCase) {
			defer wg.Done()

			start := time.Now()
			result := testResult{Name: tc.Name}

			c, err := createTestHTTPContextWithFingerprint(tc)
			if err != nil {
				result.Error = fmt.Errorf("创建测试上下文失败: %v", err)
				result.Duration = time.Since(start)
				mu.Lock()
				results = append(results, result)
				mu.Unlock()
				t.Errorf("测试用例 %s: %v", tc.Name, result.Error)
				return
			}

			// 记录执行前的请求数量
			requestCountBefore := c.Task().Client.RequestCounter.Load()

			// 执行测试
			err = poc.fuzzRequestParams(c, tc.OS, tc.IsPHP)
			if err != nil {
				result.Error = fmt.Errorf("fuzzRequestParams执行失败: %v", err)
				result.Duration = time.Since(start)
				result.RequestCount = c.Task().Client.RequestCounter.Load() - requestCountBefore
				mu.Lock()
				results = append(results, result)
				mu.Unlock()
				t.Errorf("测试用例 %s: %v", tc.Name, result.Error)
				return
			}

			// 记录执行后的请求数量
			requestCountAfter := c.Task().Client.RequestCounter.Load()
			result.RequestCount = requestCountAfter - requestCountBefore

			vuln := c.GetResult().Vulnerabilities
			result.VulnNum = len(vuln)
			result.Duration = time.Since(start)

			mu.Lock()
			trueVuln += len(vuln)
			totalRequestCount += result.RequestCount
			results = append(results, result)
			mu.Unlock()

			t.Logf("测试用例 %s 执行完成: %s (耗时: %v, 发包数: %d)",
				tc.Name, tc.Description, result.Duration, result.RequestCount)
		}(tc)
	}

	wg.Wait()
	totalDuration := time.Since(totalStart)

	// 计算平均时间和平均发包数
	if len(results) > 0 {
		var totalTestDuration time.Duration
		for _, result := range results {
			totalTestDuration += result.Duration
		}
		avgDuration := totalTestDuration / time.Duration(len(results))
		avgRequestCount := float64(totalRequestCount) / float64(len(results))

		t.Logf("并发执行完成 - 总耗时: %v, 平均单个测试耗时: %v, 总发包数: %d, 平均发包数: %.2f",
			totalDuration, avgDuration, totalRequestCount, avgRequestCount)

		// 打印详细结果
		fmt.Printf("\n=== TestFuzzRequestParamsConcurrent 详细结果 ===\n")
		fmt.Printf("总测试数: %d\n", len(results))
		fmt.Printf("总耗时: %v\n", totalDuration)
		fmt.Printf("平均单个测试耗时: %v\n", avgDuration)
		fmt.Printf("总发包数: %d\n", totalRequestCount)
		fmt.Printf("平均发包数: %.2f\n", avgRequestCount)
		fmt.Printf("发现漏洞总数: %d\n", trueVuln)

		fmt.Printf("\n各测试用例详情:\n")
		for _, result := range results {
			status := "成功"
			if result.Error != nil {
				status = "失败"
			}
			fmt.Printf("  %s: %s (耗时: %v, 发包数: %d, 漏洞数: %d)\n",
				result.Name, status, result.Duration, result.RequestCount, result.VulnNum)
		}
		fmt.Printf("=======================================\n\n")
	}

	if trueVuln != vulnNum {
		t.Errorf("期望得到的漏洞数量：%d，实际得到的漏洞数量：%d", vulnNum, trueVuln)
	}
}

func TestFuzzRequestPathConcurrent(t *testing.T) {
	testCases := []TestCase{
		{
			Name:       "路径【..%2f/】测试",
			URL:        "http://**********:9023/payload1/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【..%2F】测试",
			URL:        "http://**********:9023/payload2/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【.%%32%65/】测试",
			URL:        "http://**********:9023/payload3/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【%C0%AF../】测试",
			URL:        "http://**********:9023/payload4/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【..%252F】测试",
			URL:        "http://**********:9023/payload5/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【%c0%ae%c0%ae/】测试",
			URL:        "http://**********:9023/payload6/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【?】测试",
			URL:        "http://**********:9023/payload7/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "路径【#/////】测试",
			URL:        "http://**********:9023/payload8/index.html",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerUnknown,
			ExpectVuln: true,
		},
		{
			Name:       "ApacheDocument RootConfusion漏洞",
			URL:        "http://**********:9024/aa/bb/cc/",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerApache,
			ExpectVuln: true,
		},
		{
			Name:       "Nginx Alias漏洞",
			URL:        "http://**********:9025/uploads/files.txt",
			Method:     "GET",
			OS:         detector.OSUnknown,
			Server:     detector.ServerNginx,
			ExpectVuln: true,
		},
	}

	poc := &PoC{}
	vulnNum := len(testCases)
	trueVuln := 0
	var totalRequestCount int64
	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make([]testResult, 0, len(testCases))

	// 记录总开始时间
	totalStart := time.Now()

	for _, tc := range testCases {
		wg.Add(1)
		go func(tc TestCase) {
			defer wg.Done()

			start := time.Now()
			result := testResult{Name: tc.Name}

			c, err := createTestHTTPContextWithFingerprint(tc)
			if err != nil {
				result.Error = fmt.Errorf("创建测试上下文失败: %v", err)
				result.Duration = time.Since(start)
				mu.Lock()
				results = append(results, result)
				mu.Unlock()
				t.Errorf("测试用例 %s: %v", tc.Name, result.Error)
				return
			}

			// 记录执行前的请求数量
			requestCountBefore := c.Task().Client.RequestCounter.Load()

			// 执行测试
			err = poc.fuzzURIPath(c, tc.OS, tc.Server)
			if err != nil {
				result.Error = fmt.Errorf("fuzzURIPath执行失败: %v", err)
				result.Duration = time.Since(start)
				result.RequestCount = c.Task().Client.RequestCounter.Load() - requestCountBefore
				mu.Lock()
				results = append(results, result)
				mu.Unlock()
				t.Errorf("测试用例 %s: %v", tc.Name, result.Error)
				return
			}

			// 记录执行后的请求数量
			requestCountAfter := c.Task().Client.RequestCounter.Load()
			result.RequestCount = requestCountAfter - requestCountBefore

			vuln := c.GetResult().Vulnerabilities
			result.VulnNum = len(vuln)
			result.Duration = time.Since(start)

			// 打印第一个漏洞的payload（如果存在）
			if len(vuln) > 0 {
				fmt.Printf("测试用例 %s 发现漏洞Payload: %s\n", tc.Name, vuln[0].Payload)
			}

			mu.Lock()
			trueVuln += len(vuln)
			totalRequestCount += result.RequestCount
			results = append(results, result)
			mu.Unlock()

			t.Logf("测试用例 %s 执行完成: %s (耗时: %v, 发包数: %d)",
				tc.Name, tc.Description, result.Duration, result.RequestCount)
		}(tc)
	}

	wg.Wait()
	totalDuration := time.Since(totalStart)

	// 计算平均时间和平均发包数
	if len(results) > 0 {
		var totalTestDuration time.Duration
		for _, result := range results {
			totalTestDuration += result.Duration
		}
		avgDuration := totalTestDuration / time.Duration(len(results))
		avgRequestCount := float64(totalRequestCount) / float64(len(results))

		t.Logf("并发执行完成 - 总耗时: %v, 平均单个测试耗时: %v, 总发包数: %d, 平均发包数: %.2f",
			totalDuration, avgDuration, totalRequestCount, avgRequestCount)

		// 打印详细结果
		fmt.Printf("\n=== TestFuzzRequestPathConcurrent 详细结果 ===\n")
		fmt.Printf("总测试数: %d\n", len(results))
		fmt.Printf("总耗时: %v\n", totalDuration)
		fmt.Printf("平均单个测试耗时: %v\n", avgDuration)
		fmt.Printf("总发包数: %d\n", totalRequestCount)
		fmt.Printf("平均发包数: %.2f\n", avgRequestCount)
		fmt.Printf("发现漏洞总数: %d\n", trueVuln)

		fmt.Printf("\n各测试用例详情:\n")
		for _, result := range results {
			status := "成功"
			if result.Error != nil {
				status = "失败"
			}
			fmt.Printf("  %s: %s (耗时: %v, 发包数: %d, 漏洞数: %d)\n",
				result.Name, status, result.Duration, result.RequestCount, result.VulnNum)
		}
		fmt.Printf("=======================================\n\n")
	}

	if trueVuln != vulnNum {
		t.Errorf("期望得到的漏洞数量：%d，实际得到的漏洞数量：%d", vulnNum, trueVuln)
	}
}

// 错误处理测试（保持原样，因为主要用于测试错误情况）
func TestErrorHandling(t *testing.T) {
	testCases := []TestCase{
		{
			Name:        "Invalid_URL",
			URL:         "invalid-url",
			Method:      "GET",
			OS:          detector.OSLinux,
			IsPHP:       false,
			ExpectVuln:  false,
			Description: "无效URL测试",
		},
		{
			Name:        "Empty_URL",
			URL:         "",
			Method:      "GET",
			OS:          detector.OSLinux,
			IsPHP:       false,
			ExpectVuln:  false,
			Description: "空URL测试",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.Name, func(t *testing.T) {
			_, err := createTestHTTPContextWithFingerprint(tc)
			if err != nil {
				t.Logf("预期的错误: %v", err)
				return // 这是预期的错误
			}
			t.Errorf("应该产生错误但没有: %s", tc.Description)
		})
	}
}
