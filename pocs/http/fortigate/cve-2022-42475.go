package fortigate

import (
	"github.com/hashicorp/go-version"

	"github.acme.red/intelli-sec/npoc"
)

var versionLimit42475 []versionLimit //nolint:gochecknoglobals // 初始化的时候创建的全局变量

func init() { //nolint:gochecknoinits // 初始化规则
	versionLimit42475Str := [][]string{
		{"v7.0.0-build", "v7.0.13"},
		{"v7.2.0-build", "v7.2.6"},
		{"v7.4.0-build", "v7.4.2"},
	}
	for _, limit := range versionLimit42475Str {
		v1, _ := version.NewVersion(limit[0])
		v2, _ := version.NewVersion(limit[1])
		versionLimit42475 = append(versionLimit42475, versionLimit{v1, v2})
	}
}

func check42475(v *version.Version) *npoc.Vulnerability {
	for _, limit := range versionLimit42475 {
		if versionInLimit(v, limit.min, limit.max) {
			return &npoc.Vulnerability{
				PoC:         "CVE-2022-42475",
				Name:        "Fortigate FortiOS 远程代码执行漏洞（CVE-2022-42475）",
				Category:    npoc.Fortigate,
				Severity:    npoc.SeverityCritical,
				Method:      "GET",
				Param:       "",
				Payload:     "",
				Description: "FortiGate 是 美国飞塔(fortinet)公司旗下的硬件设备，用于防护和管理网络安全，其底层使用的是FortiOS 操作系统，在v7.0.0~v7.0.13，v7.2.0~v7.2.6，v7.4.0~v7.4.2范围内存在远程代码执行漏洞（CVE-2022-42475），攻击者可构造恶意请求出发溢出等，造成远程代码执行",
				URL:         "",
				HTTP:        nil,
				Extra:       nil,
				OOBUrl:      nil,
				OOBDetails:  nil,
				Confidence:  npoc.ConfidenceMedium,
			}
		}
	}
	return nil
}
