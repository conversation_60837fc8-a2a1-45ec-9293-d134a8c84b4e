package fortigate

import (
	"fmt"
	"log/slog"
	"net/url"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/hashicorp/go-version"

	"github.acme.red/intelli-sec/npoc"
)

const ID = string(npoc.Fortigate)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "fortigate 漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.Fortigate,
		Tags:           nil,
		Description:    "FortiGate 是 Fortinet 的下一代防火墙（NGFW），用于保护网络免受各种安全威胁。其底层操作系统FortiOS 在部分版本中存在漏洞",
		Product:        "fortigate",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

type fuzzFn func(v *version.Version) *npoc.Vulnerability

var fuzzfns = []fuzzFn{check42475, check27997, check21762, check55591} //nolint:gochecknoglobals // 初始化定义好的规则

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	if _, ok := c.Task().TaskCache.LoadOrStore(ID, struct{}{}); ok { // 对于同一次task只fuzz一遍
		return nil
	}
	baseURL := fmt.Sprintf("%s://%s", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host)
	for _, r := range rules {
		// 版本获取失败则继续后面的规则，获取成功则停止后续校验
		newReq, resp, hash := getVersionHash(c, r, baseURL)
		if hash == "" { // 获取版本hash失败则尝试下一个规则，否则进行校验后终止其他规则的校验
			continue
		}
		checkHash(c, hash, newReq, resp)
		return nil
	}

	return nil
}

func checkHash(c *npoc.HTTPContext, hash string, newReq *httpv.Request, resp *httpv.Response) {
	data, ok := fortigateInfo[hash]
	if !ok {
		return
	}
	vs, err := version.NewVersion(data.Version)
	if err != nil {
		slog.ErrorContext(c.Context, "version new failed", slogext.Error(err))
		return
	}
	for _, fuzzfn := range fuzzfns {
		vuln := fuzzfn(vs)
		if vuln != nil {
			vuln.URL = newReq.URL.String()
			vuln.HTTP = &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln.Extra = map[string]string{
				npoc.ExtraKeyDes: fmt.Sprintf("hash:%s, model:%s, version:%s, build:%s, cpu:%s, arch:%s", data.Hash, data.Model, data.Version, data.Build, data.CPU, data.ARCH),
			}
			c.OutputVulnerability(vuln)
		}
	}
}

func getVersionHash(c *npoc.HTTPContext, r rule, baseURL string) (*httpv.Request, *httpv.Response, string) {
	newURL, err := url.JoinPath(baseURL, r.Path)
	if err != nil {
		slog.ErrorContext(c.Context, "make new url failed", slogext.Error(err))
		return nil, nil, ""
	}
	newReq, err := httpv.NewRequest("GET", newURL, nil)
	if err != nil {
		slog.ErrorContext(c.Context, "make new http request failed", slogext.Error(err))
		return nil, nil, ""
	}
	newReq.FollowRedirects = true
	if c.Task().Request.Header != nil && c.Task().Request.Header.Get("User-Agent") != "" {
		newReq.Header.Set("User-Agent", c.Task().Request.Header.Get("User-Agent"))
	}
	resp, err := c.Task().Client.Do(c.Context, newReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
		return nil, nil, ""
	}
	group := r.hashRegexp.FindAllStringSubmatch(string(resp.Body), -1)
	if len(group) == 0 || len(group[0]) < 2 {
		return nil, nil, ""
	}
	return newReq, resp, group[0][1]
}

func versionInLimit(v, minVersion, maxVersion *version.Version) bool {
	if v.GreaterThanOrEqual(minVersion) && v.LessThanOrEqual(maxVersion) {
		return true
	} else {
		return false
	}
}
