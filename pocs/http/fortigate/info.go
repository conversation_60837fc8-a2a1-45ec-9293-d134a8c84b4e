package fortigate

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"regexp"

	"github.com/hashicorp/go-version"
)

type versionLimit struct {
	min *version.Version
	max *version.Version
}

type info struct {
	Hash    string `json:"hash"`
	Model   string `json:"model"`
	Version string `json:"version"`
	Build   string `json:"build,omitempty"`
	CPU     string `json:"cpu,omitempty"`
	ARCH    string `json:"arch,omitempty"`
}

type rule struct {
	Path       string
	hashRegexp *regexp.Regexp
}

var rules = []rule{ //nolint:gochecknoglobals // 初始化定义好的规则
	{
		Path:       "/remote/login",
		hashRegexp: regexp.MustCompile(`(?i)src="/sslvpn/js/login\.js\?q=([0-9a-z]{32})">`),
	},
	{
		Path:       "/login",
		hashRegexp: regexp.MustCompile(`(?i)src="/([0-9a-z]{32})/js/login\.js">`),
	},
}

//go:embed fortigate_version.json
var rawInfoData []byte
var fortigateInfo = make(map[string]info) //nolint:gochecknoglobals // 初始化加载好的好的规则

func init() { //nolint:gochecknoinits // 初始化加载静态文件
	if err := json.Unmarshal(rawInfoData, &fortigateInfo); err != nil {
		panic(fmt.Errorf("fortigate info data unmarshal failed: %w", err))
	}
}
