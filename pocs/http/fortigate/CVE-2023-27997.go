package fortigate

import (
	"github.com/hashicorp/go-version"

	"github.acme.red/intelli-sec/npoc"
)

var versionLimit27997 []versionLimit //nolint:gochecknoglobals // 初始化的时候创建的全局变量

func init() { //nolint:gochecknoinits // 初始化规则
	versionLimit27997Str := [][]string{
		{"v7.0.0-build", "v7.0.11"},
		{"v7.2.0-build", "v7.2.4"},
		{"v6.0.0-build", "v6.0.16"},
		{"v6.2.0-build", "v6.2.13"},
		{"v6.4.0-build", "v6.4.14"},
	}
	for _, limit := range versionLimit27997Str {
		v1, _ := version.NewVersion(limit[0])
		v2, _ := version.NewVersion(limit[1])
		versionLimit27997 = append(versionLimit27997, versionLimit{v1, v2})
	}
}

func check27997(v *version.Version) *npoc.Vulnerability {
	for _, limit := range versionLimit27997 {
		if versionInLimit(v, limit.min, limit.max) {
			return &npoc.Vulnerability{
				PoC:         "CVE-2023-27997",
				Name:        "Fortigate FortiOS 远程代码执行漏洞（CVE-2023-27997）",
				Category:    npoc.Fortigate,
				Severity:    npoc.SeverityCritical,
				Method:      "GET",
				Param:       "",
				Payload:     "",
				Description: "FortiGate 是 美国飞塔(fortinet)公司旗下的硬件设备，用于防护和管理网络安全，其底层使用的是FortiOS 操作系统，FortiOS在v6.0.0~v6.0.16，v6.2.0~v6.2.13，v6.4.0~v6.4.12，v7.0.0~v7.0.11，v7.2.0~v7.2.4，范围内存在远程代码执行漏洞（CVE-2023-27997），攻击者可构造恶意请求出发溢出等，造成远程代码执行",
				URL:         "",
				HTTP:        nil,
				Extra:       nil,
				OOBUrl:      nil,
				OOBDetails:  nil,
				Confidence:  npoc.ConfidenceMedium,
			}
		}
	}
	return nil
}
