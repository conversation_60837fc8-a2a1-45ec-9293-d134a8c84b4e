package fortigate

import (
	"github.com/hashicorp/go-version"

	"github.acme.red/intelli-sec/npoc"
)

var versionLimit55591 []versionLimit //nolint:gochecknoglobals // 初始化的时候创建的全局变量

func init() { //nolint:gochecknoinits // 初始化规则
	versionLimit55591Str := [][]string{
		{"v7.0.0-build", "v7.0.16"},
		{"v7.2.0-build", "v7.2.12"},
	}
	for _, limit := range versionLimit55591Str {
		v1, _ := version.NewVersion(limit[0])
		v2, _ := version.NewVersion(limit[1])
		versionLimit55591 = append(versionLimit55591, versionLimit{v1, v2})
	}
}

func check55591(v *version.Version) *npoc.Vulnerability {
	for _, limit := range versionLimit55591 {
		if versionInLimit(v, limit.min, limit.max) {
			return &npoc.Vulnerability{
				PoC:         "CVE-2024-55591",
				Name:        "FortiOS/FortiProxy 身份认证绕过漏洞(CVE-2024-55591)",
				Category:    npoc.Fortigate,
				Severity:    npoc.SeverityCritical,
				Method:      "GET",
				Param:       "",
				Payload:     "",
				Description: "Fortinet FortiOS 是美国飞塔（Fortinet）公司的一套专用于FortiGate网络安全平台上的安全操作系统。该系统为用户提供防火墙、防病毒、IPSec/SSLVPN、Web内容过滤和反垃圾邮件等多种安全功能。FortiProxy 是 Fortinet 推出的一款高性能的安全 Web 网关产品，结合了 Web 过滤、DNS 过滤、数据泄露防护（DLP）、反病毒、入侵防御和高级威胁保护等多种检测技术，以保护用户免受网络攻击。FortiOS 和 FortiProxy 中存在一个身份认证绕过漏洞。未经身份验证的远程攻击者可以通过向 Node.js websocket 模块发送特制请求，成功利用此漏洞可使攻击者获得超级管理员权限",
				URL:         "",
				HTTP:        nil,
				Extra:       nil,
				OOBUrl:      nil,
				OOBDetails:  nil,
				Confidence:  npoc.ConfidenceMedium,
			}
		}
	}
	return nil
}
