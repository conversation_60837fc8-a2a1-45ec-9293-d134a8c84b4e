package ssrf

import (
	"testing"
	"time"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/test"
)

const (
	PayloadTypeSSRFPath  = "ssrf_path"
	PayloadTypeSSRFParam = "ssrf_param"
)

// CreateSSRFTestCases 创建SSRF测试用例
func CreateSSRFTestCases() []test.TestCase {
	return []test.TestCase{
		// 注意：该测试会同时触发参数SSRF和SNI SSRF，所以期望漏洞数为2
		test.CreateSSRFTestCase(
			"参数型SSRF - 参数传递路径",
			"http://**********:9021/vuln1/load_image?path=http://example.com",
			"path",
			"//{{.OOBHost}}",
			PayloadTypeSSRFParam,
			2,
		),

		test.CreateSSRFTestCase(
			"路径型SSRF - OOB测试",
			"http://**********:9021/vuln2/api_proxy/example.com/data",
			"",
			"{{.RawValue}}.{{.OOBHost}}",
			PayloadTypeSSRFPath,
			1,
		),

		test.CreateSSRFTestCase(
			"参数型SSRF - 完整URL参数",
			"http://**********:9021/vuln3/fetch_url?url=http://example.com",
			"url",
			"http:%2f%2f{{.OOBHost}}%2f",
			PayloadTypeSSRFParam,
			1,
		),

		test.CreateSSRFTestCase(
			"参数型SSRF - 参数作为主机名",
			"http://**********:9021/vuln4/proxy?target_host=example.com&path=/resource",
			"target_host",
			"{{.RawValue}}@{{.OOBHost}}",
			PayloadTypeSSRFParam,
			1,
		),

		test.CreateSSRFTestCase(
			"参数型SSRF - 自定义Scheme",
			"http://**********:9021/vuln5/custom_fetch?uri=url:http://example.com/from_uri",
			"uri",
			"url:http:%2f%2f{{.OOBHost}}%2f",
			PayloadTypeSSRFParam,
			1,
		),

		test.CreateSSRFTestCase(
			"参数型SSRF - 双重编码",
			"http://**********:9021/vuln6/double_decode_fetch?param_url=http://example.com",
			"param_url",
			"%252f%252f{{.OOBHost}}",
			PayloadTypeSSRFParam,
			1,
		),
	}
}

// TestSSRF 使用构建器模式的SSRF测试
func TestSSRF(t *testing.T) {
	// 创建PoC实例
	poc := &PoC{}

	// 创建测试框架
	config := test.DefaultTestConfig()
	config.EnableOOB = true
	config.Timeout = 15 * time.Second
	config.DetailedLogging = true
	config.ContinueOnError = true

	framework, err := test.NewTestFramework(t, config)
	if err != nil {
		t.Fatalf("创建测试框架失败: %v", err)
	}

	// 定义自定义执行函数，支持各种payload类型
	executeFunc := func(hc *npoc.HTTPContext, tc test.TestCase) error {
		// 获取自定义payload
		customPayload, hasCustomPayload := tc.Extra["custom_payload"].(string)
		if !hasCustomPayload {
			// 没有自定义payload，使用默认逻辑
			return poc.Execute(hc)
		}

		// 根据payload类型决定使用路径还是参数payload
		var testPathPayloads, testParamPayloads []string
		if tc.PayloadType == PayloadTypeSSRFPath {
			testPathPayloads = []string{customPayload}
		} else {
			testParamPayloads = []string{customPayload}
		}

		// 创建请求副本并设置参数
		checkReq := hc.Task().Request.Clone()
		checkReq.FollowRedirects = false

		// 确保路径参数被解析
		if len(testPathPayloads) > 0 {
			checkReq.ParsePathParams()
		}

		// 执行SSRF探测
		return poc.exploreSSRF(hc, checkReq, testPathPayloads, testParamPayloads)
	}

	// 执行所有测试用例
	testCases := CreateSSRFTestCases()
	results := framework.ExecuteTestCases(testCases, executeFunc)
	framework.ValidateResults(results)
}
