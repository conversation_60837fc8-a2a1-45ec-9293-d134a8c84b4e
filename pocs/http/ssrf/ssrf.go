package ssrf

import (
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/guess"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

const (
	ID                   = string(npoc.SSRFType)
	subCategorySSRFEcho  = "ssrf_echo"
	subCategorySSRFBlind = "ssrf_blind"
	subCategorySSRFSNI   = "ssrf_sni"
)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "ssrf",
		PocType:        npoc.GenericPocType,
		Category:       npoc.SSRFType,
		Tags:           nil,
		Description:    "SSRF漏洞（服务器端请求伪造）：是一种由攻击者构造形成由服务端发起请求的一个安全漏洞。一般情况下，SSRF攻击的目标是从外网无法访问的内部系统。（正是因为它是由服务端发起的，所以它能够请求到与它相连而与外网隔离的内部系统）。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	if c.Task().Client.RdnsClient == nil {
		return errors.New("rdns client is nil")
	}
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false

	return p.exploreSSRF(c, checkReq, pathPayloads, paramPayloads)
}

func (p *PoC) exploreSSRF(c *npoc.HTTPContext, checkReq *httpv.Request, pathPayloads, paramPayloads []string) error {
	params := checkReq.Params()
	if len(params) == 0 {
		return nil
	}

	var oobWg sync.WaitGroup

	// 使用DNSLog探测回显以及HTTP、DNS协议。
	for key, checkParam := range params {
		isPathType := false
		if checkParam.ParamType == httpv.ParamTypePath {
			if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_%s", ID, checkParam.Key), struct{}{}); ok {
				continue
			}
			isPathType = true
		}

		if checkParam.ParamType == httpv.ParamTypeHeader {
			continue // header不需要fuzz。
		}

		var originHost string
		if !checkParam.NeedCheck {
			continue
		}
		if guess.ParamValueIsLink(checkParam) { // 以http[s]开头的参数值，提取其hostname
			rawURL, err := url.Parse(checkParam.Value)
			if err != nil {
				slog.ErrorContext(c.Context, "解析URL错误", slog.Any("err", err.Error()))
				continue
			}
			originHost = rawURL.Host
		}

		var oobPayloads []OOBPayload
		if isPathType {
			oobPayloads = MakePayloads(c, originHost, checkParam.Value, pathPayloads)
		} else {
			oobPayloads = MakePayloads(c, originHost, checkParam.Value, paramPayloads)
		}

		for _, oobPayload := range oobPayloads {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}

			newParam := params[key]
			newParam.Value = oobPayload.Payload
			checkReq.DisableEncoding = true
			newReq := checkReq.BuildRequestWithParams(key, newParam)
			rawRequest, err := newReq.Dump()
			if err != nil {
				slog.ErrorContext(c.Context, "请求体导出字符串失败", slog.Any("err", err.Error()))
				continue
			}
			newReq.UnsafeRawHTTP = []byte(rawRequest)
			resp, err := c.Task().Client.Do(c.Context, newReq)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			}
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			if resp != nil {
				if strings.Contains(string(resp.Body), oobPayload.CheckCode) {
					p.notify(c, oobPayload, vHTTP, checkParam.Key, subCategorySSRFEcho, npoc.ConfidenceHigh, nil)
					return nil
				}
			}
			oobWg.Add(1)
			go func(op OOBPayload, cp httpv.Param, vp *npoc.VulnerabilityHTTP) {
				defer utils.RecoverFun(c.Context)
				defer c.Task().Client.RdnsClient.RemoveURL(op.OOBUrl)
				defer oobWg.Done()
				if oobutils.WaitForOOBTrigger(c.Context, op.OOBUrl) {
					hasVuln := false
					if len(op.OOBUrl.TriggerTypes) == 1 && op.OOBUrl.TriggerTypes[0] == client.LogTypeDNS {
						for _, interaction := range op.OOBUrl.GetInteractions() {
							if interaction.FullID != interaction.UniqueID {
								hasVuln = true
								break
							}
						}
					} else {
						hasVuln = true
					}
					if hasVuln {
						oobDetails := oobutils.ExtractOOBDetails(c.Context, op.OOBUrl)
						p.notify(c, op, vp, cp.Key, subCategorySSRFBlind, npoc.ConfidenceMedium, oobDetails)
					}
				}
			}(oobPayload, checkParam, vHTTP)
		}
	}
	if err := p.sniSSRF(c, checkReq, &oobWg); err != nil {
		return err
	}
	oobWg.Wait()
	return nil
}

// createSNIHTTPClient 创建用于SNI SSRF检测的HTTP客户端
func (p *PoC) createSNIHTTPClient(c *npoc.HTTPContext, oobURL *url.URL) *http.Client {
	transPort := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return (&net.Dialer{
				Timeout:   10 * time.Second, //nolint:mnd // 需要
				KeepAlive: 30 * time.Second, //nolint:mnd // 需要
			}).DialContext(ctx, network, addr)
		},
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,              //nolint:mnd // 需要
		MaxIdleConnsPerHost:   10,               //nolint:mnd // 需要
		IdleConnTimeout:       40 * time.Second, //nolint:mnd // 需要
		TLSHandshakeTimeout:   10 * time.Second, //nolint:mnd // 需要
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig: &tls.Config{ //nolint:gosec // need indeed
			ServerName: oobURL.Host,
		},
	}
	proxy, ok := c.Task().Client.Raw.HTTPClient.Transport.(*http.Transport)
	if ok && proxy.Proxy != nil {
		transPort.Proxy = proxy.Proxy
	}
	return &http.Client{Transport: transPort}
}

// convertHTTPRequestResponse 转换标准HTTP请求响应为httpv格式
func (p *PoC) convertHTTPRequestResponse(sniRequest *http.Request, sniResp *http.Response) *npoc.VulnerabilityHTTP {
	req := &httpv.Request{}
	req.ConvertRequest(sniRequest)

	var resp *httpv.Response
	if sniResp != nil {
		resp = &httpv.Response{}
		resp.ConvertResponse(sniResp)
	}

	return &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}
}

// startOOBDetectionGoroutine 启动OOB检测协程
func (p *PoC) startOOBDetectionGoroutine(c *npoc.HTTPContext, oobLog *client.URL, sniRequest *http.Request, oobWg *sync.WaitGroup) {
	oobWg.Add(1)
	go func() {
		defer utils.RecoverFun(c.Context)
		defer c.Task().Client.RdnsClient.RemoveURL(oobLog)
		defer oobWg.Done()

		if oobutils.WaitForOOBTrigger(c.Context, oobLog) {
			vHTTP := p.convertHTTPRequestResponse(sniRequest, nil)
			oobPayload := OOBPayload{OOBUrl: oobLog}
			oobDetails := oobutils.ExtractOOBDetails(c.Context, oobLog)
			p.notify(c, oobPayload, vHTTP, "", subCategorySSRFSNI, npoc.ConfidenceMedium, oobDetails)
		}
	}()
}

func (p *PoC) sniSSRF(c *npoc.HTTPContext, checkReq *httpv.Request, oobWg *sync.WaitGroup) error {
	// SNI SSRF
	oobLog, checkCode := getSSRFPayload(c)
	if oobLog == nil {
		slog.ErrorContext(c.Context, "注册OOB域名失败", slog.Any("poc.id", p.ID()))
		return nil
	}
	oobURL, err := url.Parse(oobLog.URL())
	if err != nil {
		return err
	}

	if exceeded, _ := c.Task().Client.FailExceededLimit(); exceeded {
		return errors.New(httpv.TooManyFailERR)
	}
	c.Task().Client.HostLimiter.Take()
	select {
	case <-c.Context.Done():
		return c.Context.Err()
	default:
	}

	// 创建SNI客户端并发送请求
	sniClient := p.createSNIHTTPClient(c, oobURL)
	sniRequest, err := http.NewRequestWithContext(c.Context, checkReq.Method, checkReq.URL.String(), bytes.NewReader(checkReq.Body))
	if err != nil {
		return err
	}
	sniRequest.Header.Set("User-Agent", c.Task().Request.Header.Get("User-Agent"))
	sniResp, _ := sniClient.Do(sniRequest)

	// 处理响应
	if sniResp != nil {
		defer func() {
			_ = sniResp.Body.Close()
		}()
		data, err := io.ReadAll(io.LimitReader(sniResp.Body, httpv.RequestBodyMax))
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: err})
			return err
		}

		// 检查是否有直接回显
		if strings.Contains(string(data), checkCode) {
			defer c.Task().Client.RdnsClient.RemoveURL(oobLog)
			vHTTP := p.convertHTTPRequestResponse(sniRequest, sniResp)
			oobPayload := OOBPayload{OOBUrl: oobLog}
			p.notify(c, oobPayload, vHTTP, "", subCategorySSRFSNI, npoc.ConfidenceHigh, nil)
			return nil
		}
	}

	// 启动OOB检测协程（无论是否有响应都需要检测）
	p.startOOBDetectionGoroutine(c, oobLog, sniRequest, oobWg)
	return nil
}

func (p *PoC) notify(c *npoc.HTTPContext, oobPayload OOBPayload, vHTTP *npoc.VulnerabilityHTTP, key, category string, confidence npoc.Confidence, oobDetails []*npoc.OOBDetail) {
	metaData := p.Metadata()
	vuln := &npoc.Vulnerability{
		Method:      c.Task().Request.Method,
		Category:    metaData.Category,
		PocType:     metaData.PocType,
		Severity:    metaData.Severity,
		Param:       key,
		Payload:     oobPayload.Payload,
		URL:         c.Task().Request.URL.String(),
		PoC:         p.ID(),
		HTTP:        vHTTP,
		Name:        metaData.Name,
		Description: metaData.Description,
		Extra:       map[string]string{npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: oobPayload.OOBUrl.URL(), npoc.ExtraKeySubCategory: category},
		OOBUrl:      oobPayload.OOBUrl,
		OOBDetails:  oobDetails,
		Confidence:  confidence,
	}
	c.OutputVulnerability(vuln)
}
