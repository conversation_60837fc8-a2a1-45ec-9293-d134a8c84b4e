package struts2

import (
	"fmt"
	"path"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/struts2/plugins"
)

const JSESSIONID = "JSESSIONID="

const ID = string(npoc.Struts2Type)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "struts2 反序列化漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.Struts2Type,
		Tags:           nil,
		Description:    "Apache Struts是美国阿帕奇（Apache）软件基金会的一个开源项目，是一套用于创建企业级Java Web应用的开源MVC框架。 Apache Struts 2的一些版本中存在安全漏洞。攻击者可通过操纵请求利用该漏洞造成任意命令执行。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	var oobWg sync.WaitGroup

	for id, payloads := range plugins.GenPayloads {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}

		// 检查是否需要退出
		if p.shouldExitEarly(c, id) {
			return nil
		}

		// 检查是否需要缓存过滤
		if p.shouldSkipDueToCache(c, id) {
			continue
		}

		// 执行不同类型的检测
		if p.executePayloadBasedDetection(c, id, payloads, &oobWg) {
			continue // 发现可回显漏洞，直接检测下一个
		}

		p.executeFunctionBasedDetection(c, id)
		p.executeOOBBasedDetection(c, id, &oobWg)
	}

	oobWg.Wait()
	return nil
}

func (p *PoC) processVulnerabilities(c *npoc.HTTPContext, vulns []*npoc.Vulnerability, id string) {
	if len(vulns) == 0 {
		return
	}

	metaData := p.Metadata()
	for i := range vulns {
		vulns[i].Category = metaData.Category
		vulns[i].PocType = metaData.PocType
		vulns[i].Name = metaData.Name + fmt.Sprintf("(%s)", id)
		vulns[i].Description = metaData.Description
		vulns[i].Extra[npoc.ExtraKeySubCategory] = id
		if vulns[i].Severity == "" {
			vulns[i].Severity = metaData.Severity
		}
		c.OutputVulnerability(vulns[i])
	}
}

func (p *PoC) shouldExitEarly(c *npoc.HTTPContext, id string) bool {
	// S2-016 和 S2-017 会fuzz路径，不需要判断struts
	if id != "S2-016" && id != "S2-017" {
		return !IsStruts(c)
	}
	return false
}

func (p *PoC) shouldSkipDueToCache(c *npoc.HTTPContext, id string) bool {
	if id == "S2-015" || id == "S2-037" {
		dir, _ := path.Split(c.Task().Request.URL.Path)
		cacheKey := fmt.Sprintf("%s_%s", ID, dir)
		if _, loaded := c.Task().TaskCache.LoadOrStore(cacheKey, struct{}{}); loaded {
			return true
		}
	}
	return false
}

func (p *PoC) executePayloadBasedDetection(c *npoc.HTTPContext, id string, payloads []plugins.GenPayload, oobWg *sync.WaitGroup) bool {
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return false
		default:
		}

		vulns := plugins.GenericWithParam(c, payload, oobWg)
		p.processVulnerabilities(c, vulns, id)

		// 如果发现存在可回显漏洞，则直接返回
		if len(vulns) > 0 {
			for _, vuln := range vulns {
				if vuln.OOBUrl != nil {
					return true
				}
			}
		}
	}
	return false
}

func (p *PoC) executeFunctionBasedDetection(c *npoc.HTTPContext, id string) {
	if fun, exists := plugins.FuncMap[id]; exists {
		vulns := fun(c)
		p.processVulnerabilities(c, vulns, id)
	}
}

func (p *PoC) executeOOBBasedDetection(c *npoc.HTTPContext, id string, oobWg *sync.WaitGroup) {
	if fun, exists := plugins.OOBFuncMap[id]; exists {
		vulns := fun(c, oobWg)
		p.processVulnerabilities(c, vulns, id)
	}
}

func IsStruts(c *npoc.HTTPContext) bool {
	for _, info := range c.Task().FingerPrint {
		if strings.Contains(info.Name, "struts") {
			return true
		}
	}

	if strings.Contains(c.Task().Request.URL.Path, ".action") || strings.Contains(c.Task().Request.URL.Path, ".do") {
		return true
	}
	if strings.Contains(c.Task().Request.Header.Get("Cookie"), JSESSIONID) || (c.Task().Response != nil && strings.Contains(c.Task().Response.Header.Get("Set-Cookie"), JSESSIONID)) {
		return true
	}

	return false
}
