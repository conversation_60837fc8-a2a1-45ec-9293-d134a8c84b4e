package plugins

import (
	"fmt"
	"net/url"
	"strings"

	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
)

func Check017(c *npoc.HTTPContext) []*npoc.Vulnerability {
	var fazzPath []string
	var vulns []*npoc.Vulnerability

	if c.Task().Request.IsRoot() {
		fazzPath = append(fazzPath, FazzPaths...)
	} else {
		if strings.HasSuffix(c.Task().Request.URL.Path, ".action") {
			fazzPath = append(fazzPath, c.Task().Request.URL.Path)
		}
	}

	for _, path := range fazzPath {
		select {
		case <-c.Context.Done():
			return vulns
		default:
		}
		testRef := fmt.Sprintf("http://%s.com", funk.RandomString(5, utils.LowerChars))
		payload := "redirect:" + testRef
		newReq := c.Task().Request.Clone()
		newReq.URL.Path = path
		newReq.URL.RawQuery = url.QueryEscape(payload)
		newReq.URL.Fragment = ""
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
			continue
		}
		if strings.Contains(resp.Header.Get("Location"), testRef) {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Severity:   npoc.SeverityMedium,
				Payload:    payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: fmt.Sprintf("Location: %s", testRef)},
				Confidence: npoc.ConfidenceMedium,
			}
			vulns = append(vulns, vuln)
			return vulns
		}
	}

	return nil
}
