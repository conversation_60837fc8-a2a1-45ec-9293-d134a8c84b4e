package plugins

import (
	"bytes"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

func GenericWithParam(c *npoc.HTTPContext, genPayload GenPayload, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var vulns []*npoc.Vulnerability
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false

	var localOOBWg sync.WaitGroup
	oobVulnChan := make(chan *npoc.Vulnerability, 100)

	for key, param := range c.Task().Request.Params() {
		if param.ParamType == httpv.ParamTypePath {
			continue
		}
		newPayloads := ReplaceCommand(c.Task().Client.RdnsClient, genPayload)
		for _, payload := range newPayloads {
			select {
			case <-c.Context.Done():
				return vulns
			default:
			}

			req, resp, err := c.Task().Client.SendNewRequest(c.Context, checkReq, key, payload.Payload)
			if err != nil {
				return nil
			}

			if vuln := processPayload(c, payload, req, resp, param.Key, oobWg, &localOOBWg, oobVulnChan); vuln != nil {
				vulns = append(vulns, vuln)
				return vulns
			}
		}
	}

	vulns = append(vulns, waitAndCollectOOBVulns(&localOOBWg, oobVulnChan)...)

	// 如果没有参数则创建一个参数进行测试
	nonPathParamsCount := 0
	for _, param := range c.Task().Request.Params() {
		if param.ParamType != httpv.ParamTypePath {
			nonPathParamsCount++
		}
	}

	if nonPathParamsCount == 0 {
		genPayload.Payload = "a=" + genPayload.Payload
		vulns = append(vulns, GenericWithQuery(c, genPayload, oobWg)...)
	}

	return vulns
}

func GenericWithQuery(c *npoc.HTTPContext, genPayload GenPayload, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var vulns []*npoc.Vulnerability
	newPayloads := ReplaceCommand(c.Task().Client.RdnsClient, genPayload)

	var localOOBWg sync.WaitGroup
	oobVulnChan := make(chan *npoc.Vulnerability, 100)

	for _, payload := range newPayloads {
		select {
		case <-c.Context.Done():
			return vulns
		default:
		}

		newReq := c.Task().Request.Clone()
		newReq.FollowRedirects = false
		if newReq.Method == httpv.MethodGet {
			newReq.URL.RawQuery = payload.Payload
			newReq.URL.RawQuery = newReq.URL.Query().Encode()
		} else {
			newReq.Body = []byte(payload.Payload)
		}

		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			return nil
		}

		if vuln := processPayload(c, payload, newReq, resp, "", oobWg, &localOOBWg, oobVulnChan); vuln != nil {
			vulns = append(vulns, vuln)
			return vulns
		}
	}

	vulns = append(vulns, waitAndCollectOOBVulns(&localOOBWg, oobVulnChan)...)
	return vulns
}

func GenericWithBody(c *npoc.HTTPContext, genPayload GenPayload, ct string, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var vulns []*npoc.Vulnerability
	newPayloads := ReplaceCommand(c.Task().Client.RdnsClient, genPayload)

	var localOOBWg sync.WaitGroup
	oobVulnChan := make(chan *npoc.Vulnerability, 100)

	for _, payload := range newPayloads {
		select {
		case <-c.Context.Done():
			return vulns
		default:
		}

		newReq := c.Task().Request.Clone()
		newReq.FollowRedirects = false
		newReq.Method = httpv.MethodPost
		newReq.Header.Set("Content-Type", ct)
		newReq.Body = []byte(payload.Payload)

		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			return nil
		}

		if vuln := processPayload(c, payload, newReq, resp, "", oobWg, &localOOBWg, oobVulnChan); vuln != nil {
			vulns = append(vulns, vuln)
			return vulns
		}
	}

	vulns = append(vulns, waitAndCollectOOBVulns(&localOOBWg, oobVulnChan)...)
	return vulns
}

func GenericWithPath(c *npoc.HTTPContext, genPayload GenPayload, paths []string, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var vulns []*npoc.Vulnerability
	var localOOBWg sync.WaitGroup
	oobVulnChan := make(chan *npoc.Vulnerability, 100)

	for _, path := range paths {
		newPayloads := ReplaceCommand(c.Task().Client.RdnsClient, genPayload)
		for _, payload := range newPayloads {
			select {
			case <-c.Context.Done():
				return vulns
			default:
			}

			newReq := c.Task().Request.Clone()
			newReq.FollowRedirects = true
			newReq.URL.Path = path
			if newReq.Method == httpv.MethodGet {
				newReq.URL.RawQuery = payload.Payload
				newReq.URL.RawQuery = newReq.URL.Query().Encode()
			} else {
				newReq.Body = []byte(payload.Payload)
			}

			resp, err := c.Task().Client.Do(c.Context, newReq)
			if err != nil {
				return nil
			}

			if vuln := processPayload(c, payload, newReq, resp, "", oobWg, &localOOBWg, oobVulnChan); vuln != nil {
				vulns = append(vulns, vuln)
				return vulns
			}
		}
	}

	vulns = append(vulns, waitAndCollectOOBVulns(&localOOBWg, oobVulnChan)...)
	return vulns
}

func GenericCheck(genPayload GenPayload, resp *httpv.Response) bool {
	if genPayload.CheckerType == CheckTypeExpr {
		if bytes.Contains(resp.Body, []byte(genPayload.CheckerStr)) || strings.Contains(resp.Header.Get("Location"), genPayload.CheckerStr) {
			return true
		}
	}
	return false
}

// 创建漏洞对象的通用函数
func createVulnerability(c *npoc.HTTPContext, payload GenPayload, req *httpv.Request, resp *httpv.Response, param string) *npoc.Vulnerability {
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}

	vuln := &npoc.Vulnerability{
		Method:     c.Task().Request.Method,
		Param:      param,
		Payload:    payload.Payload,
		URL:        c.Task().Request.URL.String(),
		HTTP:       vHTTP,
		Confidence: npoc.ConfidenceHigh,
	}

	if payload.CheckerType == CheckTypeReverse {
		vuln.OOBUrl = payload.OOBUrl
		vuln.Extra = map[string]string{
			npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck,
			npoc.ExtraKeyOOBUrl:    payload.OOBUrl.URL(),
		}
	} else {
		vuln.Extra = map[string]string{npoc.ExtraKeyCheckStr: payload.CheckerStr}
	}

	return vuln
}

// OOB检测的通用函数
func handleOOBCheck(c *npoc.HTTPContext, payload GenPayload, req *httpv.Request, resp *httpv.Response, param string, oobWg, localOOBWg *sync.WaitGroup, oobVulnChan chan *npoc.Vulnerability) {
	oobWg.Add(1)
	localOOBWg.Add(1)
	go func() {
		defer utils.RecoverFun(c.Context)
		defer c.Task().Client.RdnsClient.RemoveURL(payload.OOBUrl)
		defer oobWg.Done()
		defer localOOBWg.Done()

		if oobutils.WaitForOOBTrigger(c.Context, payload.OOBUrl) {
			vuln := createVulnerability(c, payload, req, resp, param)
			vuln.OOBDetails = oobutils.ExtractOOBDetails(c.Context, payload.OOBUrl)
			oobVulnChan <- vuln
		}
	}()
}

// 处理单个payload的通用逻辑
func processPayload(c *npoc.HTTPContext, payload GenPayload, req *httpv.Request, resp *httpv.Response, param string, oobWg, localOOBWg *sync.WaitGroup, oobVulnChan chan *npoc.Vulnerability) *npoc.Vulnerability {
	result := GenericCheck(payload, resp)
	if result {
		return createVulnerability(c, payload, req, resp, param)
	} else if payload.CheckerType == CheckTypeReverse {
		handleOOBCheck(c, payload, req, resp, param, oobWg, localOOBWg, oobVulnChan)
	}
	return nil
}

// 等待OOB结果并收集漏洞的通用函数
func waitAndCollectOOBVulns(localOOBWg *sync.WaitGroup, oobVulnChan chan *npoc.Vulnerability) []*npoc.Vulnerability {
	go func() {
		localOOBWg.Wait()
		close(oobVulnChan)
	}()

	var vulns []*npoc.Vulnerability
	for oobVuln := range oobVulnChan {
		vulns = append(vulns, oobVuln)
	}
	return vulns
}
