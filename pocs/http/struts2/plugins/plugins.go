package plugins

import (
	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"sync"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
)

type Flag struct {
	Client     *httpv.Client
	Req        *httpv.Request
	GenPayload GenPayload
}

type GenPayload struct {
	Payload     string
	CheckerStr  string
	CheckerType string
	OOBUrl      *client.URL
	FazzPath    []string
}

type Func func(c *npoc.HTTPContext) []*npoc.Vulnerability
type OOBFunc func(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability

const (
	CheckTypeReverse = "CheckTypeReverse"
	CheckTypeExpr    = "CheckTypeExpr"
)

var FuncMap = map[string]Func{
	"S2-015": Check015,
	"S2-017": Check017,
	"S2-020": Check020,
	"S2-037": Check037,
	"S2-045": Check045,
	"S2-046": Check046,
	"S2-057": Check057,
	"S2-061": Check061,
}

var OOBFuncMap = map[string]OOBFunc{
	"S2-005": Check005,
	"S2-008": Check008,
	"S2-009": Check009,
	"S2-016": Check016,
	"S2-032": Check032,
	"S2-052": Check052,
	"S2-059": Check059,
	"S2-062": Check062,
}
