package plugins

import (
	"strings"

	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"github.acme.red/intelli-sec/npoc"
)

func Check020(c *npoc.HTTPContext) []*npoc.Vulnerability {
	var fazzPath []string
	var vulns []*npoc.Vulnerability

	if c.Task().Request.IsRoot() {
		fazzPath = append(fazzPath, FazzPaths...)
	} else {
		if strings.HasSuffix(c.Task().Request.URL.Path, ".action") {
			fazzPath = append(fazzPath, c.Task().Request.URL.Path)
		}
	}
	for _, path := range fazzPath {
		var (
			resp    *httpv.Response
			success = make([]bool, 3)
			resp1   *httpv.Response
			resp2   *httpv.Response
			req1    *httpv.Request
			req2    *httpv.Request
			err     error
		)
		newReq, resp, err := sendReqWithPath(c, path, "")
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
			continue
		}
		if resp.Status > 399 {
			continue
		}
		for i := range 3 {
			select {
			case <-c.Context.Done():
				return vulns
			default:
			}
			req1, resp1, err = sendReqWithPath(c, path, "class['classLoader']['URLs'][0]="+funk.RandomString(5))
			if err != nil {
				continue
			}
			if resp1.Status != 404 {
				continue
			}
			req2, resp2, err = sendReqWithPath(c, path, "class['classLoader']['URLs'][x]="+funk.RandomString(5))
			if err != nil {
				continue
			}
			if resp2.Status != 305 && resp2.Status != 200 {
				continue
			}
			success[i] = true
		}
		result := true
		// 要求三次必须都成功
		for i, b := range success {
			if !b {
				result = false
			}
			// 后续复用
			success[i] = false
		}
		if result {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}, {Request: req1, Response: resp1}, {Request: req2, Response: resp2}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    "class['classLoader']['URLs'][0]=",
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Confidence: npoc.ConfidenceHigh,
			}
			vulns = append(vulns, vuln)
			return vulns
		}
	}
	return nil
}

func sendReqWithPath(c *npoc.HTTPContext, path, query string) (*httpv.Request, *httpv.Response, error) {
	newReq := c.Task().Request.Clone()
	if path != "" {
		newReq.URL.Path = path
	} else {
		return nil, nil, nil
	}
	newReq.URL.RawQuery = query
	resp, err := c.Task().Client.Do(c.Context, newReq)
	return newReq, resp, err
}
