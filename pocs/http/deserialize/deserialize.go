package deserialize

import (
	"fmt"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/detector"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

const (
	ID             = string(npoc.DeserializeType)
	DesDeserialize = "该参数的值可能是序列化的，可能暴露反序列化攻击面，存在安全风险"
	VulnNameInfo   = "info"
)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "deserialize",
		PocType:        npoc.GenericPocType,
		Category:       npoc.DeserializeType,
		Tags:           nil,
		Description:    "反序列化漏洞：是一种程序没有对用户输入的反序列化字符串进行检测，导致反序列化过程可以被恶意控制的漏洞。攻击者可以构造恶意信息，服务器收到该恶意代码后解析出的命令可能会造成信息泄露或者被攻击者直接用拿下主机服务器等安全风险。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	checkReq := DecodeRequestURL(c, c.Task().Request)
	checkReq.FollowRedirects = false

	var (
		allKeys []string
		oobWg   sync.WaitGroup
	)
	for key := range checkReq.Header {
		allKeys = append(allKeys, key)
	}
	checkReq.GetAllParams(allKeys...)
	param := checkReq.Params()

	languages := c.GetLanguage()

	// 如果是未知语言，则不检测
	if len(languages) == 1 && languages[0] == detector.LangUnknown {
		return nil
	}

	detectors := map[string]func(*npoc.HTTPContext, *httpv.Request, map[string]httpv.Param, *sync.WaitGroup) error{
		detector.LangPython: p.detectPython,
		detector.LangRuby:   p.detectRuby,
		detector.LangJava:   p.detectJava,
		detector.LangPHP:    p.detectPHP,
	}

	for _, lang := range languages {
		if detectFunc := detectors[lang]; detectFunc != nil {
			if err := detectFunc(c, checkReq, param, &oobWg); err != nil {
				return err
			}
		}
	}
	oobWg.Wait()

	return nil
}

func (p *PoC) detectPHP(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) (err error) {
	needPayloads := p.getPHPPayloads(c)

	for _, v := range param {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}

		// 检测PHP参数是否需要进行fuzz测试
		shouldFuzz, encodeType := p.processPHPParam(c, req, v)

		if shouldFuzz {
			p.performPHPFuzzTesting(c, req, v, needPayloads, encodeType, oobWg)
		}
	}
	return nil
}

func (p *PoC) detectJava(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) error {
	if err := p.detectJavaInParams(c, req, param, oobWg); err != nil {
		return err
	}

	if req.Method == httpv.MethodPost {
		if err := p.detectJavaInPostBody(c, req, oobWg); err != nil {
			return err
		}
	}

	return nil
}

func (p *PoC) detectRuby(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) error {
	if err := p.detectRubyInParams(c, req, param, oobWg); err != nil {
		return err
	}

	if req.Method == httpv.MethodPost {
		if err := p.detectRubyInPostBody(c, req, oobWg); err != nil {
			return err
		}
	}
	return nil
}

func (p *PoC) detectPython(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) error {
	if err := p.detectPythonInParams(c, req, param, oobWg); err != nil {
		return err
	}

	if req.Method == httpv.MethodPost && strings.Contains(req.Header.Get("Content-Type"), "json") {
		if err := p.detectPythonInPostBody(c, req, oobWg); err != nil {
			return err
		}
	}
	return nil
}

// 处理OOB检测
func (p *PoC) handleOOBDetection(c *npoc.HTTPContext, fuzzReq *httpv.Request, fuzzResp *httpv.Response, newPayload payload, param httpv.Param, oobWg *sync.WaitGroup, lang string) {
	defer utils.RecoverFun(c.Context)
	defer c.Task().Client.RdnsClient.RemoveURL(newPayload.OOBURL)
	defer oobWg.Done()

	if oobutils.WaitForOOBTrigger(c.Context, newPayload.OOBURL) {
		httpFlow := []npoc.HTTPFollow{{Request: fuzzReq, Response: fuzzResp}}
		metadata := p.Metadata()
		extra := map[string]string{
			npoc.ExtraKeyDes:    fmt.Sprintf("使用的编码为：%s", newPayload.EncodeType),
			npoc.ExtraKeyOOBUrl: newPayload.OOBURL.URL(),
		}
		if newPayload.Name == payloadNamePTRAndCurl {
			for _, interaction := range newPayload.OOBURL.GetInteractions() {
				if interaction.QType == "PTR" || strings.Contains(interaction.RawRequest, "curl") {
					p.outputOOBVuln(c, httpFlow, getVulnName(metadata.Name, lang), metadata.Description, newPayload.Payload, param.Key, npoc.ConfidenceHigh, npoc.SeverityCritical, extra, newPayload.OOBURL)
					break
				}
			}
		} else {
			p.outputOOBVuln(c, httpFlow, getVulnName(metadata.Name, lang), metadata.Description, newPayload.Payload, param.Key, npoc.ConfidenceHigh, npoc.SeverityCritical, extra, newPayload.OOBURL)
		}
	}
}

func (p *PoC) reportPassiveFinding(c *npoc.HTTPContext, param httpv.Param, encodeType string, lang string) {
	vHTTP := []npoc.HTTPFollow{{Request: c.Task().Request, Response: c.Task().Response}}
	extra := map[string]string{
		npoc.ExtraKeyDes: fmt.Sprintf("疑似序列化的值为：`%s`\n使用的编码为：%s", param.Value, encodeType),
	}
	vulnName := getVulnName(p.Metadata().Name, lang, VulnNameInfo)
	p.outputVuln(c, vHTTP, vulnName, DesDeserialize, "", param.Key, npoc.ConfidenceMedium, npoc.SeverityLow, extra)
}

func (p *PoC) fuzzParameter(c *npoc.HTTPContext, req *httpv.Request, param httpv.Param, payloads []payload, oobWg *sync.WaitGroup, lang string) error {
	for _, pld := range payloads {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		req.DisableEncoding = true
		fuzzReq, fuzzResp, err := c.Task().Client.SendNewRequest(c.Context, req, param.OnlyKey, pld.Payload)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%s主动探测发包失败: %w", lang, err)})
		}

		oobWg.Add(1)
		go p.handleOOBDetection(c, fuzzReq, fuzzResp, pld, param, oobWg, lang)
	}
	return nil
}

func (p *PoC) fuzzPostBody(c *npoc.HTTPContext, req *httpv.Request, pld payload, contentType string, oobWg *sync.WaitGroup, lang string) error {
	select {
	case <-c.Context.Done():
		return c.Context.Err()
	default:
	}
	req.DisableEncoding = true
	postReq := req.BuildReqWithBodyAndContentType([]byte(pld.Payload), contentType)
	postResp, err := c.Task().Client.Do(c.Context, postReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("%s 主动探测发包失败 (POST body): %w", lang, err)})
	}
	tmpParam := httpv.Param{Key: ""}

	oobWg.Add(1)
	go p.handleOOBDetection(c, postReq, postResp, pld, tmpParam, oobWg, lang)
	return nil
}
