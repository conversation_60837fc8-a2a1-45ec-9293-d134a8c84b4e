package deserialize

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

func (p *PoC) detectRubyInParams(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) error {
	for _, v := range param {
		encodeType := detectRubyMarshalEncoding(v.Value)
		jsonEncodeType := detectRubyJSONEncoding(v.Value)

		if encodeType != encodeTypeUnknown {
			p.reportPassiveFinding(c, v, encodeType, detector.LangRuby)
		}

		if jsonEncodeType != encodeTypeUnknown {
			jPayloads := genRubyJSONDNSPayload(c, jsonEncodeType)
			err := p.fuzzParameter(c, req, v, jPayloads, oobWg, detector.LangRuby)
			if err != nil {
				return err
			}
		}

		allEncodeTypes := []string{encodeTypeBase64, encodeTypeHex, encodeTypeGzipBase64, encodeTypeZlibBase64}
		rubyPayloads := genRubyDNSPayload(c, allEncodeTypes)
		err := p.fuzzParameter(c, req, v, rubyPayloads, oobWg, detector.LangRuby)
		if err != nil {
			return err
		}
	}

	return nil
}

func (p *PoC) detectRubyInPostBody(c *npoc.HTTPContext, req *httpv.Request, oobWg *sync.WaitGroup) error {
	rPayloads := genRubyJSONDNSPayload(c, encodeTypeRawJSON)

	for _, rPayload := range rPayloads {
		err := p.fuzzPostBody(c, req, rPayload, httpv.ContentTypeJson, oobWg, detector.LangRuby)
		if err != nil {
			return err
		}
	}
	return nil
}
