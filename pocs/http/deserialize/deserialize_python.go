package deserialize

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

func (p *PoC) detectPythonInPostBody(c *npoc.HTTPContext, req *httpv.Request, oobWg *sync.WaitGroup) error {
	pyPayload := genPythonJSONPicklePayload(c, encodeTypeRawJSON)
	err := p.fuzzPostBody(c, req, pyPayload, httpv.ContentTypeJson, oobWg, detector.LangPython)
	if err != nil {
		return err
	}
	return nil
}

func (p *PoC) detectPythonInParams(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) error {
	for _, v := range param {
		encodeType := detectPythonPickleEncoding(v.Value)
		jsonEncodeType := detectPythonJSONPickleEncoding(v.Value)

		if encodeType != encodeTypeUnknown {
			p.reportPassiveFinding(c, v, encodeType, detector.LangPython)
			pyPayload := genPythonPicklePayload(c, encodeType)
			err := p.fuzzParameter(c, req, v, []payload{pyPayload}, oobWg, detector.LangPython)
			if err != nil {
				return err
			}
		}

		if jsonEncodeType != encodeTypeUnknown {
			p.reportPassiveFinding(c, v, jsonEncodeType, detector.LangPython)
			pyPayload := genPythonJSONPicklePayload(c, jsonEncodeType)
			err := p.fuzzParameter(c, req, v, []payload{pyPayload}, oobWg, detector.LangPython)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
