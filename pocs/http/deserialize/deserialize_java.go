package deserialize

import (
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

func (p *PoC) detectJavaInParams(c *npoc.HTTPContext, req *httpv.Request, param map[string]httpv.Param, oobWg *sync.WaitGroup) error {
	for _, v := range param {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}

		encodeType := detectJavaSerializationEncoding(v.Value)
		jsonEncodeType := detectJavaJSONEncoding(v.Value)

		if encodeType != encodeTypeUnknown {
			// 输出初步漏洞信息
			p.reportPassiveFinding(c, v, encodeType, detector.LangJava)

			// 主要检测
			jPayload := genJavaDNSPayload(c, encodeType)
			if err := p.fuzzParameter(c, req, v, []payload{jPayload}, oobWg, detector.LangJava); err != nil {
				return err
			}
		} else {
			// 补充测试多种编码类型
			supplementaryEncodeTypes := []string{encodeTypeBase64, encodeTypeHex, encodeTypeGzipBase64, encodeTypeZlibBase64}
			for _, suppEncodeType := range supplementaryEncodeTypes {
				select {
				case <-c.Context.Done():
					return c.Context.Err()
				default:
				}

				jPayload := genJavaDNSPayload(c, suppEncodeType)
				if err := p.fuzzParameter(c, req, v, []payload{jPayload}, oobWg, detector.LangJava); err != nil {
					return err
				}
			}
		}

		if jsonEncodeType != encodeTypeUnknown {
			for _, j := range javaPayloadSlice {
				jPayload := genJavaJSONDNSPayload(c, j, jsonEncodeType)
				if err := p.fuzzParameter(c, req, v, []payload{jPayload}, oobWg, detector.LangJava); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (p *PoC) detectJavaInPostBody(c *npoc.HTTPContext, req *httpv.Request, oobWg *sync.WaitGroup) error {
	allEncodeTypes := []string{encodeTypeBase64, encodeTypeHex, encodeTypeGzipBase64, encodeTypeZlibBase64, encodeTypeRaw, encodeTypeGzip, encodeTypeRawJSON}

	for _, encodeType := range allEncodeTypes {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}

		if encodeType != encodeTypeRawJSON {
			jPayload := genJavaDNSPayload(c, encodeType)
			if err := p.fuzzPostBody(c, req, jPayload, httpv.ContentTypeForm, oobWg, detector.LangJava); err != nil {
				return err
			}
		} else {
			for _, j := range javaPayloadSlice {
				jPayload := genJavaJSONDNSPayload(c, j, encodeTypeRawJSON)
				if err := p.fuzzPostBody(c, req, jPayload, httpv.ContentTypeJson, oobWg, detector.LangJava); err != nil {
					return err
				}
			}
		}
	}

	return nil
}
