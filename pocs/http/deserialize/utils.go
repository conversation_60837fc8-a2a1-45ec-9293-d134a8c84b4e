package deserialize

import (
	"bytes"
	"compress/gzip"
	"compress/zlib"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/url"
	"regexp"
	"strings"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/guess"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

// DecodeRequestURL 创建解码后的请求副本，不修改原请求
func DecodeRequestURL(c *npoc.HTTPContext, req *httpv.Request) *httpv.Request {
	if req == nil || req.URL == nil {
		return nil
	}
	needsDecoding := false
	nreq := req.Clone()

	// 检查路径
	if req.URL.Path != "" && guess.IsURLEncoded(req.URL.Path) {
		needsDecoding = true
	}

	// 检查查询参数
	if !needsDecoding && req.URL.RawQuery != "" {
		if guess.IsURLEncoded(req.URL.RawQuery) || strings.Contains(req.URL.RawQuery, "+") {
			needsDecoding = true
		}
	}

	// 只有在需要时才进行解码
	if needsDecoding {
		err := decodeRequestURL(nreq)
		if err != nil {
			slog.ErrorContext(c.Context, "request解码错误", slog.Any("err", err.Error()))
		}
		nreq.ParsePathParams()
		return nreq
	}

	return nreq
}

// decodeRequestURL 对 req 的路径和查询参数进行URL解码
func decodeRequestURL(req *httpv.Request) error {
	if err := decodeRequestPath(req); err != nil {
		return err
	}

	if err := decodeRequestQuery(req); err != nil {
		return err
	}
	return nil
}

// 解码请求路径
func decodeRequestPath(req *httpv.Request) error {
	if req.URL.Path != "" && guess.IsURLEncoded(req.URL.Path) {
		decoded, err := url.PathUnescape(req.URL.Path)
		if err != nil {
			return err
		}
		req.URL.Path = decoded
		req.URL.RawPath = "" // 清除原始路径，让Go重新生成
	}
	return nil
}

// 解码查询参数
func decodeRequestQuery(req *httpv.Request) error {
	if req.URL.RawQuery == "" {
		return nil
	}

	// 快速检查是否需要解码
	needsDecoding := guess.IsURLEncoded(req.URL.RawQuery) || strings.Contains(req.URL.RawQuery, "+")
	if !needsDecoding {
		return nil
	}

	values := req.URL.Query()
	newValues := make(url.Values)
	hasDecoding := false

	for key, valueList := range values {
		// 解码key
		decodedKey := key
		if guess.IsURLEncoded(key) {
			decoded, err := url.QueryUnescape(key)
			if err != nil {
				return err
			}
			decodedKey = decoded
			hasDecoding = true
		}

		// 解码values
		decodedValues := make([]string, len(valueList))
		for i, value := range valueList {
			if guess.IsURLEncoded(value) || strings.Contains(value, "+") {
				decoded, err := url.QueryUnescape(value)
				if err != nil {
					return err
				}
				decodedValues[i] = decoded
				hasDecoding = true
			} else {
				decodedValues[i] = value
			}
		}

		newValues[decodedKey] = decodedValues
	}

	// 如果有解码操作，重新设置查询字符串
	if hasDecoding {
		req.URL.RawQuery = newValues.Encode()
	}

	return nil
}

func encodeWithMethod(returnData []byte, encoding string) string {
	switch encoding {
	case encodeTypeRaw:
		return urlEncodeWithEscapedPlus(string(returnData))
	case encodeTypeHex:
		return hex.EncodeToString(returnData)
	case encodeTypeGzip:
		data, err := compressWithMethod(returnData, encodeTypeGzip)
		if err != nil {
			return ""
		}
		return string(data)
	case encodeTypeGzipBase64:
		data, err := compressWithMethod(returnData, encodeTypeGzip)
		if err != nil {
			return ""
		}
		return urlEncodeWithEscapedPlus(base64.StdEncoding.EncodeToString(data))
	case encodeTypeZlibBase64:
		data, err := compressWithMethod(returnData, encodeTypeZlib)
		if err != nil {
			return ""
		}
		return urlEncodeWithEscapedPlus(base64.StdEncoding.EncodeToString(data))
	case encodeTypeBase64:
		return urlEncodeWithEscapedPlus(base64.StdEncoding.EncodeToString(returnData))
	default:
		return urlEncodeWithEscapedPlus(base64.StdEncoding.EncodeToString(returnData))
	}
}

func urlEncodeWithEscapedPlus(input string) string {
	return url.QueryEscape(input)
}

func compressWithMethod(data []byte, method string) ([]byte, error) {
	var buffer bytes.Buffer
	var writer io.WriteCloser

	switch method {
	case encodeTypeGzip:
		writer = gzip.NewWriter(&buffer)
	case encodeTypeZlib:
		writer = zlib.NewWriter(&buffer)
	default:
		return nil, fmt.Errorf("unsupported compression method: %s", method)
	}
	defer writer.Close()

	if _, err := writer.Write(data); err != nil {
		return nil, err
	}

	if err := writer.Close(); err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func decodeWithMethod(input string, encoding string) string {
	var data []byte
	if encoding != encodeTypeHex {
		data = decodeBase64Universal(input)
		if len(data) == 0 {
			return ""
		}
	}

	switch encoding {
	case encodeTypeHex:
		decoded, err := hex.DecodeString(input)
		if err != nil {
			return ""
		}
		return string(decoded)

	case encodeTypeGzipBase64:
		reader, err := gzip.NewReader(bytes.NewReader(data))
		if err != nil {
			return ""
		}
		defer reader.Close()
		out, err := io.ReadAll(reader)
		if err != nil {
			return ""
		}
		return string(out)

	case encodeTypeZlibBase64:
		reader, err := zlib.NewReader(bytes.NewReader(data))
		if err != nil {
			return ""
		}
		defer reader.Close()
		out, err := io.ReadAll(reader)
		if err != nil {
			return ""
		}
		return string(out)

	case encodeTypeBase64:
		return string(data)

	default:
		return ""
	}
}

func decodeBase64Universal(input string) []byte {
	for len(input)%4 != 0 {
		input += "="
	}

	if data, err := base64.StdEncoding.DecodeString(input); err == nil {
		return data
	}
	if data, err := base64.URLEncoding.DecodeString(input); err == nil {
		return data
	}
	return nil
}

func isValidJSONFormat(s string) bool {
	var j json.RawMessage
	return json.Unmarshal([]byte(s), &j) == nil
}

func detectPythonJSONPickleEncoding(input string) string {
	if pythonRawJSONPickleRe.MatchString(input) {
		return encodeTypeRaw
	}

	if pythonHexJSONPickleRe.MatchString(input) {
		return encodeTypeHex
	}

	checkDecoded := func(encoded, mode string) bool {
		decoded := decodeWithMethod(encoded, mode)
		if decoded == "" {
			return false
		}
		return pythonRawJSONPickleRe.MatchString(decoded)
	}

	switch {
	case strings.HasPrefix(input, "eyJweS") && checkDecoded(input, encodeTypeBase64):
		return encodeTypeBase64
	case strings.HasPrefix(input, "H4sI") && checkDecoded(input, encodeTypeGzipBase64):
		return encodeTypeGzipBase64
	case strings.HasPrefix(input, "eJ") && checkDecoded(input, encodeTypeZlibBase64):
		return encodeTypeZlibBase64
	}

	return encodeTypeUnknown
}

func detectPythonPickleEncoding(input string) string {
	isPickle := func(s string) bool {
		return strings.HasSuffix(s, ".") && strings.Contains(s, "\x0a")
	}

	if isPickle(input) {
		return encodeTypeRaw
	}

	decodeSteps := []struct {
		mode   string
		source string
	}{
		{encodeTypeBase64, input},
		{encodeTypeGzipBase64, input},
		{encodeTypeZlibBase64, input},
		{encodeTypeHex, input},
	}

	for _, step := range decodeSteps {
		decoded := decodeWithMethod(step.source, step.mode)
		if isPickle(decoded) {
			return step.mode
		}
	}

	return encodeTypeUnknown
}

func detectJavaJSONEncoding(input string) string {
	if isValidJSONFormat(input) {
		return encodeTypeRaw
	}

	checkDecoded := func(encoded, mode string) bool {
		decoded := decodeWithMethod(encoded, mode)
		if decoded == "" {
			return false
		}
		return isValidJSONFormat(decoded)
	}

	switch {
	case strings.HasPrefix(input, "ey") && checkDecoded(input, encodeTypeBase64):
		return encodeTypeBase64
	case strings.HasPrefix(input, "H4sI") && checkDecoded(input, encodeTypeGzipBase64):
		return encodeTypeGzipBase64
	case strings.HasPrefix(input, "eJ") && checkDecoded(input, encodeTypeZlibBase64):
		return encodeTypeZlibBase64
	case strings.HasPrefix(input, "7b22") && checkDecoded(input, encodeTypeHex):
		return encodeTypeHex
	}

	return encodeTypeUnknown
}

func detectJavaSerializationEncoding(input string) string {
	serializeBytes := []byte{0xac, 0xed}

	isSerialized := func(data []byte) bool {
		return len(data) >= 2 && data[0] == serializeBytes[0] && data[1] == serializeBytes[1]
	}

	if isSerialized([]byte(input)) {
		return encodeTypeRaw
	}

	checkDecoded := func(encoded, mode string) bool {
		decoded := decodeWithMethod(encoded, mode)
		if decoded == "" {
			return false
		}
		return isSerialized([]byte(decoded))
	}

	switch {
	case strings.HasPrefix(input, "rO0") && checkDecoded(input, encodeTypeBase64):
		return encodeTypeBase64
	case strings.HasPrefix(input, "H4sI") && checkDecoded(input, encodeTypeGzipBase64):
		return encodeTypeGzipBase64
	case strings.HasPrefix(input, "eJ") && checkDecoded(input, encodeTypeZlibBase64):
		return encodeTypeZlibBase64
	case strings.HasPrefix(strings.ToLower(input), "aced") && checkDecoded(input, encodeTypeHex):
		return encodeTypeHex
	default:
		return encodeTypeUnknown
	}
}

func detectPHPSerializationEncoding(input string) string {
	if isPHPSerialized(input) {
		return encodeTypeRaw
	}

	encodeType := detectPHPEncoding(input)
	if encodeType == encodeTypeUnknown {
		return encodeTypeUnknown
	}

	decoded := decodeWithMethod(input, encodeType)
	if isPHPSerialized(decoded) {
		return encodeType
	}

	return encodeTypeUnknown
}

func isPHPSerialized(input string) bool {
	return phpSerializeObjectRe.MatchString(input) ||
		phpSerializeArrayRe.MatchString(input) ||
		phpSerializeStrRe.MatchString(input) ||
		phpSerializeIntRe.MatchString(input)
}

func detectPHPEncoding(input string) string {
	switch {
	case phpSerializeBase64Re.MatchString(input):
		return encodeTypeBase64
	case strings.HasPrefix(input, "H4sI"):
		return encodeTypeGzipBase64
	case strings.HasPrefix(input, "eJ"):
		return encodeTypeZlibBase64
	case phpSerializeHexRe.MatchString(input):
		return encodeTypeHex
	default:
		return encodeTypeUnknown
	}
}

func detectRubyJSONEncoding(input string) string {
	if isValidJSONFormat(input) {
		return encodeTypeRawJSON
	}

	checkDecoded := func(encoded, mode string) bool {
		decoded := decodeWithMethod(encoded, mode)
		if decoded == "" {
			return false
		}
		return isValidJSONFormat(decoded)
	}

	switch {
	case strings.HasPrefix(input, "ey") && checkDecoded(input, encodeTypeBase64):
		return encodeTypeBase64
	case strings.HasPrefix(input, "H4sI") && checkDecoded(input, encodeTypeGzipBase64):
		return encodeTypeGzipBase64
	case strings.HasPrefix(input, "eJ") && checkDecoded(input, encodeTypeZlibBase64):
		return encodeTypeZlibBase64
	case strings.HasPrefix(input, "7b22") && checkDecoded(input, encodeTypeHex):
		return encodeTypeHex
	}

	return encodeTypeUnknown
}

func detectRubyMarshalEncoding(input string) string {
	encodeType := detectRubyEncoding(input)

	if encodeType == encodeTypeUnknown {
		return encodeTypeUnknown
	}

	MarshalType := DetectRubyMarshalType(decodeWithMethod(input, encodeType))

	if MarshalType != encodeTypeUnknown {
		return encodeType
	}

	return encodeTypeUnknown
}

func DetectRubyMarshalType(s string) string {
	if len(s) < 3 {
		return encodeTypeUnknown
	}
	if s[0] != 0x04 || s[1] != 0x08 {
		return encodeTypeUnknown
	}

	switch s[2] {
	case 'o':
		if len(s) >= 4 && s[3] == ':' {
			return "object"
		}
	case '[':
		return "array"
	case '{':
		return "hash"
	case '"':
		return "string"
	case ':':
		return "symbol"
	}

	return encodeTypeUnknown
}

func detectRubyEncoding(input string) string {
	base64Re := regexp.MustCompile(`^BAh[A-Za-z0-9+/=]{10,}`)
	hexRe := regexp.MustCompile(`(?i)^0408[0-9a-fA-F]{10,}`)

	switch {
	case base64Re.MatchString(input):
		return encodeTypeBase64
	case strings.HasPrefix(input, "H4sI"):
		return encodeTypeGzipBase64
	case strings.HasPrefix(input, "eJ"):
		return encodeTypeZlibBase64
	case hexRe.MatchString(input):
		return encodeTypeHex

	default:
		return encodeTypeUnknown
	}
}

func getVulnName(parts ...string) string {
	return strings.Join(parts, "-")
}

func (p *PoC) outputVuln(c *npoc.HTTPContext, vHTTP []npoc.HTTPFollow, name, des, payload, key string, confidence npoc.Confidence, severity npoc.Severity, extra map[string]string) {
	p.outputOOBVuln(c, vHTTP, name, des, payload, key, confidence, severity, extra, nil)
}

func (p *PoC) outputOOBVuln(c *npoc.HTTPContext, vHTTP []npoc.HTTPFollow, name, des, payload, key string, confidence npoc.Confidence, severity npoc.Severity, extra map[string]string, oobURL *client.URL) {
	metaData := p.Metadata()
	var oobDetails []*npoc.OOBDetail
	if oobURL != nil {
		oobDetails = oobutils.ExtractOOBDetails(c.Context, oobURL)
	}
	vuln := &npoc.Vulnerability{
		Method:      c.Task().Request.Method,
		Category:    metaData.Category,
		PocType:     metaData.PocType,
		Severity:    severity,
		Param:       key,
		Payload:     payload,
		URL:         c.Task().Request.URL.String(),
		PoC:         p.ID(),
		HTTP:        &npoc.VulnerabilityHTTP{Follows: vHTTP},
		Name:        name,
		Description: des,
		OOBUrl:      oobURL,
		OOBDetails:  oobDetails,
		Extra:       extra,
		Confidence:  confidence,
	}
	c.OutputVulnerability(vuln)
}
