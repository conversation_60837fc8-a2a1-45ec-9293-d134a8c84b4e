package deserialize

import (
	"bytes"
	"compress/gzip"
	"compress/zlib"
	"encoding/base64"
	"encoding/hex"
	"strings"
	"testing"
)

func TestEncodeWithMethod(t *testing.T) {
	testData := []byte("Hello World")

	tests := []struct {
		name     string
		encoding string
		expected func([]byte) string
	}{
		{
			name:     "Raw encoding",
			encoding: encodeTypeRaw,
			expected: func(data []byte) string {
				return urlEncodeWithEscapedPlus(string(data))
			},
		},
		{
			name:     "Hex encoding",
			encoding: encodeTypeHex,
			expected: func(data []byte) string {
				return hex.EncodeToString(data)
			},
		},
		{
			name:     "Base64 encoding",
			encoding: encodeTypeBase64,
			expected: func(data []byte) string {
				return urlEncodeWithEscapedPlus(base64.StdEncoding.EncodeToString(data))
			},
		},
		{
			name:     "Unknown encoding (default to base64)",
			encoding: "unknown",
			expected: func(data []byte) string {
				return urlEncodeWithEscapedPlus(base64.StdEncoding.EncodeToString(data))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := encodeWithMethod(testData, tt.encoding)
			expected := tt.expected(testData)
			if result != expected {
				t.Errorf("encodeWithMethod() = %v, want %v", result, expected)
			}
		})
	}
}

func TestUrlEncodeWithEscapedPlus(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Simple string",
			input:    "hello world",
			expected: "hello%20world",
		},
		{
			name:     "String with plus",
			input:    "hello+world",
			expected: "hello%2Bworld",
		},
		{
			name:     "String with special characters",
			input:    "hello@world#test",
			expected: "hello@world%23test",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := urlEncodeWithEscapedPlus(tt.input)
			if result != tt.expected {
				t.Errorf("urlEncodeWithEscapedPlus() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCompressWithMethod(t *testing.T) {
	testData := []byte("Hello World! This is a test string for compression.")

	tests := []struct {
		name   string
		method string
		hasErr bool
	}{
		{
			name:   "Gzip compression",
			method: encodeTypeGzip,
			hasErr: false,
		},
		{
			name:   "Zlib compression",
			method: encodeTypeZlib,
			hasErr: false,
		},
		{
			name:   "Unknown compression method",
			method: "unknown",
			hasErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := compressWithMethod(testData, tt.method)
			if tt.hasErr {
				if err == nil {
					t.Errorf("compressWithMethod() expected error, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("compressWithMethod() unexpected error: %v", err)
				}
				if len(result) == 0 {
					t.Errorf("compressWithMethod() returned empty result")
				}
			}
		})
	}
}

func TestDecodeWithMethod(t *testing.T) {
	testString := "Hello World"

	// 准备测试数据
	hexEncoded := hex.EncodeToString([]byte(testString))
	base64Encoded := base64.StdEncoding.EncodeToString([]byte(testString))

	// 准备压缩数据
	var gzipBuf bytes.Buffer
	gzipWriter := gzip.NewWriter(&gzipBuf)
	gzipWriter.Write([]byte(testString))
	gzipWriter.Close()
	gzipBase64 := base64.StdEncoding.EncodeToString(gzipBuf.Bytes())

	var zlibBuf bytes.Buffer
	zlibWriter := zlib.NewWriter(&zlibBuf)
	zlibWriter.Write([]byte(testString))
	zlibWriter.Close()
	zlibBase64 := base64.StdEncoding.EncodeToString(zlibBuf.Bytes())

	tests := []struct {
		name     string
		input    string
		encoding string
		expected string
	}{
		{
			name:     "Hex decoding",
			input:    hexEncoded,
			encoding: encodeTypeHex,
			expected: testString,
		},
		{
			name:     "Base64 decoding",
			input:    base64Encoded,
			encoding: encodeTypeBase64,
			expected: testString,
		},
		{
			name:     "Gzip Base64 decoding",
			input:    gzipBase64,
			encoding: encodeTypeGzipBase64,
			expected: testString,
		},
		{
			name:     "Zlib Base64 decoding",
			input:    zlibBase64,
			encoding: encodeTypeZlibBase64,
			expected: testString,
		},
		{
			name:     "Unknown encoding",
			input:    "test",
			encoding: "unknown",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decodeWithMethod(tt.input, tt.encoding)
			if result != tt.expected {
				t.Errorf("decodeWithMethod() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDecodeBase64Universal(t *testing.T) {
	testString := "Hello World"
	stdEncoded := base64.StdEncoding.EncodeToString([]byte(testString))
	urlEncoded := base64.URLEncoding.EncodeToString([]byte(testString))

	tests := []struct {
		name     string
		input    string
		expected []byte
	}{
		{
			name:     "Standard base64",
			input:    stdEncoded,
			expected: []byte(testString),
		},
		{
			name:     "URL base64",
			input:    urlEncoded,
			expected: []byte(testString),
		},
		{
			name:     "Base64 without padding",
			input:    strings.TrimRight(stdEncoded, "="),
			expected: []byte(testString),
		},
		{
			name:     "Invalid base64",
			input:    "invalid@base64!",
			expected: nil,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: []byte{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decodeBase64Universal(tt.input)
			if !bytes.Equal(result, tt.expected) {
				t.Errorf("decodeBase64Universal() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestIsValidJSONFormat(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Valid JSON object",
			input:    `{"key": "value"}`,
			expected: true,
		},
		{
			name:     "Valid JSON array",
			input:    `["item1", "item2"]`,
			expected: true,
		},
		{
			name:     "Valid JSON string",
			input:    `"hello"`,
			expected: true,
		},
		{
			name:     "Valid JSON number",
			input:    `123`,
			expected: true,
		},
		{
			name:     "Invalid JSON",
			input:    `{invalid json}`,
			expected: false,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidJSONFormat(tt.input)
			if result != tt.expected {
				t.Errorf("isValidJSONFormat() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectPythonJSONPickleEncoding(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Raw Python JSON Pickle",
			input:    `{"py/object": "test"}`,
			expected: encodeTypeRaw,
		},
		{
			name:     "Hex Python JSON Pickle",
			input:    "7b2270792f6f626a656374223a202274657374227d",
			expected: encodeTypeHex,
		},
		{
			name:     "Base64 Python JSON Pickle",
			input:    "eyJweS9vYmplY3QiOiAidGVzdCJ9",
			expected: encodeTypeBase64,
		},
		{
			name:     "Unknown format",
			input:    "invalid data",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectPythonJSONPickleEncoding(tt.input)
			if result != tt.expected {
				t.Errorf("detectPythonJSONPickleEncoding() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectJavaJSONEncoding(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Raw JSON",
			input:    `{"key": "value"}`,
			expected: encodeTypeRaw,
		},
		{
			name:     "Base64 JSON",
			input:    base64.StdEncoding.EncodeToString([]byte(`{"test": "data"}`)),
			expected: encodeTypeBase64,
		},
		{
			name:     "Hex JSON",
			input:    "7b2274657374223a202264617461227d", // {"test": "data"}
			expected: encodeTypeHex,
		},
		{
			name:     "Unknown format",
			input:    "invalid data",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectJavaJSONEncoding(tt.input)
			if result != tt.expected {
				t.Errorf("detectJavaJSONEncoding() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectJavaSerializationEncoding(t *testing.T) {
	serializedData := []byte{0xac, 0xed, 0x00, 0x05} // Java serialization magic + version
	base64Serialized := base64.StdEncoding.EncodeToString(serializedData)
	hexSerialized := hex.EncodeToString(serializedData)

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Raw Java serialization",
			input:    string(serializedData),
			expected: encodeTypeRaw,
		},
		{
			name:     "Base64 Java serialization",
			input:    base64Serialized,
			expected: encodeTypeBase64,
		},
		{
			name:     "Hex Java serialization",
			input:    hexSerialized,
			expected: encodeTypeHex,
		},
		{
			name:     "Unknown format",
			input:    "invalid data",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectJavaSerializationEncoding(tt.input)
			if result != tt.expected {
				t.Errorf("detectJavaSerializationEncoding() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestIsPHPSerialized(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "PHP serialized object",
			input:    `O:4:"Test":1:{s:4:"name";s:5:"value";}`,
			expected: true,
		},
		{
			name:     "PHP serialized array",
			input:    `a:2:{i:0;s:5:"hello";i:1;s:5:"world";}`,
			expected: true,
		},
		{
			name:     "PHP serialized string",
			input:    `s:5:"hello";`,
			expected: true,
		},
		{
			name:     "PHP serialized integer",
			input:    `i:123;`,
			expected: true,
		},
		{
			name:     "Not PHP serialized",
			input:    "regular string",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isPHPSerialized(tt.input)
			if result != tt.expected {
				t.Errorf("isPHPSerialized() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectPHPEncoding(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Base64 PHP",
			input:    "TzozOiJQRE8iOjA6e30=",
			expected: encodeTypeBase64,
		},
		{
			name:     "Gzip Base64",
			input:    "H4sIAAAAAAAAA",
			expected: encodeTypeGzipBase64,
		},
		{
			name:     "Zlib Base64",
			input:    "eJwL",
			expected: encodeTypeZlibBase64,
		},
		{
			name:     "Hex PHP",
			input:    "613a303a7b7d", // hex data
			expected: encodeTypeHex,
		},
		{
			name:     "Unknown format",
			input:    "invalid data",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectPHPEncoding(tt.input)
			if result != tt.expected {
				t.Errorf("detectPHPEncoding() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectRubyJSONEncoding(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Raw JSON",
			input:    `{"test": "data"}`,
			expected: encodeTypeRawJSON,
		},
		{
			name:     "Base64 JSON",
			input:    base64.StdEncoding.EncodeToString([]byte(`{"test": "data"}`)),
			expected: encodeTypeBase64,
		},
		{
			name:     "Hex JSON",
			input:    "7b2274657374223a202264617461227d",
			expected: encodeTypeHex,
		},
		{
			name:     "Unknown format",
			input:    "invalid data",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectRubyJSONEncoding(tt.input)
			if result != tt.expected {
				t.Errorf("detectRubyJSONEncoding() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectRubyEncoding(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Base64 Ruby Marshal",
			input:    "BAhVGGhlbGxvZHNm",
			expected: encodeTypeBase64,
		},
		{
			name:     "Gzip Base64",
			input:    "H4sIAAAAAAAAA",
			expected: encodeTypeGzipBase64,
		},
		{
			name:     "Zlib Base64",
			input:    "eJwL",
			expected: encodeTypeZlibBase64,
		},
		{
			name:     "Hex Ruby Marshal",
			input:    "04085b0769003a0a40776964746869073a0b40686569676874690c3a0c407a696e646578690d",
			expected: encodeTypeHex,
		},
		{
			name:     "Unknown format",
			input:    "invalid data",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectRubyEncoding(tt.input)
			if result != tt.expected {
				t.Errorf("detectRubyEncoding() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDetectRubyMarshalType(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Ruby Marshal object",
			input:    "\x04\x08o:",
			expected: "object",
		},
		{
			name:     "Ruby Marshal object with additional data",
			input:    "\x04\x08o:SomeClass",
			expected: "object",
		},
		{
			name:     "Ruby Marshal array",
			input:    "\x04\x08[",
			expected: "array",
		},
		{
			name:     "Ruby Marshal array with data",
			input:    "\x04\x08[i\x06",
			expected: "array",
		},
		{
			name:     "Ruby Marshal hash",
			input:    "\x04\x08{",
			expected: "hash",
		},
		{
			name:     "Ruby Marshal hash with data",
			input:    "\x04\x08{i\x06i\x07",
			expected: "hash",
		},
		{
			name:     "Ruby Marshal string",
			input:    "\x04\x08\"",
			expected: "string",
		},
		{
			name:     "Ruby Marshal string with data",
			input:    "\x04\x08\"\x0bhello",
			expected: "string",
		},
		{
			name:     "Ruby Marshal symbol",
			input:    "\x04\x08:",
			expected: "symbol",
		},
		{
			name:     "Ruby Marshal symbol with name",
			input:    "\x04\x08:\x0asymbol",
			expected: "symbol",
		},
		{
			name:     "Invalid Ruby Marshal header - wrong first byte",
			input:    "\x03\x08[",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Invalid Ruby Marshal header - wrong second byte",
			input:    "\x04\x07[",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Valid header but unknown type",
			input:    "\x04\x08x",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Object marker but missing colon",
			input:    "\x04\x08ox",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Object marker with colon but only 4 bytes total",
			input:    "\x04\x08o:",
			expected: "object",
		},
		{
			name:     "Too short input - less than 3 bytes",
			input:    "\x04\x08",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Too short input - 1 byte",
			input:    "\x04",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Empty input",
			input:    "",
			expected: encodeTypeUnknown,
		},
		{
			name:     "Valid header with null bytes",
			input:    "\x04\x08\x00",
			expected: encodeTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DetectRubyMarshalType(tt.input)
			if result != tt.expected {
				t.Errorf("DetectRubyMarshalType() = %v, want %v", result, tt.expected)
			}
		})
	}
}
