package deserialize

import (
	"bytes"
	"fmt"
	"log/slog"
	"net/url"
	"sync"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

// PHP获取需要的payloads
func (p *PoC) getPHPPayloads(c *npoc.HTTPContext) []phpggc {
	needPayloads := make([]phpggc, 0, 20)
	for _, f := range c.Task().FingerPrint {
		if payloads, exists := phpPayloadMap[f.Name]; exists {
			needPayloads = append(needPayloads, payloads...)
		}
	}
	if len(needPayloads) == 0 {
		needPayloads = getAllPHPPayloads()
	}
	return needPayloads
}

// PHP被动检测和主动探测的入口
func (p *PoC) processPHPParam(c *npoc.HTTPContext, req *httpv.Request, param httpv.Param) (bool, string) {
	// PHP被动检测
	encodeType := detectPHPSerializationEncoding(param.Value)
	if encodeType != encodeTypeUnknown {
		p.reportPassiveFinding(c, param, encodeType, detector.LangPHP)
		return true, encodeType
	}

	if param.ParamType == httpv.ParamTypeHeader {
		return false, ""
	}

	// PHP主动探测
	return p.performPHPActiveDetection(c, req, param)
}

// PHP主动探测
func (p *PoC) performPHPActiveDetection(c *npoc.HTTPContext, req *httpv.Request, param httpv.Param) (bool, string) {
	jsonFormat := param.ParamType == httpv.ParamTypeJson

	// PHP第一阶段：特征匹配
	if found, encodeType := p.detectPHPByFeatureMatching(c, req, param, jsonFormat); found {
		return true, encodeType
	}

	// PHP第二阶段：错误状态恢复
	if found, encodeType := p.detectPHPByErrorRecovery(c, req, param, jsonFormat); found {
		return true, encodeType
	}

	return false, ""
}

// PHP通过特征匹配进行检测
func (p *PoC) detectPHPByFeatureMatching(c *npoc.HTTPContext, req *httpv.Request, param httpv.Param, jsonFormat bool) (bool, string) {
	for encodeType, stepPayload := range phpSerializePayloadStep1 {
		select {
		case <-c.Context.Done():
			return false, ""
		default:
		}

		phpPayload, err := p.preparePHPPayload(stepPayload, jsonFormat, encodeType)
		if err != nil {
			slog.ErrorContext(c.Context, "php payload准备失败", "error", err, "encodeType", encodeType)
			continue
		}

		req.DisableEncoding = true
		phpReq, phpResp, err := c.Task().Client.SendNewRequest(c.Context, req, param.OnlyKey, phpPayload)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("php主动探测发包失败(步骤一): %w", err)})
			continue
		}

		if bytes.Contains(phpResp.Body, []byte(matchedFeaturePHP)) {
			httpFlow := []npoc.HTTPFollow{{Request: phpReq, Response: phpResp}}
			extra := map[string]string{
				npoc.ExtraKeyDes: fmt.Sprintf("使用的编码为：%s", encodeType),
			}
			p.outputVuln(c, httpFlow, getVulnName(p.Metadata().Name, detector.LangPHP, VulnNameInfo), DesDeserialize, matchedFeaturePHP, param.Key, npoc.ConfidenceHigh, npoc.SeverityLow, extra)
			return true, encodeType
		}
	}
	return false, ""
}

// PHP通过错误状态恢复进行检测
func (p *PoC) detectPHPByErrorRecovery(c *npoc.HTTPContext, req *httpv.Request, param httpv.Param, jsonFormat bool) (bool, string) {
	originalStatus := c.Task().Response.Status

	for encodeType, step1Payload := range phpSerializePayloadStep1 {
		select {
		case <-c.Context.Done():
			return false, ""
		default:
		}

		payload1, err := p.preparePHPPayload(step1Payload, jsonFormat, encodeType)
		if err != nil {
			slog.ErrorContext(c.Context, "php payload准备失败", "error", err, "encodeType", encodeType)
			continue
		}

		req.DisableEncoding = true
		phpReq, phpResp, err := c.Task().Client.SendNewRequest(c.Context, req, param.OnlyKey, payload1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("php主动探测发包失败(步骤一错误检测): %w", err)})
			continue
		}

		// 只有当原始请求是200且当前请求是500时才进行第二阶段
		if originalStatus == 200 && phpResp.Status == 500 {
			phpHTTPFlow := []npoc.HTTPFollow{{Request: phpReq, Response: phpResp}}
			if found := p.tryPHPErrorRecovery(c, phpHTTPFlow, req, param, encodeType, jsonFormat); found {
				return true, encodeType
			}
		}
	}
	return false, ""
}

// PHP尝试错误恢复
func (p *PoC) tryPHPErrorRecovery(c *npoc.HTTPContext, phpHTTPFlow []npoc.HTTPFollow, req *httpv.Request, param httpv.Param, encodeType string, jsonFormat bool) bool {
	step2Payloads, exists := phpSerializePayloadStep2[encodeType]
	if !exists {
		return false
	}

	for _, step2Payload := range step2Payloads {
		payload2, err := p.preparePHPPayload(step2Payload, jsonFormat, encodeType)
		if err != nil {
			slog.ErrorContext(c.Context, "php payload准备失败", "error", err, "encodeType", encodeType)
			continue
		}

		secReq, secResp, err := c.Task().Client.SendNewRequest(c.Context, req, param.OnlyKey, payload2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("php主动探测发包失败(步骤二): %w", err)})
			continue
		}

		if secResp.Status == 200 {
			httpFlow := append(phpHTTPFlow, []npoc.HTTPFollow{{Request: secReq, Response: secResp}}...)
			extra := map[string]string{
				npoc.ExtraKeyDes: fmt.Sprintf("使用的编码为：%s", encodeType),
			}
			p.outputVuln(c, httpFlow, getVulnName(p.Metadata().Name, detector.LangPHP, VulnNameInfo), DesDeserialize, step2Payload, param.Key, npoc.ConfidenceHigh, npoc.SeverityLow, extra)
			return true
		}
	}
	return false
}

// PHP准备payload（统一处理URL解码和JSON转换）
func (p *PoC) preparePHPPayload(rawPayload string, jsonFormat bool, encodeType string) (string, error) {
	unescape, err := url.QueryUnescape(rawPayload)
	if err != nil {
		return "", fmt.Errorf("URL解码失败: %w", err)
	}

	if jsonFormat && encodeType == encodeTypeRaw {
		unescape, err = text.ToJSONSafeString(unescape)
		if err != nil {
			return "", fmt.Errorf("JSON转换失败: %w", err)
		}
	}

	return unescape, nil
}

// PHP执行模糊测试
func (p *PoC) performPHPFuzzTesting(c *npoc.HTTPContext, req *httpv.Request, param httpv.Param, needPayloads []phpggc, encodeType string, oobWg *sync.WaitGroup) {
	jsonFormat := param.ParamType == httpv.ParamTypeJson

	for _, needPayload := range needPayloads {
		select {
		case <-c.Context.Done():
			return
		default:
		}

		newPayload, err := genPHPPayload(c, needPayload, jsonFormat, encodeType)
		if err != nil {
			slog.ErrorContext(c.Context, "php主动fuzz的payload获取失败", "error", err)
			continue
		}

		fuzzReq, fuzzResp, err := c.Task().Client.SendNewRequest(c.Context, req, param.OnlyKey, newPayload.Payload)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: fmt.Errorf("php主动fuzz探测发包失败: %w", err)})
		}

		oobWg.Add(1)
		go p.handleOOBDetection(c, fuzzReq, fuzzResp, newPayload, param, oobWg, detector.LangPHP)
	}
}
