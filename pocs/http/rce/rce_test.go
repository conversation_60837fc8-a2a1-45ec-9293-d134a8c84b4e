package rce

import (
	"context"
	"strings"
	"sync"
	"testing"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/test"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/template"
)

type TestEnvironment struct {
	Name        string
	URL         string
	PayloadType string
	Enabled     bool
	ParamName   string
}

var testEnvironments = []TestEnvironment{
	{Name: "php-1", URL: "http://10.4.4.120:9012/?route=test1&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-2", URL: "http://10.4.4.120:9012/?route=test2&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-3", URL: "http://10.4.4.120:9012/?route=test3&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-4", URL: "http://10.4.4.120:9012/?route=test4&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-5", URL: "http://10.4.4.120:9012/?route=test5&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-6", URL: "http://10.4.4.120:9012/?route=test6&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-7", URL: "http://10.4.4.120:9012/?route=test7&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-8", URL: "http://10.4.4.120:9012/?route=test8&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-9", URL: "http://10.4.4.120:9012/?route=test9&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "php-10", URL: "http://10.4.4.120:9012/?route=test10&id=test", PayloadType: "php", Enabled: true, ParamName: "id"},
	{Name: "python-1", URL: "http://10.4.4.120:9013/vuln1?input=test", PayloadType: "python", Enabled: true, ParamName: "input"},
	{Name: "python-2", URL: "http://10.4.4.120:9013/vuln2?input=test", PayloadType: "python", Enabled: true, ParamName: "input"},
	{Name: "python-3", URL: "http://10.4.4.120:9013/vuln3?input=3-1", PayloadType: "python", Enabled: true, ParamName: "input"},
	{Name: "python-4", URL: "http://10.4.4.120:9013/vuln4?code=test", PayloadType: "python", Enabled: true, ParamName: "code"},
	{Name: "python-5", URL: "http://10.4.4.120:9013/vuln5?code=test", PayloadType: "python", Enabled: true, ParamName: "code"},
	{Name: "java-1", URL: "http://10.4.4.120:9018/vuln/java-1?input=1", PayloadType: "java", Enabled: true, ParamName: "input"},
	{Name: "java-2", URL: "http://10.4.4.120:9018/vuln/java-2?input=1", PayloadType: "java", Enabled: true, ParamName: "input"},
	{Name: "java-3", URL: "http://10.4.4.120:9018/vuln/java-3?input=1", PayloadType: "java", Enabled: true, ParamName: "input"},
	{Name: "java-4", URL: "http://10.4.4.120:9018/vuln/java-4?input=1", PayloadType: "java", Enabled: true, ParamName: "input"},
	{Name: "java-5", URL: "http://10.4.4.120:9018/vuln/java-5?input=1", PayloadType: "java", Enabled: true, ParamName: "input"},
	{Name: "perl-1", URL: "http://10.4.4.120:9019/perl1?input=1", PayloadType: "perl", Enabled: true, ParamName: "input"},
	{Name: "perl-2", URL: "http://10.4.4.120:9019/perl2?input=1", PayloadType: "perl", Enabled: true, ParamName: "input"},
	{Name: "ruby-1", URL: "http://10.4.4.120:9020/vuln1?input=1", PayloadType: "ruby", Enabled: true, ParamName: "input"},
	{Name: "ruby-2", URL: "http://10.4.4.120:9020/vuln2?input=1", PayloadType: "ruby", Enabled: true, ParamName: "input"},
}

type oobTestData struct {
	name    string
	payload Payload
	testEnv TestEnvironment
	checker checker
	sent    bool
	success bool
}

func TestPayloads(t *testing.T) {
	regularClient, err := test.CreateNewTestClient(false)
	if err != nil {
		t.Fatalf("Failed to create regular client: %v", err)
	}
	oobClient, err := test.CreateNewTestClient(true)
	if err != nil {
		t.Fatalf("Failed to create OOB client: %v", err)
	}
	ctx := context.Background()
	testEnvMap := make(map[string]TestEnvironment)
	for _, env := range testEnvironments {
		testEnvMap[env.Name] = env
	}

	var oobTests []oobTestData
	poc := &PoC{}

	for _, payload := range Payloads {
		testEnv, exists := testEnvMap[payload.Name]
		if !exists || !testEnv.Enabled {
			continue
		}

		if payload.Oob {
			trueParam := ""
			tp := test.GetURLParam(testEnv.URL, testEnv.ParamName)
			if tp != "" {
				trueParam = tp
			} else {
				trueParam = "test"
			}
			ck, err := makeTestOOBChecker(oobClient, trueParam, payload.Value)
			if err != nil {
				t.Errorf("Failed to create OOB checker for payload %s: %v", payload.Name, err)
				continue
			}

			oobTests = append(oobTests, oobTestData{
				name:    payload.Name,
				payload: payload,
				testEnv: testEnv,
				checker: ck,
			})
		} else {
			t.Run(payload.Name, func(t *testing.T) {
				testRegularPayload(t, ctx, regularClient, testEnv, payload)
			})
		}
	}

	if len(oobTests) > 0 {
		t.Log("Sending all OOB payloads...")

		// 使用WaitGroup等待所有OOB测试完成
		var wg sync.WaitGroup

		for i := range oobTests {
			o := &oobTests[i]

			wg.Add(1)

			go func(testData *oobTestData) {
				defer wg.Done()

				t.Run(testData.name+"_OOB", func(t *testing.T) {
					hc, err := test.CreateTestHTTPContext(testData.testEnv.URL, httpv.MethodGet, nil, "")
					if err != nil {
						t.Errorf("Failed to create HTTP context: %v", err)
						return
					}

					// 创建检测上下文
					detectionCtx := poc.initDetectionContext(hc)

					// 查找目标参数
					params := hc.Task().Request.Params()
					var targetParam httpv.Param
					found := false

					for _, param := range params {
						if param.Key == testData.testEnv.ParamName {
							targetParam = param
							found = true
							break
						}
					}

					if !found {
						t.Errorf("Target parameter %s not found", testData.testEnv.ParamName)
						return
					}

					// 执行OOB检测
					detectionCtx.oobWaitGroup.Add(1)
					poc.executeOOBDetection(detectionCtx, targetParam, testData.checker, testData.payload)

					// 等待OOB检测完成
					detectionCtx.oobWaitGroup.Wait()

					result := hc.GetResult()
					if len(result.Vulnerabilities) == 0 {
						t.Errorf("OOB payload %s returned no results", testData.payload.Name)
					} else {
						t.Logf("OOB payload %s returned %d results", testData.payload.Name, len(result.Vulnerabilities))
					}
				})
			}(o)
		}

		wg.Wait()
	}
}

func testRegularPayload(t *testing.T, ctx context.Context, client *httpv.Client, testEnv TestEnvironment, payload Payload) {
	req, _ := httpv.NewGetRequest(testEnv.URL)
	req.ParseParam()
	trueParam := ""
	tp := test.GetURLParam(testEnv.URL, testEnv.ParamName)
	if tp != "" {
		trueParam = tp
	} else {
		trueParam = "test"
	}

	ck, err := makeChecker(trueParam, payload.Value)
	if err != nil {
		t.Fatalf("Failed to create checker for payload %s: %v", payload.Name, err)
	}

	if payload.UnnecessaryEncode {
		req.DisableEncoding = true
	}

	// 查找目标参数
	params := req.Params()
	var targetParam httpv.Param
	found := false

	for _, param := range params {
		if param.Key == testEnv.ParamName {
			targetParam = param
			found = true
			break
		}
	}

	if !found {
		t.Errorf("Target parameter %s not found", testEnv.ParamName)
		return
	}

	_, resp, err := client.SendNewRequest(ctx, req, targetParam.OnlyKey, ck.payload)
	if err != nil {
		t.Errorf("Failed to send request for payload %s: %v", payload.Name, err)
		return
	}

	if strings.Contains(string(resp.Body), ck.checkStr) {
		t.Logf("✅ Payload %s successful! Found check string: %s", payload.Name, ck.checkStr)
	} else {
		t.Errorf("❌ Payload %s failed. Check string '%s' not found in response", payload.Name, ck.checkStr)
		t.Logf("Response body: %s", string(resp.Body))
	}
}

func makeTestOOBChecker(c *httpv.Client, raw, payload string) (checker, error) {
	var ck checker
	oobURL := dnslog.CreateURLAdapter(c.RdnsClient, client.LogTypeDNS)
	randstr := funk.RandomString(6, utils.LowerChars)
	payloadTemp := template.PayloadTemp{
		RawValue: raw,
		OOBHost:  oobURL.URL(),
		RandStr1: randstr,
	}
	payloadStr, err := template.ArgEval(payload, payloadTemp)
	ck = checker{
		payload:  payloadStr,
		checkStr: randstr,
		oobURL:   oobURL,
	}
	return ck, err
}
