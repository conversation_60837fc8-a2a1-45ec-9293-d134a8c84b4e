package rce

import (
	"bytes"
	"fmt"
	"log/slog"
	"slices"
	"strings"
	"sync"

	"github.acme.red/pictor/dnslog/pkg/server"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/detector"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

const ID = string(npoc.RCEType)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "命令/代码执行",
		PocType:        npoc.GenericPocType,
		Category:       npoc.RCEType,
		Tags:           nil,
		Description:    "任意命令执行漏洞是指代码未对用户可控参数做过滤，导致直接带入执行命令和代码，通过漏洞执行恶意构造的语句，执行任意命令或代码。攻击者可在服务器上执行任意命令，读写文件操作等，危害巨大。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

// 检测上下文，包含所有检测需要的信息
type detectionContext struct {
	httpContext       *npoc.HTTPContext
	request           *httpv.Request
	detectedLanguages []string
	hasJava           bool
	blackDomains      []string
	oobWaitGroup      *sync.WaitGroup
}

// 载荷分组结果
type payloadGroups struct {
	regularRCE  []Payload // 常规RCE载荷（命令注入、代码注入）
	sstiOOB     []Payload // SSTI OOB载荷
	sstiError   []Payload // SSTI错误检测载荷
	sstiRegular []Payload // SSTI常规载荷
}

// OOB检测数据
type oobDetectionData struct {
	interactions []*server.Interaction
	checkParam   httpv.Param
	payload      Payload
	checker      checker
	httpFlow     []npoc.HTTPFollow
}

// 二次验证结果
type doubleCheckResult struct {
	request    *httpv.Request
	response   *httpv.Response
	oobDetail  *npoc.OOBDetail
	matchValue string
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	ctx := p.initDetectionContext(c)

	params := ctx.request.Params()
	if len(params) == 0 {
		return nil
	}

	payloads := p.classifyPayloads(ctx.detectedLanguages)

	// 对每个参数执行检测
	for _, param := range params {
		if !param.NeedCheck || param.PathParam.IsFilePath {
			continue
		}

		if err := p.detectParameter(ctx, param, payloads); err != nil {
			return err
		}
	}

	ctx.oobWaitGroup.Wait()
	return nil
}

// 初始化检测上下文
func (p *PoC) initDetectionContext(c *npoc.HTTPContext) *detectionContext {
	req := c.Task().Request.Clone()
	req.FollowRedirects = false

	detectedLanguages := p.extractDetectedLanguages(c.Task().FingerPrint)
	hasJava := slices.Contains(detectedLanguages, detector.LangJava)
	blackDomains := []string{"baidu.com", "google.com", "sogou.com", "bing.com"}

	return &detectionContext{
		httpContext:       c,
		request:           req,
		detectedLanguages: detectedLanguages,
		hasJava:           hasJava,
		blackDomains:      blackDomains,
		oobWaitGroup:      &sync.WaitGroup{},
	}
}

// 对单个参数执行完整检测流程
func (p *PoC) detectParameter(ctx *detectionContext, param httpv.Param, payloads *payloadGroups) error {
	hasEchoDetection := false

	// 1. 常规RCE检测（回显检测）
	if err := p.detectRegularRCE(ctx, param, payloads.regularRCE, &hasEchoDetection); err != nil {
		return err
	}

	// 2. 如果无回显且支持OOB，执行OOB检测
	if !hasEchoDetection && ctx.httpContext.Task().Client.RdnsClient != nil {
		if err := p.detectRCEWithOOB(ctx, param, payloads.regularRCE); err != nil {
			return err
		}
	}

	// 3. SSTI检测
	if err := p.detectSSTI(ctx, param, payloads); err != nil {
		return err
	}

	return nil
}

// 常规RCE检测（回显检测）
func (p *PoC) detectRegularRCE(ctx *detectionContext, param httpv.Param, payloads []Payload, hasEcho *bool) error {
	for _, payload := range payloads {
		select {
		case <-ctx.httpContext.Context.Done():
			return ctx.httpContext.Context.Err()
		default:
		}

		if !p.shouldProcessRegularPayload(payload, ctx) {
			continue
		}

		ck, err := makeChecker(param.Value, payload.Value)
		if err != nil {
			slog.ErrorContext(ctx.httpContext.Context, "生成RCE载荷失败", "error", err)
			continue
		}

		p.executeEchoDetection(ctx, param, ck, payload, hasEcho)
	}
	return nil
}

// RCE OOB检测
func (p *PoC) detectRCEWithOOB(ctx *detectionContext, param httpv.Param, payloads []Payload) error {
	for _, payload := range payloads {
		select {
		case <-ctx.httpContext.Context.Done():
			return ctx.httpContext.Context.Err()
		default:
		}

		if !payload.Oob || (!ctx.httpContext.Task().FullScan && payload.Fuzz) {
			continue
		}

		ck, err := makeOOBChecker(ctx.httpContext, param.Value, payload.Value)
		if err != nil {
			slog.ErrorContext(ctx.httpContext.Context, "生成RCE OOB载荷失败", "error", err)
			continue
		}

		ctx.oobWaitGroup.Add(1)
		p.executeOOBDetection(ctx, param, ck, payload)
	}
	return nil
}

// SSTI检测
func (p *PoC) detectSSTI(ctx *detectionContext, param httpv.Param, payloads *payloadGroups) error {
	hasEcho := false

	// 1. SSTI常规检测
	for _, payload := range payloads.sstiRegular {
		select {
		case <-ctx.httpContext.Context.Done():
			return ctx.httpContext.Context.Err()
		default:
		}

		if payload.Language == detector.LangJava && !ctx.hasJava {
			continue
		}

		ck, err := makeChecker(param.Value, payload.Value)
		if err != nil {
			slog.ErrorContext(ctx.httpContext.Context, "生成SSTI载荷失败", "error", err)
			continue
		}

		p.executeEchoDetection(ctx, param, ck, payload, &hasEcho)
	}

	if hasEcho {
		return nil
	}

	// 2. SSTI错误检测 + OOB检测
	return p.detectSSTIWithErrorAndOOB(ctx, param, payloads.sstiError, payloads.sstiOOB)
}

// SSTI错误检测和OOB检测
func (p *PoC) detectSSTIWithErrorAndOOB(ctx *detectionContext, param httpv.Param, errorPayloads, oobPayloads []Payload) error {
	for _, payload := range errorPayloads {
		newPayload := param.Value + payload.Value
		_, errResp, err := ctx.httpContext.Task().Client.SendNewRequest(
			ctx.httpContext.Context, ctx.request, param.OnlyKey, newPayload)
		if err != nil {
			ctx.httpContext.OutputPoCError(&npoc.PoCError{
				PoC: p.ID(),
				Err: fmt.Errorf("SSTI错误检测发包失败: %w", err),
			})
			continue
		}

		// 如果响应状态码发生变化，说明可能存在SSTI，进行OOB检测
		if errResp.Status != ctx.httpContext.Task().Response.Status {
			for _, oobPayload := range oobPayloads {
				ck, err := makeOOBChecker(ctx.httpContext, param.Value, oobPayload.Value)
				if err != nil {
					slog.ErrorContext(ctx.httpContext.Context, "生成SSTI OOB载荷失败", "error", err)
					continue
				}

				ctx.oobWaitGroup.Add(1)
				p.executeOOBDetection(ctx, param, ck, oobPayload)
			}
		}
	}
	return nil
}

// 执行回显检测
func (p *PoC) executeEchoDetection(ctx *detectionContext, param httpv.Param, ck checker, payload Payload, hasEcho *bool) {
	tmpReq := ctx.request.Clone()
	if payload.UnnecessaryEncode {
		tmpReq.DisableEncoding = true
	}

	newReq, resp, err := ctx.httpContext.Task().Client.SendNewRequest(
		ctx.httpContext.Context, tmpReq, param.OnlyKey, ck.payload)
	if err != nil {
		ctx.httpContext.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		return
	}

	if bytes.Contains(resp.Body, []byte(ck.checkStr)) {
		*hasEcho = true
		p.reportEchoVulnerability(ctx, param, ck, payload, newReq, resp)
	}
}

// 执行OOB检测
func (p *PoC) executeOOBDetection(ctx *detectionContext, param httpv.Param, ck checker, payload Payload) {
	tmpReq := ctx.request.Clone()
	if payload.UnnecessaryEncode {
		tmpReq.DisableEncoding = true
	}

	newReq, resp, err := ctx.httpContext.Task().Client.SendNewRequest(
		ctx.httpContext.Context, tmpReq, param.OnlyKey, ck.payload)
	if err != nil {
		ctx.httpContext.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
	}

	go func() {
		defer utils.RecoverFun(ctx.httpContext.Context)
		defer ctx.httpContext.Task().Client.RdnsClient.RemoveURL(ck.oobURL)
		defer ctx.oobWaitGroup.Done()

		if !oobutils.WaitForOOBTrigger(ctx.httpContext.Context, ck.oobURL) {
			return
		}

		interactions := ck.oobURL.GetInteractions()
		if len(interactions) == 0 {
			return
		}

		data := &oobDetectionData{
			interactions: interactions,
			checkParam:   param,
			payload:      payload,
			checker:      ck,
			httpFlow:     []npoc.HTTPFollow{{Request: newReq, Response: resp}},
		}

		vuln := p.processOOBVulnerability(ctx, data)
		if vuln != nil {
			ctx.httpContext.OutputVulnerability(vuln)
		}
	}()
}

// 处理OOB漏洞
func (p *PoC) processOOBVulnerability(ctx *detectionContext, data *oobDetectionData) *npoc.Vulnerability {
	switch data.payload.VulnType {
	case VulnTypeCMD:
		return p.processCMDVulnerability(ctx, data)
	case VulnTypeCode:
		return p.processCodeVulnerability(ctx, data)
	case VulnTypeSSTI:
		return p.processSSTIVulnerability(ctx, data)
	default:
		return p.buildOOBVulnerability(ctx, data, npoc.ConfidenceLow, nil)
	}
}

// 处理命令注入漏洞
func (p *PoC) processCMDVulnerability(ctx *detectionContext, data *oobDetectionData) *npoc.Vulnerability {
	for _, inter := range data.interactions {
		if inter.QType == "PTR" || strings.Contains(inter.RawRequest, "curl") {
			return p.buildOOBVulnerability(ctx, data, npoc.ConfidenceHigh, nil)
		}
	}
	return nil
}

// 处理代码注入漏洞
func (p *PoC) processCodeVulnerability(ctx *detectionContext, data *oobDetectionData) *npoc.Vulnerability {
	confidence := npoc.ConfidenceHigh
	foundCheckValue := false

	for _, interaction := range data.interactions {
		if strings.Contains(interaction.RawRequest, data.checker.checkStr) {
			foundCheckValue = true
			break
		}
	}

	if !foundCheckValue {
		confidence = npoc.ConfidenceLow
	}

	return p.buildOOBVulnerability(ctx, data, confidence, map[string]string{
		npoc.ExtraCodeInjCheckValue: data.checker.checkStr,
	})
}

// 处理SSTI漏洞
func (p *PoC) processSSTIVulnerability(ctx *detectionContext, data *oobDetectionData) *npoc.Vulnerability {
	hasValidInteraction := false
	for _, interaction := range data.interactions {
		if interaction.QType == "PTR" {
			hasValidInteraction = true
			break
		}
	}

	if !hasValidInteraction {
		return nil
	}

	return p.buildOOBVulnerability(ctx, data, npoc.ConfidenceHigh, nil)
}

// 报告回显漏洞
func (p *PoC) reportEchoVulnerability(ctx *detectionContext, param httpv.Param, ck checker, payload Payload, req *httpv.Request, resp *httpv.Response) {
	metaData := p.Metadata()
	name := p.getVulnerabilityName(payload, metaData.Name)

	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}
	ctx.httpContext.OutputVulnerability(&npoc.Vulnerability{
		Method:      ctx.httpContext.Task().Request.Method,
		Category:    metaData.Category,
		Severity:    metaData.Severity,
		Param:       param.Key,
		Payload:     ck.payload,
		URL:         ctx.request.URL.String(),
		PoC:         p.ID(),
		HTTP:        vHTTP,
		Name:        name,
		Description: metaData.Description,
		Extra:       map[string]string{npoc.ExtraKeyCheckStr: ck.checkStr},
		Confidence:  npoc.ConfidenceHigh,
	})
}

// 构建OOB漏洞
func (p *PoC) buildOOBVulnerability(ctx *detectionContext, data *oobDetectionData, confidence npoc.Confidence, extraValues map[string]string) *npoc.Vulnerability {
	extra := map[string]string{
		npoc.ExtraKeyCheckRule:   npoc.ExtraValueOOBCheck,
		npoc.ExtraKeyOOBUrl:      data.checker.oobURL.URL(),
		npoc.ExtraKeySubCategory: "rce_dns_out_of_band",
		npoc.ExtraRCEPayloadType: data.payload.VulnType,
	}

	if confidence == npoc.ConfidenceHigh {
		extra[npoc.ExtraDoubleCheckID] = data.checkParam.OnlyKey
	}

	for k, v := range extraValues {
		if v != "" {
			extra[k] = v
		}
	}

	oobDetails := oobutils.ExtractOOBDetails(ctx.httpContext.Context, data.checker.oobURL)
	metaData := p.Metadata()

	return &npoc.Vulnerability{
		Method:      ctx.httpContext.Task().Request.Method,
		PocType:     npoc.GenericPocType,
		Category:    metaData.Category,
		Severity:    metaData.Severity,
		Param:       data.checkParam.Key,
		Payload:     data.checker.payload,
		URL:         ctx.request.URL.String(),
		PoC:         p.ID(),
		HTTP:        &npoc.VulnerabilityHTTP{Follows: data.httpFlow},
		Name:        p.getVulnerabilityName(data.payload, metaData.Name),
		Description: metaData.Description,
		Extra:       extra,
		OOBUrl:      data.checker.oobURL,
		OOBDetails:  oobDetails,
		Confidence:  confidence,
	}
}

// 判断是否应该处理常规载荷
func (p *PoC) shouldProcessRegularPayload(payload Payload, ctx *detectionContext) bool {
	if payload.Oob {
		return false
	}
	if !ctx.httpContext.Task().FullScan && payload.Fuzz {
		return false
	}
	if payload.Name == "common-5" {
		for _, domain := range ctx.blackDomains {
			if strings.Contains(ctx.request.URL.String(), domain) {
				return false
			}
		}
	}
	return true
}

// 提取检测到的语言
func (p *PoC) extractDetectedLanguages(fingerPrints []httpv.FingerprintInfo) []string {
	var languages []string
	for _, finger := range fingerPrints {
		if slices.Contains(detector.AllLanguages, finger.Name) {
			languages = append(languages, finger.Name)
		}
	}
	return languages
}

// 分类载荷
func (p *PoC) classifyPayloads(detectedLanguages []string) *payloadGroups {
	var regularRCE, sstiAll []Payload

	// 分离RCE和SSTI载荷
	for _, payload := range Payloads {
		if payload.VulnType == VulnTypeSSTI {
			sstiAll = append(sstiAll, payload)
		} else {
			regularRCE = append(regularRCE, payload)
		}
	}

	return &payloadGroups{
		regularRCE:  p.selectRegularPayloads(regularRCE, detectedLanguages),
		sstiOOB:     p.selectSSTIOOBPayloads(sstiAll, detectedLanguages),
		sstiError:   p.selectSSTIErrorPayloads(sstiAll),
		sstiRegular: p.selectSSTIRegularPayloads(sstiAll, detectedLanguages),
	}
}

// 选择常规RCE载荷
func (p *PoC) selectRegularPayloads(regularPayloads []Payload, detectedLanguages []string) []Payload {
	if len(detectedLanguages) == 0 {
		return regularPayloads
	}

	var result []Payload
	languageSet := make(map[string]bool)
	for _, lang := range detectedLanguages {
		languageSet[lang] = true
	}

	// 添加语言特定的载荷
	for _, payload := range regularPayloads {
		if languageSet[payload.Language] {
			result = append(result, payload)
		}
	}

	// 如果没有找到语言特定的载荷，使用所有常规载荷
	if len(result) == 0 {
		result = regularPayloads
	} else {
		// 添加通用载荷
		for _, payload := range regularPayloads {
			if strings.Contains(payload.Name, "common") {
				result = append(result, payload)
			}
		}
	}

	return result
}

// 选择SSTI OOB载荷
func (p *PoC) selectSSTIOOBPayloads(sstiPayloads []Payload, detectedLanguages []string) []Payload {
	var result []Payload
	languageSet := make(map[string]bool)
	for _, lang := range detectedLanguages {
		languageSet[lang] = true
	}

	for _, payload := range sstiPayloads {
		if payload.Oob && (payload.Language == "" || languageSet[payload.Language]) {
			result = append(result, payload)
		}
	}

	return result
}

// 选择SSTI错误检测载荷
func (p *PoC) selectSSTIErrorPayloads(sstiPayloads []Payload) []Payload {
	var result []Payload
	for _, payload := range sstiPayloads {
		if strings.Contains(payload.Name, "error") {
			result = append(result, payload)
		}
	}
	return result
}

// 选择SSTI常规载荷
func (p *PoC) selectSSTIRegularPayloads(sstiPayloads []Payload, detectedLanguages []string) []Payload {
	var result []Payload
	languageSet := make(map[string]bool)
	for _, lang := range detectedLanguages {
		languageSet[lang] = true
	}

	for _, payload := range sstiPayloads {
		if !payload.Oob && !strings.Contains(payload.Name, "error") {
			if payload.Language == "" || languageSet[payload.Language] {
				result = append(result, payload)
			}
		}
	}

	return result
}

// 获取漏洞名称
func (p *PoC) getVulnerabilityName(payload Payload, defaultName string) string {
	code, _, _ := strings.Cut(payload.Name, "-")
	switch payload.VulnType {
	case VulnTypeCMD:
		return "命令注入"
	case VulnTypeCode:
		return "代码注入-" + code
	case VulnTypeSSTI:
		return "模版注入"
	default:
		return defaultName
	}
}
