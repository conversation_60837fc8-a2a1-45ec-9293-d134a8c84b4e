package fileupload

import (
	"fmt"
	"log/slog"
	"net/url"
	"path"
	"regexp"
	"slices"
	"strings"
	"sync"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"
)

var (
	PathRegex       = regexp.MustCompile(`(?i)(?:[^</\w](\.{0,2}(?:/(?:[\w.\-])+)*(?:/(?:[\w.\-])*(?:upload|img|image|pic|file|www/html)(?:[\w.\-])*)(?:/(?:[\w.\-])+)*)(?:['"?/:<]|\s))|((?:(?:[\w.\-])+/)*(?:(?:[\w.\-])*(?:upload|img|image|pic|file|www/html)(?:[\w.\-])*/)(?:(?:[\w.\-])+/)*(?:[\w.\-])+)|([a-zA-Z0-9]*\.[a-zA-Z]+)`)
	defaultFuzzDirs = []string{
		"/upload/",
		"/upload/images/",
		"/static/upload/",
		"/",
	}
)

const ID = string(npoc.FileUploadType)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "文件上传",
		Category:       npoc.FileUploadType,
		PocType:        npoc.GenericPocType,
		Tags:           nil,
		Description:    "攻击者可以通过利用任意文件上传漏洞，上传包含恶意代码的Web shell，然后通过该Web Shell获得对服务器的完全控制，窃取服务器上的敏感数据。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	rawReq := c.Task().Request
	rawResp := c.Task().Response
	params := rawReq.Params()
	if !needCheck(rawReq) {
		return nil
	}
	reqUrl := rawReq.URL
	// 网站根目录
	baseDirUrl := fmt.Sprintf("%s://%s", reqUrl.Scheme, reqUrl.Host)
	homeUrls := []string{baseDirUrl}
	dir := path.Dir(reqUrl.Path)
	if dir != "." && dir != "/" {
		currDirUrl, _ := url.JoinPath(baseDirUrl, dir)
		homeUrls = []string{baseDirUrl, currDirUrl}
	}
	// 获取原始响应包中的一些目录，地址信息
	dirs, dynamicFilePaths := getRespPath(rawResp)
	dirs = append(dirs, defaultFuzzDirs...)
	siteExt := path.Ext(rawReq.URL.String())
	if siteExt != "" {
		siteExt = strings.Trim(siteExt, ".")
	}
	shellTypes := []string{PHPType, JSPType, JSPXType, ASPType, ASPXType}

	var (
		fuzzPayloads []Payload
		scanType     string
		oobWg        sync.WaitGroup
	)

	for _, shellType := range shellTypes {
		if strings.Contains(shellType, strings.ToLower(siteExt)) {
			scanType = shellType
			break
		}
	}

	// 如果接口文件类型是shell中的某一种则只上传该种shell，否则都上传
	if scanType != "" {
		for _, payload := range shellPayloads {
			if payload.ShellType == scanType {
				fuzzPayloads = append(fuzzPayloads, payload)
			}
		}
	} else {
		fuzzPayloads = shellPayloads
	}
	if scanType == PHPType {
		generalPayloads = append(generalPayloads, picPayloads...)
	}

loop:
	for key, param := range params {
		if param.ParamType == httpv.ParamTypePath || (param.Multipart.Filename == "" && !strings.Contains(strings.ToLower(param.Multipart.ContentType), "octet-stream")) {
			continue
		}
		checkers := makeChecker(c, param.Multipart.Filename, generalPayloads, fuzzPayloads)
	fuzzLoop:
		for _, checker := range checkers {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			multipart := httpv.Multipart{
				Filename:    checker.UploadName,
				ContentType: checker.ContentType,
				Content:     []byte(checker.Content),
			}
			newParam := param
			newParam.Multipart = multipart
			newReq := rawReq.BuildRequestWithParams(key, newParam)
			resp, err := c.Task().Client.Do(c.Context, newReq)
			ext := path.Ext(checker.CheckName)
			if ext == ".gif" || ext == ".svg" {
				oobWg.Add(1)
				go func(ck Checker, req *httpv.Request, resp *httpv.Response, param httpv.Param) {
					defer utils.RecoverFun(c.Context)
					defer c.Task().Client.RdnsClient.RemoveURL(ck.OOBURL)
					defer oobWg.Done()
					if oobutils.WaitForOOBTrigger(c.Context, ck.OOBURL) {
						metaData := p.Metadata()
						oobDetails := oobutils.ExtractOOBDetails(c.Context, ck.OOBURL)
						vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: req, Response: resp}}}
						c.OutputVulnerability(&npoc.Vulnerability{
							Method:      c.Task().Request.Method,
							Category:    metaData.Category,
							Severity:    metaData.Severity,
							Param:       param.Key,
							URL:         c.Task().Request.URL.String(),
							PoC:         p.ID(),
							HTTP:        vHTTP,
							Name:        metaData.Name,
							Description: metaData.Description,
							OOBUrl:      ck.OOBURL,
							OOBDetails:  oobDetails,
							Extra:       map[string]string{npoc.ExtraKeyDes: VulDesc[ck.VulName]},
							Confidence:  npoc.ConfidenceMedium,
						})
					}
				}(checker, newReq, resp, param)
				continue
			}
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
				continue
			}
			if ext == ".htaccess" {
				continue
			}
			// 获取payload发送后响应中的地址信息
			// 如果获取到的动态文件地址是之前原始响应中没有的，大概率就是文件上传后的地址
			// 部分站点会将上传的文件进行重命名。比如:  shell.php  --> asd4gfds45645vc415sad.php
			_, dynamicFilePaths2 := getRespPath(resp)
			var (
				diffPaths []string
				checkUrls []string
			)
			for _, filePath := range dynamicFilePaths2 {
				if !slices.Contains(dynamicFilePaths, filePath) {
					diffPaths = append(diffPaths, filePath)
				}
			}

			for _, diffPath := range diffPaths {
				for _, homeUrl := range homeUrls {
					checkUrl, _ := url.JoinPath(homeUrl, diffPath)
					checkUrls = append(checkUrls, checkUrl)
				}
			}

			for _, pathDir := range dirs {
				for _, homeUrl := range homeUrls {
					checkUrl, _ := url.JoinPath(homeUrl, pathDir, checker.CheckName)
					checkUrls = append(checkUrls, checkUrl)
				}
			}
			checkUrls = funk.UniqString(checkUrls)
			for _, checkUrl := range checkUrls {
				select {
				case <-c.Context.Done():
					return c.Context.Err()
				default:
				}
				checkUrlP, err := url.Parse(checkUrl)
				if err != nil {
					slog.ErrorContext(c.Context, "url格式不正确", slogext.Error(err), "url", checkUrl)
					continue
				}
				checkUrlP.Path = path.Clean(checkUrlP.Path)
				checkReq := &httpv.Request{
					Method: "GET",
					URL:    checkUrlP,
				}
				checkResp, err := c.Task().Client.Do(c.Context, checkReq)
				if err != nil {
					continue
				}
				if checkResp.Status == 200 {
					res := checkResp.Body
					ct := checkResp.Header.Get("Content-Type")
					if (ct == "application/octet-stream" || ct == "image/png") && strings.HasSuffix(checkUrlP.Path, ".png") {
						result, err := checker.readPNGContent(checkResp.Body)
						if err != nil {
							slog.ErrorContext(c.Context, "读取PNG文件失败", slogext.Error(err))
						}
						res = []byte(result)
					}
					if checker.CheckReg != nil && checker.CheckReg.Match(res) {
						metaData := p.Metadata()
						if checker.VulName == HTMLType {
							metaData.Name = "文件上传-XSS"
							metaData.Severity = npoc.SeverityMedium
						}
						vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}, {Request: checkReq, Response: checkResp}}}
						c.OutputVulnerability(&npoc.Vulnerability{
							Method:      c.Task().Request.Method,
							Category:    metaData.Category,
							Severity:    metaData.Severity,
							Param:       param.Key,
							URL:         c.Task().Request.URL.String(),
							PoC:         p.ID(),
							HTTP:        vHTTP,
							Name:        metaData.Name,
							Description: metaData.Description,
							Extra:       map[string]string{npoc.ExtraKeyCheckRule: checker.CheckReg.String(), npoc.ExtraKeyShellURL: checkReq.URL.String(), npoc.ExtraKeyDes: VulDesc[checker.VulName]},
							Confidence:  npoc.ConfidenceHigh,
						})
						if ext == ".html" || ext == "" {
							continue fuzzLoop
						}
						continue loop
					}
				}
			}
		}
	}

	oobWg.Wait()

	return nil
}

func needCheck(req *httpv.Request) bool {
	params := req.Params()
	if len(params) == 0 {
		return false
	}
	if req.Method != httpv.MethodPost && req.Method != httpv.MethodPut {
		return false
	}
	ct := req.Header.Get("Content-Type")
	return strings.Contains(strings.ToLower(ct), "multipart/form-data")
}

func getRespPath(resp *httpv.Response) (dirs, dynamicFilePaths []string) {
	if resp == nil {
		return
	}
	pathMatches := PathRegex.FindAllStringSubmatch(string(resp.Body), 200)
	for _, matches := range pathMatches {
		for _, match := range matches {
			match = strings.Trim(match, "'")
			match = strings.TrimSpace(match)
			match = strings.Trim(match, "\"")
			if len(match) == 0 || len(match) > 256 {
				continue
			}
			if match[0] != '/' {
				match = "/" + match
			}
			ext := path.Ext(match)
			if ext != "" {
				dir := path.Dir(match)
				dirs = append(dirs, dir+"/")
				if utils.IsDynamicFileExt(ext) || ext == ".png" || ext == ".html" || ext == ".jpg" {
					dynamicFilePaths = append(dynamicFilePaths, match)
				}
			} else {
				if match[len(match)-1] != '/' {
					match = match + "/"
				}
				dirs = append(dirs, match)
			}
		}
	}
	dirs = funk.UniqString(dirs)
	dynamicFilePaths = funk.UniqString(dynamicFilePaths)
	return dirs, dynamicFilePaths
}
