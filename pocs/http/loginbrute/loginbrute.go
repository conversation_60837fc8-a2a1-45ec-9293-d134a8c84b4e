package loginbrute

import (
	"fmt"
	"log/slog"
	"regexp"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"golang.org/x/sync/semaphore"
)

const (
	ID               = string(npoc.WebWeakPasswordType)
	userDictName     = "web_pass_dict/web-user.txt"
	passwordDictName = "web_pass_dict/web-password.txt" // nolint:gosec // 1
	userCutPwdChar   = "%@@%"
)

var (
	passwordParamReg = regexp.MustCompile("(?i)(password)|(pass_word)|(pass_code)|(passcode)|(passw)|(pwd)|(psw)|(psd)|(pswd)|(passwd)|(mima)|(txtmm)|(yhmm)|(pass$)")
	usernameParamReg = regexp.MustCompile(`(?i)(^name$)|(uname)|(^uid$)|(^uin$)|(account)|(user_id)|(userid)|(txtuser$)|(nick)|(:user$)|(^user$)|(user.*login)|(login.*user)`)
	captchaParamReg  = regexp.MustCompile(`(?i)(captcha)|(vcode)|(v_code)|(validcode)|(valid_code)|(yzm)|(yanzhengma)|(textcode)|(imagecode)|(^code$)`)
	csrfParamReg     = regexp.MustCompile(`(?i)(csrf)`)
	signupParamReg   = regexp.MustCompile(`(?i)(signup)`)
)

type PoC struct {
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "弱口令登录",
		PocType:        npoc.GenericPocType,
		Category:       npoc.WebWeakPasswordType,
		Tags:           nil,
		Description:    "攻击者可以通过枚举的方法枚举出系统的账号密码，获取系统的权限从而进行下一步操作。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	bruteSmp := semaphore.NewWeighted(int64(15))
	if c.Task().WebLoginBruteDict == nil {
		return fmt.Errorf("web login brute dict is nil")
	}
	if c.Task().Request.Method == httpv.MethodGet && len(c.Task().Request.Params()) == 0 {
		if httpNeedBrute(c) {
			err := p.httpBrute(c, bruteSmp, c.Task().WebLoginBruteDict)
			if err != nil {
				slog.Error("p.httpBrute", "error", err)
			}
		}
	} else {
		needBrute, userKey, passKey := formNeedBrute(c)
		if needBrute {
			err := p.formAuth(c, bruteSmp, userKey, passKey, c.Task().WebLoginBruteDict)
			if err != nil {
				slog.WarnContext(c.Context, "form auth login brute have error", slogext.Error(err))
			}
		}
	}
	return nil
}
