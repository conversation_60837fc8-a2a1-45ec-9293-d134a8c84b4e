package loginbrute

import (
	"encoding/base64"
	"fmt"
	"log/slog"
	"strings"
	"sync"

	"github.com/thoas/go-funk"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc/utils"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/cache"
)

// 需要进行http的基础认证爆破
func httpNeedBrute(c *npoc.HTTPContext) bool {
	if c.Task().Response == nil {
		return false
	}
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false
	if _, ok := checkReq.Header["Authorization"]; ok {
		if _, ok2 := c.Task().Response.Header["Www-Authenticate"]; ok2 {
			return true
		}
	}
	return false
}

// http基础认证爆破
func (p *PoC) httpBrute(c *npoc.HTTPContext, bruteSmp *semaphore.Weighted, dictOption *npoc.WebLoginBruteDict) error { // nolint:funlen // 1
	randUser := funk.RandomString(8)
	randPass := funk.RandomString(8)
	userSuccessMap := sync.Map{}
	wg := sync.WaitGroup{}
	auth := "Basic " + base64.StdEncoding.EncodeToString([]byte(randUser+":"+randPass))
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false
	newReq := checkReq.Clone()
	newReq.Header.Set("Authorization", auth)
	resp, err := c.Task().Client.Do(c.Context, newReq)
	if err != nil {
		return err
	}
	if resp.Status != 401 {
		return fmt.Errorf("非http的basic认证请求")
	}

	fn := func(user, pass string, isOtherDict bool) {
		auth2 := "Basic " + base64.StdEncoding.EncodeToString([]byte(user+":"+pass))
		newReq2 := checkReq.Clone()
		newReq2.Header.Set("Authorization", auth2)
		resp2, err := c.Task().Client.Do(c.Context, newReq2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		// 401为http basic认证失败
		if resp2.Status == 401 {
			return
		}
		randPass2 := funk.RandomString(8)
		auth3 := "Basic " + base64.StdEncoding.EncodeToString([]byte(user+":"+randPass2))
		newReq3 := checkReq.Clone()
		newReq3.Header.Set("Authorization", auth3)
		resp3, err := c.Task().Client.Do(c.Context, newReq3)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		if resp3.Status == 401 {
			metaData := p.Metadata()
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{
				{Request: newReq2, Response: resp2, Payload: fmt.Sprintf("用户名: %s, 密码: %s", user, pass)},
				{Request: newReq3, Response: resp3, Payload: fmt.Sprintf("用户名: %s, 密码: %s", user, randPass2)},
				{Request: newReq, Response: resp, Payload: fmt.Sprintf("用户名: %s, 密码: %s", randUser, randPass)},
			}}
			extra := map[string]string{npoc.ExtraKeyUser: user, npoc.ExtraKeyPass: pass, npoc.ExtraKeySubCategory: "http_auth"}
			if isOtherDict {
				slog.InfoContext(c.Context, "tg dict brute success", "user", user, "password", pass)
				extra["dict_type"] = "tg_db"
			}
			c.OutputVulnerability(&npoc.Vulnerability{
				Method:      c.Task().Request.Method,
				Category:    metaData.Category,
				Severity:    metaData.Severity,
				Param:       "",
				Payload:     fmt.Sprintf("用户名: %s, 密码: %s", user, pass),
				URL:         checkReq.URL.String(),
				PoC:         p.ID(),
				HTTP:        vHTTP,
				Name:        metaData.Name,
				Description: metaData.Description,
				Extra:       extra,
				Confidence:  npoc.ConfidenceHigh,
			})
			// 当前用户认证成功，不再对当前账号做爆破
			userSuccessMap.Store(user, true)
		}
	}
	for _, dict := range dictOption.UserAndPassword {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		user, password, ok := strings.Cut(dict, userCutPwdChar)
		if !ok {
			user, password, ok = strings.Cut(dict, ":")
			if !ok {
				slog.ErrorContext(c.Context, "login other dict cut error", "dict", dict)
				continue
			}
		}
		err := bruteSmp.Acquire(c.Context, 1)
		if err != nil {
			return err
		}
		wg.Add(1)
		go func() {
			defer utils.RecoverFun(c.Context)
			defer wg.Done()
			defer bruteSmp.Release(1)
			if _, ok := userSuccessMap.Load(user); ok {
				return
			}
			fn(user, password, true)
		}()
	}
	var users, passwords []string
	if dictOption.UseDefaultDict {
		users, err = cache.LoadDict(userDictName)
		if err != nil {
			return err
		}
		passwords, err = cache.LoadDict(passwordDictName)
		if err != nil {
			return err
		}

	}
	if len(dictOption.CustomUsers) > 0 && len(dictOption.CustomPasswords) > 0 {
		users = append(users, dictOption.CustomUsers...)
		passwords = append(passwords, dictOption.CustomPasswords...)
	}
	for _, user := range users {
		for _, pass := range passwords {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			if strings.Contains(pass, "{{user}}") {
				pass = strings.ReplaceAll(pass, "{{user}}", "admin")
			}
			err := bruteSmp.Acquire(c.Context, 1)
			if err != nil {
				return err
			}
			wg.Add(1)
			go func() {
				defer wg.Done()
				defer bruteSmp.Release(1)
				if _, ok := userSuccessMap.Load(user); ok {
					return
				}
				fn(user, pass, false)
			}()
		}
	}

	wg.Wait()
	return nil
}
