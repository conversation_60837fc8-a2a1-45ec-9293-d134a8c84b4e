package sqli

import (
	"fmt"
	"log/slog"
	"math"
	"net/http"
	"strconv"
	"sync"

	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/utils/text"

	"github.acme.red/pictor/foundation/slogext"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/base"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/boolinject"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/errorinject"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/time"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
)

const ID = string(npoc.SqlInjectType)

type PoC struct {
	AllowTimeType    bool
	AllowBoolType    bool
	AllowCheckCookie bool
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "SQL",
		PocType:        npoc.GenericPocType,
		Category:       npoc.SqlInjectType,
		Tags:           nil,
		Description:    "攻击者利用 SQL 注入漏洞，可以访问或者修改数据，用来从数据库中获取，敏感信息，或者利用潜在的特征执行添加用户，导出文件等一系列恶意操作，甚至有可能获取数据库乃至系统的最高权限，利用数据库漏洞进行攻击。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	// var cookieParams []httpv.Param
	// if p.AllowCheckCookie { // www_todo这里这样不太合理，需要重新规划，不应该在扫描过程中对请求的参数做变动，会影响其他扫描
	// 	cookieParams = c.Task().Request.GetCookieParams()
	// }
	if !needScan(c) { // 判断是否需要进行sql注入测试
		return nil
	}
	// 对原始请求重发，获取响应包。设置重发次数
	sampRespNum := 5
	onceFunc := sync.Once{}
	// 获取五次原始请求的响应
	sampResps, err := getSampResp(c, sampRespNum)
	if err != nil {
		slog.WarnContext(c.Context, "sqli test 重复获取原始响应包失败, 跳过测试", slogext.Error(err))
		return nil
	}
	// 判断原始响应中是否有sql报错语句
	var rawRespExistSQLErrorStr bool
	for _, resp := range sampResps {
		if rawRespExistSQLErrorStr {
			break
		}
		var body []byte
		utf8Body, err := resp.GetUTF8Body()
		if err != nil || len(utf8Body) == 0 {
			body = resp.Body
		} else {
			body = utf8Body
		}
		_, _, rawRespExistSQLErrorStr = errorinject.BodyExistSQLError(body)
	}

	// 判断原始请求的响应是否都相似，不相似说明网站不稳定，无法进行bool盲注。
	sampRespsIsSimilar, err := similarity.RespsIsLike(sampResps, false, nil, 95)
	if err != nil {
		slog.Warn("相似度计算失败", slogext.Error(err))
	}
	var maxWaitTime int64
	checkReq := c.Task().Request.Clone()
	checkReq.FollowRedirects = false

	var (
		lastParamExistVuln bool
		sqlErrorStrVuln    *npoc.Vulnerability // 响应中sql报错语句，但是没有成功执行报错函数的漏洞
	)
nextParam:
	for key, param := range checkReq.Params() {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		if param.PathParam.IsFilePath {
			continue
		}
		if param.ParamType == httpv.ParamTypePath {
			// 判断之前是否有对该目录进行扫描
			if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_%s", ID, param.Key), struct{}{}); ok {
				continue
			}
		}
		if !param.NeedCheck {
			continue
		}
		// 原始页面没有sql报错信息信息，上一个参数没有能利用的sql注入漏洞，但是有sql报错信息的漏洞，将该漏洞返回
		if !rawRespExistSQLErrorStr && !lastParamExistVuln && sqlErrorStrVuln != nil {
			p.outputVuln(c, sqlErrorStrVuln)
			sqlErrorStrVuln = nil
		}
		lastParamExistVuln = false
		var paramValueIsNumber bool
		// 判断参数是否是 number类型
		_, err = strconv.ParseFloat(param.Value, 32)
		if err == nil {
			// 能正常转换为float则可以先fuzz number类型
			paramValueIsNumber = true
		}
		var lastVuln *npoc.Vulnerability
		// 布尔注入检测,需要满足网站同一个请求响应是一样的
		if sampRespsIsSimilar && p.AllowBoolType {
		boolLoop:
			for i := range boolinject.CheckTimes { // bool注入进行两次重复判断，仅有一次成功可信度设置为中，两次都成功可信度为高
				vuln, done, err := boolinject.CheckVuln(c, param, paramValueIsNumber, i == 0)
				if done {
					p.outputVuln(c, vuln)
					lastParamExistVuln = true
					continue nextParam
				}
				if err != nil {
					slog.WarnContext(c.Context, "sqli bool inject check fail", slogext.Error(err))
				}

				if vuln != nil { // 两种情况，1是bool注入本次成功了，2是存在sql的报错语句
					lastVuln = vuln
					if i+1 == boolinject.CheckTimes { // 最后一次校验成功，推送漏洞并继续扫描后续的参数
						p.outputVuln(c, vuln)
						lastParamExistVuln = true
						continue nextParam
					}
					if vuln.Name == boolinject.VulnName {
						continue
					}
					sqlErrorStrVuln = vuln
				} else { // 本次校验失败
					switch i {
					case boolinject.CheckTimes - 1: // 最后一次校验不成功，可信度设置为中
						lastVuln.Confidence = npoc.ConfidenceMedium
						p.outputVuln(c, lastVuln)
						continue nextParam
					default: // 前面几次校验不成功，不进入后续校验
						break boolLoop
					}
				}
			}
		} else if p.AllowBoolType {
			// 当页面不稳定时的单纯报错注入检测
			if vuln := p.checkErrorInjection(c, param, paramValueIsNumber); vuln != nil {
				p.outputVuln(c, vuln)
				lastParamExistVuln = true
				continue
			}
		}

		if p.AllowTimeType {
			// 时间盲注
			// 再获取五次原始请求
			onceFunc.Do(func() {
				sampResps2, err := getSampResp(c, sampRespNum)
				if err != nil {
					slog.Warn("重复获取原始响应包失败！", "error", err)
					return
				}
				sampResps = append(sampResps, sampResps2...)
				var times []float64
				for _, resp := range sampResps {
					waitTime := resp.TimeTrace.GetServerHandledTime().Milliseconds()
					times = append(times, float64(waitTime))
				}
				maxWaitTime = computeMaxWaitTime(times)
			})
			// 第二次获取的响应包没成功获取到五次
			if len(sampResps) < sampRespNum*2 {
				continue
			}
			vuln, err := time.CheckVuln(c, key, checkReq, maxWaitTime, paramValueIsNumber)
			if err != nil {
				slog.Warn("sqli time_inject scan fail", slogext.Error(err))
			}
			if vuln != nil {
				p.outputVuln(c, vuln)
				lastParamExistVuln = true
				continue
			}
		}
	}

	return nil
}

func (p *PoC) outputVuln(c *npoc.HTTPContext, vuln *npoc.Vulnerability) {
	metaData := p.Metadata()
	vuln.Category = metaData.Category
	vuln.Description = metaData.Description
	vuln.PoC = p.ID()
	c.OutputVulnerability(vuln)
}

// 根据多次的请求响应时间，通过正态分布逻辑计算出请求响应大概率不会超过的一个时间临界值
// todo_www 实际上直接取多个时间中的最大时间然后加两秒感觉也能达到效果
func computeMaxWaitTime(times []float64) int64 {
	var (
		// 时间之和
		timeSum float64
		// 平均时间
		timeAvg float64
		// 方差
		variance float64
		// 标准差
		stdDev float64
		// 等待时间临界值
		maxWaitTime int64
	)
	for _, t := range times {
		timeSum += t
	}
	timeAvg = timeSum / float64(len(times))
	for _, item := range times {
		variance += math.Pow(item-timeAvg, 2)
	}
	stdDev = math.Sqrt(variance / float64(len(times)))
	// 动态分布中标准差的两倍按理说是95%的概率是不一会超过这个时间的，设置1s允许的误差范围
	maxWaitTime = int64(math.Ceil((timeAvg+stdDev*2)/1000.0)) + 1
	return maxWaitTime
}

func needScan(c *npoc.HTTPContext) bool {
	if c.Task().Response == nil || c.Task().Request == nil {
		return false
	}
	method := c.Task().Request.Method
	statusCode := c.Task().Response.Status
	params := c.Task().Request.Params()

	if method == http.MethodOptions || method == http.MethodDelete || statusCode/100 == 5 || len(params) == 0 {
		return false
	}

	for _, param := range params { // 参数中存在有需要扫描的参数才进行扫描
		if param.NeedCheck {
			return true
		}
	}
	return false
}

func getSampResp(c *npoc.HTTPContext, num int) ([]*httpv.Response, error) {
	var responses []*httpv.Response
	req := c.Task().Request
	req.FollowRedirects = false
	for i := 0; i < num; i++ {
		resp, err := c.Task().Client.Do(c.Context, req)
		if err != nil {
			return nil, err
		}
		responses = append(responses, resp)
	}
	return responses, nil
}

func (p *PoC) checkErrorInjection(c *npoc.HTTPContext, param httpv.Param, isNumber bool) *npoc.Vulnerability {
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{}}
	randStrLen := funk.RandomInt(1, 5) //nolint:mnd //need indeed
	// number类型检测
	if isNumber {
		payload := fmt.Sprintf("%s-%s", param.Value, funk.RandomString(randStrLen, text.LowerLetterChars))
		if !boolinject.CheckPayload(c, param.OnlyKey, vHTTP, payload, false, true) {
			return nil
		}
		vuln, _, _ := errorinject.CheckVuln(c, param, vHTTP.Follows[0], []string{fmt.Sprintf("%s-%s", param.Value, errorinject.ErrorPayload)})
		if vuln != nil {
			return vuln
		}
	}

	// orderby检测
	payload := fmt.Sprintf("%s,%s", param.Value, funk.RandomString(randStrLen, text.LowerLetterChars))
	if !boolinject.CheckPayload(c, param.OnlyKey, vHTTP, payload, false, true) {
		return nil
	}
	vuln, _, _ := errorinject.CheckVuln(c, param, vHTTP.Follows[0], []string{fmt.Sprintf("%s,%s", param.Value, errorinject.ErrorPayload)})
	if vuln != nil {
		return vuln
	}

	// string类型检测
	payload1 := fmt.Sprintf("%s%s", param.Value, "'")
	if !boolinject.CheckPayload(c, param.OnlyKey, vHTTP, payload1, false, true) {
		return nil
	}
	paramEscapeCharArr := []string{"'", "\""}
	for _, escapeChar := range paramEscapeCharArr {
		errorInjectPayloads := base.MakeErrorInjectPayloads(escapeChar)
		vuln, _, _ := errorinject.CheckVuln(c, param, vHTTP.Follows[0], errorInjectPayloads)
		if vuln != nil {
			return vuln
		}
	}

	return nil
}
