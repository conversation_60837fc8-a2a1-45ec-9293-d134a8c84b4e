package base

import "github.acme.red/intelli-sec/npoc/pkg/httpv"

const (
	MySQL      = "MySQL"
	SQLServer  = "SQLServer"
	Oracle     = "Oracle"
	PostgreSQL = "PostgreSQL"
	SQLite     = "SQLite"
	Access     = "Access"
	GenericDB  = "generic"

	ParamTypeNum     = "number"
	ParamTypeStr     = "string"
	ParamTypeDjango  = "django"
	ParamTypeSQLWord = "order"
)

var AllDBNames = []string{MySQL, SQLServer, Oracle, PostgreSQL, SQLite, Access}

type FuncParams struct {
	Client         *httpv.Client
	Req            *httpv.Request
	Resp           *httpv.Response
	ParamType      []string
	ReqParamMapKey string // 当前参数的map对应的key,非param.key
}
