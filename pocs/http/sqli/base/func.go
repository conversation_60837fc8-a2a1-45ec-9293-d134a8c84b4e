package base

import (
	"fmt"
	"slices"

	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/errorinject"
)

func ParamTypeFilter(paramType []string, allowTypes []string) bool {
	for _, allowType := range allowTypes {
		if slices.Contains(paramType, allowType) {
			return true
		}
	}
	return false
}

//func DBTypeFilter(paramType string, allowDBs []string) bool {
//	for _, allowType := range allowDBs {
//		if allowType == paramType {
//			return true
//		}
//	}
//	return false
//}

func MakeErrorInjectPayloads(escapeChar string) []string {
	var (
		results  []string
		midChars = []string{" and ", "+", "||"}
	)
	for _, midChar := range midChars {
		results = append(results, fmt.Sprintf("%s%s%s%s%s", escapeChar, midChar, errorinject.ErrorPayload, midChar, escapeChar))
	}

	return results
}
