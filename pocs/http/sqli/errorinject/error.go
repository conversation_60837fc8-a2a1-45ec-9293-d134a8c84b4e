package errorinject

import (
	"log/slog"
	"strings"

	"github.acme.red/pictor/foundation/slogext"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

const (
	VulnSubCategoryErrorStr = "sqli_error_string"
	VulnSubCategoryErrorFun = "sqli_error_function"
	VulnNameErrorStr        = "SQL-报错语句"
	VulnNameErrorFun        = "SQL-报错注入"
)

func CheckVuln(c *npoc.HTTPContext, rawParam httpv.Param, httpFollow npoc.HTTPFollow, initPayloads []string) (*npoc.Vulnerability, bool, error) {
	initPayloads = append(initPayloads, ErrorPayload) // 添加一种只有报错语句作为参数值的payload
	rawReq := c.Task().Request.Clone()
	dbmsName, errorStr, existError := BodyExistSQLError(httpFollow.Response.Body)
	if !existError {
		return nil, false, nil
	}
	if _, existPayload := SQLIErrorPayloads[dbmsName]; !existPayload {
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{httpFollow}}
		vuln := &npoc.Vulnerability{
			Name:    VulnNameErrorStr,
			Method:  c.Task().Request.Method,
			Param:   rawParam.Key,
			Payload: httpFollow.Payload,
			URL:     c.Task().Request.URL.String(),
			HTTP:    vHTTP,
			Extra: map[string]string{
				npoc.ExtraKeyCheckStr: errorStr, npoc.ExtraKeySubCategory: VulnSubCategoryErrorStr,
				npoc.ExtraKeyDBM: dbmsName, npoc.ExtraKeyDes: "检测到页面中有数据库报错信息，可能存在误报，请自行校验",
			},
			Severity:   npoc.SeverityMedium,
			Confidence: npoc.ConfidenceMedium,
		}
		return vuln, false, nil
	}
	for _, sqlPayload := range SQLIErrorPayloads[dbmsName] {
		for _, initPayload := range initPayloads {
			checker, err := MakeChecker(sqlPayload, initPayload)
			if err != nil {
				slog.ErrorContext(c.Context, "sqli_error make_checker failed", slogext.Error(err))
				return nil, false, nil
			}
			checkReq, checkResp, err := c.Task().Client.SendNewRequest(c.Context, rawReq, rawParam.OnlyKey, checker.Payload)
			if err != nil {
				c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
				return nil, false, nil //nolint:nilerr //need indeed
			}
			if strings.Contains(strings.ToLower(string(checkResp.Body)), checker.CheckStr) {
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: checkReq, Response: checkResp, Payload: checker.Payload}}}
				vuln := &npoc.Vulnerability{
					Name:     VulnNameErrorFun,
					Method:   c.Task().Request.Method,
					Param:    rawParam.Key,
					Payload:  checker.Payload,
					URL:      c.Task().Request.URL.String(),
					HTTP:     vHTTP,
					Severity: npoc.SeverityHigh,
					Extra: map[string]string{
						npoc.ExtraKeyCheckStr: checker.CheckStr, npoc.ExtraKeySubCategory: VulnSubCategoryErrorFun,
						npoc.ExtraKeyDBM: dbmsName,
					},
					Confidence: npoc.ConfidenceHigh,
				}
				return vuln, true, nil
			}
		}
	}

	return nil, false, nil
}

func BodyExistSQLError(body []byte) (string, string, bool) {
	for dbmsName, regexps := range SQLIErrorsRegex {
		for _, rg := range regexps {
			matchBytes := rg.Find(body)
			if len(matchBytes) > 1 {
				return dbmsName, string(matchBytes), true
			}
		}
	}
	return "", "", false
}
