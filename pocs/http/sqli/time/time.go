package time //nolint:cyclop // need indeed

import (
	"fmt"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"log/slog"
	"slices"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/base"
	"github.acme.red/pictor/foundation/slogext"
)

// CheckVuln rawWaiteTime为从多次请求的原始响应时间中，使用动态分布的两倍标准差计算得出的请求大概率(95%)不会超过的时间
// 如果超过则认定为sleep睡眠生效存在漏洞，或是waf拦截
func CheckVuln(c *npoc.HTTPContext, paramMapKey string, oCheckReq *httpv.Request, maxWaitTime int64, paramValueIsNumber bool) (*npoc.Vulnerability, error) { //nolint:gocognit,funlen // need indeed
	checkReq := oCheckReq.Clone()
	checkParam := checkReq.Params()[paramMapKey]
	checkReq.FollowRedirects = false
	// 参数的原始值，如果是django类型的参数，则只保留前缀一部分用于后续fuzz的数据。
	rawParamValue := checkParam.Value
	for _, payload := range Payloads {
		select {
		case <-c.Context.Done():
			return nil, c.Context.Err()
		default:
		}
		if !c.Task().FullScan && payload.Fuzz {
			continue
		}
		// 部分payload只有参数是整型的时候才用
		// 参数是字符串，但是payload不适用字符串则不测试
		if !slices.Contains(payload.ParamType, base.ParamTypeStr) && !paramValueIsNumber {
			continue
		}
		// 对payload中的预制参数赋值(原参数值，sleep时间)
		// 设置第一次请求的sleep时间为原始请求的响应时间
		sleepTime1 := maxWaitTime
		payload1, err := MakePayload(payload.Value, rawParamValue, sleepTime1)
		if err != nil {
			slog.ErrorContext(c.Context, "makePayload failed", slogext.Error(err))
			continue
		}
		req1, resp1, err := c.Task().Client.SendNewRequest(c.Context, checkReq, paramMapKey, payload1)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
			continue
		}
		resp1WaiteTime := resp1.TimeTrace.GetServerHandledTime().Milliseconds()
		// waitTime = sleepTime1 + maxWaitTime,应该比 sleepTime1 大，才符合逻辑。不符合逻辑则检测下一条payload
		if resp1WaiteTime <= sleepTime1*1000 || resp1WaiteTime > (sleepTime1+maxWaitTime)*1000 {
			continue
		}
		// 设置第二次请求的sleep时间为0
		sleepTime2 := int64(0)
		payload2, err := MakePayload(payload.Value, rawParamValue, sleepTime2)
		if err != nil {
			slog.ErrorContext(c.Context, "makePayload failed", slogext.Error(err))
			continue
		}
		req2, resp2, err := c.Task().Client.SendNewRequest(c.Context, checkReq, paramMapKey, payload2)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
			continue
		}
		resp2WaiteTime := resp2.TimeTrace.GetServerHandledTime().Milliseconds()
		// 按理说rawTime应该是从多次请求中计算出的请求大概率不会超过的时间，如果sleep为0时waitTime超过该时间，大概率是waf拦截了。停止扫描后面的请求
		if resp2WaiteTime >= maxWaitTime*1000 {
			return nil, nil
		}
		// 第一次请求的时间(maxWaitTime+sleepTime1)减去第二次的时间(maxWaitTime)差值为第一次sleep的时间，按理说应该等于sleepTime1，这里设置1s的误差
		// 如果 sleepTime1+1<sleepTime1 则不符合逻辑
		if resp1WaiteTime-resp2WaiteTime+1000 < sleepTime1*1000 {
			continue
		}
		// 设置第三次请求的sleep时间为原始请求的响应时间加两秒的两倍，再一次做确认校验
		sleepTime3 := (maxWaitTime + 2) * 2
		payload3, err := MakePayload(payload.Value, rawParamValue, sleepTime3)
		if err != nil {
			slog.ErrorContext(c.Context, "makePayload failed", slogext.Error(err))
			continue
		}
		req3, resp3, err := c.Task().Client.SendNewRequest(c.Context, checkReq, paramMapKey, payload3)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
			continue
		}
		resp3WaiteTime := resp3.TimeTrace.GetServerHandledTime().Milliseconds()
		// waitTime = sleepTime3 + maxWaitTime,应该比sleepTime3 大，才符合逻辑
		if resp3WaiteTime <= sleepTime3*1000 || resp3WaiteTime > (sleepTime3+maxWaitTime)*1000 {
			continue
		}

		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{
			{Request: req1, Response: resp1, Payload: payload1},
			{Request: req2, Response: resp2, Payload: payload2},
			{Request: req3, Response: resp3, Payload: payload3},
		}}
		vuln := &npoc.Vulnerability{
			Name:       "SQL-时间盲注",
			Method:     c.Task().Request.Method,
			Param:      checkParam.Key,
			Payload:    payload1,
			URL:        c.Task().Request.URL.String(),
			HTTP:       vHTTP,
			Severity:   npoc.SeverityHigh,
			Extra:      map[string]string{npoc.ExtraKeySubCategory: "base_time", npoc.ExtraKeyDBM: payload.DBName},
			Confidence: npoc.ConfidenceMedium,
		}
		if payload.CloseChar != "" {
			vuln.Extra["close_char"] = payload.CloseChar
			vuln.Extra["param_type"] = base.ParamTypeStr
		} else {
			vuln.Extra["param_type"] = base.ParamTypeNum
		}
		des := fmt.Sprintf("经预判该目标响应延时误差一般不会超过%vs,第一个请求sleep:%vs, 响应时间为: %vms", maxWaitTime, sleepTime1, resp1WaiteTime)
		des += fmt.Sprintf(",第二个请求sleep:%vs, 响应时间为: %vms", sleepTime2, resp2WaiteTime)
		des += fmt.Sprintf(",第三个请求sleep:%vs, 响应时间为: %vms", sleepTime3, resp3WaiteTime)
		vuln.Extra[npoc.ExtraKeyDes] = des
		// 到这里其实已经可以确定漏洞存在了，后续的校验只是为了提高漏洞可信度
		// 设置第四次请求的sleep时间为原始请求的响应时间加两秒，再一次做确认校验
		sleepTime4 := maxWaitTime * 2

		payload4, err := MakePayload(payload.Value, rawParamValue, sleepTime4)
		if err != nil {
			slog.ErrorContext(c.Context, "makePayload failed", slogext.Error(err))
			continue
		}
		req4, resp4, err := c.Task().Client.SendNewRequest(c.Context, checkReq, paramMapKey, payload4)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
			continue
		}
		resp4WaiteTime := resp4.TimeTrace.GetServerHandledTime().Milliseconds()
		des += fmt.Sprintf(",第四个请求sleep:%vs, 响应时间为: %vms", sleepTime4, resp4WaiteTime)
		vuln.HTTP.Follows = append(vuln.HTTP.Follows, npoc.HTTPFollow{
			Request:  req4,
			Response: resp4,
			Payload:  payload4,
		})
		// waitTime = sleepTime4 + maxWaitTime,sleepTime4 大，才符合逻辑
		if resp4WaiteTime <= sleepTime4*1000 { // 比sleep时间小，不存在漏洞
			continue
		} else if resp4WaiteTime > (sleepTime4+maxWaitTime)*1000 {
			vuln.Confidence = npoc.ConfidenceLow
			vuln.Extra[npoc.ExtraKeyDes] = des
			return vuln, nil
		}
		// 设置第五次请求的sleep时间为0
		sleepTime5 := int64(0)
		payload5, err := MakePayload(payload.Value, rawParamValue, sleepTime5)
		if err != nil {
			slog.ErrorContext(c.Context, "makePayload failed", slogext.Error(err))
			continue
		}
		req5, resp5, err := c.Task().Client.SendNewRequest(c.Context, checkReq, paramMapKey, payload5)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
			continue
		}
		resp5WaiteTime := resp5.TimeTrace.GetServerHandledTime().Milliseconds()
		des += fmt.Sprintf(",第五个请求sleep:%vs, 响应时间为: %vms", sleepTime5, resp5WaiteTime)
		vuln.HTTP.Follows = append(vuln.HTTP.Follows, npoc.HTTPFollow{
			Request:  req5,
			Response: resp5,
			Payload:  payload5,
		})
		if resp5WaiteTime > maxWaitTime*1000 {
			vuln.Extra[npoc.ExtraKeyDes] = des
			vuln.Confidence = npoc.ConfidenceLow
			return vuln, nil
		}

		// 设置第六次请求的sleep时间
		sleepTime6 := (maxWaitTime + 2) * 2
		payload6, err := MakePayload(payload.Value, rawParamValue, sleepTime6)
		if err != nil {
			slog.ErrorContext(c.Context, "makePayload failed", slogext.Error(err))
			return vuln, nil
		}
		req6, resp6, err := c.Task().Client.SendNewRequest(c.Context, checkReq, paramMapKey, payload6)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
			return vuln, nil //nolint:nilerr // need indeed
		}
		resp6WaiteTime := resp6.TimeTrace.GetServerHandledTime().Milliseconds()
		des += fmt.Sprintf(",第六个请求sleep:%vs, 响应时间为: %vms", sleepTime6, resp6WaiteTime)
		vuln.HTTP.Follows = append(vuln.HTTP.Follows, npoc.HTTPFollow{
			Request:  req6,
			Response: resp6,
			Payload:  payload6,
		})
		if resp6WaiteTime <= sleepTime6*1000 { // 比sleep时间小，不存在漏洞
			continue
		} else if resp6WaiteTime > (sleepTime6+maxWaitTime)*1000 {
			vuln.Extra[npoc.ExtraKeyDes] = des
			vuln.Confidence = npoc.ConfidenceLow
			return vuln, nil
		}
		vuln.Extra[npoc.ExtraKeyDes] = des
		vuln.Confidence = npoc.ConfidenceHigh
		return vuln, nil
	}
	return nil, nil
}
