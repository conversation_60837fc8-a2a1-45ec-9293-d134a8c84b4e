package boolinject

import (
	"fmt"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/base"
	"github.acme.red/intelli-sec/npoc/pocs/http/sqli/errorinject"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
	"github.acme.red/intelli-sec/npoc/utils/text"
	"github.com/thoas/go-funk"
)

const (
	// CheckTimes bool注入重复判断次数。
	CheckTimes = 3
	// 两种不同的payload的相似度阈值，小于这个阈值则认为请求结果不同。
	similarityMax = 80
	// payload为true的与原始响应的相似度，大于这个阈值则认为请求结果相同。
	similarityMax2             = 95
	VulnSubCategoryBoolStr     = "sqli_bool_string"
	VulnSubCategoryBoolNum     = "sqli_bool_number"
	VulnSubCategoryBoolOrderBy = "sqli_bool_order_by"
	VulnName                   = "SQL-布尔注入"
	IntZeroth                  = 0
	IntFirst                   = 1
	IntSecond                  = 2
	IntThird                   = 3
)

func CheckVuln(c *npoc.HTTPContext, rawParam httpv.Param, paramIsNumber bool, isFirst bool) (*npoc.Vulnerability, bool, error) {
	var sqlErrorStrVuln *npoc.Vulnerability // 响应中sql报错语句，但是没有成功执行报错函数的漏洞

	if paramIsNumber { // 参数类型属于number
		vulnBool := checkNumber(c, rawParam, isFirst)
		if vulnBool != nil {
			switch vulnBool.Name {
			case errorinject.VulnNameErrorFun: // sql报错函数注入，结束后续检测
				return vulnBool, true, nil
			case errorinject.VulnNameErrorStr:
				sqlErrorStrVuln = vulnBool
			case VulnName: // 布尔注入检测成功，继续进行后续检测(多次尝试)
				return vulnBool, false, nil
			}
		}
	}

	vulnOrderBy := checkOrderBy(c, rawParam, isFirst)
	if vulnOrderBy != nil {
		switch vulnOrderBy.Name {
		case errorinject.VulnNameErrorFun: // sql报错函数注入，结束后续检测
			return vulnOrderBy, true, nil
		case errorinject.VulnNameErrorStr:
			sqlErrorStrVuln = vulnOrderBy
		case VulnName: // 布尔注入检测成功，继续进行后续检测(多次尝试)
			return vulnOrderBy, false, nil
		}
	}

	vulnString := checkString(c, rawParam, isFirst)
	if vulnString != nil {
		switch vulnString.Name {
		case errorinject.VulnNameErrorFun: // sql报错函数注入，结束后续检测
			return vulnString, true, nil
		case errorinject.VulnNameErrorStr:
			sqlErrorStrVuln = vulnString
		case VulnName: // 布尔注入检测成功，继续进行后续检测(多次尝试)
			return vulnString, false, nil
		}
	}
	return sqlErrorStrVuln, false, nil
}

func checkString(c *npoc.HTTPContext, rawParam httpv.Param, isFirst bool) *npoc.Vulnerability { //nolint:gocognit // no need to reduce complexity
	var (
		// 逃逸字符
		paramEscapeCharArr = []string{"'", "\""}
		// 连接字符
		payloadMidCharArr = []string{" ", "+", "||"}
	)

	var sqlErrorStrVuln *npoc.Vulnerability // 响应中sql报错语句，但是没有成功执行报错函数的漏洞
	for _, escapeChar := range paramEscapeCharArr {
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{}}
		payload1 := fmt.Sprintf("%s%s", rawParam.Value, escapeChar)
		if !CheckPayload(c, rawParam.OnlyKey, vHTTP, payload1, false, true) {
			continue
		}
		if isFirst {
			errorInjectPayloads := base.MakeErrorInjectPayloads(escapeChar)
			// 判断是否有报错注入
			vuln, done, _ := errorinject.CheckVuln(c, rawParam, vHTTP.Follows[0], errorInjectPayloads)
			if vuln != nil {
				if done {
					return vuln
				} else {
					sqlErrorStrVuln = vuln
				}
			}
		}
		payload2 := fmt.Sprintf("%s%s%s%s", rawParam.Value, escapeChar, escapeChar, escapeChar)
		if !CheckPayload(c, rawParam.OnlyKey, vHTTP, payload2, false, true) {
			continue
		}

		for _, midChar := range payloadMidCharArr {
			randStrLen := funk.RandomInt(1, 5) //nolint:mnd //need indeed
			payload3 := fmt.Sprintf("%s%s%s%s", rawParam.Value, escapeChar, midChar, escapeChar)
			if !CheckPayload(c, rawParam.OnlyKey, vHTTP, payload3, true, true) {
				continue
			}
			payload4 := fmt.Sprintf("%s%s%s%s%s%s", rawParam.Value, escapeChar, midChar, funk.RandomString(randStrLen, text.LowerLetterChars), midChar, escapeChar)
			if !CheckPayload(c, rawParam.OnlyKey, vHTTP, payload4, false, true) {
				continue
			}
			payload5 := fmt.Sprintf("%s%s%s", rawParam.Value, escapeChar, midChar)
			if !CheckPayload(c, rawParam.OnlyKey, vHTTP, payload5, false, true) {
				continue
			}
			vuln2 := &npoc.Vulnerability{
				Name:       VulnName,
				Method:     c.Task().Request.Method,
				Param:      rawParam.Key,
				Payload:    vHTTP.Follows[2].Payload,
				URL:        c.Task().Request.URL.String(),
				HTTP:       vHTTP,
				Severity:   npoc.SeverityHigh,
				Extra:      map[string]string{npoc.ExtraKeySubCategory: VulnSubCategoryBoolStr, npoc.ExtraKeyParamType: base.ParamTypeStr},
				Confidence: npoc.ConfidenceHigh,
			}
			return vuln2
		}
	}
	return sqlErrorStrVuln
}

func checkOrderBy(c *npoc.HTTPContext, rawParam httpv.Param, isFirst bool) *npoc.Vulnerability {
	var sqlErrorStrVuln *npoc.Vulnerability // 响应中sql报错语句，但是没有成功执行报错函数的漏洞
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{}}
	for i, payloadType := range []bool{false, true, true, false} {
		randStrLen := funk.RandomInt(1, 5) //nolint:mnd //need indeed
		var payload string
		switch i {
		case IntZeroth:
			payload = fmt.Sprintf("%s,%s", rawParam.Value, funk.RandomString(randStrLen, text.LowerLetterChars))
		case IntFirst:
			payload = fmt.Sprintf("%s,(%d-%d-1)", rawParam.Value, randStrLen, randStrLen-2) //nolint:mnd //need indeed
		case IntSecond:
			payload = fmt.Sprintf("%s,(1*1*1)", rawParam.Value)
		case IntThird:
			payload = fmt.Sprintf("%s,(%d*%s*%d)", rawParam.Value, funk.RandomInt(0, 100), funk.RandomString(randStrLen, text.LowerLetterChars), funk.RandomInt(0, 100)) //nolint:mnd //need indeed
		}

		success := CheckPayload(c, rawParam.OnlyKey, vHTTP, payload, payloadType, false)
		if !success { // bool注入失败，如果有sql报错语句漏洞的也可以返回一下
			return sqlErrorStrVuln
		}
		if i == 0 && isFirst { // bool注入可能有多次重复校验，对于报错注入多次尝试没有什么意义，就只测试一遍
			// 判断是否有报错注入
			vuln, done, _ := errorinject.CheckVuln(c, rawParam, vHTTP.Follows[0], []string{fmt.Sprintf("%s,%s", rawParam.Value, errorinject.ErrorPayload)})
			if vuln != nil {
				if done {
					return vuln
				} else {
					sqlErrorStrVuln = vuln
				}
			}
		}
	}
	vuln := &npoc.Vulnerability{
		Name:       VulnName,
		Method:     c.Task().Request.Method,
		Param:      rawParam.Key,
		Payload:    vHTTP.Follows[1].Payload,
		URL:        c.Task().Request.URL.String(),
		HTTP:       vHTTP,
		Severity:   npoc.SeverityHigh,
		Extra:      map[string]string{npoc.ExtraKeySubCategory: VulnSubCategoryBoolOrderBy, npoc.ExtraKeyParamType: base.ParamTypeNum},
		Confidence: npoc.ConfidenceHigh,
	}
	return vuln
}

func checkNumber(c *npoc.HTTPContext, rawParam httpv.Param, isFirst bool) *npoc.Vulnerability {
	var sqlErrorStrVuln *npoc.Vulnerability // 响应中sql报错语句，但是没有成功执行报错函数的漏洞
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{}}
	for i, payloadType := range []bool{false, true, true, false} {
		randStrLen := funk.RandomInt(1, 5) //nolint:mnd //need indeed
		var payload string
		switch i {
		case IntZeroth:
			payload = fmt.Sprintf("%s-%s", rawParam.Value, funk.RandomString(randStrLen, text.LowerLetterChars))
		case IntFirst:
			payload = fmt.Sprintf("%s-0-0-0", rawParam.Value)
		case IntSecond:
			payload = fmt.Sprintf("%s-0*%d*%d", rawParam.Value, funk.RandomInt(0, 100), funk.RandomInt(0, 100)) //nolint:mnd //need indeed
		case IntThird:
			payload = fmt.Sprintf("%s-0*%s*%d", rawParam.Value, funk.RandomString(randStrLen, text.LowerLetterChars), funk.RandomInt(0, 100)) //nolint:mnd //need indeed
		}

		success := CheckPayload(c, rawParam.OnlyKey, vHTTP, payload, payloadType, false)
		if !success { // bool注入失败，如果有sql报错语句漏洞的也可以返回一下
			return sqlErrorStrVuln
		}
		if i == 0 && isFirst { // bool注入可能有多次重复校验，对于报错注入多次尝试没有什么意义，就只测试一遍
			// 判断是否有报错注入
			vuln, done, _ := errorinject.CheckVuln(c, rawParam, vHTTP.Follows[0], []string{fmt.Sprintf("%s-%s", rawParam.Value, errorinject.ErrorPayload)})
			if vuln != nil {
				if done {
					return vuln
				} else {
					sqlErrorStrVuln = vuln
				}
			}
		}
	}
	vuln := &npoc.Vulnerability{
		Name:       VulnName,
		Method:     c.Task().Request.Method,
		Param:      rawParam.Key,
		Payload:    vHTTP.Follows[1].Payload,
		URL:        c.Task().Request.URL.String(),
		HTTP:       vHTTP,
		Severity:   npoc.SeverityHigh,
		Extra:      map[string]string{npoc.ExtraKeySubCategory: VulnSubCategoryBoolNum, npoc.ExtraKeyParamType: base.ParamTypeNum},
		Confidence: npoc.ConfidenceHigh,
	}
	return vuln
}

func CheckPayload(c *npoc.HTTPContext, paramMapKey string, vHTTP *npoc.VulnerabilityHTTP, payload string, payloadType bool, paramStrType bool) bool {
	rawResp := c.Task().Response
	rawParam := c.Task().Request.Params()[paramMapKey]
	rawReq := c.Task().Request.Clone()
	rawReq.FollowRedirects = false

	checkReq1, checkResp1, err := c.Task().Client.SendNewRequest(c.Context, rawReq, paramMapKey, payload)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: "sqli", Err: err})
		return false
	}
	newFollow := npoc.HTTPFollow{Request: checkReq1, Response: checkResp1, Payload: payload}

	simParams := similarity.SimiParam{
		Resp1:        rawResp,
		Resp2:        newFollow.Response,
		Remove1:      []byte(rawParam.Value),
		Remove2:      []byte(newFollow.Payload),
		OnlyBody:     true,
		AllowCodeDif: false,
	}
	simi, _ := simParams.Compute()
	if (simi > similarityMax2) != payloadType {
		return false
	}
	for i, httpFollow := range vHTTP.Follows {
		simParams2 := similarity.SimiParam{
			Resp1:        httpFollow.Response,
			Resp2:        newFollow.Response,
			Remove1:      []byte(httpFollow.Payload),
			Remove2:      []byte(newFollow.Payload),
			OnlyBody:     true,
			AllowCodeDif: false,
		}
		simi2, _ := simParams2.Compute()
		var samePayload bool // payload是否是相同属性的(true或者false)
		if paramStrType {
			// string类型的只有第三个请求的payload是true类型
			samePayload = payloadType == (i == IntSecond)
		} else {
			// 数字型第二和第三个请求的payload是true类型
			samePayload = payloadType == (i == IntFirst || i == IntSecond)
		}

		dissimilar := simi2 < similarityMax // 响应不相似
		if samePayload == dissimilar {
			return false
		}
	}
	vHTTP.Follows = append(vHTTP.Follows, newFollow)
	return true
}
