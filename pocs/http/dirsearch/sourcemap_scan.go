package dirsearch

import (
	"bytes"
	"fmt"
	htmllib "html"
	"net/url"
	"regexp"
	"strings"
	"sync"

	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"

	"github.acme.red/intelli-sec/npoc"
)

var (
	JsReg           = regexp.MustCompile(`(?i)<script\b.+?src=?[="']([^"']+?)["'\s>]`)
	SourceMapUrlReg = regexp.MustCompile(`//# sourceMappingURL=(\S+)`)
)

// 扫描sourceMap文件
func (p *PoC) sourceMapScan(c *npoc.HTTPContext) {
	if c.Task().Response == nil || !c.Task().Response.IsHTMLResp() {
		return
	}
	sourceMapScanQPS := semaphore.NewWeighted(int64(20))
	var wg sync.WaitGroup
	// 获取响应中的js相关路径
	links := extractJSLink(c.Task().Response.Body)
	for _, link := range links {
		select {
		case <-c.Context.Done():
			return
		default:
		}
		u, err := relativeLinkParse(link, c.Task().Request.URL)
		if err != nil {
			continue
		}
		// 只扫描js文件
		if !strings.HasSuffix(u.Path, ".js") {
			continue
		}
		if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_%s", ID, u.String()), struct{}{}); ok {
			continue
		}
		newReq, err := c.Task().Request.BuildReqWithNewPath(u.Path, true)
		if err != nil {
			continue
		}
		err = sourceMapScanQPS.Acquire(c.Context, 1)
		if err != nil {
			continue
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer sourceMapScanQPS.Release(1)
			p.checkSourceMap(c, newReq, u)
		}()
	}
	wg.Wait()
}

func (p *PoC) checkSourceMap(c *npoc.HTTPContext, req *httpv.Request, u *url.URL) {
	req.Header.Set("Range", "bytes=-1024")
	req.FollowRedirects = false
	resp, err := c.Task().Client.Do(c.Context, req)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		return
	}
	if resp.Status > 299 {
		return
	}
	sourceMapUrl := req.URL
	sourceMapUrl.Path = sourceMapUrl.Path + ".map"

	// 如果响应中指定了sourceMap地址则用指定的，如果没有指定默认用 xxx.js.map
	if bytes.Contains(resp.Body, []byte("//# sourceMappingURL=")) {
		matches := SourceMapUrlReg.FindSubmatch(resp.Body)
		if len(matches) > 1 {
			u2, err := url.Parse(string(matches[1]))
			if err == nil && len(u2.Path) > 0 {
				if u2.Scheme != "" {
					sourceMapUrl.Scheme = u2.Scheme
				}
				if u2.Host != "" {
					sourceMapUrl.Host = u2.Host
				}
				sourceMapUrl = sourceMapUrl.ResolveReference(u2)
			}
		}
	}
	sourceMapReq := req.Clone()
	sourceMapReq.URL = sourceMapUrl
	sourceMapReq.Header.Set("Range", "bytes=0-128")
	sourceMapReq.FollowRedirects = false
	sourceMapResp, err := c.Task().Client.Do(c.Context, sourceMapReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		return
	}
	if isSourceMapUrl(sourceMapResp) {
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: sourceMapReq, Response: sourceMapResp}}}
		metaData := p.Metadata()
		c.OutputVulnerability(&npoc.Vulnerability{
			Method:      c.Task().Request.Method,
			Category:    metaData.Category,
			Severity:    npoc.SeverityInfo,
			Payload:     sourceMapReq.URL.String(),
			URL:         sourceMapReq.URL.String(),
			PoC:         p.ID(),
			HTTP:        vHTTP,
			Name:        "SourceMap 文件",
			Description: metaData.Description,
			Extra:       map[string]string{npoc.ExtraKeySubCategory: "sourcemap_file", npoc.ExtraKeyDes: "sourceMap文件扫描"},
			Confidence:  npoc.ConfidenceHigh,
		})
	}
}

// 通过发送一个请求获取响应的前面比较少的字符，判断是否是以 `<` 尖括号开头的，如果是则判定为sourceMap文件
func isSourceMapUrl(resp *httpv.Response) bool {
	if resp.Status > 299 {
		return false
	}
	data := bytes.TrimSpace(resp.Body)
	// 0x7B(16进制) => 123(ascii编码) => "{"
	if len(data) > 0 && data[0] == 0x7B {
		return true
	}
	return false
}

func extractJSLink(html []byte) []string {
	data := JsReg.FindAllSubmatchIndex(html, -1)
	links := make([]string, len(data))
	for i, block := range data {
		links[i] = htmllib.UnescapeString(string(html[block[2]:block[3]]))
	}
	return links
}

// 根据当前的URL和一个相对路径link来生成一个新的URL，但如果生成的新的url不在原url的域名下，则返回error
func relativeLinkParse(link string, currentURL *url.URL) (*url.URL, error) {
	uu, err := url.Parse(link)
	if err != nil {
		return nil, err
	}

	if uu.Path == "" {
		uu.Path = "/"
	}

	uu = currentURL.ResolveReference(uu)
	if uu.Hostname() != "" && uu.Hostname() != currentURL.Hostname() {
		return nil, fmt.Errorf("%s is not a relative URL", link)
	}
	return uu, nil
}
