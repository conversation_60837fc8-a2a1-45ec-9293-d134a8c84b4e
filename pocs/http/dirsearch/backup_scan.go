package dirsearch

import (
	"bytes"
	"log/slog"
	"path"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	slogext "github.acme.red/pictor/foundation/slogext"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
)

var BackupFilename = []string{
	".{filename}}.swp",
	"{filename}}.bak",
	"{filename}}~",
	".{filename}}~",
	".{filename}}.un~",
	"{name}.bak",
	"{name}.bak.{ext}}",
}

// 扫描备份的脚本文件
func (p *PoC) backupScan(c *npoc.HTTPContext, resp404s []*httpv.Response) {
	backupScanQPS := semaphore.NewWeighted(int64(20))
	wg := sync.WaitGroup{}
	ext := path.Ext(c.Task().Request.URL.Path)
	// 非动态地址不进行该类型扫描
	if !utils.IsDynamicFileExt(ext) {
		return
	}
	dir, fileName := path.Split(c.Task().Request.URL.Path)
	if fileName == "" {
		return
	}
	name := strings.ReplaceAll(fileName, ext, "")
	ext = strings.TrimLeft(ext, ".")
	for _, pathFile := range BackupFilename {
		select {
		case <-c.Context.Done():
			return
		default:
		}
		err := backupScanQPS.Acquire(c.Context, 1)
		if err != nil {
			continue
		}
		rp := strings.NewReplacer("{filename}}", fileName, "{name}", name, "{ext}", ext)
		newFile := rp.Replace(pathFile)
		newPath := path.Join(dir, newFile)
		scanReq, err := c.Task().Request.BuildReqWithNewPath(newPath, false)
		if err != nil {
			slog.Error("建立新的请求失败", slogext.Error(err))
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer backupScanQPS.Release(1)
			p.backupCheckReq(c, scanReq, resp404s)
		}()
	}
	wg.Wait()
}

func (p *PoC) backupCheckReq(c *npoc.HTTPContext, scanReq *httpv.Request, resp404s []*httpv.Response) {
	scanReq.FollowRedirects = false
	scanResp, err := c.Task().Client.Do(c.Context, scanReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		return
	}
	if scanResp.Status > 299 {
		return
	}
	// 满足动态文件的特殊标签符内容
	if bytes.HasPrefix(scanResp.Body, []byte("<?")) || bytes.HasPrefix(scanResp.Body, []byte("<%")) || bytes.HasPrefix(scanResp.Body, []byte("#!/")) {
		if isLike404Resps(scanResp, resp404s) {
			return
		}
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: scanReq, Response: scanResp}}}
		metaData := p.Metadata()

		c.OutputVulnerability(&npoc.Vulnerability{
			Method:      c.Task().Request.Method,
			Category:    metaData.Category,
			Severity:    metaData.Severity,
			Payload:     scanReq.URL.String(),
			URL:         scanReq.URL.String(),
			PoC:         p.ID(),
			HTTP:        vHTTP,
			Name:        metaData.Name,
			Description: metaData.Description,
			Extra:       map[string]string{npoc.ExtraKeySubCategory: "backup_file", npoc.ExtraKeyDes: "备份文件扫描"},
			Confidence:  npoc.ConfidenceMedium,
		})
	}
}
