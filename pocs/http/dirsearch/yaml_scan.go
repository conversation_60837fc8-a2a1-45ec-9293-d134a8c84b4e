package dirsearch

import (
	"log/slog"
	"path"
	"slices"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"
	"golang.org/x/net/publicsuffix"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc/pkg/expression"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

const (
	yamlQPS  = 20
	yamlDeep = 2
)

// 根据yaml中的规则来扫描相关地址
func (p *PoC) yamlTempScan(c *npoc.HTTPContext, resp404s []*httpv.Response) {
	if !needScanYaml(c.Task().Response) {
		return
	}
	yamlScanQPS := semaphore.NewWeighted(int64(yamlQPS))
	wg := sync.WaitGroup{}
	dir, _ := path.Split(c.Task().Request.URL.Path)
	for _, rule := range TempData.Rules {
		// 将部分类型的地址设置为允许fuzz每一级目录，其他地址不满足扫描校验的就不进行扫描了
		if !needFuzzMaxDeep(rule.Category) { // 这部分不需要fuzz最大目录
			if getPathDeep(c.Task().Request.URL.Path) > yamlDeep { // 大于二级目录则不进行fuzz
				continue
			}
		}
		// root为真的地址只对根目录进行扫描，如果不是根目录则不需要扫描
		if rule.Root && dir != "/" && dir != "" {
			continue
		}
		var rulePaths []string
		if len(rule.Paths) > 0 {
			rulePaths = append(rulePaths, rule.Paths...)
		}
		if rule.Path != "" {
			rulePaths = append(rulePaths, rule.Path)
		}
		for _, pathStr := range rulePaths {
			p.fuzzRulePath(c, resp404s, rule, dir, pathStr, yamlScanQPS, &wg)
		}
	}
	wg.Wait()
}

func (p *PoC) fuzzRulePath(c *npoc.HTTPContext, resp404s []*httpv.Response, rule *Rule, dir string, pathStr string, yamlScanQPS *semaphore.Weighted, wg *sync.WaitGroup) {
	if len(rule.Suffix) == 0 {
		rule.Suffix = append(rule.Suffix, "")
	}
	for _, suffix := range rule.Suffix {
		newPath := path.Join(dir, pathStr+suffix)
		// 需要进行替换的动态地址
		var dynamicNames []string
		if strings.Contains(newPath, "{dynamic}") {
			dynamicNames = makeBackupName(c.Task().Request.URL.Hostname())
		} else {
			dynamicNames = []string{""}
		}
		for _, name := range dynamicNames {
			select {
			case <-c.Context.Done():
				return
			default:
			}
			scanPath := strings.ReplaceAll(newPath, "{dynamic}", name)
			err := yamlScanQPS.Acquire(c.Context, int64(1))
			if err != nil {
				continue
			}
			wg.Add(1)
			go func() {
				defer wg.Done()
				defer yamlScanQPS.Release(1)
				p.yamlCheckPath(c, scanPath, rule, resp404s)
			}()
		}
	}
}

// 针对地址进行发包并校验对应的Expression中的表达式
func (p *PoC) yamlCheckPath(c *npoc.HTTPContext, scanPath string, rule *Rule, resp404s []*httpv.Response) {
	customLib := expression.NewCustomLib()
	scanReq, err := c.Task().Request.BuildReqWithNewPath(scanPath, rule.Partial)
	if err != nil {
		return
	}
	scanReq.FollowRedirects = false
	scanResp, err := c.Task().Client.Do(c.Context, scanReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		return
	}
	if scanResp.Status > 300 && !strings.Contains(rule.Expression, "status") {
		return
	}
	// 使用了分块传输是不能返回一个压缩包格式的文件的
	if rule.Partial && strings.Contains(scanResp.Header.Get("Content-Encoding"), "gzip") {
		return
	}
	if isLike404Resps(scanResp, resp404s) {
		return
	}
	protoResp, err := scanResp.ToExprProtoResponse()
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{
			PoC: p.ID(),
			Err: err,
		})
		return
	}
	var checkSuccess bool
	exprMap := make(map[string]any)
	exprMap["response"] = protoResp
	evalResult, err := customLib.RunEval(rule.Expression, exprMap)
	if err == nil {
		checkSuccess, _ = evalResult.Value().(bool)
	}
	if checkSuccess {
		if secondCheck(c, scanReq, rule, p, scanResp) {
			return
		}
	}
}

const randStrLen = 8

func secondCheck(c *npoc.HTTPContext, scanReq *httpv.Request, rule *Rule, p *PoC, scanResp *httpv.Response) bool {
	// 生成一个随机地址重新获取一下404响应，做二次校验
	randStr := funk.RandomString(randStrLen)
	newPath2 := path.Join(randStr, scanReq.URL.Path)
	checkReq, err := c.Task().Request.BuildReqWithNewPath(newPath2, rule.Partial)
	if err != nil {
		return true
	}
	checkReq.FollowRedirects = false
	checkResp, err := c.Task().Client.Do(c.Context, checkReq)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
		return true
	}
	simiParam2 := similarity.SimiParam{
		Resp1:        scanResp,
		Resp2:        checkResp,
		Remove1:      []byte(scanReq.URL.String()),
		Remove2:      []byte(checkReq.URL.String()),
		AllowCodeDif: false,
	}
	simi2, err := simiParam2.Compute()
	if err != nil {
		slog.InfoContext(c.Context, "相似度计算失败", slogext.Error(err))
	}
	// 当前随机页面响应与扫描地址的页面响应相似则证明随机页面发生了变化，将新的随机页面放入随机页面中
	if simi2 > 80 && err == nil {
		return true
	}
	vulnType := GetCategory(rule.Category)
	var vulnName string
	if vulnType != "" {
		vulnName = strings.Split(vulnType, " > ")[0]
	}
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: scanReq, Response: scanResp}}}
	metaData := p.Metadata()
	severity := metaData.Severity
	// 对不同的文件类型设置不同的漏洞危害程度
	severityLowCategory := []string{
		"directory/default", "directory/help", "debug", "debug/readme", "error", "temp",
		"sensitive/crossdomain", "sensitive/upload", "sensitive/statistic",
		"system/log", "system/config", "system/known_hosts", "system/core",
		"config/htaccess", "config/dependence", "config/sensitive", "config/ide", "config/dependence",
	}
	severityInfoCategory := []string{"sensitive/upload"}
	switch {
	case slices.Contains(severityInfoCategory, rule.Category) || strings.Contains(rule.Category, "admin"):
		severity = npoc.SeverityInfo
	case slices.Contains(severityLowCategory, rule.Category):
		severity = npoc.SeverityLow
	case strings.Contains(rule.Category, "debug/php"):
		severity = npoc.SeverityMedium
	default:
		// 处理其他情况（如果需要）
	}
	c.OutputVulnerability(&npoc.Vulnerability{
		Method:      c.Task().Request.Method,
		Category:    metaData.Category,
		Severity:    severity,
		Payload:     scanReq.URL.String(),
		URL:         scanReq.URL.String(),
		PoC:         p.ID(),
		HTTP:        vHTTP,
		Name:        vulnName,
		Description: metaData.Description,
		Extra:       map[string]string{npoc.ExtraKeyCheckRule: rule.Expression, npoc.ExtraKeySubCategory: rule.Category, npoc.ExtraKeyDes: vulnType},
	})
	return false
}

// 通过响应包判断站点是否需要进行yaml目录扫描
func needScanYaml(resp *httpv.Response) bool {
	if resp != nil {
		// oss 存储桶在没有访问权限的情况下，爆破出来没意义
		if _, ok := resp.Header["X-Oss-Request-Id"]; ok && resp.Status == 403 {
			body, err := resp.GetUTF8Body()
			if err != nil {
				return false
			}
			utf8Body := string(body)
			for _, f1 := range []string{"Anonymous access is forbidden", "bucket acl", "this endpoint"} {
				if strings.Contains(utf8Body, f1) {
					return false
				}
			}
		}
	}
	return true
}

func makeBackupName(hostName string) []string {
	hostName_ := strings.ReplaceAll(hostName, ".", "_")
	parts := []string{hostName, hostName_}
	if text.IsIp(hostName) {
		return parts
	}
	// eg: http://localhost:8080 => add `localhost` to wordlist
	if !strings.Contains(hostName, ".") {
		return parts
	}

	domain, err := publicsuffix.EffectiveTLDPlusOne(hostName)
	if err != nil {
		return parts
	}

	if domain != hostName {
		parts = append(parts, domain)
	}

	domainSuffix, _ := publicsuffix.PublicSuffix(hostName)
	validDomain := strings.TrimRight(strings.TrimSuffix(hostName, domainSuffix), ".")
	validDomain = strings.TrimLeft(strings.TrimPrefix(validDomain, "www"), ".")

	hostParts := strings.Split(validDomain, ".")

	// eg: http://example.com => add `example` to wordlist
	parts = append(parts, hostParts[len(hostParts)-1])

	// eg: http://www.example.com => add `example` to wordlist
	// eg: http://study.example.com => add `example` / `study` to wordlist
	// eg: http://study.sub.example.co.jp => add `example` / `study` to wordlist
	if len(hostParts) >= 2 && hostParts[0] != hostParts[len(hostParts)-1] {
		parts = append(parts, hostParts[0])
	}

	return parts
}

var whiteCategory = []string{"directory", "config", "code", "backup"}

func needFuzzMaxDeep(category string) bool {
	for _, s := range whiteCategory {
		if strings.Contains(strings.ToLower(category), s) {
			return true
		}
	}
	return false
}
