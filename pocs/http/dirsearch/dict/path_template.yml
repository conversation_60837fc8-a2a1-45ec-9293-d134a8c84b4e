version: "2.0"
category:
  - name: backup
    title: 备份文件泄露
    sub:
      - name: php
        title: PHP备份文件泄漏
      - name: data
        title: 备份数据泄漏
      - name: sql
        title: SQL文件泄漏
      - name: code
        title: 源代码泄漏
  - name: temp
    title: 临时文件泄露
  - name: debug
    title: debug页面
    sub:
      - name: readme
        title: 描述文件泄漏
      - name: php
        title: PHPinfo页面泄漏
  - name: directory
    title: 列目录
    sub:
      - name: root
        title: 目录可枚举
      - name: backup
        title: 备份目录可枚举
      - name: files
        title: 文件资源目录可枚举
      - name: logs
        title: 日志目录可枚举
      - name: source
        title: 源码文件目录可枚举
      - name: help
        title: 帮助目录可枚举
  - name: config
    title: 配置文件泄露
    sub:
      - name: htaccess
        title: htaccess文件泄漏
      - name: htpasswd
        title: htpasswd文件泄漏
      - name: web
        title: web站点配置泄漏
      - name: database
        title: 数据库配置文件泄漏
      - name: dependence
        title: 依赖文件泄漏
      - name: exec
        title: 执行脚本文件泄漏
      - name: sensitive
        title: 敏感配置泄漏
      - name: ide
        title: IDE工程配置文件泄漏
      - name: sftp
        title: sftp配置文件泄漏
      - name: pac
        title: 代理配置文件泄漏
  - name: system
    title: 系统文件泄露
    sub:
      - name: core
        title: core文件泄漏
      - name: password
        titile: 密码文件泄漏
      - name: history
        title: 操作历史文件泄漏
      - name: config
        title: 配置文件泄漏
      - name: log
        title: 日志文件泄漏
      - name: known_hosts
        title: known_hosts文件泄漏
      - name: key
        title: 私钥文件泄漏
      - name: pubkey
        title: 公钥文件泄漏
  - name: admin
    title: 登录表单页面
    sub:
      - name: solr
        title: Solr管理页面泄漏
      - name: tomcat
        title: Tomcat管理页面泄漏
      - name: jboss
        title: Jboss管理页面泄漏
      - name: cacti
        title: Cacti管理页面泄漏
      - name: zabbix
        title: Zabbix管理页面泄漏
      - name: jenkins
        title: Jenkins管理页面泄漏
      - name: memadmin
        title: MemAdmin管理页面泄漏
      - name: phpmyadmin
        title: PHPMyAdmin管理页面泄漏
      - name: resin
        title: Resin管理页面泄漏
  - name: error
    title: 报错页面
  - name: sensitive
    title: 敏感信息泄露
    sub:
      - name: crossdomain
        title: 跨域策略文件泄漏
      - name: statistic
        title: 统计页面泄漏
      - name: sqlite
        title: sqlite数据库文件泄漏
      - name: upload
        title: 文件上传页泄露
  - name: code
    title: 源码仓库泄露
    sub:
      - name: svn
        title: SVN源码仓库泄漏
      - name: git
        title: Git源码仓库泄漏
  - name: webshell
    title: 疑似Webshell
rules:
  - paths:
      - /
      - /000~ROOT~000/
    expression: |
      (response.body.bcontains(b'Everything</title>') && response.body.bcontains(b'class="indexof"')) ||
      ('(?i)Index of'.bmatches(response.body) && ('(?i)Parent Directory'.bmatches(response.body) || response.body.bcontains(b'<a href="../">../</a>'))) ||
      '(?i)Directory Listing For'.bmatches(response.body) ||
      (response.body.bcontains(b'Caddy</a>') && response.body.bcontains(b'directory</span>') && response.body.bcontains(b'Modified</a>'))
    category: directory/default
  - paths:
      - /backup/
      - /php/backup/
    expression: |
      (response.body.bcontains(b'Everything</title>') && response.body.bcontains(b'class="indexof"')) ||
      ('(?i)Index of'.bmatches(response.body) && ('(?i)Parent Directory'.bmatches(response.body) || response.body.bcontains(b'<a href="../">../</a>'))) ||
      '(?i)Directory Listing For'.bmatches(response.body) ||
      (response.body.bcontains(b'Caddy</a>') && response.body.bcontains(b'directory</span>') && response.body.bcontains(b'Modified</a>'))
    category: directory/backup
  - paths:
      - /files/
    expression: |
      (response.body.bcontains(b'Everything</title>') && response.body.bcontains(b'class="indexof"')) ||
      ('(?i)Index of'.bmatches(response.body) && ('(?i)Parent Directory'.bmatches(response.body) || response.body.bcontains(b'<a href="../">../</a>'))) ||
      '(?i)Directory Listing For'.bmatches(response.body) ||
      (response.body.bcontains(b'Caddy</a>') && response.body.bcontains(b'directory</span>') && response.body.bcontains(b'Modified</a>'))
    category: directory/files
  - paths:
      - /logs/
    expression: |
      (response.body.bcontains(b'Everything</title>') && response.body.bcontains(b'class="indexof"')) ||
      ('(?i)Index of'.bmatches(response.body) && ('(?i)Parent Directory'.bmatches(response.body) || response.body.bcontains(b'<a href="../">../</a>'))) ||
      '(?i)Directory Listing For'.bmatches(response.body) ||
      (response.body.bcontains(b'Caddy</a>') && response.body.bcontains(b'directory</span>') && response.body.bcontains(b'Modified</a>'))
    category: directory/logs
  - paths:
      - /source/
    expression: |
      (response.body.bcontains(b'Everything</title>') && response.body.bcontains(b'class="indexof"')) ||
      ('(?i)Index of'.bmatches(response.body) && ('(?i)Parent Directory'.bmatches(response.body) || response.body.bcontains(b'<a href="../">../</a>'))) ||
      '(?i)Directory Listing For'.bmatches(response.body) ||
      (response.body.bcontains(b'Caddy</a>') && response.body.bcontains(b'directory</span>') && response.body.bcontains(b'Modified</a>'))
    category: directory/source
  - paths:
      - /help/
    expression: |
      (response.body.bcontains(b'Everything</title>') && response.body.bcontains(b'class="indexof"')) ||
      ('(?i)Index of'.bmatches(response.body) && ('(?i)Parent Directory'.bmatches(response.body) || response.body.bcontains(b'<a href="../">../</a>'))) ||
      '(?i)Directory Listing For'.bmatches(response.body) ||
      (response.body.bcontains(b'Caddy</a>') && response.body.bcontains(b'directory</span>') && response.body.bcontains(b'Modified</a>'))
    category: directory/help
  - path: /core
    expression: |
      r'^\x7f\x45\x4c\x46'.bmatches(response.body)
    category: system/core
    partial: true
  - path: /crossdomain.xml
    expression: |
      response.body.bcontains(b'<allow-access-from domain="*"') && response.content_type.icontains('xml')
    category: sensitive/crossdomain
    root: true
    partial: false
  - path: /statistics.html
    expression: |
      response.body.bcontains(b'statistic') && response.content_type.icontains('html')
    category: sensitive/statistic
    partial: false
  - path: /debug.txt
    expression: |
      response.content_type.icontains('text/plain') && r'(?i)err|fail\b|kill\b\s|\d+:'.bmatches(response.body)
    category: debug
    partial: true
  - path: /debug
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(?i)err|fail\b|kill\b\s|\d+:'.bmatches(response.body)
    category: debug
    partial: false
  - paths:
      - /etc/passwd
      - /../../../../../../../../../../etc/passwd
      - /%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd
    expression: |
      response.body.bcontains(b'root:x:')
    category: system/password
    partial: true
  - paths:
      - /.bash_history
      - /.zsh_history
      - /.sh_history
      - /.ksh_history
    expression: |
      response.content_type.icontains('application/octet-stream') && r'\n(cd|ls|rm|vim|chmod|mkdir|sudo)\b'.bmatches(response.body)
    category: system/history
    partial: true
  - path: /.rediscli_history
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(?i)\n(select \d|keys \*|get )'.bmatches(response.body)
    category: system/history
    partial: true
  - paths:
      - /.bashrc
      - /.zshrc
      - /.bash_profile
      - /.bash_logout
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(PATH|export|source)\b'.bmatches(response.body)
    category: system/config
    partial: false
  - path: /.vimrc
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(filetype|smarttab|autoindent)\b'.bmatches(response.body)
    category: system
    partial: false
  - path: /.DS_Store
    expression: |
      response.body.bstartsWith(b'\x00\x00\x00\x01\x42\x75\x64\x31')
    category: temp
    partial: false
  - paths:
      - /.htaccess
      - /.htaccess.bak
      - /example.htaccess
      - /_.htaccess
      - /sample.htaccess
      - /a.htaccess
      - /htaccess_for_page_not_found_redirects.htaccess
    expression: |
      !response.content_type.icontains('html') && r'<IfModule|RewriteRule\b\s'.bmatches(response.body)
    category: config/htaccess
    partial: false
  - paths:
      - /.htpasswd
      - /.htpasswd.bak
      - /htpasswd.bak
    expression: |
      response.content_type.icontains('application/octet-stream') && r':.+?($|\r|\n)'.bmatches(response.body) && !r'^\s*(\{.*\}|\[.*\])\s*$'.bmatches(response.body)
    category: config/htpasswd
    partial: false
  - path: /nohup.out
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(?i)err|fail\b|kill\b\s|\d+:'.bmatches(response.body)
    category: system/log
    partial: true
  - path: /.mysql_history
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(?i)SELECT|UPDATE|INSERT|DELETE'.bmatches(response.body)
    category: system/history
    partial: false
  - path: /httpd.conf
    expression: |
      !response.content_type.icontains('html') && r'<Directory|<IfModule|ErrorDocument\s+\d+'.bmatches(response.body)
    category: config/web
    partial: false
  - paths:
      - /web.config
    expression: |
      response.content_type.icontains('application/octet-stream') && response.body.bcontains(b'</configuration>')
    category: config/web
    partial: false
    root: true
  - path: /solr/
    expression: |
      response.content_type.icontains('html') && response.body.bcontains(b'<title>Solr Admin</title>')
    category: admin/solr
    partial: false
    root: true
  - path: /examples/
    expression: |
      response.content_type.icontains('html') && r'(?i)<TITLE>Apache Tomcat Examples</TITLE>'.bmatches(response.body)
    category: debug
    partial: false
    root: true
  - paths:
      - /examples/servlets/servlet/SessionExample
      - /examples/servlets/index.html
      - /examples/jsp/index.html
      - /examples/websocket/index.xhtml
      - /..;/examples/servlets/index.html
      - /..;/examples/jsp/index.html
      - /..;/examples/websocket/index.xhtml
      - /..;/examples/servlets/servlet/SessionExample
    expression: |
      response.content_type.icontains('html') && r'(?i)<title>.*? Example</title>'.bmatches(response.body)
    category: debug
    partial: false
    root: true
  - paths:
      - /
      - /admin.html
      - /admin.php
      - /admin.jsp
      - /admin.do
      - /admin.asp
      - /login.html
      - /login.php
      - /login.do
      - /login.jsp
      - /login.asp
      - /login
      - /admin
      - /admin/
      - /signin
      - /user/login
    expression: >-
      (r'''(?i)<input\b.+?type=["']?password["']?'''.bmatches(response.body))
    category: admin
    partial: false
  - paths:
      - /db.inc
      - /config/database.yml
      - /database.yml
    expression: |
      !response.content_type.icontains('html') && r'\s\w+:'.bmatches(response.body) && !r'^\s*(\{.*\}|\[.*\])\s*$'.bmatches(response.body)
    category: config/database
    partial: true
    root: true
  - paths:
      - /composer.json
      - /.composer/composer.json
      - /vendor/composer/installed.json
    expression: |
      !response.content_type.icontains('html') && response.body.bcontains(b'"require": {')
    category: config/dependence
    partial: true
  - path: /composer.lock
    expression: |
      !response.content_type.icontains('html') && response.body.bcontains(b'packages')
    category: config/dependence
    partial: true
  - path: /requirements.txt
    expression: |
      response.content_type.icontains('text/plain') && r'==|>|<'.bmatches(response.body) && !r'<[a-z]+'.bmatches(response.body)
    category: config/dependence
    partial: true
  - path: /db.conf
    expression: |
      !response.content_type.icontains('html') && !response.body.bcontains(b'</') && response.body.bcontains(b'pass') && response.body.bcontains(b'port') && !r'^\s*(\{.*\}|\[.*\])\s*$'.bmatches(response.body)
    category: config/database
    partial: false
  - path: /jmx-console/HtmlAdaptor
    expression: |
      response.body.bcontains(b'JBoss Management Console')
    category: admin/jboss
    partial: false
    root: true
  - path: /cacti/
    expression: |
      response.body.bcontains(b'<title>Login to Cacti</title>')
    category: admin/cacti
    partial: false
    root: true
  - path: /zabbix/
    expression: |
      response.body.bcontains(b'<meta name="Author" content="Zabbix SIA"')
    category: admin/zabbix
    partial: false
    root: true
  - path: /jenkins/script
    expression: |
      response.body.bcontains(b'Type in an arbitrary')
    category: admin/jenkins
    partial: false
    root: true
  - path: /memadmin/index.php
    expression: |
      response.body.bcontains(b'<title>Login - MemAdmin')
    category: admin/memadmin
    partial: false
    root: true
  - paths:
      - /phpmyadmin/index.php
      - /_phpmyadmin/index.php
      - /phpMyAdmin/index.php
      - /pma/index.php
    expression: |
      response.body.bcontains(b'<title>phpMyAdmin') && response.body.bcontains(b'pma')
    category: admin/phpmyadmin
    partial: false
    root: true
  - path: /resin-doc/resource/tutorial/jndi-appconfig/test?inputFile=/etc/profile
    expression: |
      r'/etc/profile\.d/\*\.sh'.bmatches(response.body)
    category: debug
    partial: false
    root: true
  - path: /resin-doc/viewfile/?contextpath=/&servletpath=&file=index.jsp
    expression: |
      response.body.bcontains(b'This is the default start page for the Resin server')
    category: debug
    partial: false
    root: true
  - path: /resin-admin/
    expression: |
      response.body.bcontains(b'<title>Resin Admin Login for')
    category: admin/resin
    partial: false
    root: true
  - path: /.svn/entries
    expression: |
      !response.body.bcontains(b'<html') && !response.body.bcontains(b'</body>') && !response.body.bcontains(b'</script>') && !response.content_type.contains("application/json") && !response.body.bstartsWith(b"{") && (r'^\d+\n$'.bmatches(response.body) || (r'\d+\n(http|file|svn|https)://'.bmatches(response.body) && r'^\d+\n'.bmatches(response.body) ) || (response.body.bcontains(b'<wc-entries') && response.body.bcontains(b'xmlns=')))
    category: code/svn
    partial: false
  - path: /.git/index
    expression: |
      response.body.bstartsWith(b'DIRC')
    category: code/git
    partial: false
  - path: /.git/config
    expression: |
      response.body.bcontains(b'[core]')
    category: code/git
    partial: false
  - path: /.git/HEAD
    expression: |
      response.body.bcontains(b'refs/heads/')
    category: code/git
    partial: false
  - paths:
      - /.ssh/known_hosts
      - /.ssh/known_hosts.old
    expression: |
      response.content_type.icontains('application/octet-stream') && r'\s(ssh-rsa|ecdsa|ssh-dss|ssh-ed25519)'.bmatches(response.body)
    category: system
    partial: true
  - paths:
      - /.ssh/id_rsa
      - /.ssh/id_dsa
      - /id_dsa
      - /id_rsa
    expression: |
      r'(?i)PRIVATE KEY-'.bmatches(response.body)
    category: system/key
    partial: true
  - paths:
      - /.ssh/id_rsa.pub
      - /.ssh/id_dsa.pub
      - /id_rsa.pub
      - /.ssh/authorized_keys
    expression: |
      r'(\s|^)(ssh-rsa|ecdsa|ssh-dss)'.bmatches(response.body)
    category: system/pubkey
    partial: true
  - paths:
      - /readme
      - /README
      - /LICENSE
      - /CHANGELOG
      - /changelog
      - /CONTRIBUTING
      - /install
      - /install.php
      - /INSTALL
      - /MAINTAINERS
      - /UPGRADE
    suffix:
      - .md
      - .txt
      - ""
    expression: >-
      !response.content_type.icontains('html')
      && (response.content_type.icontains('text/') || response.content_type.icontains('application/octet-stream') || response.content_type == "")
      && r'(?i)Introduction|README|文档|开源|(\n|^)#|releases|upgrade|change|Contribut|install|copyright'.bmatches(response.body)
    category: debug/readme
    partial: true
  - path: /readme.html
    expression: |
      response.content_type.icontains('html') && r'(?i)<title>[^<]*readme[^<]*</title>'.bmatches(response.body)
    category: debug/readme
    partial: false
  - paths:
      - /vendor/composer/LICENSE
    expression: |
      !response.body.bcontains(b'</') && !response.content_type.icontains('html') && response.body.bcontains(b'Copyright (c)')
    category: debug
    partial: false
  - path: /data.txt
    expression: |
      response.content_type.icontains('text/plain') && response.body.bcontains(b'pass|email|mobile|user')
    category: backup/data
    partial: true
  - paths:
      - /install.sh
      - /deploy.sh
      - /upload.sh
      - /setup.sh
      - /backup.sh
      - /rsync.sh
      - /sync.sh
      - /test.sh
      - /.build.sh
      - /.jenkins.sh
      - /.travis.sh
      - /config.sh
      - /build.sh
      - /run.sh
      - /compile.sh
      - /env.sh
      - /init.sh
      - /startup.sh
      - /wp-setup.sh
      - /aws.sh
      - /reminder.sh
      - /mysqlbackup.sh
      - /dev2local.sh
      - /local2dev.sh
      - /local2prod.sh
      - /prod2local.sh
    expression: |
      !response.body.bcontains(b'</') && !response.content_type.icontains('html') && (response.body.bstartsWith(b'#') || response.body.bstartsWith(b'\xEF\xBB\xBF#'))
    category: config/exec
    partial: true
  - paths:
      - /config
      - /config/config
    suffix:
      - .php
      - .inc
    expression: |
      response.body.ibcontains(b'<?php')
    category: config/web
    partial: false
  - paths:
      - /settings.ini
      - /application.ini
      - /conf.ini
      - /app.ini
      - /configs/application.ini
      - /configuration.ini
      - /config/config.ini
      - /config.ini
      - /conf/config.ini
      - /application/configs/application.ini
      - /php.ini
      - /.user.ini
      - /user.ini
      - /db.ini
    expression: |
      response.content_type.icontains('text/plain') && r'\[[\w\-.]+\]'.bmatches(response.body) && !response.body.bcontains(b'</') && !response.content_type.icontains('html')
    category: config/web
    partial: false
  - path: /config.json
    expression: |
      response.content_type.icontains('json') && r'(?i)"(password|secret_key|token)"\s*:\s*"[^"]+"'.bmatches(response.body)
    category: config/sensitive
    partial: false
    root: true
  - path: /.idea/workspace.xml
    expression: |
      response.content_type.icontains('xml') && response.body.bcontains(b'<project version=')
    category: config/ide
    partial: false
  - path: /.idea/modules.xml
    expression: |
      response.content_type.icontains('xml') && response.body.bcontains(b'ProjectModuleManager')
    category: config/ide
    partial: false
  - path: /a.out
    expression: |
      response.body.bstartsWith(b'\x7F\x45\x4C\x46')
    category: temp
    partial: true
  - paths:
      - /.env
      - /app.cfg
    expression: >-
      response.content_type.icontains('application/octet-stream') &&
      (r'''[\n#]\w+\s*=\s*["']?\w+'''.bmatches(response.body) || r'\n\w+:\s*\w+'.bmatches(response.body))
    category: config/web
    partial: false
  - paths:
      - /key
      - /keys
      - /secret_key
      - /secret
      - /.secret
      - /.key
      - /.secret_key
    expression: |
      response.content_type.icontains('application/octet-stream') && ((size(response.body) >= 6 && size(response.body) <= 40) || response.body.bcontains(b'PRIVATE KEY'))
    category: config/sensitive
    partial: false
  - path: /key.txt
    expression: |
      response.content_type.icontains('text/plain') && ((size(response.body) >= 6 && size(response.body) <= 40) || response.body.bcontains(b'PRIVATE KEY'))
    category: config/sensitive
    partial: false
  - paths:
      - /temp.txt
      - /tmp.txt
    expression: |
      response.content_type.icontains('text/plain') && r'(?i)password|username|email|mobile|phone'.bmatches(response.body)
    category: temp
    partial: true
  - path: /sftp-config.json
    expression: |
      response.content_type.icontains('json') && r'(?i)"ftp_passive_mode":\s+(true|false)'.bmatches(response.body)
    category: config/sftp
    partial: true
  - paths:
      - /index.php~
      - /config.php~
      - /index.php.bak
      - /config.php.bak
      - /db.php.bak
      - /config.inc.php.bak
      - /.index.php.swp
      - /.config.inc.php.swp
      - /config/.config.php.swp
      - /.config.php.swp
      - /.settings.php.swp
      - /.database.php.swp
      - /.db.php.swp
      - /.mysql.php.swp
    expression: |
      !response.content_type.icontains('html') && response.body.ibcontains(b'<?php')
    category: backup
    partial: true
  - path: /index.cgi.bak
    expression: >-
      response.content_type.icontains('application/octet-stream')
      && (response.body.bstartsWith(b'#!') || response.body.bstartsWith(b'\xEF\xBB\xBF#!'))
    category: backup
    partial: true
  - paths:
      - /upload
      - /upfile
    suffix:
      - .php
      - .jsp
      - .asp
      - .aspx
      - .html
      - .do
    expression: |
      response.content_type.icontains('html') && r'''(?i)<input\b.+?(?i)type=["']?file['"]?'''.bmatches(response.body)
    category: sensitive/upload
    level: warning
    partial: false
  # 测试 phpinfo 页面
  - paths:
      - /phpinfo.php
      - /info.php
      - /tz.php
      - /1.php
      - /p.php
      - /debug.php
      - /php.php
      - /infophp.php
      - /php_info.php
      - /test.php
      - /i.php
      - /asdf.php
      - /pinfo.php
      - /phpversion.php
      - /time.php
      - /index.php
      - /temp.php
      - /old_phpinfo.php
      - /infos.php
      - /linusadmin-phpinfo.php
      - /php-info.php
      - /dashboard/phpinfo.php
      - /_profiler/phpinfo.php
      - /_profiler/phpinfo
      - /?phpinfo=1
    expression: |
      response.body.bcontains(b'<title>phpinfo()</title>') ||
        (response.body.bcontains(b'allow_url_fopen') && response.body.bcontains(b'upload_max_filesize')) || (response.body.bcontains(b'PHP Extension') && response.body.bcontains(b'PHP Version'))
    category: debug/php
    partial: false
  # 备份文件扫描
  - paths:
      - /db.sqlite
      - /db.sqlite3
    expression: |
      response.content_type.icontains('application/') &&
        (response.body.bcontains(b'** This file contains an SQLite') || response.body.bstartsWith(b'SQLite format'))
    category: sensitive/sqlite
    partial: true
  - paths:
      - /data.sql
      - /database.sql
      - /db.sql
      - /test.sql
      - /admin.sql
      - /backup.sql
      - /dump.sql
      - /create.sql
      - /1.sql
      - /db_backup.sql
      - /dbdump.sql
      - /localhost.sql
      - /mysqldump.sql
      - /mysql.sql
      - /site.sql
      - /sql.sql
      - /temp.sql
      - /translate.sql
      - /users.sql
      - /wp-content/uploads/dump.sql
      - /wp-content/mysql.sql
    expression: |
      r'(?i)(?:DROP|CREATE|(?:UN)?LOCK) TABLE|INSERT INTO'.bmatches(response.body) && !response.body.bcontains(b'html>')
    category: backup/sql
    partial: true
  - path: /index.bak
    expression: >-
      response.content_type.icontains('application/octet-stream') &&
      (response.body.ibcontains(b'<?php') || response.body.bcontains(b'<%') || response.body.bcontains(b'require('))
    category: backup
    partial: true
  - path: /proxy.pac
    expression: |
      response.content_type.icontains('application/octet-stream') && r'(?i)socks|FindProxyForURL'.bmatches(response.body)
    category: config/pac
    partial: true
  - path: /server.cfg
    expression: >-
      response.content_type.icontains('application/octet-stream')
      && (response.body.bcontains(b'\n//') || response.body.bcontains(b'\n#'))
    category: config/web
    partial: true
    reptile: false
  - paths:
      - /code.tar.gz
      - /src.tar.gz
      - /htdocs.tar.gz
      - /webserver.tar.gz
      - /tools.tar.gz
      - /webroot.zip
      - /site.tar.gz
      - /install.tar.gz
      - /build.tar.gz
      - /deploy.tar.gz
      # \x1F\x8B for .gz, BZ(h|0) for .bz/.bz2, PK\x03\x04 for .zip
    expression: |
      response.body.bstartsWith(b'\x1F\x8B') || r'^BZ(h|0)'.bmatches(response.body) || response.body.bstartsWith(b'PK\x03\x04')
    category: backup/code
    partial: true
  - paths:
      - /config.tar.gz
      - /conf.tar.gz
      - /conf/conf.zip
      - /o.tar.gz
      - /x.tar.gz
      - /output.tar.gz
      - /backup.sql.gz
      - /database.sql.gz
      - /dump.sql.gz
      - /db.sql.gz
      - /back.tar.bz2
      - /temp.tar.gz
    # \x1F\x8B for .gz, BZ(h|0) for .bz/.bz2, PK\x03\x04 for .zip
    expression: |
      response.body.bstartsWith(b'\x1F\x8B') || r'^BZ(h|0)'.bmatches(response.body) || response.body.bstartsWith(b'PK\x03\x04')
    category: backup
    partial: true
  - paths:
      - /a
      - /1
      - /old
      - /index
      - /sql
      - /package
      - /website
      - /upload
      - /admin
      - /wwwroot
      - /www
      - /web
      - /ftp
      - /database
      - /data
      - /db
      - /backup
      - /test
      - /tmp
      - /temp
      - /{dynamic}
    suffix:
      - .zip
      - .tar.gz
      - .rar
      - .7z
      - .gz
      - .tgz
      - .tar.bz2
    # \x1F\x8B for .gz, BZ(h|0) for .bz/.bz2, PK\x03\x04 for .zip, Rar!\x1a for RAR, 7z\xBC\xAF\x27\x1C for .7z
    expression: |
      response.body.bstartsWith(b'\x1F\x8B') || r'^BZ(h|0)'.bmatches(response.body) || response.body.bstartsWith(b'PK\x03\x04') ||
      response.body.bstartsWith(b'Rar!\x1A') || response.body.bstartsWith(b'7z\xBC\xAF\x27\x1C')
    category: backup
    partial: true
    reptile: false
  - paths:
      - /{dynamic}.sql
      - /{dynamic}_db.sql
    expression: |
      r'(?i)CREATE TABLE'.bmatches(response.body)
    category: backup/sql
    partial: true
  # 测试大马（webshell）
  - paths:
      - /shell
      - /webshell
      - /1
      - /s
      - /x
      - /ooxx
      - /dama
      - /test
    suffix:
      - .php
      - .jsp
      - .jspx
      - .asp
      - .aspx
    expression: |
      response.content_type.icontains('html') && size(response.body) <= 250 && r'''(?i)<input\b.+?type=["']?password["']?'''.bmatches(response.body)
    category: webshell
    partial: true
  - paths:
      - /phpspy.php
      - /jspspy.jsp
      - /jspspy.jspx
      - /aspxspy.aspx
    expression: |
      response.content_type.icontains('html') && size(response.body) <= 250 && r'''(?i)<input\b.+?type=["']?password["']?'''.bmatches(response.body)
    category: webshell
    partial: true
  - paths:
      - /WEB-INF/web.xml
      - .//WEB-INF/web.xml
    expression: |
      response.body.bcontains(b'</web-app')
    category: config
    partial: true
  - path: .//WEB-INF/weblogic.xml
    expression: |
      response.body.bcontains(b'</weblogic-web-app') && (response.content_type.icontains('text/xml') || response.content_type.icontains('application/xml'))
    category: config
    partial: true