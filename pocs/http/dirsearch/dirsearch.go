package dirsearch

import (
	"fmt"
	"log/slog"
	"net/http"
	"path"
	"strings"
	"unicode"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
)

const (
	ID       = string(npoc.DirScanType)
	scanDeep = 3 // 允许扫描的目录深度，默认扫描三级目录。
)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "敏感文件泄露",
		PocType:        npoc.GenericPocType,
		Category:       npoc.DirScanType,
		Tags:           nil,
		Description:    "识别到备份文件、临时文件、debug 页面、配置文件等敏感路径和文件,可能造成网站敏感信息泄露。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityHigh,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	req := c.Task().Request
	dir, _ := path.Split(req.URL.Path)
	// 判断之前是否有对该目录进行扫描
	if _, ok := c.Task().TaskCache.LoadOrStore(fmt.Sprintf("%s_%s", ID, dir), struct{}{}); ok {
		return nil
	}
	if getPathDeep(c.Task().Request.URL.Path) > scanDeep { // 当前url目录大于3 http://www.example.com/admin/test/index.html
		return nil
	}
	resp404s, err := get404Resps(c)
	if err != nil || len(resp404s) == 0 {
		newErr := fmt.Errorf("dirsearch get resp404s fail, resp404s_len:%d, err: %w", len(resp404s), err)
		c.OutputPoCError(&npoc.PoCError{PoC: ID, Err: newErr})
		return nil
	}
	if !scanCheck(c, resp404s) {
		return nil
	}
	p.yamlTempScan(c, resp404s)
	p.backupScan(c, resp404s)
	p.sourceMapScan(c)
	return nil
}

// 生成随机地址获取当前目录的404页面响应
func get404Resps(c *npoc.HTTPContext) ([]*httpv.Response, error) { //nolint:gocognit // 个人感觉复杂度不高
	req := c.Task().Request
	var resps []*httpv.Response
	dir, _ := path.Split(req.URL.Path)
	// 随机文件 http://127.0.0.1/dsglfhdkfgd
	pathRand := funk.RandomString(8, utils.LowerChars)
	newPath := dir + pathRand
	newReq, err := req.BuildReqWithNewPath(newPath, true)
	if err != nil {
		return nil, err
	}
	resp1, err := c.Task().Client.Do(c.Context, newReq)
	if err != nil {
		return nil, err
	}
	resps = append(resps, resp1)
	// 切割响应包成功，获取一下没有切割的原始响应
	if resp1.Status == 206 {
		newReq2, err := req.BuildReqWithNewPath(newPath, false)
		if err != nil {
			return nil, err
		}
		resp2, err := c.Task().Client.Do(c.Context, newReq2)
		if err != nil {
			return nil, err
		}
		simiParam1 := similarity.SimiParam{
			Resp1:        resp1,
			Resp2:        resp2,
			Remove1:      []byte(newReq.URL.String()),
			Remove2:      []byte(newReq2.URL.String()),
			AllowCodeDif: false,
		}
		// 只保留不相似的404页面
		simi1, err := simiParam1.Compute()
		if simi1 < 90 || err != nil {
			resps = append(resps, resp2)
		}
	}

	// 随机目录   http://127.0.0.1/dsglfhdkfgd/
	dirRand := funk.RandomString(8, utils.LowerChars) + "/"
	newDir := dir + dirRand
	newReq3, err := req.BuildReqWithNewPath(newDir, true)
	if err != nil {
		return nil, err
	}
	resp3, err := c.Task().Client.Do(c.Context, newReq3)
	if err != nil {
		return nil, err
	}
	simiParam2 := similarity.SimiParam{
		Resp1:        resp1,
		Resp2:        resp3,
		Remove1:      []byte(newReq.URL.String()),
		Remove2:      []byte(newReq3.URL.String()),
		AllowCodeDif: false,
	}
	simi2, err := simiParam2.Compute()
	if simi2 < 90 || err != nil {
		resps = append(resps, resp3)
	}
	if resp3.Status == 206 {
		newReq4, err := req.BuildReqWithNewPath(newDir, false)
		if err != nil {
			return nil, err
		}
		resp4, err := c.Task().Client.Do(c.Context, newReq4)
		if err != nil {
			return nil, err
		}
		simiParam3 := similarity.SimiParam{
			Resp1:        resp3,
			Resp2:        resp4,
			Remove1:      []byte(newReq3.URL.String()),
			Remove2:      []byte(newReq4.URL.String()),
			AllowCodeDif: false,
		}
		simi3, err := simiParam3.Compute()
		if simi3 < 90 || err != nil {
			resps = append(resps, resp4)
		}
	}

	return resps, nil
}

// 判断该目标是否该进行扫描。
func scanCheck(c *npoc.HTTPContext, resp404s1 []*httpv.Response) bool {
	homeURL := fmt.Sprintf("%s://%s", c.Task().Request.URL.Scheme, c.Task().Request.URL.Host)
	dir, _ := path.Split(c.Task().Request.URL.Path)
	rawDirURL := path.Join(homeURL, dir)
	var err error
	resp404s2, err := get404Resps(c)
	if err != nil || len(resp404s2) < 1 {
		return false
	}
	simiParam := similarity.SimiParam{
		Resp1:        resp404s1[0],
		Resp2:        resp404s2[0],
		Remove1:      []byte(rawDirURL),
		Remove2:      []byte(rawDirURL),
		AllowCodeDif: false,
	}
	simi, err := simiParam.Compute()
	if err != nil {
		slog.InfoContext(c.Context, "相似度计算失败", slogext.Error(err))
		return false
	}
	// 404页面不稳定则不进行扫描
	if simi < 90 {
		return false
	}
	if resp404s1[0].Header.Get("Server") == "******" && resp404s2[0].Header.Get("Server") == "******" {
		if isBadCookie(resp404s1[0].Cookies()) && isBadCookie(resp404s2[0].Cookies()) {
			return false
		}
	}
	return true
}

func getPathDeep(path string) int {
	pathStr := strings.Trim(path, "/")
	pathPart := strings.Split(pathStr, "/")
	return len(pathPart)
}

func isBadCookie(cookies []*http.Cookie) bool {
	for _, cookie := range cookies {
		if cookie.Path != "/" {
			continue
		}
		if len(cookie.Name) < 8 || len(cookie.Value) < 64 {
			continue
		}
		var hasDigit, hasLetter, hasOther bool
		for _, c := range cookie.Name {
			if unicode.IsDigit(c) {
				hasDigit = true
			} else if unicode.IsLetter(c) {
				hasLetter = true
			} else {
				hasOther = true
			}
		}
		if hasOther || !hasDigit || !hasLetter {
			continue
		}
		return true
	}
	return false
}

func isLike404Resps(scanResp *httpv.Response, resp404s []*httpv.Response) bool {
	// 与404页面相似度高于80则判定为不是正常页面
	for _, resp404 := range resp404s {
		simiParam := similarity.SimiParam{
			Resp1:        resp404,
			Resp2:        scanResp,
			Remove1:      nil,
			Remove2:      nil,
			AllowCodeDif: false,
		}
		simi, err := simiParam.Compute()
		if err == nil && simi > 80 {
			return true
		}
	}
	return false
}
