package dirsearch

import (
	"embed"
	"fmt"
	"log/slog"
	"strings"

	"github.acme.red/pictor/foundation/slogext"
	"gopkg.in/yaml.v2"
)

//go:embed dict
var dict embed.FS

type Template struct {
	Version  string      `yaml:"version"`
	Category []*Category `yaml:"category"`
	Rules    []*Rule     `yaml:"rules"`
}

type Category struct {
	Name  string        `yaml:"name"`
	Title string        `yaml:"title"`
	Sub   []SubCategory `yaml:"sub"`
}

type SubCategory struct {
	Name  string `yaml:"name"`
	Title string `yaml:"title"`
}

type Rule struct {
	Category         string   `yaml:"category"`
	Path             string   `yaml:"path"`
	Paths            []string `yaml:"paths"`
	DictionaryPath   string
	UserDictParallel int
	Suffix           []string `yaml:"suffix"`
	Expression       string   `yaml:"expression"`
	Partial          bool     `yaml:"partial"`
	Root             bool     `yaml:"root"`
}

var TempData Template

func init() {
	content, err := dict.ReadFile("dict/path_template.yml")
	if err != nil {
		slog.Error("模板文件读取失败", slogext.Error(err))
	}
	err = yaml.Unmarshal(content, &TempData)
	if err != nil {
		slog.Error("模板文件 yaml.Unmarshal 失败", slogext.Error(err))
	}
}

func GetCategory(category string) string {
	var bigType, subType, bigTitle, subTitle string
	catPart := strings.Split(category, "/")
	if len(catPart) > 0 {
		bigType = catPart[0]
		for _, cg := range TempData.Category {
			if cg.Name == bigType {
				bigTitle = cg.Title
				if len(catPart) > 1 {
					subType = catPart[1]
					for _, subCg := range cg.Sub {
						if subType == subCg.Name {
							subTitle = subCg.Title
						}
					}
				}

				break
			}
		}
	}
	if subTitle != "" {
		return fmt.Sprintf("%s > %s", bigTitle, subTitle)
	} else {
		return bigTitle
	}
}
