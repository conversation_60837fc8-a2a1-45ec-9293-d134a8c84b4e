package csrf

import (
	"strings"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/guess"
)

const ID = string(npoc.CSRFType)

type PoC struct{}

var cgiBlankList = []string{
	"login",
	"search",
	"get",
	"find",
}

var cgiWhiteList = []string{
	"save",
	"creat",
	"edit",
	"change",
	"reset",
	"set",
	"add",
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "CSRF 漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.CSRFType,
		Tags:           nil,
		Description:    "跨站请求伪造（Cross-Site Request Forgery，CSRF）是一种网络攻击，攻击者通过欺骗用户，使用户在不知情的情况下执行不愿意执行的操作。具体来说，CSRF攻击利用了用户已经在目标网站上认证的状态，迫使用户在该网站上执行某些操作，比如更改账号信息、提交表单或进行交易。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityLow,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	if c.Task().Response == nil || !guess.IsHTMLResponse(c.Task().Response) {
		return nil
	}
	for _, blank := range cgiBlankList {
		if strings.Contains(c.Task().Request.URL.String(), blank) {
			return nil
		}
	}
	for i, white := range cgiWhiteList {
		if strings.Contains(c.Task().Request.URL.String(), white) {
			break
		}
		if i == len(cgiWhiteList)-1 {
			return nil
		}
	}

	if p.DefaultExecute(c) {
		return nil
	}
	if c.Task().Request.Method == httpv.MethodGet {
		p.AutoExecute(c)
	}
	return nil
}
