package csrf

import (
	"bytes"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"golang.org/x/net/html"

	"github.acme.red/intelli-sec/npoc"
)

var commonTokenRegexp = regexp.MustCompile(`(?i)csrf|token|__EVENTTARGET|__EVENTARGUMENT|__VIEWSTATE|__EVENTVALIDATION|MAX_FILE_SIZE|tk`)

func (p *PoC) DefaultExecute(c *npoc.HTTPContext) bool {
	body, err := c.Task().Response.GetUTF8Body()
	if err != nil {
		body = c.Task().Response.Body
	}
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return false
	}
	for _, n := range doc.Find("meta").Nodes {
		for _, attr := range n.Attr {
			if strings.ToLower(attr.Key) == "name" && strings.Contains(strings.ToLower(attr.Val), "csrf") {
				return false
			}
			if strings.ToLower(attr.Key) == "content" && strings.EqualFold(attr.Val, "authenticity_token") {
				return false
			}
		}
	}
	forms := doc.Find("form")
	if forms.Length() == 0 {
		return false
	}
	for _, n := range forms.Find("input").Nodes {
		if isCSRFTokenInput(n) {
			return false
		}
		if isSearchInput(n) {
			return false
		}
	}
	metadata := p.Metadata()
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: c.Task().Request, Response: c.Task().Response}}}
	vuln := &npoc.Vulnerability{
		PoC:         p.ID(),
		Name:        metadata.Name,
		Category:    metadata.Category,
		Severity:    metadata.Severity,
		Method:      c.Task().Request.Method,
		Param:       "",
		Payload:     "",
		Description: metadata.Description,
		URL:         c.Task().Request.URL.String(),
		HTTP:        vHTTP,
		Extra:       map[string]string{npoc.ExtraKeyDes: "在响应页面的meta和form表单中没有找到与csrf防护有关的token参数，改页面可能存在csrf漏洞"},
		OOBUrl:      nil,
		Confidence:  npoc.ConfidenceLow,
	}
	c.OutputVulnerability(vuln)
	return true
}

func isSearchInput(n *html.Node) bool {
	for _, attr := range n.Attr {
		if strings.Contains(strings.ToLower(attr.Val), "search") {
			return true
		}
	}
	return false
}

func isCSRFTokenInput(n *html.Node) bool {
	var name, inputType, value string
	for _, attr := range n.Attr {
		if strings.EqualFold(attr.Key, "name") {
			name = strings.ToLower(attr.Val)
		} else if strings.EqualFold(attr.Key, "type") {
			inputType = strings.ToLower(attr.Val)
		} else if strings.EqualFold(attr.Key, "value") {
			value = strings.ToLower(attr.Val)
		}
	}
	if inputType != "hidden" {
		return false
	}
	if value == "" {
		return false
	}
	if commonTokenRegexp.Match([]byte(name)) {
		return true
	}
	return false
}
