package csrf

import (
	"bytes"
	"regexp"

	"github.com/PuerkitoBio/goquery"

	"github.acme.red/intelli-sec/npoc"
)

var passwordInputRegexp = regexp.MustCompilePOSIX(`type=.?password`)

func (p *PoC) AutoExecute(c *npoc.HTTPContext) {
	if !passwordInputRegexp.Match(c.Task().Response.Body) {
		return
	}
	utf8Body, err := c.Task().Response.GetUTF8Body()
	if err != nil {
		utf8Body = c.Task().Response.Body
	}
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(utf8Body))
	if err != nil {
		return
	}
	for _, node := range doc.Find("input[type=password]").Nodes {
		var got bool
		for _, attr := range node.Attr {
			select {
			case <-c.Context.Done():
				return
			default:
			}
			if attr.Key == "autocomplete" {
				got = true
				if attr.Val != "off" {
					metadata := p.Metadata()
					vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: c.Task().Request, Response: c.Task().Response}}}
					vuln := &npoc.Vulnerability{
						PoC:         p.ID(),
						Name:        metadata.Name,
						Category:    metadata.Category,
						Severity:    metadata.Severity,
						Method:      c.Task().Request.Method,
						Param:       "",
						Payload:     "",
						Description: metadata.Description,
						URL:         c.Task().Request.URL.String(),
						HTTP:        vHTTP,
						Extra:       map[string]string{npoc.ExtraKeyDes: "密码的input输入框的的autocomplete没有设置为off状态，可能造成csrf漏洞"},
						OOBUrl:      nil,
						Confidence:  npoc.ConfidenceHigh,
					}
					c.OutputVulnerability(vuln)
					return
				}
			}
		}
		if !got {
			metadata := p.Metadata()
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: c.Task().Request, Response: c.Task().Response}}}
			vuln := &npoc.Vulnerability{
				PoC:         p.ID(),
				Name:        metadata.Name,
				Category:    metadata.Category,
				Severity:    metadata.Severity,
				Method:      c.Task().Request.Method,
				Param:       "",
				Payload:     "",
				Description: metadata.Description,
				URL:         c.Task().Request.URL.String(),
				HTTP:        vHTTP,
				Extra:       map[string]string{npoc.ExtraKeyDes: "密码的input输入框的没有autocomplete属性，可能造成csrf漏洞"},
				OOBUrl:      nil,
				Confidence:  npoc.ConfidenceLow,
			}
			c.OutputVulnerability(vuln)
			return
		}
	}
}
