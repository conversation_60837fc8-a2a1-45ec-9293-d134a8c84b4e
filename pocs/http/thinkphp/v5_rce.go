package thinkphp

import (
	"bytes"
	"fmt"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils"
)

func (p *PoC) CheckV5Rce(c *npoc.HTTPContext) {
	payloads := append(v5MethodPayloads, v5FuncPayloads...)
	for _, payload := range payloads {
		select {
		case <-c.Context.Done():
			return
		default:
		}
		randStr := funk.RandomString(8, utils.LowerChars)
		payload.query = strings.ReplaceAll(payload.query, "{{RandStr1}}", randStr)
		payload.body = bytes.ReplaceAll(payload.body, []byte("{{RandStr1}}"), []byte(randStr))
		newReq := c.Task().Request.Clone()
		newReq.URL.RawQuery = payload.query
		if len(payload.body) != 0 {
			newReq.Method = httpv.MethodPost
			newReq.Body = payload.body
		} else {
			newReq.Method = httpv.MethodGet
		}
		newReq.FollowRedirects = false
		resp, err := c.Task().Client.Do(c.Context, newReq)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			continue
		}
		if strings.Contains(resp.Header.Get("TESTECHO"), randStr) {
			metaData := p.Metadata()
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: newReq, Response: resp}}}
			vuln := &npoc.Vulnerability{
				Method:      c.Task().Request.Method,
				Category:    metaData.Category,
				Severity:    metaData.Severity,
				Param:       "",
				URL:         c.Task().Request.URL.String(),
				PoC:         p.ID(),
				HTTP:        vHTTP,
				Name:        metaData.Name,
				Description: metaData.Description,
				Extra:       map[string]string{npoc.ExtraKeyCheckStr: fmt.Sprintf("TESTECHO: %s", randStr)},
				Confidence:  npoc.ConfidenceHigh,
			}
			if len(payload.body) == 0 {
				vuln.Payload = payload.query
			} else {
				vuln.Payload = string(payload.body)
			}
			c.OutputVulnerability(vuln)
			return
		}
	}
}
