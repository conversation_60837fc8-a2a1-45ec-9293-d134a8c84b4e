package thinkphp

import (
	"net/url"
	"slices"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
)

var builtInVarKeys = []string{"m", "c", "a", "s", "t", "callback", "jsonp", "_method", "_ajax", "_pjax"}

func (p *PoC) CheckSqli(c *npoc.HTTPContext) {
	for _, param := range c.Task().Request.Params() {
		if strings.Contains(param.Key, "[") || slices.Contains(builtInVarKeys, param.Key) || param.ParamType != httpv.ParamTypeQuery {
			continue
		}
		for i, values := range [][]string{{"exp", "1"}, {"2", "3"}} {
			select {
			case <-c.Context.Done():
				return
			default:
			}
			newReq, params := buildFazzParams(c.Task().Request, param, values[0], values[1])
			if newReq == nil || len(params) == 0 {
				continue
			}
			if i == 0 {
				newParam := newReq.Params()[params[1].OnlyKey]
				newParam.Prefix = "="
				newReq.Params()[params[1].OnlyKey] = newParam
			} else {
				newParam := newReq.Params()[params[0].OnlyKey]
				newParam.Prefix = "="
				newParam.Suffix = "and 3 in "
				newReq.Params()[params[0].OnlyKey] = newParam
			}
			// sqlI := sqli.PoC{
			// 	AllowErrorType:   true,
			// 	AllowTimeType:    false,
			// 	AllowBoolType:    false,
			// 	AllowCheckCookie: false,
			// }

			// resp, err := c.Task().Client.Do(c.Context, newReq)
			// if err != nil {
			// 	c.OutputPoCError(&npoc.PoCError{PoC: p.ID(), Err: err})
			// 	return
			// }
			// newHttpContext := c.WithRequestResponse(newReq, resp)
			// _ = sqlI.Execute(newHttpContext)
		}
	}
}

func buildFazzParams(req *httpv.Request, param httpv.Param, value1, value2 string) (*httpv.Request, []httpv.Param) {
	newReq := req.Clone()
	// 原始参数都设置为不需要fuzz，只对新构造的参数进行fuzz
	for key := range newReq.Params() {
		newReq.SetNotNeedFuzz(key)
	}
	newUrlQuery := buildNewQuery(newReq.URL.RawQuery, param.Key, value1, value2)
	newReq.URL.RawQuery = newUrlQuery
	if newReq.Method == httpv.MethodPost && len(newReq.Body) > 0 {
		newBody := buildNewQuery(string(req.Body), param.Key, value1, value2)
		newReq.Body = []byte(newBody)
	}

	delete(newReq.Params(), param.OnlyKey)
	newParam1 := param
	newParam1.Key = param.Key + "[0]"
	newParam1.Value = value1
	newParam1.OnlyKey = funk.RandomString(8)
	newParam1.NeedCheck = true

	newParam2 := param
	newParam2.Key = param.Key + "[1]"
	newParam2.Value = value2
	newParam2.NeedCheck = true

	newParam2.OnlyKey = funk.RandomString(8)
	newReq.Params()[newParam1.OnlyKey] = newParam1
	newReq.Params()[newParam2.OnlyKey] = newParam2

	params := []httpv.Param{newParam1, newParam2}
	return newReq, params
}

// 将query中ids=123 换成两个参数ids[0],ids[1] 分别对这两个参数测试sql报错注入
func buildNewQuery(query, key, value1, value2 string) string {
	values, err := url.ParseQuery(query)
	if err != nil {
		values, err = url.ParseQuery(strings.ReplaceAll(query, ";", "&"))
		if err != nil {
			return ""
		}
	}
	newQuery := &url.Values{}

	for k, vs := range values {
		// php中一个参数一般只有一个值，如果存在多个值则不进行扫描
		if len(vs) > 1 {
			return ""
		}
		if k == key {
			newKey0 := key + "[0]"
			newKey1 := key + "[1]"
			newQuery.Set(newKey0, value1)
			newQuery.Set(newKey1, value2)
			continue
		}
		newQuery.Set(k, vs[0])
	}
	return newQuery.Encode()
}
