package thinkphp

import (
	"strings"

	"github.acme.red/intelli-sec/npoc"
)

const ID = string(npoc.ThinkphpType)

type PoC struct{}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "thinkphp v5.x 代码执行漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.ThinkphpType,
		Tags:           nil,
		Description:    "ThinkPHP 是一款运用极广的 PHP 开发框架。在v5的一些版本中，没有正确处理方法名，导致攻击者可以调用特定的类任意方法并构造利用链，从而导致远程代码执行漏洞。",
		Product:        "thinkphp",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityCritical,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	// 有传入指纹信息则进行指纹判断，如果根本就没传入则不判断指纹直接扫描
	if c.Task().FingerPrint != nil && !IsThinkphp(c) {
		return nil
	}
	p.CheckV5Rce(c)
	p.CheckSqli(c)
	return nil
}

func IsThinkphp(c *npoc.HTTPContext) bool {
	for _, info := range c.Task().FingerPrint {
		if strings.Contains(strings.ToLower(info.Name), "thinkphp") {
			return true
		}
	}
	return false
}
