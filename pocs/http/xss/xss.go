package xss

import (
	"log/slog"
	"sync"

	"github.acme.red/pictor/foundation/slogext"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/base"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/reflectedxss"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/storedxss"
	"github.acme.red/intelli-sec/npoc/utils"
)

const ID = string(npoc.XSSType)

type PoC struct {
	BrowserQPS *semaphore.Weighted
}

func (p *PoC) ID() string {
	return ID
}

func (p *PoC) Protocol() string {
	return npoc.ProtocolHTTP
}

func (p *PoC) Metadata() npoc.Metadata {
	return npoc.Metadata{
		Name:           "XSS 漏洞",
		PocType:        npoc.GenericPocType,
		Category:       npoc.XSSType,
		Tags:           nil,
		Description:    "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
		Product:        "",
		ProductVersion: nil,
		Reference:      nil,
		Severity:       npoc.SeverityMedium,
		Extra:          nil,
		CVE:            nil,
		CPE:            "",
	}
}

func (p *PoC) Execute(c *npoc.HTTPContext) error {
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer utils.RecoverFun(c.Context)
		defer wg.Done()
		reflectedxss.Execute(c)
	}()
	wg.Add(1)
	go func() {
		defer utils.RecoverFun(c.Context)
		defer wg.Done()
		if storedxss.NeedCheck(c) {
			err := p.BrowserQPS.Acquire(c.Context, 1)
			if err != nil {
				return
			}
			defer p.BrowserQPS.Release(1)
			browser, lc, err := base.NewBrowser(c.Context)
			if err != nil {
				slog.ErrorContext(c.Context, "xss poc new browser failed", slogext.Error(err))
				return
			}
			defer func() {
				if lc != nil {
					lc.Kill()
					lc.Cleanup()
				}
				if browser != nil {
					_ = browser.Close()
				}
			}()
			storedxss.Execute(c, browser)
		}
	}()
	wg.Wait()
	return nil
}
