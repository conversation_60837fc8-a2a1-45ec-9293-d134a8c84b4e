package base

import (
	"context"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/cdp"
	"github.com/go-rod/rod/lib/launcher"
)

const chromePath = "/opt/npoc/chromium/chrome-linux/chrome"

func NewBrowser(ctx context.Context) (*rod.Browser, *launcher.Launcher, error) {
	lc := launcher.New().Bin(chromePath).Headless(true).Devtools(false).
		Set("no-sandbox").
		Set("ignore-certificate-errors").
		// 不要禁用图片加载，否则会影响存储型 xss 的扫描
		// Set("blink-settings", "imagesEnabled=false").
		Set("disable-xss-auditor").
		Set("disable-web-security").
		Set("no-default-browser-check")
	ws, err := lc.Launch()
	if err != nil {
		return nil, nil, err
	}
	c, err := cdp.StartWithURL(ctx, ws, nil)
	if err != nil {
		return nil, nil, err
	}
	browser := rod.New().Client(c).Context(ctx)
	if err := browser.Connect(); err != nil {
		// _ = browser.Close()
		return nil, nil, err
	}
	return browser, lc, nil
}
