package base

import (
	"bytes"
	"fmt"
	"strings"

	"github.com/thoas/go-funk"
	"golang.org/x/net/html"
)

func TraverseNodes(node *html.Node, fn func(*html.Node) bool) bool {
	if node == nil {
		return false
	}

	stop := fn(node)
	if stop {
		return true
	}
	cur := node.FirstChild

	for cur != nil {
		next := cur.NextSibling
		stop = TraverseNodes(cur, fn)
		if stop {
			return true
		}
		cur = next
	}
	return false
}

func Node2String(node *html.Node) string {
	if node.Type == html.ElementNode {
		chunks := []string{"<", node.Data, " "}
		for _, attr := range node.Attr {
			chunks = append(chunks, fmt.Sprintf(`%s="%s" `, attr.Key, html.EscapeString(attr.Val)))
		}
		chunks = append(chunks, ">")
		return strings.Join(chunks, "")
	} else if node.Type == html.TextNode {
		return node.Data
	} else if node.Type == html.CommentNode {
		return fmt.Sprintf("<!--%s-->", node.Data)
	}

	return ""
}

func RandChar2Upper(str string) string {
	buf := bytes.NewBufferString("")
	for _, char := range str {
		if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z')) {
			buf.WriteRune(char)
			continue
		}
		toUpper := funk.RandomInt(0, 2)
		if toUpper == 0 {
			buf.WriteString(strings.ToUpper(string(char)))
		} else {
			buf.WriteString(strings.ToLower(string(char)))
		}
	}
	return buf.String()
}
