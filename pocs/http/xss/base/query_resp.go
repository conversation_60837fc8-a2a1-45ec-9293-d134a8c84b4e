package base

import (
	"bytes"
	"errors"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.com/PuerkitoBio/goquery"
	"golang.org/x/net/html"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

type QueryResp struct {
	Request  *httpv.Request
	Response *httpv.Response
	Doc      *goquery.Document
}

func GetQueryResp(c *npoc.HTTPContext, req *httpv.Request) (*QueryResp, error) {
	resp, err := c.Task().Client.Do(c, req)
	if err != nil {
		c.OutputPo<PERSON>rror(&npoc.PoCError{
			PoC: "reflected_xss",
			Err: err,
		})
		return nil, err
	}
	body, err := resp.GetUTF8Body()
	if err != nil {
		body = resp.Body
	}
	ct := resp.Header.Get("Content-Type")
	if !text.LowerStrContains(ct, "xml") && !text.LowerStrContains(ct, "html") {
		return nil, errors.New("Content-Type not supported: " + ct)
	}
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return nil, err
	}

	qr := QueryResp{
		Request:  req,
		Response: resp,
		Doc:      doc,
	}
	return &qr, nil
}

func (qr *QueryResp) QueryFirst(selector string) (*html.Node, error) {
	nodes, err := qr.Query(selector)
	if err == nil {
		return nodes[0], nil
	} else {
		return nil, err
	}
}

func (qr *QueryResp) Query(selector string) ([]*html.Node, error) {
	sel := qr.Doc.Find(selector)
	if len(sel.Nodes) == 0 {
		return nil, errors.New("node is not exists")
	}

	return sel.Nodes, nil
}

func (qr *QueryResp) QueryText(selector string) string {
	sel := qr.Doc.Find(selector)
	return sel.Text()
}

func (qr *QueryResp) Exists(selector string) bool {
	_, err := qr.Query(selector)
	return err == nil
}
