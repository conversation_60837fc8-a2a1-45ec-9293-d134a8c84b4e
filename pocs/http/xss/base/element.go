package base

import (
	"fmt"
	"strings"

	"github.com/thoas/go-funk"
	"golang.org/x/net/html"
)

type Element struct {
	node       *html.Node
	Name       string
	Attributes map[string]string
	Text       string
}

func (el Element) BuildSelector() string {
	var chains []string
	node := el.node

	for node != nil && node.Type != html.DocumentNode {
		parent := node.Parent
		tagName := node.Data

		fd := funk.Find(node.Attr, func(attr html.Attribute) bool {
			return attr.Key == "id"
		})

		attr, ok := fd.(html.Attribute)
		if ok {
			chains = append(chains, fmt.Sprintf("%s#%s", tagName, attr.Val))
			break
		}

		if parent != nil {
			var currentCount, childCount int
			for child := parent.FirstChild; child != nil; child = child.NextSibling {
				if child.Type == html.ElementNode && child.Data == node.Data {
					childCount++

					if node == child {
						currentCount = childCount
					}
				}
			}

			if childCount > 1 {
				tagName = fmt.Sprintf("%s:nth-of-type(%d)", tagName, currentCount)
			}
		}

		chains = append(chains, tagName)
		node = parent
	}

	// 将chains反转
	for i := len(chains)/2 - 1; i >= 0; i-- {
		opp := len(chains) - 1 - i
		chains[i], chains[opp] = chains[opp], chains[i]
	}

	return strings.Join(chains, " > ")
}

func CreateElement(node *html.Node) *Element {
	el := new(Element)
	el.node = node
	el.Name = node.Data
	el.Attributes = make(map[string]string)
	for _, attr := range node.Attr {
		el.Attributes[attr.Key] = attr.Val
	}

	for child := node.FirstChild; child != nil; child = child.NextSibling {
		if child.Type == html.TextNode {
			el.Text += child.Data
		}
	}

	return el
}
