package jsparser

import (
	"strings"

	"github.com/dop251/goja"
)

const parserCode = `function parserCode(_code, _callback) {
	return esprima.parseScript(_code, {
		comment: true
	}, function(node, bind_proxy) {
		var r = _callback(node, bind_proxy);
		if (r) {
			throw new Error(r);
		}
	});
}`

type JSParser struct {
	vm *goja.Runtime
}

func (jp *JSParser) VM() *goja.Runtime {
	return jp.vm
}

func (jp *JSParser) Init() error {
	jp.vm = goja.New()

	data, err := FSString(false, "/plugins/xss/js/esprima.js")
	if err != nil {
		return err
	}

	_, err = jp.vm.RunString(data)
	if err != nil {
		return err
	}

	_, err = jp.vm.RunString(parserCode)
	if err != nil {
		return err
	}

	return nil
}

/*
将代码树中object.object.object...property这样的结构，还原成字符串（代码）"object.object.object...property"
*/
func (jp *JSParser) RestoreMemberExpression(node *goja.Object) string {
	var stack []*goja.Object
	var chain []string
	var topNode, objectNode, propertyNode *goja.Object
	nodeType := jp.NodeType(node)

	if nodeType == "Identifier" {
		return jp.ExpressionToString(node)
	}

	if nodeType != "MemberExpression" {
		return ""
	}

	for stack = append(stack, node); len(stack) > 0; {
		// 获得栈顶对象
		n := len(stack) - 1
		topNode = stack[n]

		objectNode = topNode.Get("object").ToObject(jp.vm)
		propertyNode = topNode.Get("property").ToObject(jp.vm)

		if jp.NodeType(propertyNode) != "Identifier" {
			return ""
		}

		chain = append(chain, jp.ExpressionToString(propertyNode))

		// 弹出栈顶对象
		stack[n] = nil
		stack = stack[:n]

		objectType := jp.NodeType(objectNode)
		if objectType == "MemberExpression" {
			stack = append(stack, objectNode)
		} else if objectType == "Identifier" {
			chain = append(chain, jp.ExpressionToString(objectNode))
		}
	}

	for i := len(chain)/2 - 1; i >= 0; i-- {
		opp := len(chain) - 1 - i
		chain[i], chain[opp] = chain[opp], chain[i]
	}

	return strings.Join(chain, ".")
}

/*
将部分结构转换成字符串

	支持的结构有：Identifier、Literal、BlockComment、LineComment、TemplateElement
*/
func (jp *JSParser) ExpressionToString(node *goja.Object) string {
	nodeType := jp.NodeType(node)
	if nodeType == "Identifier" {
		return node.Get("name").String()
	}

	if nodeType == "Literal" {
		return node.Get("value").String()
	}

	if nodeType == "BlockComment" || nodeType == "LineComment" {
		return node.Get("value").String()
	}

	if nodeType == "TemplateElement" {
		nodeValue := node.Get("value").ToObject(jp.VM())
		return nodeValue.Get("cooked").String()
	}

	return ""
}

func (jp *JSParser) NodeType(node *goja.Object) string {
	return node.Get("type").String()
}

func (jp *JSParser) Parse(code string, callback func(goja.FunctionCall) goja.Value) error {
	err := jp.vm.Set("code", code)
	if err != nil {
		return err
	}
	err = jp.vm.Set("callback", callback)
	if err != nil {
		return err
	}
	_, err = jp.vm.RunString("parserCode(code, callback);")
	return err
}
