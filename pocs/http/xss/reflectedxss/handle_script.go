package reflectedxss

import (
	"fmt"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.com/dop251/goja"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/base"
	jsparser "github.acme.red/intelli-sec/npoc/pocs/http/xss/base/js"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

var jsVMPool = sync.Pool{
	New: func() interface{} {
		vm := new(jsparser.JSParser)
		_ = vm.Init()
		return vm
	},
}

var magicKey = "vm_running_times"

func handleScript(c *npoc.HTTPContext, selector string, builder *builder) bool {
	payload := funk.RandomString(8, utils.LowerChars)

	request := builder.build(payload)
	qr, err := base.GetQueryResp(c, request)
	if err != nil {
		return false
	}

	node, err := qr.QueryFirst(selector)
	if err != nil {
		return false
	}

	el := base.CreateElement(node)
	data := findScriptData(el, payload)
	if data == "" {
		return false
	}

	if text.StringHasPrefix(data, "javascript:") {
		data = data[11:]
	}

	jp, ok := jsVMPool.Get().(*jsparser.JSParser)
	if !ok {
		return false
	}
	defer func() {
		// 回收资源，为了防止 goja 出 bug，vm 用了超过 10 次就丢掉重新初始化。
		var cnt int64
		v := jp.VM().Get(magicKey)
		if v == nil {
			cnt = 0
		} else {
			cnt = v.ToInteger()
		}
		if cnt >= 10 {
			return
		}
		_ = jp.VM().Set(magicKey, cnt+1)
		jsVMPool.Put(jp)
	}()
	var stop bool
	firstCheck := func(call goja.FunctionCall) goja.Value {
		nextWalk := func(request *httpv.Request) goja.Value {
			nextCheck := func(call goja.FunctionCall) goja.Value {
				node := call.Arguments[0].ToObject(jp.VM())
				if jp.NodeType(node) == "Identifier" && text.LowerStrContains(jp.ExpressionToString(node), payload) {
					vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
					newPayload := strings.ReplaceAll(builder.lastPayload, payload, "prompt(66666)")
					des := fmt.Sprintf("payload 可变形为: %s", newPayload)
					vuln := &npoc.Vulnerability{
						PoC:         string(npoc.XSSType),
						Name:        "XSS 漏洞",
						Category:    npoc.XSSType,
						Severity:    npoc.SeverityMedium,
						Method:      request.Method,
						Param:       builder.param.Key,
						Payload:     builder.wrap(payload),
						Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
						URL:         c.Task().Request.URL.String(),
						HTTP:        vHTTP,
						Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_script", npoc.ExtraKeyDes: des},
						Confidence:  npoc.ConfidenceHigh,
					}
					c.OutputVulnerability(vuln)
					stop = true
					return jp.VM().ToValue(1)
				}

				return jp.VM().ToValue(0)
			}
			if stop {
				return jp.VM().ToValue(-1)
			}
			qr, err := base.GetQueryResp(c, request)
			if err != nil {
				return jp.VM().ToValue(0)
			}

			node, err := qr.QueryFirst(selector)
			if err != nil {
				return jp.VM().ToValue(0)
			}

			el := base.CreateElement(node)
			data := findScriptData(el, payload)
			if data == "" {
				return jp.VM().ToValue(0)
			}

			err = jp.Parse(data, nextCheck)
			return jp.VM().ToValue(err != nil)
		}
		if stop {
			return jp.VM().ToValue(-1)
		}
		node := call.Arguments[0].ToObject(jp.VM())
		switch jp.NodeType(node) {
		case "Identifier":
			if text.LowerStrContains(jp.ExpressionToString(node), payload) {
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
				des := fmt.Sprintf("payload 可变形为: %s ", builder.wrap("prompt(66666)//"))
				vuln := &npoc.Vulnerability{
					PoC:         string(npoc.XSSType),
					Name:        "XSS 漏洞",
					Category:    npoc.XSSType,
					Severity:    npoc.SeverityMedium,
					Method:      request.Method,
					Param:       builder.param.Key,
					Payload:     builder.wrap(payload),
					Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
					URL:         c.Task().Request.URL.String(),
					HTTP:        vHTTP,
					Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_script", npoc.ExtraKeyDes: des},
					Confidence:  npoc.ConfidenceHigh,
				}
				c.OutputVulnerability(vuln)
				stop = true
			}
		case "Literal":
			nodeRaw := node.Get("raw").String()
			if text.LowerStrContains(nodeRaw, payload) {
				identifier := nodeRaw[0]
				if identifier == '\'' || identifier == '"' {
					return nextWalk(builder.build(fmt.Sprintf("%c-%s-%c", identifier, payload, identifier)))
				}
			}
		case "TemplateElement":
			if text.LowerStrContains(jp.ExpressionToString(node), payload) {
				return nextWalk(builder.build(fmt.Sprintf("${%s}", payload)))
			}
		case "BlockComment":
			if text.LowerStrContains(jp.ExpressionToString(node), payload) {
				return nextWalk(builder.build(fmt.Sprintf("*/;%s;/*", payload)))
			}
		case "LineComment":
			if text.LowerStrContains(jp.ExpressionToString(node), payload) {
				return nextWalk(builder.build(fmt.Sprintf("\n;%s;//", payload)))
			}
		case "AssignmentExpression":
			if checkLocationAssignment(jp, node, payload) {
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
				des := fmt.Sprintf("payload 可变形为: %s ", builder.wrap("javascript:prompt(66666)"))
				vuln := &npoc.Vulnerability{
					PoC:         string(npoc.XSSType),
					Name:        "XSS 漏洞",
					Category:    npoc.XSSType,
					Severity:    npoc.SeverityMedium,
					Method:      request.Method,
					Param:       builder.param.Key,
					Payload:     builder.wrap(payload),
					Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
					URL:         c.Task().Request.URL.String(),
					HTTP:        vHTTP,
					Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_script", npoc.ExtraKeyDes: des},
					Confidence:  npoc.ConfidenceHigh,
				}
				c.OutputVulnerability(vuln)
				stop = true
			}
		case "CallExpression":
			if checkDangerCall(jp, node, payload) {
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
				des := fmt.Sprintf("payload 可变形为: %s ", builder.wrap("javascript:prompt(66666)"))
				vuln := &npoc.Vulnerability{
					PoC:         string(npoc.XSSType),
					Name:        "XSS 漏洞",
					Category:    npoc.XSSType,
					Severity:    npoc.SeverityMedium,
					Method:      request.Method,
					Param:       builder.param.Key,
					Payload:     builder.wrap(payload),
					Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
					URL:         c.Task().Request.URL.String(),
					HTTP:        vHTTP,
					Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_script", npoc.ExtraKeyDes: des},
					Confidence:  npoc.ConfidenceHigh,
				}
				c.OutputVulnerability(vuln)
				stop = true
			}
		}

		return jp.VM().ToValue(nil)
	}

	_ = jp.Parse(data, firstCheck)
	return stop
}

func findScriptData(el *base.Element, payload string) string {
	if el.Name == "script" {
		if data := el.Text; text.LowerStrContains(data, payload) {
			return data
		}
	}

	for _, value := range el.Attributes {
		if text.LowerStrContains(value, payload) {
			return value
		}
	}

	return ""
}

// 检查location.href = '%s'形式的XSS
func checkLocationAssignment(jp *jsparser.JSParser, node *goja.Object, payload string) bool {
	if node.Get("operator").String() != "=" {
		return false
	}

	leftNode := node.Get("left").ToObject(jp.VM())
	rightNode := node.Get("right").ToObject(jp.VM())
	leftType := jp.NodeType(leftNode)
	rightType := jp.NodeType(rightNode)

	// 如果赋值语句右值不是payload开头的，则直接退出
	if !(rightType == "Literal" && strings.HasPrefix(jp.ExpressionToString(rightNode), payload)) {
		return false
	}

	// 诸如 location.href = 'payload' 等
	if leftType == "MemberExpression" || leftType == "Identifier" {
		assignLeftString := jp.RestoreMemberExpression(leftNode)
		if "location.href" == assignLeftString ||
			"location" == assignLeftString ||
			strings.HasSuffix(assignLeftString, ".location") ||
			strings.HasSuffix(assignLeftString, ".location.href") {
			return true
		}
	}

	return false
}

// 对于SpreadElement暂时不做处理
// TODO: 兼容SpreadElement，即[...elements]
func checkDangerCall(jp *jsparser.JSParser, node *goja.Object, payload string) bool {
	calleeNode := node.Get("callee").ToObject(jp.VM())
	argumentNodes := node.Get("arguments").ToObject(jp.VM())
	argumentNodeKeys := argumentNodes.Keys()

	// 一个参数且能控制其开头部分
	if len(argumentNodeKeys) == 1 &&
		strings.HasPrefix(jp.ExpressionToString(argumentNodes.Get(argumentNodeKeys[0]).ToObject(jp.VM())), payload) {
		assignLeftString := jp.RestoreMemberExpression(calleeNode)
		if funk.Contains([...]string{"location.replace", "location.assign", "window.open"}, assignLeftString) {
			return true
		}
	}

	return false
}
