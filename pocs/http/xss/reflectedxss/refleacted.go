package reflectedxss

import (
	"bytes"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.com/PuerkitoBio/goquery"
	"github.com/thoas/go-funk"
	"golang.org/x/net/html"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/base"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

func Execute(c *npoc.HTTPContext) {
	newReq := c.Task().Request.Clone()
	// 默认允许fuzzReferer和cookie的每个参数
	// 这里会将cookie和允许fuzz的Header中的参数提取出来用于漏洞扫描。
	newReq.GetAllParams([]string{"Referer", "User-Agent"}...)
	for _, param := range newReq.Params() {
		if !param.NeedCheck {
			continue
		}
		if !firstCheck(c, param, newReq) {
			return
		}
	}
}

func firstCheck(c *npoc.HTTPContext, param httpv.Param, req *httpv.Request) bool {
	randStr := funk.RandomString(15, utils.LowerChars)
	newReq, resp, err := c.Task().Client.SendNewRequest(c.Context, req, param.OnlyKey, randStr)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{
			PoC: "reflected_xss",
			Err: err,
		})
		return true
	}
	ct := resp.Header.Get("Content-Type")
	if !text.LowerStrContains(ct, "xml") && !text.LowerStrContains(ct, "html") {
		return false
	}
	if !bytes.Contains(resp.Body, []byte(randStr)) {
		return true
	}
	DeepCheck(c, newReq, resp, param, randStr)
	return true
}

func DeepCheck(c *npoc.HTTPContext, req *httpv.Request, resp *httpv.Response, param httpv.Param, randStr string) {
	b := newRequestBuilder(req, param)
	body, err := resp.GetUTF8Body()
	if err != nil {
		body = resp.Body
	}
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return
	}
	for _, node := range doc.Nodes {
		stop := base.TraverseNodes(node, func(node *html.Node) bool {
			nodeString := base.Node2String(node)
			if !text.LowerStrContains(nodeString, randStr) {
				return false
			}
			switch node.Type {
			case html.ElementNode:
				el := base.CreateElement(node)
				result, _ := handleTag(c, randStr, el, b)
				if result {
					return true
				}
			case html.CommentNode:
				handleComment(c, b)
			case html.TextNode:
				if node.Parent != nil && node.Parent.Type == html.ElementNode {
					el := base.CreateElement(node.Parent)
					if handleText(c, el, b) {
						return true
					}
				}
			case html.DocumentNode:
			case html.DoctypeNode:
			default:
			}
			return false
		})
		if stop {
			return
		}
	}
}
