package reflectedxss

import (
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

type builder struct {
	prefix      string
	suffix      string
	request     *httpv.Request
	param       httpv.Param
	lastPayload string
}

func newRequestBuilder(request *httpv.Request, param httpv.Param) *builder {
	newRequest := request.Clone()

	b := builder{
		request: newRequest,
		param:   param,
	}
	return &b
}

func (b *builder) clone() *builder {
	newBuilder := newRequestBuilder(b.request, b.param)
	newBuilder.prefix = b.prefix
	newBuilder.suffix = b.suffix
	return newBuilder
}

func (b *builder) withPrefix(data string) *builder {
	newBuilder := b.clone()
	newBuilder.prefix = b.prefix + data
	return newBuilder
}

func (b *builder) withSuffix(data string) *builder { //nolint:unused
	newBuilder := b.clone()
	newBuilder.suffix = data + b.suffix
	return newBuilder
}

func (b *builder) wrap(value string) string {
	return b.prefix + value + b.suffix
}

func (b *builder) build(value string) *httpv.Request {
	newParam := b.param
	newParam.Value = b.wrap(value)
	b.lastPayload = newParam.Value

	newRequest := b.request.BuildRequestWithParams(newParam.OnlyKey, newParam)
	return newRequest
}
