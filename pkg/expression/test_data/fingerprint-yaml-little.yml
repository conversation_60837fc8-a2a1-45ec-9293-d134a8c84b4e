name: fingerprint-yaml-1C-Bitrix
manual: false
detail:
  fingerprint:
    name: 1C-Bitrix
  fofa: ""
  links:
  - https://www.1c-bitrix.ru
transport: http
rules:
  r1:
    request:
      cache: true
      method: GET
      path: /
      follow_redirects: true
    expression: (response.headers.Get("x-powered-cms") != "" && "bitrix site manager".imatches(response.headers.Get("x-powered-cms")))
      || (response.headers.Get("set-cookie") != "" && "bitrix_".imatches(response.headers.Get("set-cookie")))
  r2:
    request:
      cache: true
      method: GET
      path: /
      follow_redirects: true
    expression: (response.headers.Get("Set-Cookie") != "" && "bitrix_sm_guest_id=xxa{1,2}".imatches(response.headers.Get("Set-Cookie")))
      || (response.headers.Get("Set-Cookie") != "" && "bitrix_sm_last_ip=xxa{1,2}".imatches(response.headers.Get("Set-Cookie")))
      || (response.headers.Get("Set-Cookie") != "" && "bitrix_sm_sale_uid=xxa{2,3}".imatches(response.headers.Get("Set-Cookie")))
  r3:
    request:
      cache: true
      method: GET
      path: /
      follow_redirects: true
    expression: '"<a-1[^<>]{0,250}>".imatches(response.body_string)'
  r4:
    request:
      cache: true
      method: GET
      path: /
      follow_redirects: true
    expression: '"bitrix(?:\\.info/|/js/main/core)".anyMatch(response.script_src)'
  r5:
    request:
      cache: true
      method: GET
      path: /
      follow_redirects: true
    expression: '"section[class*=''player30nama'']".domExist(response.cached_doc)'
  r6:
    request:
      cache: true
      method: GET
      path: /
      follow_redirects: true
    expression: '"assets\\.adapt\\.ws/".imatches(response.cached_doc.getMeta("image"))'
expression: r1() || r2() || r3() || r4() || r5() || r6()
