package expression

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log/slog"
	"math/rand"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.acme.red/intelli-sec/npoc/utils"
	text2 "github.acme.red/intelli-sec/npoc/utils/text"
	"github.com/dlclark/regexp2"
	"github.com/google/cel-go/cel"
	"github.com/google/cel-go/checker/decls"
	"github.com/google/cel-go/common/types"
	"github.com/google/cel-go/common/types/ref"
	"github.com/google/cel-go/common/types/traits"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/npoc/pkg/expression/proto"
)

var (
	DeclStrStrMapType = decls.NewMapType(decls.String, decls.String)
	StrStrMapType     = cel.MapType(cel.StringType, cel.StringType)
	// NewEnvOptions NewStrStrMapType = types.NewMapType(types.StringType, types.StringType)
	// NewProgOptions = []cel.ProgramOption{
	// 	cel.EvalOptions(cel.OptOptimize),
	// }
	NewEnvOptions = []cel.EnvOption{
		cel.Container("proto"),
		cel.Types(
			&proto.Request{},
			&proto.Response{},
		),
		cel.Declarations(
			decls.NewVar("request", decls.NewObjectType("proto.Request")),
			decls.NewVar("response", decls.NewObjectType("proto.Response")),
			decls.NewVar("headers", DeclStrStrMapType),
		),
		// join(): join a list of bytes, return bytes
		cel.Function("join",
			cel.MemberOverload("string_join_bytes", []*cel.Type{cel.StringType, cel.BytesType}, cel.BytesType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					delimiter, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "join(): not a string")
					}
					l, ok := rhs.(types.Bytes)
					if !ok {
						types.ValOrErr(rhs, "join(): not a bytes")
					}
					bs := make([][]byte, 0)
					err := json.Unmarshal(l, &bs)
					if err != nil {
						return types.NewErr("join() fail to unmarshal: %v", err)
					}
					res := bytes.Join(bs, []byte(string(delimiter)))
					return types.Bytes(res)
				}))),
		// imatches(): match() ignore case
		cel.Function("imatches",
			cel.MemberOverload("string_imatches_string", []*cel.Type{cel.StringType, cel.StringType}, cel.BoolType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					re, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "imatches(): not a string")
					}
					target, ok := rhs.(types.String)
					if !ok {
						return types.ValOrErr(rhs, "imatches(): not a string")
					}
					if strings.Contains(string(target), "c758dea036133e583d03145d721bcf75") {
						fmt.Printf("")
					}
					reg := regexp2.MustCompile(string(re), regexp2.IgnoreCase)
					res, err := reg.MatchString(string(target))
					if err != nil {
						return types.NewErr("imatches() error: %s", err.Error())
					}
					return types.Bool(res)
				}))),
		// domAttr(): extract attributes of nodes
		cel.Function("domAttr",
			cel.MemberOverload("string_domAttr_bytes_string", []*cel.Type{cel.StringType, cel.BytesType, cel.StringType}, cel.BytesType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					if len(values) != 3 {
						return types.NewErr("domAttr(): wrong argument number")
					}
					selector, ok := values[0].(types.String)
					if !ok {
						return types.ValOrErr(values[0], "domAttr(): not a string")
					}
					cachedDocSer, ok := values[1].(types.Bytes)
					if !ok {
						return types.ValOrErr(values[1], "domAttr(): not a bytes")
					}
					attrName, ok := values[2].(types.String)
					if !ok {
						return types.ValOrErr(values[2], "domAttr(): not a string")
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(cachedDocSer, cachedDoc)
					if err != nil {
						return types.NewErr("domAttr(): fail to unmarshal CachedDoc, err: %v", err)
					}
					res, err := utils.DomAttrFromCache(cachedDoc, string(attrName), string(selector))
					if err != nil {
						return types.NewErr("domAttr() error: %w", err)
					}
					return types.Bytes(res)
				}))),
		// domAttrMatch(): perform regexp match in dom
		cel.Function("domAttrMatch",
			cel.MemberOverload("string_domAttrMatch_bytes_string_string", []*cel.Type{cel.StringType, cel.BytesType, cel.StringType, cel.StringType}, cel.BoolType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					if len(values) != 4 {
						return types.NewErr("domAttrMatch(): wrong argument number")
					}
					selector, ok := values[0].(types.String)
					if !ok {
						return types.ValOrErr(values[0], "domAttrMatch(): not a string")
					}
					docSerial, ok := values[1].(types.Bytes)
					if !ok {
						return types.ValOrErr(values[1], "domAttrMatch(): not a bytes")
					}
					attrName, ok := values[2].(types.String)
					if !ok {
						return types.ValOrErr(values[2], "domAttrMatch(): not a string")
					}
					regex, ok := values[3].(types.String)
					if !ok {
						return types.ValOrErr(values[3], "domAttrMatch(): not a string")
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(docSerial, cachedDoc)
					if err != nil {
						return types.NewErr("domAttrMatch(): fail to unmarshal CachedDoc, err: %v", err)
					}
					res, err := utils.DomAttrMatchFromCache(cachedDoc, string(attrName), string(regex), string(selector))
					if err != nil {
						return types.NewErr("domAttrMatch() error: %w", err)
					}
					return types.Bool(res)
				}))),
		// domText(): extract text content of dom nodes
		cel.Function("domText",
			cel.MemberOverload("string_domText_bytes", []*cel.Type{cel.StringType, cel.BytesType}, cel.BytesType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					selector, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "domTextMatch(): not a string")
					}
					docSerial, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "domTextMatch(): not a bytes")
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(docSerial, cachedDoc)
					if err != nil {
						return types.NewErr("domText(): fail to unmarshal CachedDoc, err: %v", err)
					}
					b, err := utils.DomTextFromCache(cachedDoc, string(selector))
					if err != nil {
						return types.NewErr("domText() error: %w", err)
					}
					return types.Bytes(b)
				}))),
		// domTextMatch(): perform regexp match in dom
		cel.Function("domTextMatch",
			cel.MemberOverload("string_domTextMatch_bytes_string", []*cel.Type{cel.StringType, cel.BytesType, cel.StringType}, cel.BoolType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					if len(values) != 3 {
						return types.NewErr("domTextMatch(): wrong argument number")
					}
					selector, ok := values[0].(types.String)
					if !ok {
						return types.ValOrErr(values[0], "domTextMatch(): not a string")
					}
					docSerial, ok := values[1].(types.Bytes)
					if !ok {
						return types.ValOrErr(values[1], "domTextMatch(): not a bytes")
					}
					regex, ok := values[2].(types.String)
					if !ok {
						return types.ValOrErr(values[2], "domTextMatch(): not a string")
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(docSerial, cachedDoc)
					if err != nil {
						return types.NewErr("domTextMatch(): fail to unmarshal CachedDoc, err: %v", err)
					}
					res, err := utils.DomTextMatchFromCache(cachedDoc, string(regex), string(selector))
					if err != nil {
						return types.NewErr("domTextMatch() error: %w", err)
					}
					return types.Bool(res)
				}))),
		// getMeta(): get meta content via meta name
		cel.Function("getMeta",
			cel.MemberOverload("bytes_getMeta_string", []*cel.Type{cel.BytesType, cel.StringType}, cel.StringType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					v1, ok := lhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(lhs, "not a bytes")
					}
					v2, ok := rhs.(types.String)
					if !ok {
						return types.ValOrErr(rhs, "not a string")
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(v1, cachedDoc)
					if err != nil {
						return types.NewErr("getMeta(): fail to unmarshal CachedDoc, err: %v", err)
					}
					con, err := utils.GetMetaContentFromCache(cachedDoc, string(v2))
					if err != nil {
						return types.NewErr("getMeta() error: %v", err)
					}
					return types.String(con)
				}))),

		// domExist(): perform jQuery selector and returns whether there is at least one element selected
		cel.Function("domExist",
			cel.MemberOverload("string_domExist_bytes", []*cel.Type{cel.StringType, cel.BytesType}, cel.BoolType,
				cel.BinaryBinding(func(lhs, rhs ref.Val) ref.Val {
					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "not a string")
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "not a bytes")
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(v2, cachedDoc)
					if err != nil {
						return types.NewErr("domExist(): fail to unmarshal CachedDoc, err: %v", err)
					}
					exist, err := utils.ExistQuerySelectorAllFromCache(cachedDoc, string(v1))
					if err != nil {
						return types.NewErr("domExist() fail to run, error: %v", err)
					}
					return types.Bool(exist)
				}))),

		// scriptSrc(): extract the src attribute of all script tags, accept serialized goquery document, the return value is [][]byte with json encoded
		cel.Function("scriptSrc",
			cel.Overload("scriptSrc_bytes", []*cel.Type{cel.BytesType}, cel.BytesType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Bytes)
					if !ok {
						return types.ValOrErr(value, "'%v' is not of type 'bytes'", value)
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(v, cachedDoc)
					if err != nil {
						return types.NewErr("scriptSrc(): fail to unmarshal CachedDoc, err: %v", err)
					}
					scriptSrcs, err := utils.ExtractAllScriptSrcFromCache(cachedDoc)
					if err != nil {
						return types.NewErr("scriptSrc() fail to run, error: %v", err)
					}
					res, err := json.Marshal(scriptSrcs)
					if err != nil {
						return types.NewErr("scriptSrc() fail to marshal, error: %v", err)
					}
					return types.Bytes(res)
				}))),

		// script(): extract the contents of all script tags, the return value is [][]byte with json encoded
		cel.Function("script",
			cel.Overload("script_bytes", []*cel.Type{cel.BytesType}, cel.BytesType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Bytes)
					if !ok {
						return types.ValOrErr(value, "'%v' is not bytes", value.Type())
					}
					cachedDoc := &utils.CachedDoc{}
					err := json.Unmarshal(v, cachedDoc)
					if err != nil {
						return types.NewErr("script(): fail to unmarshal CachedDoc, err: %v", err)
					}
					scripts, err := utils.ExtractAllScriptFromCache(cachedDoc)
					if err != nil {
						return types.NewErr("script() fail to run, error: %v", err)
					}
					res, err := json.Marshal(scripts)
					if err != nil {
						return types.NewErr("script() fail to marshal, error: %v", err)
					}
					return types.Bytes(res)
				}))),
		// anyMatch checks if any of the bytes input matches the regexp
		cel.Function("anyMatch",
			cel.MemberOverload("string_anyMatch_bytes", []*cel.Type{cel.StringType, cel.BytesType}, cel.BoolType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "'%v' is not string", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "'%v' is not bytes", rhs.Type())
					}

					reg, err := regexp.Compile(string(v1))
					if err != nil {
						return types.NewErr("anyMatch() fail to compile regexp: %v", err)
					}

					bs := make([][]byte, 0)
					err = json.Unmarshal(v2, &bs)
					if err != nil {
						return types.NewErr("anyMatch() fail to unmarshal: %v, target: %s", err, string(v2))
					}

					return types.Bool(utils.AnySubMatch(reg, bs))
				}))),
		// 判断左边string中是否包含右边string的字符，case-insensitive
		cel.Function("icontains",
			cel.MemberOverload("string_icontains_string",
				[]*cel.Type{cel.StringType, cel.StringType},
				cel.BoolType,
				cel.BinaryBinding(func(lhs, rhs ref.Val) ref.Val {
					rawStr, ok := lhs.(types.String)
					if !ok {
						slog.Error("类型转换失败")
						return types.Bool(false)
					}
					subStr, ok := rhs.(types.String)
					if !ok {
						slog.Error("类型转换失败")
						return types.Bool(false)
					}
					result := strings.Contains(strings.ToLower(string(rawStr)), strings.ToLower(string(subStr)))

					return types.Bool(result)
				}),
			),
		),
		cel.Function("ibsubmatch",
			cel.MemberOverload("string_ibsubmatch_bytes", []*cel.Type{cel.StringType, cel.BytesType}, StrStrMapType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					resultMap := make(map[string]string)
					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to ibsubmatch", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to ibsubmatch", rhs.Type())
					}

					re := regexp2.MustCompile(string(v1), regexp2.RE2|regexp2.IgnoreCase)
					if m, _ := re.FindStringMatch(string([]byte(v2))); m != nil {
						gps := m.Groups()
						for n, gp := range gps {
							if n == 0 {
								continue
							}
							resultMap[gp.Name] = gp.String()
							resultMap[strconv.Itoa(n)] = gp.String()
						}
					}

					return types.NewStringStringMap(types.NewEmptyRegistry(), resultMap)
				}))),
		cel.Function("Get",
			cel.MemberOverload("map_get_string",
				[]*cel.Type{cel.MapType(cel.StringType, cel.StringType), cel.StringType},
				cel.StringType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					headers, ok := lhs.(traits.Mapper)
					if !ok {
						return types.ValOrErr(lhs, "Get() failed '%v' is not a map type", lhs.Type())
					}
					key, ok := rhs.(types.String)
					if !ok {
						return types.ValOrErr(rhs, "Get() failed '%v' is not a string type", rhs.Type())
					}
					iter := headers.Iterator()
					found := false
					var oldKey ref.Val
					for {
						i := iter.HasNext()
						if !i.Value().(bool) {
							break
						}
						val := iter.Next()
						valS, _ := val.Value().(string)
						if strings.EqualFold(string(valS), string(key)) {
							found = true
							oldKey = val
							break
						}
					}
					if !found {
						return types.String("")
					} else {
						val, ok := headers.Find(oldKey)
						if ok {
							return types.String(val.Value().(string))
						} else {
							return types.String("")
						}
					}
				},
				))),
		// request for icon content
		cel.Function("getIconContent",
			cel.MemberOverload("object_getIconContent_int",
				[]*cel.Type{cel.ObjectType("proto.Response")},
				cel.IntType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					r, ok := value.Value().(*proto.Response)
					if !ok {
						slog.Error("类型转换失败")
						return types.ValOrErr(value, "'%v' is not a response type", value.Type())
					}
					return types.Int(r.FaviconHash)
				}))),
		cel.Function("substr",
			cel.MemberOverload("substr_string_int_int",
				[]*cel.Type{cel.StringType, cel.IntType, cel.IntType},
				cel.StringType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					if len(values) == 3 {
						str, ok := values[0].(types.String)
						if !ok {
							return types.NewErr("'%v' is not a string type to 'substr'", values[0].Type())
						}
						start, ok := values[1].(types.Int)
						if !ok {
							return types.NewErr("'%v' is not a int type to 'substr'", values[1].Type())
						}
						length, ok := values[2].(types.Int)
						if !ok {
							return types.NewErr("'%v' is not a int type to 'substr'", values[2].Type())
						}
						runes := []rune(str)
						if start < 0 || length < 0 || int(start+length) > len(runes) {
							return types.NewErr("invalid start or length to 'substr' ")
						}
						return types.String(runes[start : start+length])
					} else {
						return types.NewErr("too many arguments to 'substr'")
					}
				}))),
		cel.Function("replaceAll",
			cel.MemberOverload("replaceAll_string_string_string",
				[]*cel.Type{cel.StringType, cel.StringType, cel.StringType},
				cel.StringType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					if len(values) == 3 {
						s, ok := values[0].(types.String)
						if !ok {
							return types.NewErr("'%v' is not a string type to 'replaceAll'", values[0].Type())
						}
						old, ok := values[1].(types.String)
						if !ok {
							return types.NewErr("'%v' is not a string type to 'replaceAll'", values[1].Type())
						}
						newString, ok := values[2].(types.String)
						if !ok {
							return types.NewErr("'%v' is not a string type to 'replaceAll'", values[2].Type())
						}

						return types.String(strings.ReplaceAll(string(s), string(old), string(newString)))
					} else {
						return types.NewErr("too many arguments to 'replaceAll' ")
					}
				}))),
		cel.Function("printable",
			cel.MemberOverload("printable_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					s, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(s, "unexpected type '%v' passed to printable", s.Type())
					}

					clean := strings.Map(func(r rune) rune {
						if unicode.IsPrint(r) {
							return r
						}
						return -1
					}, string(s))

					return types.String(clean)
				}))),
		cel.Function("toUintString",
			cel.MemberOverload("toUintString_string_string", []*cel.Type{cel.StringType, cel.StringType}, cel.StringType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					s1, ok := values[0].(types.String)
					s := string(s1)
					if !ok {
						return types.ValOrErr(s1, "unexpected type '%v' passed to toUintString", s1.Type())
					}
					direction, ok := values[1].(types.String)
					if !ok {
						return types.ValOrErr(direction, "unexpected type '%v' passed to toUintString", direction.Type())
					}
					if direction == "<" {
						s = text2.ReverseString(s)
					}
					if _, err := strconv.Atoi(s); err == nil {
						return types.String(s)
					} else {
						return types.NewErr("%v", err)
					}
				}))),
		cel.Function("toUpper",
			cel.Overload("toUpper_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to toUpper_string", value.Type())
					}

					return types.String(strings.ToUpper(string(v)))
				}))),
		cel.Function("toLower",
			cel.Overload("toLower_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to toLower_string", value.Type())
					}

					return types.String(strings.ToLower(string(v)))
				}))),
		// []byte
		cel.Function("bcontains",
			cel.MemberOverload("bytes_bcontains_bytes", []*cel.Type{cel.BytesType, cel.BytesType}, cel.BoolType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					v1, ok := lhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to bcontains", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to bcontains", rhs.Type())
					}
					return types.Bool(bytes.Contains(v1, v2))
				}))),
		cel.Function("ibcontains",
			cel.MemberOverload("bytes_ibcontains_bytes", []*cel.Type{cel.BytesType, cel.BytesType}, cel.BoolType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					v1, ok := lhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to bcontains", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to bcontains", rhs.Type())
					}
					return types.Bool(bytes.Contains(bytes.ToLower(v1), bytes.ToLower(v2)))
				}))),
		cel.Function("bstartsWith",
			cel.MemberOverload("bytes_bstartsWith_bytes", []*cel.Type{cel.BytesType, cel.BytesType}, cel.BoolType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					v1, ok := lhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to bstartsWith", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to bstartsWith", rhs.Type())
					}
					return types.Bool(bytes.HasPrefix(v1, v2))
				}))),
		// 	encode
		cel.Function("md5",
			cel.Overload("md5_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to md5_string", value.Type())
					}
					res := fmt.Sprintf("%x", md5.Sum([]byte(v)))
					return types.String(res)
				}))),
		cel.Function("base64",
			cel.Overload("base64_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to base64_string", value.Type())
					}
					return types.String(base64.StdEncoding.EncodeToString([]byte(v)))
				}))),
		cel.Function("base64",
			cel.Overload("base64_bytes", []*cel.Type{cel.BytesType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Bytes)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to base64_bytes", value.Type())
					}
					return types.String(base64.StdEncoding.EncodeToString(v))
				}))),
		cel.Function("base64Decode", cel.Overload("base64Decode_string", []*cel.Type{cel.StringType}, cel.StringType,
			cel.UnaryBinding(func(value ref.Val) ref.Val {
				v, ok := value.(types.String)
				if !ok {
					return types.ValOrErr(value, "unexpected type '%v' passed to base64Decode_string", value.Type())
				}
				decodeBytes, err := base64.StdEncoding.DecodeString(string(v))
				if err != nil {
					return types.NewErr("%v", err)
				}
				return types.String(decodeBytes)
			}))),
		cel.Function("base64Decode",
			cel.Overload("base64Decode_bytes", []*cel.Type{cel.BytesType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Bytes)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to base64Decode_bytes", value.Type())
					}
					decodeBytes, err := base64.StdEncoding.DecodeString(string(v))
					if err != nil {
						return types.NewErr("%v", err)
					}
					return types.String(decodeBytes)
				}))),
		cel.Function("urlencode",
			cel.Overload("urlencode_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to urlencode_string", value.Type())
					}
					return types.String(url.QueryEscape(string(v)))
				}))),
		cel.Function("urlencode",
			cel.Overload("urlencode_bytes", []*cel.Type{cel.BytesType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Bytes)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to urlencode_bytes", value.Type())
					}
					return types.String(url.QueryEscape(string(v)))
				}))),
		cel.Function("urldecode",
			cel.Overload("urldecode_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to urldecode_string", value.Type())
					}
					decodeString, err := url.QueryUnescape(string(v))
					if err != nil {
						return types.NewErr("%v", err)
					}
					return types.String(decodeString)
				}))),
		cel.Function("urldecode",
			cel.Overload("urldecode_bytes", []*cel.Type{cel.BytesType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Bytes)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to urldecode_bytes", value.Type())
					}
					decodeString, err := url.QueryUnescape(string(v))
					if err != nil {
						return types.NewErr("%v", err)
					}
					return types.String(decodeString)
				}))),
		cel.Function("faviconHash",
			cel.Overload("faviconHash_int", []*cel.Type{cel.IntType}, cel.IntType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					r, ok := value.(types.Int)
					if !ok {
						return types.ValOrErr(value, "faviconHash error unexpected type '%v'", value.Type())
					}
					return r
				}))),
		cel.Function("hexdecode",
			cel.Overload("hexdecode_string", []*cel.Type{cel.StringType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.String)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to hexdecode_string", value.Type())
					}
					dst := make([]byte, hex.DecodedLen(len(v)))
					n, err := hex.Decode(dst, []byte(v))
					if err != nil {
						return types.ValOrErr(value, "unexpected type '%s' passed to hexdecode_string", err.Error())
					}
					return types.String(dst[:n])
				}))),
		// random
		cel.Function("randomInt",
			cel.MemberOverload("randomInt_int_int", []*cel.Type{cel.IntType, cel.IntType}, cel.IntType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					from, ok := lhs.(types.Int)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to randomInt", lhs.Type())
					}
					to, ok := rhs.(types.Int)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to randomInt", rhs.Type())
					}
					minInt, maxInt := int(from), int(to)
					return types.Int(rand.Intn(maxInt-minInt) + minInt)
				}))),
		cel.Function("randomLowercase",
			cel.Overload("randomLowercase_int", []*cel.Type{cel.IntType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					n, ok := value.(types.Int)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to randomLowercase", value.Type())
					}
					return types.String(funk.RandomString(int(n), text2.LowerChars))
				}))),
		// regex
		cel.Function("submatch",
			cel.MemberOverload("string_submatch_string", []*cel.Type{cel.StringType, cel.StringType}, StrStrMapType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					resultMap := make(map[string]string)

					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to submatch", lhs.Type())
					}
					v2, ok := rhs.(types.String)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to submatch", rhs.Type())
					}

					re := regexp2.MustCompile(string(v1), regexp2.RE2)
					if m, _ := re.FindStringMatch(string(v2)); m != nil {
						gps := m.Groups()
						for n, gp := range gps {
							if n == 0 {
								continue
							}
							resultMap[strconv.Itoa(n)] = gp.String()
							resultMap[gp.Name] = gp.String()
						}
					}
					// return types.NewStringStringMap(&reg, resultMap)
					// 修改为NewStringStringMap传入EmptyRegistry
					return types.NewStringStringMap(types.NewEmptyRegistry(), resultMap)
				}))),
		cel.Function("isubmatch",
			cel.MemberOverload("string_isubmatch_string", []*cel.Type{cel.StringType, cel.StringType}, StrStrMapType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					resultMap := make(map[string]string)

					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to bsubmatch", lhs.Type())
					}
					v2, ok := rhs.(types.String)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to bsubmatch", rhs.Type())
					}

					re := regexp2.MustCompile(string(v1), regexp2.RE2|regexp2.IgnoreCase)
					if m, _ := re.FindStringMatch(string(v2)); m != nil {
						gps := m.Groups()
						for n, gp := range gps {
							if n == 0 {
								continue
							}
							resultMap[strconv.Itoa(n)] = gp.String()
							resultMap[gp.Name] = gp.String()
						}
					}

					return types.NewStringStringMap(types.NewEmptyRegistry(), resultMap)
				}))),
		cel.Function("bsubmatch",
			cel.MemberOverload("string_bsubmatch_bytes", []*cel.Type{cel.StringType, cel.BytesType}, StrStrMapType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					resultMap := make(map[string]string)

					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to bsubmatch", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to bsubmatch", rhs.Type())
					}

					re := regexp2.MustCompile(string(v1), regexp2.RE2)
					if m, _ := re.FindStringMatch(string([]byte(v2))); m != nil {
						gps := m.Groups()
						for n, gp := range gps {
							if n == 0 {
								continue
							}
							resultMap[strconv.Itoa(n)] = gp.String()
							resultMap[gp.Name] = gp.String()
							// resultMap[strconv.Itoa(n)] = gp.String()
						}
					}

					return types.NewStringStringMap(types.NewEmptyRegistry(), resultMap)
				}))),
		cel.Function("bmatches",
			cel.MemberOverload("string_bmatches_bytes", []*cel.Type{cel.StringType, cel.BytesType}, cel.BoolType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					isMatch := false
					var err error

					v1, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to bmatches", lhs.Type())
					}
					v2, ok := rhs.(types.Bytes)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to bmatches", rhs.Type())
					}
					re := regexp2.MustCompile(string(v1), 0)
					if isMatch, err = re.MatchString(string(v2)); err != nil {
						return types.NewErr("%v", err)
					}
					return types.Bool(isMatch)
				}))),
		// other
		cel.Function("sleep",
			cel.Overload("sleep_int", []*cel.Type{cel.IntType}, cel.NullType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					v, ok := value.(types.Int)
					if !ok {
						return types.ValOrErr(value, "unexpected type '%v' passed to sleep", value.Type())
					}
					time.Sleep(time.Duration(v) * time.Second)
					return nil
				}))),
		// year
		cel.Function("year",
			cel.Overload("year_string", []*cel.Type{cel.IntType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					year := time.Now().Format("2006")
					return types.String(year)
				}))),
		cel.Function("shortyear",
			cel.Overload("shortyear_string", []*cel.Type{cel.IntType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					year := time.Now().Format("06")
					return types.String(year)
				}))),
		cel.Function("month",
			cel.Overload("month_string", []*cel.Type{cel.IntType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					month := time.Now().Format("01")
					return types.String(month)
				}))),
		cel.Function("day",
			cel.Overload("day_string", []*cel.Type{cel.IntType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					day := time.Now().Format("02")
					return types.String(day)
				}))),
		cel.Function("timestamp_second",
			cel.Overload("timestamp_second_string", []*cel.Type{cel.IntType}, cel.StringType,
				cel.UnaryBinding(func(value ref.Val) ref.Val {
					timestamp := strconv.FormatInt(time.Now().Unix(), 10)
					return types.String(timestamp)
				}))),
		// compare version
		cel.Function("versionCompare",
			cel.MemberOverload("versionCompare_string_string_string", []*cel.Type{cel.StringType, cel.StringType, cel.StringType}, cel.BoolType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					if len(values) != 3 {
						return types.Bool(false)
					}
					v1, ok := values[0].(types.String)
					if !ok {
						return types.ValOrErr(v1, "unexpected type '%v' passed to versionCompare", v1.Type())
					}
					operator, ok := values[1].(types.String)
					if !ok {
						return types.ValOrErr(operator, "unexpected type '%v' passed to versionCompare", operator.Type())
					}
					v2, ok := values[2].(types.String)
					if !ok {
						return types.ValOrErr(v2, "unexpected type '%v' passed to versionCompare", v2.Type())
					}

					return types.Bool(utils.Compare(string(v1), string(operator), string(v2)))
				}))),
		// AesCBC
		cel.Function("aesCBC",
			cel.MemberOverload("aesCBC_string_string_string", []*cel.Type{cel.StringType, cel.StringType, cel.StringType}, cel.StringType,
				cel.FunctionBinding(func(values ...ref.Val) ref.Val {
					text, ok := values[0].(types.String)
					if !ok {
						return types.ValOrErr(text, "unexpected type '%v' passed to versionCompare", text.Type())
					}
					key, ok := values[1].(types.String)
					if !ok {
						return types.ValOrErr(key, "unexpected type '%v' passed to versionCompare", key.Type())
					}
					iv, ok := values[2].(types.String)
					if !ok {
						return types.ValOrErr(iv, "unexpected type '%v' passed to versionCompare", iv.Type())
					}

					plainText := text2.Pkcs5padding([]byte(text), aes.BlockSize, len(text))
					block, _ := aes.NewCipher([]byte(key))
					ciphertext := make([]byte, len(plainText))
					mode := cipher.NewCBCEncrypter(block, []byte(iv))
					mode.CryptBlocks(ciphertext, plainText)

					return types.String(ciphertext)
				}))),
		// Repeat
		cel.Function("repeat",
			cel.MemberOverload("repeat_string_int", []*cel.Type{cel.StringType, cel.IntType}, cel.StringType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					str, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to randomLowercase", lhs.Type())
					}
					count, ok := rhs.(types.Int)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to randomLowercase", rhs.Type())
					}

					return types.String(strings.Repeat(string(str), int(count)))
				}))),
		// decimal
		cel.Function("decimal",
			cel.MemberOverload("decimal_string_string", []*cel.Type{cel.StringType, cel.StringType}, cel.StringType,
				cel.BinaryBinding(func(lhs ref.Val, rhs ref.Val) ref.Val {
					input, ok := lhs.(types.String)
					if !ok {
						return types.ValOrErr(lhs, "unexpected type '%v' passed to randomLowercase", lhs.Type())
					}
					delimiter, ok := rhs.(types.String)
					if !ok {
						return types.ValOrErr(rhs, "unexpected type '%v' passed to randomLowercase", rhs.Type())
					}

					var str []string
					for _, char := range string(input) {
						str = append(str, fmt.Sprintf("%d", char))
					}

					return types.String(strings.Join(str, string(delimiter)))
				}))),
	}
)

func ReadCompileOptions() []cel.EnvOption {
	// allEnvOptions := []cel.EnvOption{
	// 	cel.CustomTypeAdapter(&reg),
	// 	cel.CustomTypeProvider(reg),
	// }
	// allEnvOptions = append(allEnvOptions, NewEnvOptions...)
	allEnvOptions := NewEnvOptions
	return allEnvOptions
}

// // WriteRuleIsVulOptions 追加rule变量到 cel options
// func WriteRuleIsVulOptions(c CustomLib, key string) {
// 	c.envOptions = append(c.envOptions, cel.Declarations(decls.NewVar(key+"()", decls.Bool)))
// }
