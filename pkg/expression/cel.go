package expression

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/google/cel-go/cel"
	"github.com/google/cel-go/checker/decls"
	"github.com/google/cel-go/common/types"
	"github.com/google/cel-go/common/types/ref"
	exprpb "google.golang.org/genproto/googleapis/api/expr/v1alpha1"
	"gopkg.in/yaml.v2"
)

type CustomLib struct {
	envOptions     []cel.EnvOption
	programOptions []cel.ProgramOption
}

func (c *CustomLib) CompileOptions() []cel.EnvOption {
	return c.envOptions
}

func (c *CustomLib) ProgramOptions() []cel.ProgramOption {
	return c.programOptions
}

func (c *CustomLib) RunEval(expression string, variablemap map[string]any) (ref.Val, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	var (
		val ref.Val
		err error
	)
	resp := make(chan int)
	go func() {
		defer close(resp)

		env, err := c.NewCelEnv()
		if err != nil {
			resp <- 9
			slog.Error("new cel env error", slogext.Error(err))
			return
		}
		val, err = Eval(env, expression, variablemap)
		if err != nil {
			if err.Error() == "no such key: 1" {
				resp <- 99
				return
			}
			resp <- 9
			slog.Error("env eval error", slogext.Error(err))
			return
		}
		resp <- 99
	}()

	select {
	case <-ctx.Done():
		return nil, fmt.Errorf("eval timed out")
	case v := <-resp:
		if v == 99 {
			return val, err
		}
		return nil, fmt.Errorf("eval error")
	}
}

func NewCustomLib() *CustomLib {
	c := &CustomLib{}
	// reg := types.NewEmptyRegistry()
	c.envOptions = ReadCompileOptions()
	return c
}

func (c *CustomLib) NewCelEnv() (env *cel.Env, err error) {
	env, err = cel.NewEnv(cel.Lib(c))
	return env, err
}

func Eval(env *cel.Env, expression string, params map[string]any) (ref.Val, error) {
	ast, iss := env.Compile(expression)
	if iss.Err() != nil {
		slog.Info(fmt.Sprintf("cel env.Compile err, %s", iss.Err().Error()))
		return nil, iss.Err()
	}
	prg, err := env.Program(ast)
	if err != nil {
		slog.Info(fmt.Sprintf("cel env.Program err, %s", err.Error()))
		return nil, err
	}
	out, _, err := prg.Eval(params)
	if err != nil {
		if err.Error() == "no such key: 1" {
			return out, nil
		}
		slog.Info(fmt.Sprintf("cel prg.Eval err, %s", err.Error()))
		return nil, err
	}
	return out, nil
}

func (c *CustomLib) WriteRuleSetOptions(args yaml.MapSlice) {
	for _, v := range args {
		key, _ := v.Key.(string)
		value := v.Value

		var d *exprpb.Decl
		switch vv := value.(type) {
		case int64:
			d = decls.NewVar(key, decls.Int)
		case string:
			if strings.HasPrefix(vv, "newReverse") {
				d = decls.NewVar(key, decls.NewObjectType("proto.Reverse"))
			} else if strings.HasPrefix(vv, "newOOB") {
				d = decls.NewVar(key, decls.NewObjectType("proto.OOB"))
			} else if strings.HasPrefix(vv, "randomInt") {
				d = decls.NewVar(key, decls.Int)
			} else {
				d = decls.NewVar(key, decls.String)
			}
		case map[string]string:
			d = decls.NewVar(key, decls.NewMapType(decls.String, decls.String))
		default:
			d = decls.NewVar(key, decls.String)
		}
		c.envOptions = append(c.envOptions, cel.Declarations(d))
	}
}

func (c *CustomLib) WriteRuleFunctionsROptions(funcName string, returnBool bool) {
	c.envOptions = append(c.envOptions, cel.Function(funcName, cel.Overload(funcName, []*cel.Type{}, cel.BoolType,
		cel.FunctionBinding(func(values ...ref.Val) ref.Val {
			return types.Bool(returnBool)
		}))),
	)
}

func (c *CustomLib) UpdateCompileOption(k string, t *exprpb.Type) {
	c.envOptions = append(c.envOptions, cel.Declarations(decls.NewVar(k, t)))
}

func (c *CustomLib) Reset() {
	*c = CustomLib{}
}
