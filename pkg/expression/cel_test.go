package expression_test

import (
	"bytes"
	_ "embed"
	"encoding/json"
	"io"
	"net/http"
	"testing"
	"time"

	"github.acme.red/intelli-sec/npoc/pkg/expression"
	"github.acme.red/intelli-sec/npoc/pkg/expression/proto"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.com/dlclark/regexp2"
	"github.com/google/cel-go/checker/decls"
	"github.com/google/cel-go/common/types"
	"github.com/rs/xid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

//go:embed test_data/example-resp.txt
var exampleResp string

//go:embed test_data/fingerprint-yaml-little.yml
var benchFpLit []byte

//go:embed test_data/fingerprint-yaml-more-body.yml
var benchFpMoreBody []byte

type ExpressionTestSuite struct {
	suite.Suite
	lib         *expression.CustomLib
	variableMap map[string]any
}

func (suite *ExpressionTestSuite) SetupTest() {
	suite.lib = expression.NewCustomLib()
	suite.variableMap = make(map[string]any)
}

func (suite *ExpressionTestSuite) createCachedDoc(html []byte) (cachedDocJson []byte, cacheKey string) {
	cacheKey = utils.CacheKey("dummy", xid.New().String())
	_, err := utils.CachedGoqueryNewDocument(cacheKey, html)
	require.NoError(suite.T(), err)
	cachedDoc := &utils.CachedDoc{
		UseCache:  true,
		KeyOrBody: []byte(cacheKey),
	}
	cachedDocSer, err := json.Marshal(cachedDoc)
	require.NoError(suite.T(), err)
	return cachedDocSer, cacheKey
}

// TestScript tests script() function
func (suite *ExpressionTestSuite) TestScript() {
	html := []byte(`<!DOCTYPE html><html><script>a</script><script>b</script></html>`)
	exp := "script(html)"

	cachedDocSer, _ := suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	out, err := suite.lib.RunEval(exp, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BytesType)

	bs := make([][]byte, 0)
	err = json.Unmarshal(out.Value().([]byte), &bs)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), bs, [][]byte{[]byte("a"), []byte("b")})
}

func (suite *ExpressionTestSuite) TestAnyMatch() {
	html := []byte(`<!DOCTYPE html><html><script>AA-123213123-AA</script><script>b</script></html>`)
	exp := `"\\-\\d+\\-".anyMatch(script(html))`

	cachedDocSer, _ := suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	out, err := suite.lib.RunEval(exp, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BoolType)
	require.True(suite.T(), out.Value().(bool))

	html = []byte(`<!DOCTYPE html><html><script>AA</script><script>b</script></html>`)
	exp = `"\\-\\d+\\-".anyMatch(script(html))`

	cachedDocSer, _ = suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	out, err = suite.lib.RunEval(exp, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BoolType)
	require.False(suite.T(), out.Value().(bool))
}

// TestScriptSrc tests scriptSrc() function
func (suite *ExpressionTestSuite) TestScriptSrc() {
	html := []byte(`<!DOCTYPE html><html><script src="https://example.com/1.js">a</script><script>b</script></html>`)

	cachedDocSer, _ := suite.createCachedDoc(html)
	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("scriptSrc", func() {
		out, err := suite.lib.RunEval("scriptSrc(html)", suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BytesType)

		bs := make([][]byte, 0)
		err = json.Unmarshal(out.Value().([]byte), &bs)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), bs, [][]byte{[]byte("https://example.com/1.js")})
	})
	suite.Run("scriptSrc with anyMatch", func() {
		out, err := suite.lib.RunEval(`"example.+\\.js".anyMatch(scriptSrc(html))`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)

		out, err = suite.lib.RunEval(`"example\\w+\\.js".anyMatch(scriptSrc(html))`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
}

// TestDomExist tests domExist() function
func (suite *ExpressionTestSuite) TestDomExist() {
	html := []byte(`<html><script>a</script><li class="product"></li></html>`)
	cachedDocSer, _ := suite.createCachedDoc(html)
	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("domExist true", func() {
		out, err := suite.lib.RunEval(`".product".domExist(html)`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
	suite.Run("domExist false", func() {
		out, err := suite.lib.RunEval(`"a".domExist(html)`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
}

// TestDomTextMatch tests domExist() function
func (suite *ExpressionTestSuite) TestDomTextMatch() {
	html := `
<head>
<title>CAS – Central Authentication Service </title>
</head>
<body>
<div class="front">
  <div class="container">
    <h1>Welcome to Multiple Level CSS Example</h1>
    <p>This is an example of HTML with multiple levels of CSS styling.</p>
    <h2>Benefits of Multiple Level CSS</h2>
    <p>In this example, we demonstrate the use of multiple levels of CSS to style HTML elements accordingly.</p>
    <p class="important">Pay attention to the different styles applied to various elements, and how they cascade down to their child elements!</p>
  </div>
</div>
</body>`
	cachedDocSer, _ := suite.createCachedDoc([]byte(html))

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("true", func() {
		out, err := suite.lib.RunEval(`".front .container p".domTextMatch(html,"different")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
	suite.Run("false", func() {
		out, err := suite.lib.RunEval(`".front .container .important".domTextMatch(html, "welcome")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("real use case", func() {
		out, err := suite.lib.RunEval(`"head > title".domTextMatch(html, "CAS – (?:Central Authentication Service|Service Central d''Authentification)")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
}

// TestDomAttrMatch tests domExist() function
func (suite *ExpressionTestSuite) TestDomAttrMatch() {
	html := []byte(`
<body>
<div class="front">
  <div class="container">
    <h1>Welcome to Multiple Level CSS Example</h1>
    <p>This is an example of HTML with multiple levels of CSS styling.</p>
    <h2>Benefits of Multiple Level CSS</h2>
    <p>In this example, we demonstrate the use of multiple levels of CSS to style HTML elements accordingly.</p>
    <p class="important" xx="AA123AA">Pay attention to the different styles applied to various elements, and how they cascade down to their child elements!</p>
<link href=".myshopify.com"/>
  </div>
</div>
</body>`)
	cachedDocSer, _ := suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("true", func() {
		out, err := suite.lib.RunEval(`".important".domAttrMatch(html,"xx","\\d+")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
	suite.Run("false", func() {
		out, err := suite.lib.RunEval(`".important".domAttrMatch(html, "xx","none")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("real use case", func() {
		out, err := suite.lib.RunEval(`"link[href*='shopify.com']".domAttrMatch(html,"href",
      "(?:cdn\\.|\\.my)shopify\\.com")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
}

// TestDomAttr tests domExist() function
func (suite *ExpressionTestSuite) TestDomAttr() {
	html := []byte(`
<body>
<div class="front">
  <div class="container">
    <h1>Welcome to Multiple Level CSS Example</h1>
    <p>This is an example of HTML with multiple levels of CSS styling.</p>
    <h2>Benefits of Multiple Level CSS</h2>
    <p>In this example, we demonstrate the use of multiple levels of CSS to style HTML elements accordingly.</p>
    <p class="important" xx="AA123AA">Pay attention to the different styles applied to various elements, and how they cascade down to their child elements!</p>
<link href=".myshopify.com"/>
  </div>
</div>
</body>`)
	cachedDocSer, _ := suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("true", func() {
		out, err := suite.lib.RunEval(`".important".domAttr(html,"xx")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BytesType)
		require.Equal(suite.T(), out.Value().([]byte), []byte("AA123AA"))
	})
	suite.Run("false", func() {
		out, err := suite.lib.RunEval(`".important".domAttr(html, "none")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BytesType)
		require.Equal(suite.T(), out.Value().([]byte), []byte(""))
	})
	suite.Run("real use case", func() {
		out, err := suite.lib.RunEval(`"link[href*='shopify.com']".domAttr(html,"href")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BytesType)
		require.Equal(suite.T(), out.Value().([]byte), []byte(".myshopify.com"))
	})
}

func TestRegex(t *testing.T) {
	re, err := regexp2.Compile("(?:cdn\\.|\\.my)shopify\\.com", 0)
	require.NoError(t, err)
	b, _ := re.MatchString("cdn.shopify.com")
	require.Equal(t, true, b)
}

// TestMeta tests getMeta() function
func (suite *ExpressionTestSuite) TestMeta() {
	html := []byte(`<html>
<head>
  <meta charset="UTF-8">
  <meta name="description" content="Free Web tutorials">
  <meta name="keywords" content="HTML, CSS, JavaScript">
  <meta name="author" content="John Doe">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="shopify-checkout-api-token" content="xx">
</head>
</html>`)
	cachedDocSer, _ := suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("getMeta true", func() {
		out, err := suite.lib.RunEval(`html.getMeta("author")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.StringType)
		s, _ := out.Value().(string)
		require.Equal(suite.T(), "John Doe", s)
	})

	suite.Run("getMeta false", func() {
		out, err := suite.lib.RunEval(`html.getMeta("none")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.StringType)
		require.Equal(suite.T(), out.Value().(string), "")
	})

	suite.Run("getMeta compare true", func() {
		out, err := suite.lib.RunEval(`html.getMeta("none")==""`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})

	suite.Run("getMeta compare false", func() {
		out, err := suite.lib.RunEval(`html.getMeta("none")!=""`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("getMeta real use case", func() {
		out, err := suite.lib.RunEval(`html.getMeta("shopify-checkout-api-token")!="" || html.getMeta("shopify-digital-wallet")!=""`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), true, out.Value().(bool))
	})
}

// TestGet tests Get() function
func (suite *ExpressionTestSuite) TestGet() {
	headers := map[string]string{
		"Server":       "Tengine",
		"Content-Type": "text/html",
	}
	suite.lib.UpdateCompileOption("headers", decls.NewMapType(decls.String, decls.String))
	suite.variableMap["headers"] = headers

	suite.Run("Get true", func() {
		out, err := suite.lib.RunEval(`headers.Get("Server")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.StringType)
		require.Equal(suite.T(), out.Value().(string), "Tengine")
	})
	suite.Run("Get false", func() {
		out, err := suite.lib.RunEval(`headers.Get("none")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.StringType)
		require.Equal(suite.T(), out.Value().(string), "")
	})
	suite.Run("Get ignore case true", func() {
		out, err := suite.lib.RunEval(`headers.Get("content-type")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.StringType)
		require.Equal(suite.T(), out.Value().(string), "text/html")
	})
}

// TestSquareBracket tests built-in []
func (suite *ExpressionTestSuite) TestSquareBracket() {
	headers := map[string]string{
		"Server":       "Tengine",
		"Content-Type": "text/html",
	}
	suite.lib.UpdateCompileOption("headers", decls.NewMapType(decls.String, decls.String))
	suite.variableMap["headers"] = headers

	suite.Run(" true", func() {
		out, err := suite.lib.RunEval(`headers["Server"]`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.StringType)
		require.Equal(suite.T(), out.Value().(string), "Tengine")
	})
	suite.Run(" false", func() {
		_, err := suite.lib.RunEval(`headers["server"]`, suite.variableMap)
		require.Error(suite.T(), err)
	})
}

// TestMatch tests the built-in match() function
func (suite *ExpressionTestSuite) TestMatch() {
	suite.Run("Match with i flag", func() {
		out, err := suite.lib.RunEval(`"(?i)server".matches("Server")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("Match false", func() {
		out, err := suite.lib.RunEval(`"server".matches("Server")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("Match test", func() {
		out, err := suite.lib.RunEval(`"657d894d594df4ad0e836371467adf86".matches("c758dea036133e583d03145d721bcf75")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
}

func (suite *ExpressionTestSuite) TestContains() {
	suite.Run("contains false", func() {
		out, err := suite.lib.RunEval(`"c758dea036133e583d03145d721bcf75|".contains("657d894d594df4ad0e836371467adf86")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("contains true", func() {
		out, err := suite.lib.RunEval(`"657d894d594df4ad0e836371467adf86|".contains("657d894d594df4ad0e836371467adf86")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
}

func (suite *ExpressionTestSuite) TestIContains() {
	suite.Run("icontains false", func() {
		out, err := suite.lib.RunEval(`"c758dea036133e583d03145d721bcf75|".icontains("657d894d594df4ad0e836371467adf86")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), false)
	})
	suite.Run("icontains true", func() {
		out, err := suite.lib.RunEval(`"657d894d594df4ad0e836371467adf86|".icontains("657d894d594df4ad0e836371467adf86")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
	suite.Run("icontains true -2", func() {
		out, err := suite.lib.RunEval(`"657d894d594df4ad0e836371467ADF86".icontains("657d894d594df4ad0e836371467adf86")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BoolType)
		require.Equal(suite.T(), out.Value().(bool), true)
	})
}

func (suite *ExpressionTestSuite) TestBSubmatch() {
	suite.Run("bsubmatch", func() {
		out, err := suite.lib.RunEval(`"[A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+".bsubmatch(bytes("AA111AA BB222BB CC333CC"))`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), out.Value().(map[string]string), map[string]string{
			"1": "111",
			"2": "222",
			"3": "333",
		})
	})
}

func (suite *ExpressionTestSuite) TestIBSubmatch() {
	suite.Run("ibsubmatch", func() {
		out, err := suite.lib.RunEval(`"[a-z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+".ibsubmatch(bytes("AA111AA bb222BB cc333CC"))`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), out.Value().(map[string]string), map[string]string{
			"1": "111",
			"2": "222",
			"3": "333",
		})
	})
}

func (suite *ExpressionTestSuite) TestISubmatch() {
	suite.Run("isubmatch", func() {
		out, err := suite.lib.RunEval(`"[a-z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+".isubmatch("AA111AA bb222BB cc333CC")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), out.Value().(map[string]string), map[string]string{
			"1": "111",
			"2": "222",
			"3": "333",
		})
	})
}

func (suite *ExpressionTestSuite) TestSubmatch() {
	suite.Run("submatch", func() {
		out, err := suite.lib.RunEval(`"[a-z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+".submatch("AA111AA bb222BB cc333CC")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), out.Value().(map[string]string), map[string]string{})
	})
	suite.Run("submatch", func() {
		out, err := suite.lib.RunEval(`"[A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+ [A-Z]+(\\d+)[A-Z]+".submatch("AA111AA BB222BB CC333CC")`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), out.Value().(map[string]string), map[string]string{
			"1": "111",
			"2": "222",
			"3": "333",
		})
	})
}

// TestGetIconContent tests getIconContent() function
func (suite *ExpressionTestSuite) TestGetIconContent() {
	suite.Run("Get icon contents site1", func() {
		suite.RequestTest("https://www.lichoin.com/")
		out, err := suite.lib.RunEval(`response.getIconContent()`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BytesType)
	})
	suite.Run("Get icon contents site2", func() {
		suite.RequestTest("http://mail.li-zhou.com/")
		out, err := suite.lib.RunEval(`response.getIconContent()`, suite.variableMap)
		require.NoError(suite.T(), err)
		require.True(suite.T(), out.Type() == types.BytesType)
	})
}

func (suite *ExpressionTestSuite) TestConditionExpression() {
	regMatches := map[string]string{
		"1": "1.1.1",
	}
	suite.lib.UpdateCompileOption("m", decls.NewMapType(decls.String, decls.String))
	suite.variableMap["m"] = regMatches
	out, err := suite.lib.RunEval(`"1" in m ? true : false`, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BoolType)
	require.True(suite.T(), out.Value().(bool))

	out, err = suite.lib.RunEval(`"2" in m ? true : false`, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BoolType)
	require.False(suite.T(), out.Value().(bool))
}

func (suite *ExpressionTestSuite) TestJoin() {
	bs := [][]byte{
		[]byte("title"),
		[]byte("content"),
		[]byte("footer"),
	}
	html := []byte(`<!DOCTYPE html><html><script>a</script><script>b</script></html>`)

	cachedDocSer, _ := suite.createCachedDoc(html)
	con, err := json.Marshal(bs)
	require.NoError(suite.T(), err)
	suite.lib.UpdateCompileOption("bs", decls.Bytes)
	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["bs"] = con
	suite.variableMap["html"] = cachedDocSer
	out, err := suite.lib.RunEval(`"\n".join(bs)`, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BytesType)
	require.Equal(suite.T(), bytes.Join(bs, []byte("\n")), out.Value().([]byte))

	out, err = suite.lib.RunEval(`"\n".join(script(html))`, suite.variableMap)
	require.NoError(suite.T(), err)
	require.True(suite.T(), out.Type() == types.BytesType)
	require.Equal(suite.T(), []byte("a\nb"), out.Value().([]byte))
}

func (suite *ExpressionTestSuite) TestDomText() {
	html := []byte(`
<body>
<div class="front">
  <div class="container">
    <h1>Welcome to Multiple Level CSS Example</h1>
    <p>This is an example of HTML with multiple levels of CSS styling.</p>
    <h2>Benefits of Multiple Level CSS</h2>
    <p>In this example, we demonstrate the use of multiple levels of CSS to style HTML elements accordingly.</p>
    <p class="important">Pay attention to the different styles applied to various elements, and how they cascade down to their child elements!</p>
  </div>
</div>
</body>`)
	cachedDocSer, _ := suite.createCachedDoc(html)

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer
	out, err := suite.lib.RunEval(`"p".domText(html)`, suite.variableMap)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), []byte(`This is an example of HTML with multiple levels of CSS styling.
In this example, we demonstrate the use of multiple levels of CSS to style HTML elements accordingly.
Pay attention to the different styles applied to various elements, and how they cascade down to their child elements!`), out.Value().([]byte))
}

func (suite *ExpressionTestSuite) TestTmp() {
	html := `<html><body><link href="*******.animate.min.css"/></body></html>`
	cachedDocSer, _ := suite.createCachedDoc([]byte(html))

	suite.lib.UpdateCompileOption("html", decls.Bytes)
	suite.variableMap["html"] = cachedDocSer

	suite.Run("Get true", func() {
		out, err := suite.lib.RunEval(`"([\\d\\.]{1,250})?animate\\.min\\.css".bsubmatch("link[href*='animate']".domAttr(html,"href"))`, suite.variableMap)
		require.NoError(suite.T(), err)
		// require.Equal(suite.T(), out.Type(), StrStrMapType)
		require.Equal(suite.T(), out.Value().(map[string]string), map[string]string{"1": "*******."})
	})
}

func TestExpressionTestSuite(t *testing.T) {
	suite.Run(t, new(ExpressionTestSuite))
}

func (suite *ExpressionTestSuite) RequestTest(url string) {
	rawHttp, err := http.NewRequest("GET", url, nil)
	require.NoError(suite.T(), err)
	v, err := httpv.NewNHTTPClient("", true, time.Duration(10)*time.Second)
	require.NoError(suite.T(), err)
	resp, err := v.Do(rawHttp)
	require.NoError(suite.T(), err)
	data, err := io.ReadAll(resp.Body)
	require.NoError(suite.T(), err)
	protoResp := &proto.Response{Body: data, Url: "https://www.lichoin.com/"}
	suite.variableMap["response"] = protoResp
}

// // Run benchmark on specified template and response
// func RunBenchmark(b *testing.B, respStr string, templateB []byte) {
// 	s := new(ExpressionTestSuite)
// 	s.SetT(&testing.T{})
// 	s.SetupTest()
// 	// load response
// 	resp, err := proto.String2Response(respStr)
// 	require.NoError(b, err)
// 	resp.Body, err = io.ReadAll(io.LimitReader(bytes.NewBuffer(resp.Body), 1024*1024))
// 	require.NoError(b, err)
// 	celEnv, err := s.lib.NewCelEnv()
// 	require.NoError(b, err)
// 	// create template
// 	var tp *template.Template
// 	err = yaml.Unmarshal(templateB, &tp)
// 	require.NoError(b, err)
// 	for i := range tp.Rules {
// 		err = tp.Rules[i].Value.Compile(celEnv)
// 		require.NoError(s.T(), err)
// 	}
// 	cacheKey := utils.CacheKey("dummy", "dummy")
// 	cachedDocSer, err := json.Marshal(&utils.CachedDoc{
// 		UseCache:  true,
// 		KeyOrBody: []byte(cacheKey),
// 	})
// 	require.NoError(b, err)
// 	resp.CachedDoc = cachedDocSer
// 	doc, err := utils.CachedGoqueryNewDocument(cacheKey, resp.Body)
// 	require.NoError(b, err)
// 	resp.ScriptSrc, err = template.ExtractScriptSrc(doc)
// 	require.NoError(b, err)
// 	s.variableMap["response"] = resp
// 	b.ResetTimer()
// 	iter := 0
// 	ruleCtn := 0
// 	for i := 0; i < b.N; i++ {
// 		for _, rule := range tp.Rules {
// 			b.StartTimer()
// 			res, _, err := rule.Value.ExprProgram.Eval(s.variableMap)
// 			b.StopTimer()
// 			require.NoError(b, err, "eval error")
// 			_, ok := res.Value().(bool)
// 			require.True(b, ok, "eval result should be bool")
// 			ruleCtn++
// 		}
// 		iter++
// 	}
// 	// b.Logf("iteration number: %d\nrule number: %d\n", iter, ruleCtn)
// }
//
// // go test -bench=BenchmarkWappalyze_Fingerprint_Expr -run ^# -benchmem -benchtime=1000x -count=10 | tee cel.bench
// func BenchmarkWappalyze_Fingerprint_Expr_Little(b *testing.B) {
// 	RunBenchmark(b, exampleResp, benchFpLit)
// }
//
// func BenchmarkWappalyze_Fingerprint_Expr_More_Body(b *testing.B) {
// 	RunBenchmark(b, exampleResp, benchFpMoreBody)
// }
