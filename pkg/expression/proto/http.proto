syntax = "proto3";
package proto;
option go_package = "./;proto";

// https://docs.xray.cool/#/guide/poc/v2?id=expression%e7%bc%96%e5%86%99
// 编译命令：protoc --go_out=./  ./http.proto

// request 扫描请求
message Request {
  string url = 1;
  string title_string = 2; // title标签字段
  string method = 3; // request.method(string)原始请求的方法
  map<string, string> headers =
      4; // request.headers(map[string]string)原始请求的HTTP头，是一个键值对（均为小写），我们可以通过headers['server']来获取值。如果键不存在，则获取到的值是空字符串。注意，该空字符串不能用于
  // == 以外的操作，否则不存在的时候将报错，需要先 in
  // 判断下。详情参考下文常用函数章节。
  string content_type =
      5; // request.contnet_type(string)原始请求的 content-type 头的值,
  // 等于request.headers["Content-Type"]
  bytes body =
      6; // request.body([]byte)原始请求的
  // body，需要使用字节流相关方法来判断。如果是 GET， body 为空。
  string body_string = 7; // string化的request.body
  bytes raw = 8;        // request.raw([]byte)原始请求
  bytes raw_header = 9; // request.raw_header([]byte)原始的 header
  // 部分，需要使用字节流相关方法来判断。
}

// response 请求的响应，通用属性包含：raw
message Response {
  string url = 1;
  string title_string = 2; // title标签字段
  int32 status = 3; // response.status(int)返回包的satus code
  map<string, string> headers = 4; // response.headers(map[string]string)返回包的HTTP头，类似 request.headers。
  string content_type = 5; // response.content_type(string)返回包的content-type头的值
  bytes body = 6; // response.body([]byte)返回包的Body，因为是一个字节流（bytes）而非字符串，后面判断的时候需要使用字节流相关的方法
  string body_string = 7; // string化的response.body
  bytes raw = 8;         // response.raw([]byte)原始响应
  bytes raw_header = 9;  // response.raw_header([]byte)原始的 header 部分，需要使用字节流相关方法来判断。
  bytes favicon_content = 10;  // 网站图标数据
  int32 favicon_hash = 11;     // 计算出的图标哈希值，用于指纹匹配
  bytes script_src = 12; // script标签中的src字段，json序列化格式
  bytes cached_doc = 13; // goquery.Document缓存，json序列化格式
}