package pair

import (
	"encoding/json"
	"fmt"
)

// Pair 二元元组
type Pair[H, T any] struct {
	head H
	tail T
}

func NewPair[H, T any](h H, t T) Pair[H, T] {
	return Pair[H, T]{h, t}
}

func (p Pair[H, T]) Tail() T {
	return p.tail
}

func (p Pair[H, T]) Head() H {
	return p.head
}

func (p Pair[H, T]) String() string {
	return fmt.Sprintf("(%v, %v)", p.head, p.tail)
}

func (p Pair[H, T]) MarshalJSON() ([]byte, error) {
	return json.Marshal([]any{p.Head(), p.Tail()})
}
