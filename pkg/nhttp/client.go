package nhttp

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"time"

	"github.com/imroc/req/v3"
)

//nolint:mnd // 需要
func NewDefaultClient() *Client {
	cli := &Client{
		MimicRandomUserAgent: true,
		HTTPClient: &http.Client{
			Transport: &http.Transport{
				DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
					conn, err := (&net.Dialer{
						Timeout:   10 * time.Second,
						KeepAlive: 30 * time.Second,
					}).DialContext(ctx, network, addr)
					if err != nil {
						return nil, fmt.Errorf("net dail: %w", err)
					}

					return conn, nil
				},
				ForceAttemptHTTP2:     true,
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   10,
				IdleConnTimeout:       40 * time.Second,
				TLSHandshakeTimeout:   10 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true, //nolint:gosec // 需要
				},
			},
			Timeout: time.Second * 15,
		},
	}

	impersonateChrome := req.C().ImpersonateChrome()

	httpTransport := cli.HTTPClient.Transport.(*http.Transport) //nolint:errcheck // 忽略
	impersonateChrome.Transport.SetTLSClientConfig(httpTransport.TLSClientConfig)
	impersonateChrome.Transport.DialContext = httpTransport.DialContext
	impersonateChrome.Transport.MaxIdleConns = httpTransport.MaxIdleConns
	impersonateChrome.Transport.IdleConnTimeout = httpTransport.IdleConnTimeout
	impersonateChrome.Transport.TLSHandshakeTimeout = httpTransport.TLSHandshakeTimeout
	impersonateChrome.ExpectContinueTimeout = httpTransport.ExpectContinueTimeout

	cli.impersonateChromeClient = impersonateChrome

	return cli
}

type Client struct {
	MimicRandomUserAgent    bool // 使用随机值覆写 User-Agent。如果请求没有 User-Agent 头，将添加一个。
	MimicChrome             bool
	impersonateChromeClient *req.Client
	HTTPClient              *http.Client
}

func (c *Client) preHttpRequest(req *http.Request) {
	if c.MimicRandomUserAgent {
		req.Header.Set("User-Agent", GetRandomUserAgent())
	}
}

func (c *Client) Do(req *http.Request) (*http.Response, error) {
	c.preHttpRequest(req)

	httpClient := *c.HTTPClient
	if c.MimicChrome {
		httpClient.Transport = c.impersonateChromeClient.Transport
	}
	httpClient.CheckRedirect = func(_ *http.Request, via []*http.Request) error {
		if len(via) >= 4 {
			// 达到最大重定向次数，不再跟随，直接返回最后一个响应
			return http.ErrUseLastResponse
		}
		// 否则继续跟随
		return nil
	}

	return httpClient.Do(req)
}

func (c *Client) Get(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}
	return c.Do(req)
}

func (c *Client) DoWithoutAutoRedirect(req *http.Request) (*http.Response, error) {
	c.preHttpRequest(req)

	httpClient := *c.HTTPClient
	httpClient.CheckRedirect = func(_ *http.Request, _ []*http.Request) error {
		return http.ErrUseLastResponse
	}
	if c.MimicChrome {
		httpClient.Transport = c.impersonateChromeClient.Transport
	}

	return httpClient.Do(req)
}

func (c *Client) GetWithoutAutoRedirect(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	return c.DoWithoutAutoRedirect(req)
}

func (c *Client) Post(ctx context.Context, url, contentType string, body io.Reader) (resp *http.Response, err error) {
	req, err := http.NewRequestWithContext(ctx, "POST", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", contentType)
	return c.Do(req)
}
