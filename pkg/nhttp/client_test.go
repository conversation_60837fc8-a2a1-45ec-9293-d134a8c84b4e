package nhttp

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestClient_Get(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			t.<PERSON>rf("Expected GET request, got %s", r.Method)
		}
		w.<PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
		w.Write([]byte("Hello, World!"))
	}))
	defer server.Close()

	// 创建客户端
	client := NewDefaultClient()

	// 测试Get方法
	ctx := context.Background()
	resp, err := client.Get(ctx, server.URL)
	if err != nil {
		t.Fatalf("Get request failed: %v", err)
	}
	defer resp.Body.Close()

	// 验证响应状态码
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	// 验证响应内容
	body := make([]byte, 13)
	n, err := resp.Body.Read(body)
	if err != nil && err.Error() != "EOF" {
		t.Fatalf("Failed to read response body: %v", err)
	}

	expected := "Hello, World!"
	if string(body[:n]) != expected {
		t.Errorf("Expected response body %q, got %q", expected, string(body[:n]))
	}
}

func TestClient_Get_InvalidURL(t *testing.T) {
	client := NewDefaultClient()
	ctx := context.Background()

	// 测试无效URL
	_, err := client.Get(ctx, "invalid-url")
	if err == nil {
		t.Error("Expected error for invalid URL, got nil")
	}
}

func TestClient_Get_WithTimeout(t *testing.T) {
	// 创建一个慢响应的服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Slow response"))
	}))
	defer server.Close()

	client := NewDefaultClient()

	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	// 测试超时情况
	_, err := client.Get(ctx, server.URL)
	if err == nil {
		t.Error("Expected timeout error, got nil")
	}
}

func TestClient_Get_MaxRedirects(t *testing.T) {
	redirectCount := 0

	// 创建一个会无限重定向的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		redirectCount++
		t.Logf("Redirect count: %d, Path: %s", redirectCount, r.URL.Path)

		// 创建重定向到不同路径，避免浏览器缓存
		nextPath := fmt.Sprintf("/redirect%d", redirectCount)
		w.Header().Set("Location", nextPath)
		w.WriteHeader(http.StatusFound) // 302重定向
	}))
	defer server.Close()

	client := NewDefaultClient()
	ctx := context.Background()

	// 测试Get方法的重定向限制
	resp, err := client.Get(ctx, server.URL)
	if err != nil {
		t.Fatalf("Get request failed: %v", err)
	}
	defer resp.Body.Close()

	// 验证最大重定向次数为4次（即允许3次跳转后停止）
	// 根据代码中的逻辑：if len(via) >= 4，意味着第4次重定向时会停止
	if redirectCount != 4 {
		t.Errorf("Expected exactly 4 redirects (3 jumps + 1 final), got %d", redirectCount)
	}

	// 验证最终响应是重定向响应
	if resp.StatusCode != http.StatusFound {
		t.Errorf("Expected final status code %d (Found), got %d", http.StatusFound, resp.StatusCode)
	}

	// 验证Location头存在
	location := resp.Header.Get("Location")
	if location == "" {
		t.Error("Expected Location header in final redirect response, got empty")
	}
}

func TestClient_Get_SuccessfulRedirect(t *testing.T) {
	redirectCount := 0

	// 创建一个有限重定向的测试服务器（2次重定向后返回正常响应）
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		redirectCount++

		if redirectCount <= 2 {
			// 前2次请求进行重定向
			nextPath := fmt.Sprintf("/redirect%d", redirectCount)
			w.Header().Set("Location", nextPath)
			w.WriteHeader(http.StatusFound)
		} else {
			// 第3次请求返回正常响应
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("Final response"))
		}
	}))
	defer server.Close()

	client := NewDefaultClient()
	ctx := context.Background()

	// 测试正常的重定向流程
	resp, err := client.Get(ctx, server.URL)
	if err != nil {
		t.Fatalf("Get request failed: %v", err)
	}
	defer resp.Body.Close()

	// 验证经过了3次请求（2次重定向 + 1次最终响应）
	if redirectCount != 3 {
		t.Errorf("Expected 3 total requests (2 redirects + 1 final), got %d", redirectCount)
	}

	// 验证最终响应状态码
	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected final status code %d (OK), got %d", http.StatusOK, resp.StatusCode)
	}

	// 验证响应内容
	body := make([]byte, 14)
	n, err := resp.Body.Read(body)
	if err != nil && err.Error() != "EOF" {
		t.Fatalf("Failed to read response body: %v", err)
	}

	expected := "Final response"
	if string(body[:n]) != expected {
		t.Errorf("Expected response body %q, got %q", expected, string(body[:n]))
	}
}
