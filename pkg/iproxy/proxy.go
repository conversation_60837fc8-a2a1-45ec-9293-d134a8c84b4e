package iproxy

import (
	"context"
	"errors"
	"net"
	"net/url"

	"golang.org/x/net/proxy"
)

func NewProxyDialCtx(u *url.URL) (*Dialer, error) {
	if u == nil {
		return &Dialer{
			dialer: &net.Dialer{},
		}, nil
	}
	proxyDial, err := proxy.FromURL(u, proxy.Direct)
	if err != nil {
		return nil, err
	}
	return &Dialer{
		dialer: proxyDial,
	}, nil
}

type Dialer struct {
	dialer proxy.Dialer
}

func (pd *Dialer) DialContext(ctx context.Context, network, address string) (net.Conn, error) {
	if contextDialer, ok := pd.dialer.(proxy.ContextDialer); ok {
		return contextDialer.DialContext(ctx, network, address)
	} else {
		return nil, errors.New("failed type assertion to DialContext")
	}
}

func (pd *Dialer) Dial(network, address string) (net.Conn, error) {
	return pd.dialer.Dial(network, address)
}
