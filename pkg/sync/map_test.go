package sync

import (
	"flag"
	"math/rand"
	"os"
	"reflect"
	"sync"
	"testing"
)

// example command: go test -bench=. -benchmem ./pkg/sync -read 10 -write 3

const (
	elementNumber = 1 << 10
	keysRange     = elementNumber << 1
)

var (
	syncMap         = &sync.Map{}
	rwMutexMap      = NewRWMutexMap[int, int](elementNumber)
	mutexMap        = NewMutexMap[int, int](elementNumber)
	genericsSyncMap = &Map[int, int]{}

	readFrequency, writeFrequency, rwFrequency int
)

func TestMain(m *testing.M) {
	// 解析命令行参数
	flag.IntVar(&readFrequency, "read", 10, "read frequency")
	flag.IntVar(&writeFrequency, "write", 2, "write frequency")
	flag.Parse()
	rwFrequency = readFrequency + writeFrequency

	// build maps
	for i := range elementNumber {
		syncMap.Store(i, i)
		rwMutexMap.Store(i, i)
		mutexMap.Store(i, i)
		genericsSyncMap.Store(i, i)
	}

	// 运行测试
	os.Exit(m.Run())
}

func BenchmarkSyncMap(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := rand.Intn(keysRange)
			if key%rwFrequency < writeFrequency {
				syncMap.Store(key, key)
				syncMap.Delete(rand.Intn(keysRange)) // 随机删除
				continue
			}
			v, ok := syncMap.Load(key)
			if !ok {
				continue
			}
			_, _ = v.(int)
		}
	})
}

func BenchmarkGenericsSyncMap(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := rand.Intn(keysRange)
			if key%rwFrequency < writeFrequency {
				genericsSyncMap.Store(key, key)
				genericsSyncMap.Delete(rand.Intn(keysRange))
				continue
			}
			_, _ = genericsSyncMap.Load(key)
		}
	})
}

func BenchmarkRWMutexMap(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := rand.Intn(keysRange)
			if key%rwFrequency < writeFrequency {
				rwMutexMap.Store(key, key)
				rwMutexMap.Delete(rand.Intn(keysRange))
				continue
			}
			_, _ = rwMutexMap.Load(key)
		}
	})
}

func BenchmarkMutexMap(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := rand.Intn(keysRange)
			if key%rwFrequency < writeFrequency {
				mutexMap.Store(key, key)
				mutexMap.Delete(rand.Intn(keysRange))
				continue
			}
			_, _ = mutexMap.Load(key)
		}
	})
}

type TestStruct struct {
	Field1 int
	Field2 string
}

func TestMap_LoadOrStore(t *testing.T) {
	type args[K comparable, V any] struct {
		key   K
		value V
	}
	type testCase[K comparable, V any] struct {
		name       string
		m          *Map[K, V]
		args       args[K, V]
		wantActual V
		wantLoaded bool
	}
	tests := []testCase[int, *TestStruct]{
		{
			name:       "Test LoadOrStore",
			m:          &Map[int, *TestStruct]{},
			args:       args[int, *TestStruct]{key: 1, value: &TestStruct{Field1: 1, Field2: "1"}},
			wantActual: &TestStruct{Field1: 1, Field2: "1"},
			wantLoaded: false,
		}, {
			name: "Test LoadOrStore",
			m: func() *Map[int, *TestStruct] {
				m := &Map[int, *TestStruct]{}
				m.Store(1, &TestStruct{Field1: 1, Field2: "1"})
				return m
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotActual, gotLoaded := tt.m.LoadOrStore(tt.args.key, tt.args.value)
			if !reflect.DeepEqual(gotActual, tt.wantActual) {
				t.Errorf("LoadOrStore() gotActual = %v, want %v", gotActual, tt.wantActual)
			}
			if gotLoaded != tt.wantLoaded {
				t.Errorf("LoadOrStore() gotLoaded = %v, want %v", gotLoaded, tt.wantLoaded)
			}
		})
	}
}
