package sync

import "sync"

type WaitGroup struct {
	ErrHandle func(any)
	wg        sync.WaitGroup
}

func (g *WaitGroup) Go(f func()) {
	g.wg.Add(1)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				if g.<PERSON>rr<PERSON><PERSON><PERSON> == nil {
					panic(err)
				}
				g.<PERSON><PERSON><PERSON>(err)
			}
		}()
		defer g.wg.Done()
		f()
	}()
}

func (g *WaitGroup) Wait() {
	g.wg.Wait()
}
