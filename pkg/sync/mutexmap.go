package sync

import "sync"

type MutexMap[K comparable, V any] struct {
	m  map[K]V
	mu sync.Mutex
}

func NewMutexMap[K comparable, V any](size int) *MutexMap[K, V] {
	return &MutexMap[K, V]{m: make(map[K]V, size)}
}

func (m *MutexMap[K, V]) Load(key K) (value V, ok bool) {
	m.mu.Lock()
	value, ok = m.m[key]
	m.mu.Unlock()
	return value, ok
}

func (m *MutexMap[K, V]) Store(key K, value V) {
	m.mu.Lock()
	m.m[key] = value
	m.mu.Unlock()
}

func (m *MutexMap[K, V]) LoadOrStore(key K, value V) (actual V, loaded bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if v, ok := m.m[key]; ok {
		return v, true
	}
	m.m[key] = value
	return value, false
}

func (m *MutexMap[K, V]) LoadAndDelete(key K) (value V, loaded bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if v, ok := m.m[key]; ok {
		delete(m.m, key)
		return v, true
	}
	return value, false
}

func (m *MutexMap[K, V]) Delete(key K) {
	m.mu.Lock()
	delete(m.m, key)
	m.mu.Unlock()
}

func (m *MutexMap[K, V]) Swap(key K, value V) (previous V, loaded bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if v, ok := m.m[key]; ok {
		m.m[key] = value
		return v, true
	}
	m.m[key] = value
	return value, false
}

func (m *MutexMap[K, V]) Len() int {
	m.mu.Lock()
	defer m.mu.Unlock()
	return len(m.m)
}

func (m *MutexMap[K, V]) Range(f func(key K, value V) (_continue bool)) {
	m.mu.Lock()
	for k, v := range m.m {
		if !f(k, v) {
			break
		}
	}
	m.mu.Unlock()
}

func (m *MutexMap[K, V]) Clear() {
	m.mu.Lock()
	m.m = make(map[K]V, len(m.m))
	m.mu.Unlock()
}
