import subprocess
import re
import matplotlib.pyplot as plt


def run_go_benchmarks():
    results = []
    for i in range(21):
        result = subprocess.run(
            [
                "go",
                "test",
                "-bench=.",
                "-benchmem",
                "./pkg/sync",
                "-read",
                "10",
                "-write",
                str(i),
            ],
            capture_output=True,
            text=True,
        )
        results.append(result.stdout)
    return results


def parse_benchmark_data(benchmark_outputs):
    write_ratios = []
    sync_map = []
    generics_sync_map = []
    rw_mutex_map = []
    mutex_map = []

    # 正则表达式匹配
    pattern = re.compile(r"Benchmark(\w+)-\d+\s+(\d+)\s+(\d+\.\d+)\sns/op")

    for i, output in enumerate(benchmark_outputs):
        for line in output.split("\n"):
            match = pattern.match(line)
            if match:
                name = match.group(1)
                ns_op = float(match.group(3))

                if name == "SyncMap":
                    sync_map.append(ns_op)
                elif name == "GenericsSyncMap":
                    generics_sync_map.append(ns_op)
                elif name == "RWMutexMap":
                    rw_mutex_map.append(ns_op)
                elif name == "MutexMap":
                    mutex_map.append(ns_op)
                if name == "SyncMap":
                    write_ratios.append(i)

    return write_ratios, sync_map, generics_sync_map, rw_mutex_map, mutex_map


def plot_execution_speed(
    write_ratios, sync_map, generics_sync_map, rw_mutex_map, mutex_map
):
    # 绘制折线图
    plt.figure(figsize=(12, 8))
    plt.plot(write_ratios, sync_map, marker="o", label="SyncMap")
    plt.plot(write_ratios, generics_sync_map, marker="o", label="GenericsSyncMap")
    plt.plot(write_ratios, rw_mutex_map, marker="o", label="RWMutexMap")
    plt.plot(write_ratios, mutex_map, marker="o", label="MutexMap")

    # 添加标题和标签
    plt.title("Execution Speed vs. Write Ratio")
    plt.xlabel("Write Ratio")
    plt.ylabel("Execution Speed (ns/op)")
    plt.legend()
    plt.grid(True)
    plt.show()


# 运行 Go benchmarks
benchmark_outputs = run_go_benchmarks()

# 解析数据
write_ratios, sync_map, generics_sync_map, rw_mutex_map, mutex_map = (
    parse_benchmark_data(benchmark_outputs)
)

# 生成图表
plot_execution_speed(write_ratios, sync_map, generics_sync_map, rw_mutex_map, mutex_map)
