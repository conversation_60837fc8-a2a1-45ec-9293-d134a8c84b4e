package httpv

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"mime"
	"mime/multipart"
	"net/url"
	"strings"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

const (
	ParamPositionBody     = "body"
	ParamPositionURLQuery = "urlQuery"
	ParamPositionHeader   = "Header"
	ParamPositionCookie   = "cookie"
	ParamPositionPath     = "path"

	ParamTypeQuery  = "query"
	ParamTypePath   = "path"
	ParamTypeCookie = "cookie"
	ParamTypeHeader = "header"
	ParamTypeJson   = "json"
	// ParamTypeQueryJson 比如a={"id":"first"}这种为queryJson，只fuzz json内部的键对应的值，不对整体json做fuzz
	ParamTypeQueryJson = "queryJson"
	ParamTypeMultipart = "multipart"
)

type Param struct {
	Position       string
	Key            string
	Value          string
	ParamType      string
	Multipart      Multipart
	ValueIsJsonRaw bool
	OnlyKey        string
	Prefix         string
	Suffix         string
	PathParam      PathParam
	// 部分参数是不需要fuzz的
	NeedCheck bool
}

type PathParam struct {
	IsFilePath   bool
	TotalLayer   int
	CurrentLayer int
}

type Multipart struct {
	Filename    string
	ContentType string
	Content     []byte
}

func (req *Request) ParseParam() {
	if req.params == nil {
		req.params = make(map[string]Param)
	}
	req.ParseQueryParams(req.URL.RawQuery, ParamPositionURLQuery)
	err := req.ParseBody()
	if err != nil {
		slog.Error("Parse body error", slogext.Error(err))
	}
	req.ParsePathParams() // 默认不进行path解析，只有需要的时候才将path解析为参数
	req.MarkParamNoFazz()
}

// ParseBody 解析请求中body部位的参数
func (req *Request) ParseBody() error {
	if req.Method == MethodGet || len(req.Body) < 1 {
		return nil
	}
	contentType := req.Header.Get("Content-Type")
	if contentType == "" {
		contentType = ContentTypeText
	}
	mediaType, mediaParams, err := mime.ParseMediaType(contentType)
	if err != nil {
		return err
	}
	switch mediaType {
	case ContentTypeForm:
		req.ParseQueryParams(string(req.Body), ParamPositionBody)
	case ContentTypeJson:
		req.ParseJsonParams(string(req.Body), "", ParamPositionBody, ParamTypeJson)
	case contentTypeMultipart:
		boundary := mediaParams["boundary"]
		if boundary == "" {
			return nil
		}
		mp := multipart.NewReader(bytes.NewReader(req.Body), boundary)
		for {
			err = req.MultipartReader(mp)
			if err != nil {
				if errors.Is(err, io.EOF) {
					break
				}
			}
		}
	case ContentTypeText:
		var top100 []byte
		if len(req.Body) > 100 {
			top100 = req.Body[0:100]
		} else {
			top100 = req.Body
		}
		top100 = []byte(strings.TrimSpace(string(top100)))
		if len(top100) > 0 && top100[0] == '{' {
			req.Header.Set("Content-Type", ContentTypeJson)
			req.ParseJsonParams(string(req.Body), "", ParamPositionBody, ParamTypeJson)
		} else if bytes.Contains(top100, []byte{'='}) {
			req.Header.Set("Content-Type", ContentTypeForm)
			req.ParseQueryParams(string(req.Body), ParamPositionBody)
		} else {
			return nil
		}
	default:
		return nil
	}
	return nil
}

// MultipartReader 用于循环读取multipart表单，直到EOF
func (req *Request) MultipartReader(mp *multipart.Reader) error {
	part, err := mp.NextPart()
	if err != nil {
		return err
	}
	defer func(part *multipart.Part) {
		err := part.Close()
		if err != nil {
			return
		}
	}(part)
	param := Param{
		NeedCheck:      true,
		ValueIsJsonRaw: false,
	}
	var values map[string]string
	disposition := part.Header.Get("Content-Disposition")
	param.Multipart.ContentType, values, err = mime.ParseMediaType(disposition)
	if err != nil {
		return err
	}
	param.Key = values["name"]
	param.Position = ParamPositionBody
	param.ParamType = ParamTypeMultipart
	param.Multipart.Filename = values["filename"]
	param.Multipart.Content, _ = io.ReadAll(part)
	if param.Multipart.Filename == "" {
		param.Value = string(param.Multipart.Content)
	} else {
		// 有filename的参数，默认不需要fuzz其他漏洞，做文件上传的时候单独判断
		param.NeedCheck = false
	}
	randKey := funk.RandomString(8)
	param.OnlyKey = randKey
	req.params[randKey] = param
	return nil
}

func (req *Request) ParseQueryParams(query string, position string) {
	values, err := url.ParseQuery(query)
	if err != nil {
		processedQuery := smartSemicolonReplace(query)
		values, err = url.ParseQuery(processedQuery)
		if err != nil {
			return
		}
	}
	for k, vs := range values {
		for _, v := range vs {
			randKey := funk.RandomString(8)
			// 对于?a={"name": "fast"}这种格式的query进行特殊处理，后续在将参数方向拼接成请求的时候需要考虑。
			if len(v) != 0 && v[0] == '{' && v[len(v)-1] == '}' {
				req.ParseJsonParams(v, k, position, ParamTypeQueryJson)
			} else {
				param := Param{
					Position:  position,
					Key:       k,
					Value:     v,
					ParamType: ParamTypeQuery,
					Multipart: Multipart{},
					NeedCheck: true,
					OnlyKey:   randKey,
				}
				req.params[randKey] = param
			}
		}
	}
}

// ParsePathParams 路径传参的情况。
func (req *Request) ParsePathParams() {
	pathPart := strings.Split(req.URL.Path, "/")
	pathPart = funk.FilterString(pathPart, func(s string) bool { // 去除前后的无用数据。
		return s != ""
	})
	isFilePath := false
	if len(pathPart) == 0 || strings.Contains(pathPart[len(pathPart)-1], ".") { // 最后一级是具体的文件名，则一般不存在路径传参(/aaa/bbb/test.xxx)
		isFilePath = true
	}
	for i, value := range pathPart {
		randKey := funk.RandomString(8) //nolint:mnd // need indeed
		newPart := make([]string, len(pathPart))
		copy(newPart, pathPart)
		newPart[i] = "{{Value}}"
		key := "/" + strings.Join(newPart, "/") // /aaa/bbb/test.php aaa作为参数的时候key为 /{{value}}/bbb/test.php
		param := Param{
			Position:  ParamPositionPath,
			Key:       key,
			Value:     value,
			ParamType: ParamTypePath,
			Multipart: Multipart{},
			NeedCheck: true,
			OnlyKey:   randKey,
		}
		if isFilePath {
			param.PathParam.IsFilePath = true
			param.PathParam.TotalLayer = len(pathPart)
			param.PathParam.CurrentLayer = i
		}
		req.params[randKey] = param
	}
}

func (req *Request) ParseJsonParams(jsonRaw string, jsonKey string, paramPosition string, paramType string) {
	if jsonRaw == "" {
		return
	}
	h := gjson.Parse(jsonRaw)
	h.ForEach(func(key, value gjson.Result) bool {
		var newJsonKey string
		if jsonKey != "" {
			newJsonKey = fmt.Sprintf("%s.%s", jsonKey, value.Path(jsonRaw))
		} else {
			newJsonKey = value.Path(jsonRaw)
		}
		if value.IsObject() {
			// 当value值是json格式类型则循环解析其中的键值对
			req.ParseJsonParams(value.String(), newJsonKey, paramPosition, paramType)
		} else {
			isJsonRaw := true
			if value.Type == gjson.String || value.Type == gjson.Number {
				isJsonRaw = false
			}
			randKey := funk.RandomString(8)
			param := Param{
				Position:       paramPosition,
				Key:            newJsonKey,
				Value:          value.String(),
				NeedCheck:      !isJsonRaw,
				ValueIsJsonRaw: isJsonRaw,
				ParamType:      paramType,
				OnlyKey:        randKey,
			}
			req.params[randKey] = param
		}
		return true
	})
}

// GetCookieParams 单独获取cookie中的参数
func (req *Request) GetCookieParams() []Param {
	var params []Param
	cookies := req.GetCookies()
	for _, cookie := range cookies {
		randKey := funk.RandomString(8)
		param := Param{
			Position:       ParamPositionCookie,
			Key:            cookie.Name,
			Value:          cookie.Value,
			ParamType:      ParamTypeCookie,
			Multipart:      Multipart{},
			NeedCheck:      true,
			ValueIsJsonRaw: false,
			OnlyKey:        randKey,
		}
		if text.StrContainsSlice(param.Key, cookieParamPassKey) {
			param.NeedCheck = false
		}
		params = append(params, param)
	}
	return params
}

func (req *Request) GetHeaderParams(allowKeys ...string) (params []Param) {
	if len(allowKeys) == 0 {
		allowKeys = []string{"Referer", "X-Real-IP", "X-Forwarded-For"}
	}
	newHeader := req.Header.Clone()
	if newHeader.Get("Referer") == "" {
		newHeader.Set("Referer", req.URL.String())
	}
	for _, key := range allowKeys {
		for _, value := range newHeader[key] {
			randKey := funk.RandomString(8)
			param := Param{
				Position:       ParamPositionHeader,
				Key:            key,
				Value:          value,
				ParamType:      ParamTypeHeader,
				Multipart:      Multipart{},
				NeedCheck:      true,
				ValueIsJsonRaw: false,
				OnlyKey:        randKey,
			}
			params = append(params, param)
		}
	}
	return params
}

// GetAllParams 额外获取cookie和header参数
func (req *Request) GetAllParams(allowKeys ...string) {
	cookieParams := req.GetCookieParams()
	for _, param := range cookieParams {
		req.params[param.OnlyKey] = param
	}
	headerParams := req.GetHeaderParams(allowKeys...)
	for _, param := range headerParams {
		req.params[param.OnlyKey] = param
	}
}

// 智能分号处理函数
func smartSemicolonReplace(query string) string {
	if !strings.Contains(query, ";") {
		return query
	}

	result := ""
	i := 0
	inValue := false

	for i < len(query) {
		char := query[i]

		switch char {
		case '=':
			inValue = true
			result += string(char)
		case '&':
			inValue = false
			result += string(char)
		case ';':
			if inValue {
				// 在值中的分号，需要进一步判断
				if i+1 < len(query) && isNextParamStart(query[i+1:]) {
					// 这是参数分隔符，替换为&
					result += "&"
					inValue = false
				} else {
					// 这是值内的分号，进行URL编码
					result += "%3B"
				}
			} else {
				// 在键中的分号，替换为&
				result += "&"
			}
		default:
			result += string(char)
		}
		i++
	}

	return result
}

// 判断分号后是否为新参数的开始
func isNextParamStart(remaining string) bool {
	remaining = strings.TrimSpace(remaining)
	if len(remaining) == 0 {
		return false
	}

	// 查找第一个=号的位置
	equalIndex := strings.Index(remaining, "=")
	if equalIndex == -1 {
		return false
	}

	// 查找第一个&或;号的位置
	ampIndex := strings.Index(remaining, "&")
	nextSemiIndex := strings.Index(remaining, ";")

	// 找到最近的分隔符位置
	minSepIndex := -1
	if ampIndex != -1 && nextSemiIndex != -1 {
		minSepIndex = min(ampIndex, nextSemiIndex)
	} else if ampIndex != -1 {
		minSepIndex = ampIndex
	} else if nextSemiIndex != -1 {
		minSepIndex = nextSemiIndex
	}

	// 如果=号在任何分隔符之前（或者没有分隔符），并且键名有效，则这是一个新参数
	if minSepIndex == -1 || equalIndex < minSepIndex {
		keyPart := remaining[:equalIndex]
		return isValidKeyName(keyPart)
	}

	return false
}

// 检查是否是有效的键名
func isValidKeyName(key string) bool {
	if len(key) == 0 {
		return false
	}

	// 键名不应该包含这些字符
	invalidChars := " \"'{}"
	for _, char := range key {
		if strings.ContainsRune(invalidChars, char) {
			return false
		}
	}

	return true
}
