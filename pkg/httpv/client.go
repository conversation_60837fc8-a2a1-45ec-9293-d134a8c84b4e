package httpv

import (
	"bytes"
	"cmp"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"maps"
	"net"
	"net/http"
	"net/http/httptrace"
	"net/url"
	"slices"
	"sync/atomic"
	"time"

	oobClient "github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/dnslog/pkg/server"
	"github.com/hashicorp/golang-lru/v2/expirable"
	"github.com/projectdiscovery/ratelimit"
	"github.com/projectdiscovery/retryablehttp-go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.acme.red/intelli-sec/npoc/pkg/nhttp"

	"github.com/gorilla/websocket"

	"github.acme.red/intelli-sec/npoc/pkg/iproxy"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

var (
	labels                = []string{"www"}
	rdnsErrorTotalCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Namespace: "asm",
		Subsystem: "rdns_error",
		Name:      "rdns_error_total",
		Help:      "The total number of rdns error",
	}, labels)
	rdnsSuccessTotalCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Namespace: "asm",
		Subsystem: "rdns_success",
		Name:      "rdns_success_total",
		Help:      "The total number of rdns registration success",
	}, []string{})
)

// RequestFailErrorStr 请求失败报错字符串
const (
	RequestFailErrorStr      = `context deadline exceeded (Client.Timeout`
	TooManyFailERR           = `too many requests timeout, stop scan`
	RequestBodyMax           = 1024 * 1024
	OOBResponseHeaderTimeout = 10 * time.Second
	OOBTimeout               = time.Second * 10
	OOBRetryWaitMax          = 20 * time.Second
	OOBRetryWaitMin          = 1 * time.Second
	OOBRespReadLimit         = 4096
	OOBRetryMax              = 5
	defaultHostErrMaxNum     = 100
	defaultRetries           = 1
	defaultHostReqParallel   = 35
	defaultHttpTimeout       = 10
)

// rootPath 方便检查URL Path是否为根路径，内容为""和"/"
var rootPath = []string{"", "/", "/index.php", "/login", "/install", "/index.php"}

type Client struct {
	Raw             *nhttp.Client
	SPClient        *nhttp.Client // 特殊的客户端,禁止重定向
	RdnsClient      *oobClient.Client
	TCPClient       *TCPClient
	WebsocketClient *websocket.Dialer
	HTTPCache       *expirable.LRU[string, []httpFollow]
	HostLimiter     *ratelimit.Limiter // 每个站点每秒请求数量控制器
	HostErrMaxNum   int                // 允许每个主机http请求失败最大次数
	HostErrCounter  atomic.Int64       // 统计请求发送失败次数
	RequestCounter  atomic.Int64       // 统计当前任务所有请求数
	FailRetries     int                // 设置请求失败后自动重发次数
}

type ClientOpt struct {
	HostReqMaxParallel int    // 每秒钟的请求数量控制(默认60)
	Proxy              string // http/socks5代理配置，如果配置的是http代理则无法完成tcp请求的发包
	HostErrMaxNum      int    // 允许每个主机http请求失败最大次数
	FailRetries        int    // 设置请求失败后自动重发次数
	TimeoutSeconds     int    // 设置请求的超时 单位为秒
}

// npoc.HTTPFollow的替代品，npoc引用了httpv这个包没办法，
// httpv没办法引用npoc的结构体会造成循引用
type httpFollow struct {
	*Request
	*Response
}

func NewClient(ctx context.Context, opt ClientOpt) (*Client, error) {
	var (
		client = &Client{
			FailRetries:    cmp.Or(opt.FailRetries, defaultRetries),
			HostErrMaxNum:  cmp.Or(opt.HostErrMaxNum, defaultHostErrMaxNum),
			HTTPCache:      expirable.NewLRU[string, []httpFollow](0, nil, 0),                                               //nolint:mnd // 1
			HostLimiter:    ratelimit.New(ctx, uint(cmp.Or(opt.HostReqMaxParallel, defaultHostReqParallel)), 1*time.Second), //nolint:mnd //
			HostErrCounter: atomic.Int64{},
			RequestCounter: atomic.Int64{},
		}
		err error
	)
	if opt.TimeoutSeconds == 0 {
		opt.TimeoutSeconds = defaultHttpTimeout
	}
	client.Raw, err = NewNHTTPClient(opt.Proxy, true, time.Duration(opt.TimeoutSeconds)*time.Second)
	if err != nil {
		return nil, err
	}
	client.SPClient, err = NewNHTTPClient(opt.Proxy, false, time.Duration(opt.TimeoutSeconds)*time.Second)
	if err != nil {
		return nil, err
	}
	client.TCPClient, err = NewTCPClient(opt.Proxy, time.Duration(opt.TimeoutSeconds)*time.Second)
	if err != nil {
		return nil, err
	}
	return client, nil
}

func NewRdnsClient(opt oobClient.Options, proxy string) (*oobClient.Client, error) {
	var (
		rdnsClient *oobClient.Client
		err        error
	)
	// retry interval increases exponentially by retry number, i.e. 0s(immediately retry) for the first fail, 1s for the second, 2s, 4s, 8s...
	// but no larger than RetryWaitMax.
	retryableCliOpt := retryablehttp.Options{
		RetryWaitMin:    OOBRetryWaitMin,
		RetryWaitMax:    OOBRetryWaitMax,
		Timeout:         OOBTimeout,
		KillIdleConn:    false, // allow keep alive
		NoAdjustTimeout: true,  // timeout as-is
		RespReadLimit:   OOBRespReadLimit,
		RetryMax:        OOBRetryMax,
	}
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		ResponseHeaderTimeout: OOBResponseHeaderTimeout,
	}
	if proxy != "" {
		proxyUrl, err := url.Parse(proxy)
		if err != nil {
			return nil, err
		}
		tr.Proxy = http.ProxyURL(proxyUrl)
	}
	httpClient := &http.Client{
		Transport: tr,
	}
	retryableCliOpt.HttpClient = httpClient
	retryHTTPClient := retryablehttp.NewClient(retryableCliOpt)
	opt.HTTPClient = retryHTTPClient

	for range 3 {
		rdnsClient, err = oobClient.New(&opt)
		if err == nil {
			break
		}
	}
	if rdnsClient == nil {
		rdnsErrorTotalCounter.WithLabelValues("creat_rdns_error").Inc()
		return nil, fmt.Errorf("failed to create RDNS client, %w", err)
	} else {
		err = rdnsClient.StartPolling(3*time.Second, func(interaction *server.Interaction) {})
		if err != nil {
			rdnsErrorTotalCounter.WithLabelValues("start_rdns_error").Inc()
			return nil, fmt.Errorf("failed to start RDNS client polling, %w", err)
		}
		rdnsSuccessTotalCounter.WithLabelValues().Inc()
	}
	return rdnsClient, nil
}

func NewNHTTPClient(proxy string, allowRedirect bool, timeout time.Duration) (*nhttp.Client, error) {
	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			conn, err := (&net.Dialer{
				Timeout:   6 * time.Second,
				KeepAlive: 20 * time.Second,
			}).DialContext(ctx, network, addr)
			if err != nil {
				return nil, err
			}
			err = conn.SetDeadline(time.Now().Add(time.Minute))
			if err != nil {
				return nil, err
			}
			return conn, nil
		},
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   10,
		IdleConnTimeout:       40 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	}
	if proxy != "" {
		// 为http客户端设置代理, 代理可以是http也可以是socks5的
		proxyUrl, err := url.Parse(proxy)
		if err != nil {
			return nil, err
		} else {
			transport.Proxy = http.ProxyURL(proxyUrl)
		}
	}
	cl := &nhttp.Client{
		MimicRandomUserAgent: false,
		HTTPClient: &http.Client{
			Timeout:   timeout,
			Transport: transport,
		},
	}
	if !allowRedirect { // 禁止重定向
		cl.HTTPClient.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}
	} else { // 允许重定向，但是重定向最大次数默认设置为5(超过五次的重定向直接返回err)
		cl.HTTPClient.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			if len(via) >= 5 { // 假设我们限制重定向次数为5次
				return http.ErrUseLastResponse
			}
			return nil // 允许继续重定向
		}
	}
	return cl, nil
}

func NewWebSocketClient(proxy string, tlsConf *tls.Config, timeout time.Duration) (*websocket.Dialer, error) {
	dialer := &websocket.Dialer{
		HandshakeTimeout: timeout,
	}
	if len(proxy) != 0 {
		proxyURL, err := url.Parse(proxy)
		if err != nil {
			return nil, err
		}
		dialer.Proxy = http.ProxyURL(proxyURL)
	}
	if tlsConf != nil {
		// 加载指定证书和TLS配置
		dialer.TLSClientConfig = tlsConf
	}

	return dialer, nil
}

func NewTCPClient(proxy string, timeout time.Duration) (*TCPClient, error) {
	var (
		tcpClient = &TCPClient{
			Timeout: timeout,
		}
		proxyUrl *url.URL
		err      error
	)
	if proxy != "" {
		// 为http客户端设置代理
		proxyUrl, err = url.Parse(proxy)
		if err != nil {
			return nil, err
		}
	}
	tcpClient.Dialer, err = iproxy.NewProxyDialCtx(proxyUrl)
	if err != nil {
		return nil, err
	}
	return tcpClient, nil
}

func (c *Client) Do(ctx context.Context, req *Request) (*Response, error) {
	// 防止内部发包需要重定向的时候将req的url改变而影响外面req的使用
	newReq := req.Clone()
	failCount := 0
	// 失败重试机制
	for {
		resp, err := c.do(ctx, newReq)
		if err != nil {
			failCount++
			if failCount > c.FailRetries {
				return nil, err
			} else {
				continue
			}
		} else {
			return resp, nil
		}
	}
}

func RequestEqual(dst, src *Request) bool {
	// srcParams, dstParams := src.Params(), dst.Params()
	// if len(srcParams) != len(dstParams) {
	//	return false
	// }
	//
	// for k, v := range srcParams {
	//	dv, ok := dstParams[k]
	//	if !ok || v.Key != dv.Key || v.Value != dv.Value {
	//		return false
	//	}
	// }

	return maps.EqualFunc(dst.Header, src.Header, func(d, s []string) bool {
		return slices.Equal(d, s)
	})
}

func (c *Client) do(ctx context.Context, req *Request) (*Response, error) {
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}
	if exceeded, _ := c.FailExceededLimit(); exceeded {
		return nil, errors.New(TooManyFailERR)
	}
	c.HostLimiter.Take()
	select { // 上面会阻塞，这里判断一下阻塞结束是否是ctx退出导致的
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}
	if exceeded, _ := c.FailExceededLimit(); exceeded {
		return nil, errors.New(TooManyFailERR)
	}
	if len(req.UnsafeRawHTTP) > 0 && c.TCPClient != nil {
		c.RequestCounter.Add(1)
		// proxy, ok := c.Raw.HTTPClient.Transport.(*http.Transport)
		// if ok && proxy.Proxy != nil {
		// 	return nil, errors.New("unsafe request not supported proxy")
		// }
		return c.TCPClient.DoWithRequest(ctx, req)
	}
	follow := httpFollow{
		Request: req,
	}

	// 满足一定条件或者明确表明需要缓存的请求包
	if (req.Method == http.MethodGet && slices.Contains(rootPath, req.URL.Path) && req.URL.RawQuery == "") || req.Cache {
		uri := text.ToString(req.URL)
		data, ok := c.HTTPCache.Get(uri)
		if ok {
			for _, fw := range data {
				if RequestEqual(fw.Request, req) {
					return fw.Response, nil
				}
			}
		}

		defer func() {
			if follow.Response == nil {
				return
			}
			follows, _ := c.HTTPCache.Get(uri)
			if follows == nil {
				follows = make([]httpFollow, 0, 1)
			}
			c.HTTPCache.Add(uri, append(follows, follow))
		}()
	}
	c.RequestCounter.Add(1)
	timeTrace := &TimeTrace{}
	ctx = httptrace.WithClientTrace(ctx, timeTrace.ClientTrace())
	rawReq, err := http.NewRequestWithContext(ctx, req.Method, req.URL.String(), bytes.NewReader(req.Body))
	if err != nil {
		return nil, err
	}
	for key, values := range req.Header {
		for _, value := range values {
			rawReq.Header.Set(key, value)
		}
	}
	if len(rawReq.Header.Get("User-Agent")) < 3 { //nolint:mnd // 对于没有UA的则随机生成一个进行发包
		rawReq.Header.Set("User-Agent", nhttp.GetRandomUserAgent())
	}
	var client *nhttp.Client
	if req.FollowRedirects {
		client = c.Raw
	} else {
		client = c.SPClient
	}
	rawResp, err := client.Do(rawReq)
	if err != nil {
		return nil, err
	}

	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(rawResp.Body)
	follow.Response = &Response{
		Status:    rawResp.StatusCode,
		StatusMsg: rawResp.Status,
		Proto:     rawResp.Proto,
		Header:    rawResp.Header,
		Body:      nil,
		TimeTrace: timeTrace,
	}
	if rawResp.StatusCode != http.StatusSwitchingProtocols { // 切换协议后经常body的reader不会关闭，导致读取响应阻塞，所以就不读响应了
		follow.Response.Body, err = io.ReadAll(io.LimitReader(rawResp.Body, cmp.Or(req.MaxBodySize, RequestBodyMax)))
		if err != nil {
			return nil, err
		}
	}

	return follow.Response, nil
}

// SendNewRequest 将请求req中的params对应map的key的param的值设置为新的值并使用新的参数生成新的请求并发送请求获取响应
func (c *Client) SendNewRequest(ctx context.Context, req *Request, key, newValue string) (*Request, *Response, error) {
	newParam := req.params[key]
	newParam.Value = newValue
	newReq := req.BuildRequestWithParams(key, newParam)
	newResp, err := c.Do(ctx, newReq)
	if err != nil {
		return newReq, newResp, err
	}
	return newReq, newResp, nil
}

func (c *Client) FailExceededLimit() (bool, int) {
	if c.HostErrCounter.Load() >= int64(c.HostErrMaxNum) {
		return true, int(c.HostErrCounter.Load())
	} else {
		return false, int(c.HostErrCounter.Load())
	}
}

func (c *Client) Clear() error {
	var err error
	if c.RdnsClient != nil {
		err = c.RdnsClient.StopPolling()
		if err == nil {
			err = c.RdnsClient.Close()
		}
	}
	if c.HTTPCache != nil {
		c.HTTPCache.Purge()
	}
	return err
}
