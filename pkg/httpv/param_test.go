package httpv

import (
	"net/url"
	"strings"
	"testing"
)

func TestSmartSemicolonReplace(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		desc     string
	}{
		{
			name:     "无分号的正常查询",
			input:    "key1=value1&key2=value2",
			expected: "key1=value1&key2=value2",
			desc:     "不包含分号的查询字符串应该保持不变",
		},
		{
			name:     "简单分号替换",
			input:    "key1=value1;key2=value2",
			expected: "key1=value1&key2=value2",
			desc:     "参数分隔符位置的分号应该被替换为&",
		},
		{
			name:     "值中包含分号",
			input:    "config=some;config;value;name=test",
			expected: "config=some%3Bconfig%3Bvalue&name=test",
			desc:     "值内部的分号应该被URL编码为%3B",
		},
		{
			name:     "复杂混合情况",
			input:    "a=1;b=val;with;semicolon;c=3;d=another;value",
			expected: "a=1&b=val%3Bwith%3Bsemicolon&c=3&d=another%3Bvalue",
			desc:     "混合情况：值内分号编码，参数分隔符分号替换",
		},
		{
			name:     "特殊字符混合",
			input:    "data=a;b;c=d;timestamp=2023-01-01;key=value",
			expected: "data=a%3Bb&c=d&timestamp=2023-01-01&key=value",
			desc:     "c=d应该被识别为新参数",
		},
		{
			name:     "键名中有分号",
			input:    "key;name=value;data=test",
			expected: "key&name=value&data=test",
			desc:     "键名部分的分号应该被替换为&",
		},
		{
			name:     "空值参数",
			input:    "key1=;key2=value2;key3=",
			expected: "key1=&key2=value2&key3=",
			desc:     "空值参数应该正确处理",
		},
		{
			name:     "只有键没有值",
			input:    "key1;key2=value",
			expected: "key1&key2=value",
			desc:     "只有键名的参数应该正确处理",
		},
		{
			name:     "URL编码的值",
			input:    "name=%E5%BC%A0%E4%B8%89;age=25",
			expected: "name=%E5%BC%A0%E4%B8%89&age=25",
			desc:     "URL编码的值应该正确处理",
		},
		{
			name:     "以分号结尾",
			input:    "key1=value1;key2=value2;",
			expected: "key1=value1&key2=value2%3B",
			desc:     "以分号结尾的查询字符串",
		},
		{
			name:     "JSON值中包含分号",
			input:    "data={\"config\":\"a;b;c\"};type=json",
			expected: "data={\"config\":\"a%3Bb%3Bc\"}&type=json",
			desc:     "JSON值中的分号应该被URL编码",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
			desc:     "空字符串应该返回空字符串",
		},
		{
			name:     "复杂场景",
			input:    "cmd=echo hello;world;action=save;file=test.txt",
			expected: "cmd=echo hello%3Bworld&action=save&file=test.txt",
			desc:     "复杂场景的正确处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := smartSemicolonReplace(tt.input)
			if result != tt.expected {
				t.Errorf("smartSemicolonReplace() = %v, expected %v", result, tt.expected)
				t.Errorf("描述: %s", tt.desc)
			}

			// 验证处理后的结果能被url.ParseQuery正确解析
			if tt.input != "" {
				values, err := url.ParseQuery(result)
				if err != nil {
					t.Errorf("处理后的结果无法被url.ParseQuery解析: %v", err)
					t.Errorf("处理结果: %s", result)
				} else {
					// 验证解析后的值是否正确（分号应该被正确解码）
					for key, vals := range values {
						for _, val := range vals {
							if strings.Contains(val, ";") {
								t.Logf("参数 %s 的值包含分号: %s", key, val)
							}
						}
					}
				}
			}
		})
	}
}

// 集成测试：验证解析后的实际值
func TestParseQueryParamsWithSemicolonIntegration(t *testing.T) {
	testCases := []struct {
		input    string
		expected map[string]string
	}{
		{
			input: "config=a;b;c;name=test",
			expected: map[string]string{
				"config": "a;b;c",
				"name":   "test",
			},
		},
		{
			input: "data={\"setting\":\"x;y;z\"};type=json",
			expected: map[string]string{
				"data": "{\"setting\":\"x;y;z\"}",
				"type": "json",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			processed := smartSemicolonReplace(tc.input)
			values, err := url.ParseQuery(processed)
			if err != nil {
				t.Errorf("解析失败: %v", err)
				return
			}

			for expectedKey, expectedValue := range tc.expected {
				if actualValue := values.Get(expectedKey); actualValue != expectedValue {
					t.Errorf("参数 %s: expected %s, got %s", expectedKey, expectedValue, actualValue)
				}
			}
		})
	}
}
