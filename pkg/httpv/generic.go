package httpv

import (
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
)

var firstLineReg = regexp.MustCompile(`([A-Z]+)\s([http|\/].*?)\s(HTTP\/\d\.\d)$`)

// String2Request 如果没有传入schema，默认使用http来拼接url
func String2Request(rawRequest, schema string) (*Request, error) {
	var err error
	req := &Request{}
	// 按换行符拆分字符串
	lineAndHeader, body, ok := strings.Cut(rawRequest, "\n\n")
	if ok {
		req.Body = []byte(body)
	}
	lines := strings.Split(lineAndHeader, "\n")

	// 解析请求行
	requestLine := strings.TrimSpace(lines[0])
	parts := firstLineReg.FindAllStringSubmatch(requestLine, -1)
	if len(parts) < 1 {
		return req, fmt.Errorf("invalid request line: %s", requestLine)
	} else if len(parts) != 1 || len(parts[0]) != 4 {
		return req, fmt.<PERSON>rro<PERSON>("invalid request line: %s", requestLine)
	}
	req.Method = parts[0][1]

	req.Header = make(http.Header)
	if len(lines) > 0 {
		// 解析头部信息
		for _, line := range lines[1:] {
			if line == "" {
				break
			}
			name, value, ok := strings.Cut(line, ":")
			if !ok {
				continue
			}
			req.Header.Add(strings.TrimSpace(name), strings.TrimSpace(value))
		}
	}
	path, query, _ := strings.Cut(parts[0][2], "?")
	if len(path) > 0 && path[0] != '/' {
		path = "/" + path
	}
	query = strings.TrimSpace(query)
	var uri string
	if query != "" {
		uri = fmt.Sprintf("%s?%s", path, query)
	} else {
		uri = path
	}
	req.URL, err = url.Parse(fmt.Sprintf("%s://%s%s", schema, req.Header.Get("Host"), uri))
	if err != nil {
		return req, fmt.Errorf("url.Parse fail, err:%s", err)
	}
	return req, nil
}

func String2Response(rawResponse string) (*Response, error) {
	var (
		resp = &Response{
			Header: make(http.Header),
		}
		err error
	)
	enterChar := "\n"
	if strings.Contains(rawResponse, "\r\n") {
		enterChar = "\r\n"
	}
	before, after, ok := strings.Cut(rawResponse, enterChar+enterChar)
	if ok && after != "" {
		after = strings.TrimSpace(after)
		resp.Body = []byte(after)
	}
	before = strings.TrimSpace(before)
	firstLine, headerLine, _ := strings.Cut(before, enterChar)
	firstLine = strings.TrimSpace(firstLine)
	headerLine = strings.TrimSpace(headerLine)
	firstSplit := strings.Split(firstLine, " ")
	if len(firstSplit) < 3 {
		return nil, fmt.Errorf("the firstSplit is non-standard, firstLine: %s", firstLine)
	}
	resp.Status, err = strconv.Atoi(firstSplit[1])
	if err != nil {
		resp.Status = 0
	}
	resp.StatusMsg = fmt.Sprintf("%s %s", firstSplit[1], firstSplit[2:])
	resp.Proto = firstSplit[0]
	for _, line := range strings.Split(headerLine, enterChar) {
		line = strings.TrimSpace(line)

		headerParts := strings.Split(line, ": ")
		if len(headerParts) != 2 {
			continue
		}
		resp.Header.Set(headerParts[0], headerParts[1])
	}
	return resp, nil
}
