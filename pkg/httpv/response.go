package httpv

import (
	"bytes"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/http/httptrace"
	"strings"
	"time"
	"unicode/utf8"

	"github.acme.red/pictor/foundation/slogext"
	"golang.org/x/net/html/charset"

	"github.acme.red/intelli-sec/npoc/pkg/expression/proto"
	"github.acme.red/intelli-sec/npoc/utils"
)

type Response struct {
	Status    int
	StatusMsg string
	Proto     string
	Header    http.Header
	Body      []byte
	TimeTrace *TimeTrace

	utf8Body []byte
}

type TimeTrace struct {
	WroteRequestTime         time.Time
	GotFirstResponseByteTime time.Time
}

func (t *TimeTrace) GetServerHandledTime() time.Duration {
	return t.GotFirstResponseByteTime.Sub(t.WroteRequestTime)
}

func (t *TimeTrace) ClientTrace() *httptrace.ClientTrace {
	return &httptrace.ClientTrace{
		WroteRequest: func(info httptrace.WroteRequestInfo) {
			t.WroteRequestTime = time.Now()
		},
		GotFirstResponseByte: func() {
			t.GotFirstResponseByteTime = time.Now()
		},
	}
}

func (resp *Response) GetUTF8Body() ([]byte, error) {
	if !utf8.ValidString(string(resp.Body)) {
		reader, err := charset.NewReaderLabel("utf-8", bytes.NewReader(resp.Body))
		if err != nil {
			return nil, err
		}
		resp.utf8Body, err = io.ReadAll(io.LimitReader(reader, 1024*1024))
		if err != nil {
			return nil, err
		}
		return resp.utf8Body, nil
	} else {
		resp.utf8Body = resp.Body
	}
	return resp.utf8Body, nil
}

func (resp *Response) GetHeaderKeys() []string {
	var result []string
	cookies := strings.Split(resp.Header.Get("Set-Cookie"), ";")
	for _, cookie := range cookies {
		key := strings.Split(cookie, "=")[0]
		key = strings.Trim(key, " ")
		result = append(result, key)
	}
	for key := range resp.Header {
		result = append(result, key)
	}
	return result
}

func (resp *Response) Cookies() []*http.Cookie {
	r := &http.Response{Header: resp.Header}
	return r.Cookies()
}

// func (resp *Response) Dump() (string, error) {
//	var respStr string
//	respStr += fmt.Sprintf("%s %s\r\n", resp.Proto, resp.StatusCode)
//	for key, values := range resp.Header {
//		valueStr := strings.Join(values, "; ")
//		line := fmt.Sprintf("%s: %s", key, valueStr)
//		respStr += line + "\r\n"
//	}
//	respStr += "\r\n"
//	respStr += string(resp.Body)
//	return respStr, nil
// }

func (resp *Response) IsHTMLResp() bool {
	contentType := resp.Header.Get("Content-Type")
	if strings.Contains(contentType, "text/html") {
		return true
	}
	if resp.Header.Get("X-Content-Type-Options") == "nosniff" {
		return false
	}
	right := len(resp.Body) - 1
	if right <= 0 {
		return false
	}
	if right > 500 {
		right = 500
	}
	toBeTested := resp.Body[0:right]
	if bytes.Contains(toBeTested, []byte("<html")) ||
		bytes.Contains(toBeTested, []byte("<head")) ||
		bytes.Contains(toBeTested, []byte("<body")) {
		return true
	}
	return false
}

func (resp *Response) Dump() (string, error) {
	respBuilder := strings.Builder{}

	respBuilder.WriteString(fmt.Sprintf("%s %s\r\n", resp.Proto, resp.StatusMsg))
	for key := range resp.Header {
		respBuilder.WriteString(fmt.Sprintf("%s: %s\r\n", key, resp.Header.Get(key)))
	}
	respBuilder.WriteString("\r\n")
	respBuilder.WriteString(string(resp.Body))
	return respBuilder.String(), nil
}

func (resp *Response) IsRedirect() bool {
	if resp.Status == http.StatusNotModified {
		return false
	}

	return resp.Status >= 300 && resp.Status < 400
}

func (resp *Response) ToExprProtoResponse() (*proto.Response, error) {
	newRespHeader := make(map[string]string)
	rawHeaderBuilder := strings.Builder{}
	for k, v := range resp.Header {
		newRespHeader[k] = strings.Join(v, "; ")
		rawHeaderBuilder.WriteString(k)
		rawHeaderBuilder.WriteString(": ")
		rawHeaderBuilder.WriteString(strings.Join(v, "; "))
		rawHeaderBuilder.WriteString("\r\n")
	}
	utf8Body, err := resp.GetUTF8Body()
	if err != nil || utf8Body == nil {
		utf8Body = resp.Body
	}
	protoResp := &proto.Response{
		TitleString: utils.ParseTitle(resp.Body),
		Status:      int32(resp.Status),
		Headers:     newRespHeader,
		ContentType: resp.Header.Get("content-type"),
		Body:        utf8Body,
		BodyString:  string(utf8Body),
		RawHeader:   []byte(rawHeaderBuilder.String()),
	}
	rawResponse, err := resp.Dump()
	if err != nil {
		return protoResp, err
	}
	protoResp.Raw = []byte(rawResponse)
	return protoResp, nil
}

func (resp *Response) ConvertResponse(response *http.Response) {
	resp.Status = response.StatusCode
	resp.Header = response.Header
	resp.StatusMsg = response.Status
	resp.Proto = response.Proto
	data, err := io.ReadAll(io.LimitReader(response.Body, 1024*1024))
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			slog.Error("convert response body close", slogext.Error(err))
		}
	}(response.Body)
	if err != nil {
		slog.Error("convert response io read all: ", slogext.Error(err))
	}
	resp.Body = data
}
