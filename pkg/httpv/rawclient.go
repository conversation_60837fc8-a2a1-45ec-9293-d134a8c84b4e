package httpv

import (
	"bufio"
	"bytes"
	"cmp"
	"compress/gzip"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httputil"
	"net/textproto"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.acme.red/intelli-sec/npoc/pkg/iproxy"
)

const (
	defaultReadLen = 4096
)

// TCPClient 发送非标准http请求的，为了保证发送的请求不会出现非期望行为，
// 不会对原始请求做任何修改，并且也不会对http自身的请求头字段做任何反应，所以
// 原始请求的正确性需要写poc的人自行保证。
type TCPClient struct {
	Dialer  *iproxy.Dialer
	Timeout time.Duration // 整个过程的超时时间
}

// redirects 重定向计数器
type redirects struct {
	MaxRedirects int
	Current      int
}

func (r *redirects) Next() bool {
	if r.Current >= r.MaxRedirects {
		return false
	}
	r.Current++
	return true
}

func (c *TCPClient) DoWithRequest(ctx context.Context, req *Request) (*Response, error) {
	if len(req.UnsafeRawHTTP) == 0 {
		return nil, fmt.Errorf("raw client only sends unsafe requests")
	}

	rds := &redirects{}
	if req.FollowRedirects {
		rds = &redirects{MaxRedirects: 5}
	}
	req.MaxBodySize = cmp.Or(req.MaxBodySize, 1024*1024)
	// unsafe请求计算Content-Length并添加到request header中，template发起unsafe请求计算CL头需要另行实现。
	if req.Body != nil {
		contentLength := calcContentLength(bytes.NewReader(req.Body))
		req.Header.Set("Content-Length", strconv.FormatInt(contentLength, 10))
		rawRequest, err := req.Dump()
		if err != nil {
			return nil, err
		}
		req.UnsafeRawHTTP = []byte(rawRequest)
	}
	trace := &TimeTrace{}
	trace.WroteRequestTime = time.Now()
	resp, err := c.doWithRequest(ctx, req, rds)
	if err != nil {
		return nil, err
	}
	trace.GotFirstResponseByteTime = time.Now()
	resp.TimeTrace = trace

	return resp, nil
}

func (c *TCPClient) doWithRequest(ctx context.Context, req *Request, redirect *redirects) (*Response, error) {
	if req.URL == nil {
		return nil, fmt.Errorf("raw request url is nil")
	}
	var (
		resp    *Response
		isTLS   bool
		address = req.URL.Host
	)
	ctx, cancel := context.WithDeadline(ctx, time.Now().Add(c.Timeout))
	defer cancel()
	if req.URL.Scheme == "https" {
		isTLS = true
	}

	if !strings.Contains(req.URL.Host, ":") {
		if isTLS {
			address += ":443"
		} else {
			address += ":80"
		}
	}
	requestData := req.UnsafeRawHTTP
	if !bytes.Contains(requestData, []byte("\r\n")) {
		requestData = bytes.ReplaceAll(requestData, []byte("\n"), []byte("\r\n"))
	}
	// Get类型的请求包如果最后不是两个换行符则会io超时无法读取到响应内容
	if req.Method == http.MethodGet && !bytes.HasSuffix(requestData, []byte("\r\n\r\n")) {
		requestData = append(requestData, []byte("\r\n\r\n")...)
	}
	// 对于有body的http请求，是必须有Content-Length的，否则会出现异常
	if (bytes.HasPrefix(requestData, []byte("POST")) || bytes.HasPrefix(requestData, []byte("PUT"))) && !bytes.Contains(requestData, []byte("Content-Length")) {
		var contentLength int64
		before, after, ok := strings.Cut(string(requestData), "\r\n\r\n")
		if ok {
			contentLength = calcContentLength(bytes.NewReader([]byte(after)))
		}
		requestData = []byte(before + "\r\nContent-Length:" + strconv.FormatInt(contentLength, 10) + "\r\n\r\n" + after)
	}
	conn, err := c.SendData(ctx, requestData, address, isTLS)
	if err != nil {
		return nil, err
	}
	defer func(conn net.Conn) {
		_ = conn.Close()
	}(conn)
	resp, err = ReadResponse(conn, req.MaxBodySize)
	if err != nil {
		return nil, err
	}
	_ = conn.Close()
	// 使用递归来重定向
	//
	// NOTE: 可能会重定向到其他Host，所以保留
	if resp.IsRedirect() && redirect.Next() {
		uri := resp.Header.Get("Location")
		// 当Location是"/"的时候说明是会重定向到当前Host的某个path上，相当于是原地重定向
		if strings.HasPrefix(uri, "/") {
			return c.doWithRequest(ctx, req, redirect)
		}
		u, err := url.Parse(uri)
		if err != nil {
			return nil, fmt.Errorf("raw client redirect failed to parse url %s: %v", uri, err)
		}
		// 解决重定向的地址不符合url规则而导致后面发送请求异常
		req.URL = u
		return c.doWithRequest(ctx, req, redirect)
	}
	return resp, nil
}

func (c *TCPClient) DoWithRawData(ctx context.Context, rawData []byte, address string, isTLS bool, readSize int) ([]byte, error) {
	ctx, cancel := context.WithDeadline(ctx, time.Now().Add(c.Timeout)) // 设置整体的超时时间
	defer cancel()
	conn, err := c.SendData(ctx, rawData, address, isTLS)
	if err != nil {
		return nil, err
	}
	if readSize == 0 {
		readSize = defaultReadLen
	}
	b := make([]byte, readSize)
	count, err := conn.Read(b)
	// _ = conn.SetDeadline(time.Time{})
	if err != nil {
		if os.IsTimeout(err) && count > 0 {
			// in case of timeout with some value read, return the value
			return b[:count], nil
		}
		return nil, err
	}
	return b[:count], nil
}

func (c *TCPClient) SendData(ctx context.Context, data []byte, address string, isTLS bool) (net.Conn, error) {
	if !strings.Contains(address, ":") {
		return nil, fmt.Errorf("address must contain ':'")
	}
	conn, err := c.Dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		return nil, err
	}
	// 设置总体截止时间
	if deadline, ok := ctx.Deadline(); ok {
		_ = conn.SetDeadline(deadline)
	}
	if isTLS {
		conn, err = TLSHandshake(ctx, conn)
		if err != nil {
			return nil, err
		}
	}
	_, err = conn.Write(data)
	if err != nil {
		return nil, err
	}
	return conn, nil
}

func ReadResponse(conn net.Conn, size int64) (*Response, error) {
	buf := textproto.NewReader(bufio.NewReader(conn))
	line, err := buf.ReadLine()
	if err != nil {
		// if err == io.EOF {
		//	err = fmt.Errorf("raw client readline is read empty buf")
		// }
		return nil, err
	}

	_, status, ok := strings.Cut(line, " ")
	if !ok {
		return nil, fmt.Errorf("malformed HTTP response %s", line)
	}

	statusCode, _, _ := strings.Cut(strings.TrimLeft(status, " "), " ")
	if len(statusCode) != 3 {
		return nil, fmt.Errorf("malformed HTTP status code %s", statusCode)
	}

	code, err := strconv.Atoi(statusCode)
	if err != nil || code < 0 {
		return nil, fmt.Errorf("malformed HTTP status code %s", statusCode)
	}
	resp := &Response{Status: code}

	header, err := buf.ReadMIMEHeader()
	if err != nil {
		if err == io.EOF {
			err = fmt.Errorf("raw client mime header: %v", io.ErrUnexpectedEOF)
		}
		return nil, err
	}
	resp.Header = http.Header(header)

	bodyRead := io.Reader(buf.R)
	if length := resp.Header.Get("Content-Length"); length != "" {
		n, err := strconv.ParseInt(length, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("malformed HTTP Content-Length %s", length)
		}

		bodyRead = io.LimitReader(bodyRead, n)
	} else if length = resp.Header.Get("Transfer-Encoding"); length == "chunked" {
		bodyRead = httputil.NewChunkedReader(bodyRead)
	}

	if resp.Header.Get("Content-Encoding") == "gzip" {
		bodyRead, err = gzip.NewReader(bodyRead)
		if err != nil {
			return nil, err
		}
	}

	resp.Body, err = io.ReadAll(io.LimitReader(bodyRead, size))
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func TLSHandshake(ctx context.Context, conn net.Conn) (net.Conn, error) {
	tlsConn := tls.Client(conn, &tls.Config{
		InsecureSkipVerify: true,
	})
	if err := tlsConn.HandshakeContext(ctx); err != nil {
		return nil, err
	}
	return tlsConn, nil
}

// calcContent用于计算Content-Length大小，类型转换和断言可以直接使用类型下的长度计算方法，使用len方法来计算长度是不准确的。
func calcContentLength(body io.Reader) int64 {
	switch v := body.(type) {
	case *bytes.Buffer:
		return int64(v.Len())
	case *bytes.Reader:
		return int64(v.Len())
	case *strings.Reader:
		return int64(v.Len())
	default:
	}
	return 0
}
