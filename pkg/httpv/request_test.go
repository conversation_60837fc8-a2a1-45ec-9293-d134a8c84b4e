package httpv

import (
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"testing"
)

func TestRequest_BuildRequestWithParams(t *testing.T) {
	uu, _ := url.Parse("http://10.4.7.22:8000/vul/unsafeupload/clientcheck.php")
	//	body := `------WebKitFormBoundary1OFQTESZAyNmCLqH
	// Content-Disposition: form-data; name="uploadfile"; filename="小鸭几.jpg"
	// Content-Type: image/jpeg
	//
	// dfsfdsfsd
	// ------WebKitFormBoundary1OFQTESZAyNmCLqH
	// Content-Disposition: form-data; name="submit"
	//
	// 开始上传
	// ------WebKitFormBoundary1OFQTESZAyNmCLqH--`
	body := `------WebKitFormBoundary1OFQTESZAyNmCLqH
Content-Disposition: form-data; name="uploadfile"; filename="小鸭几.jpg"
Content-Type: image/jpeg

dfsfdsfsd
------WebKitFormBoundary1OFQTESZAyNmCLqH
Content-Disposition: form-data; name="submit"

开始上传
------WebKitFormBoundary1OFQTESZAyNmCLqH`
	header := http.Header{
		"Content-Type": []string{"multipart/form-data; boundary=----WebKitFormBoundary1OFQTESZAyNmCLqH"},
	}
	request := Request{
		Method:          "POST",
		URL:             uu,
		Header:          header,
		Body:            []byte(body),
		FollowRedirects: false,
		Mu:              sync.Mutex{},
		params:          map[string]Param{},
	}

	request.ParseParam()
	for key, param := range request.Params() {
		newParam := param
		newParam.Value = "vcxzvcxz"
		newReq := request.BuildRequestWithParams(key, newParam)
		fmt.Println(newReq.params)
	}
}
