package httpv

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"mime"
	"mime/multipart"
	"net/http"
	"net/http/httputil"
	"net/textproto"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/tidwall/sjson"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

const (
	MethodGet    = http.MethodGet
	MethodHead   = http.MethodHead
	MethodPost   = http.MethodPost
	MethodPut    = http.MethodPut
	MethodPatch  = http.MethodPatch
	MethodDelete = http.MethodDelete

	ContentTypeJson      = "application/json"
	ContentTypeForm      = "application/x-www-form-urlencoded"
	contentTypeMultipart = "multipart/form-data"
	ContentTypeText      = "text/plain"
)

var HTTPSchemes = []string{"http", "https"}

type Request struct {
	Method          string
	URL             *url.URL
	Header          http.Header
	Body            []byte
	FollowRedirects bool
	MaxBodySize     int64
	Mu              sync.Mutex
	// UnsafeRawHTTP 用于保存非标准的HTTP报文
	// 当不为空的时候表示当前Request是非标准HTTP报文
	UnsafeRawHTTP   []byte
	DisableEncoding bool

	Cache  bool // 是否强制将响应缓存起来(存在client中)
	params map[string]Param
}

type FingerprintInfo struct {
	Name string
}

func NewRequest(method, urlStr string, body []byte) (*Request, error) {
	u, err := url.Parse(urlStr)
	if err != nil {
		return nil, err
	}
	req := &Request{
		Method:          method,
		URL:             u,
		Header:          make(http.Header),
		Body:            body,
		FollowRedirects: false,
		MaxBodySize:     0,
		Mu:              sync.Mutex{},
		UnsafeRawHTTP:   nil,
		Cache:           false,
	}
	return req, nil
}

func NewGetRequest(urlStr string) (*Request, error) {
	return NewRequest(MethodGet, urlStr, nil)
}

// MarkParamNoFazz 将一些特定参数设置为不需要fuzz
func (req *Request) MarkParamNoFazz() {
	for key, param := range req.params {
		if text.StrContainsSlice(param.Key, paramPassKey) {
			param.NeedCheck = false
			req.params[key] = param
		}
	}
}

func (req *Request) Params() map[string]Param {
	return req.params
}

func (req *Request) SetNotNeedFuzz(key string) {
	param := req.params[key]
	param.NeedCheck = false
	req.params[key] = param
}

func (req *Request) Clone() *Request {
	req.Mu.Lock()
	defer req.Mu.Unlock()
	// req.ParseParam()
	newUrl := *req.URL
	newReq := Request{
		Method:          req.Method,
		URL:             &newUrl,
		FollowRedirects: req.FollowRedirects,
		Body:            req.Body,
		UnsafeRawHTTP:   req.UnsafeRawHTTP,
		DisableEncoding: req.DisableEncoding,
	}
	newReq.params = make(map[string]Param)
	for key, param := range req.params {
		newReq.params[key] = param
	}
	newReq.Header = make(http.Header)
	for key, values := range req.Header {
		newValues := make([]string, len(values))
		copy(newValues, values)
		newReq.Header[key] = newValues
	}
	return &newReq
}

// BuildRequestWithParams newParam为原请求中需要更改的参数，根据newParam的position来对请求的对应位置进行重构，其余位置的数据不变。
// 对于header和cookie位置的数据只更改header中对应key的数据，其余不动
func (req *Request) BuildRequestWithParams(key string, newParam Param) *Request {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	go func() { // 怀疑这里运行阻塞了，但是不知道具体什么原因导致的哪个位置阻塞，这里启一个go程出去，在超时的时候进行日志打印
		select {
		case <-ctx.Done():
			return
		case <-time.After(15 * time.Second): //nolint:mnd // debug
			reqData, _ := json.Marshal(&req)   //nolint:musttag // debug
			paramData, _ := json.Marshal(&req) //nolint:musttag // debug
			slog.Error("build request with params timeout 15s", "request", base64.StdEncoding.EncodeToString(reqData), "key", key, "param_data", base64.StdEncoding.EncodeToString(paramData))
			return
		}
	}()
	newReq := req.Clone()
	newReq.params[key] = newParam
	if newParam.Key == "" || len(req.params) == 0 {
		return newReq
	}
	switch newParam.Position {
	case ParamPositionPath:
		newReq.URL.Path = strings.ReplaceAll(newParam.Key, "{{Value}}", newParam.Value)
		newReq.URL.RawPath = newReq.URL.Path
	case ParamPositionURLQuery:
		newQuery := req.BuildNewQuery(key, newParam, ParamPositionURLQuery)
		if req.DisableEncoding {
			decodedQuery, err := url.QueryUnescape(newQuery.Encode())
			if err != nil {
				slog.ErrorContext(ctx, fmt.Sprintf("url 取消转义失败，err:%s", err.Error()))
				return newReq
			}
			newReq.URL.RawQuery = decodedQuery
		} else {
			newReq.URL.RawQuery = newQuery.Encode()
		}
	case ParamPositionBody:
		contentType := req.Header.Get("Content-Type")
		contentType, _, err := mime.ParseMediaType(contentType)
		if err != nil {
			return newReq
		}
		body := &bytes.Buffer{}
		switch contentType {
		case ContentTypeForm:
			newQuery := req.BuildNewQuery(key, newParam, ParamPositionBody)
			if req.DisableEncoding {
				decodedQuery, err := url.QueryUnescape(newQuery.Encode())
				if err != nil {
					slog.ErrorContext(ctx, fmt.Sprintf("url 取消转义失败，err:%s", err.Error()))
					return newReq
				}
				body.WriteString(decodedQuery)
			} else {
				body.WriteString(newQuery.Encode())
			}
		case ContentTypeJson:
			jsonBody := req.BuildNewJsonBody(key, newParam)
			body.WriteString(jsonBody)
		case contentTypeMultipart:
			mpWriter := req.BuildNewMultipart(key, newParam, body)
			newContentType := mpWriter.FormDataContentType()
			newReq.Header.Set("Content-Type", newContentType)
		}
		if len(body.Bytes()) > RequestBodyMax { // 当请求的body太大了打印一下日志。正常不会超过1M
			reqData, _ := json.Marshal(&req) //nolint:musttag // debug
			slog.Error("request body too large", "body_length", len(body.Bytes()), "request", base64.StdEncoding.EncodeToString(reqData))
		}
		newReq.Body = body.Bytes()
	case ParamPositionHeader:
		newReq.Header.Set(newParam.Key, newParam.Prefix+newParam.Value+newParam.Suffix)
	case ParamPositionCookie:
		var newCookieList []string
		// cookie部分的param是不会存到req的params里面的，所以这里需要重新获取全部的cookie并对fuzz的参数进行重新赋值
		cookieParams := req.GetCookieParams()
		for _, param := range cookieParams {
			value := param.Value
			if param.Key == newParam.Key {
				value = newParam.Prefix + newParam.Value + newParam.Prefix
			}
			newCookieList = append(newCookieList, fmt.Sprintf("%s=%s", param.Key, value))
		}
		newReq.Header.Set("Cookie", strings.Join(newCookieList, ";"))
	}

	return newReq
}

// BuildReqWithNewPath 使用新的uri地址构造一个请求
func (req *Request) BuildReqWithNewPath(newPath string, partial bool) (*Request, error) {
	newUrl, err := url.Parse(fmt.Sprintf("%s://%s", req.URL.Scheme, req.URL.Host))
	if err != nil {
		return nil, err
	}
	newUrl = newUrl.JoinPath(newPath)
	newReq := req.Clone()
	newReq.URL = newUrl
	newReq.Method = MethodGet
	newReq.Body = nil
	if partial { // 需要限制响应的长度，分块传输，截取成功相应码应该是206
		newReq.Header.Add("Range", "bytes=0-8192")
	}
	return newReq, nil
}

// BuildReqWithBody 使用新的body地址构造一个请求
func (req *Request) BuildReqWithBody(body []byte) *Request {
	newReq := req.Clone()
	newReq.Body = body
	if newReq.Method == MethodGet {
		newReq.Method = MethodPost
	}
	return newReq
}

// BuildReqWithBodyAndContentType 使用新的body地址和content-type构造一个请求
func (req *Request) BuildReqWithBodyAndContentType(body []byte, contentType string) *Request {
	newReq := req.Clone()
	withBody := newReq.BuildReqWithBody(body)
	withBody.Header.Set("Content-Type", contentType)
	return withBody
}

// BuildNewQuery 获取在请求的url或body中a=xxxx这种query类型的参数
func (req *Request) BuildNewQuery(key string, newParam Param, position string) *url.Values {
	newQuery := &url.Values{}
	var err error
	for k, orgParam := range req.params {
		if orgParam.Position != position {
			continue
		}
		value := orgParam.Value
		if k == key {
			value = newParam.Prefix + newParam.Value + newParam.Suffix
		}
		if orgParam.ParamType == ParamTypeQuery {
			newQuery.Add(orgParam.Key, value)
		} else if orgParam.ParamType == ParamTypeQueryJson {
			// queryJson类型orgParam.Key的第一位是query的key，后面用点隔开的是queryJson中value的带层级关系的key
			split := strings.Split(orgParam.Key, ".")
			if len(split) > 1 {
				queryKey := split[0]
				jsonKey := strings.Join(split[1:], ".")
				// 如果之前已经为queryKey对应的json设置过数据了，则在之前的基础上追加数据
				queryJsonStr := newQuery.Get(queryKey)
				if orgParam.ValueIsJsonRaw && value != "" {
					queryJsonStr, err = sjson.Set(queryJsonStr, jsonKey, json.RawMessage(value))
					if err != nil { // json.RawMessage得到的数据异常导致set失败，尝试使用原始字符串set
						queryJsonStr, err = sjson.Set(queryJsonStr, jsonKey, value)
					}
				} else {
					queryJsonStr, err = sjson.Set(queryJsonStr, jsonKey, value)
					if err != nil {
						queryJsonStr, err = sjson.Set(queryJsonStr, jsonKey, json.RawMessage(value))
					}
				}
				if err != nil {
					slog.Error("build new query sjson.set fail", slogext.Error(err), "jsonBody", queryJsonStr, "key", orgParam.Key, "value", value)
					continue
				}
				// newValueByte, err := json.Marshal(queryJsonStr)
				// if err != nil {
				//	slog.Error("数据非json格式,err: %v\n", err)
				//	continue
				// }
				newQuery.Set(queryKey, queryJsonStr)
			}
		}
	}
	return newQuery
}

// BuildNewJsonBody 获取请求的body中json格式的数据，并将key对应的参数的值设置为newParam的value值
func (req *Request) BuildNewJsonBody(key string, newParam Param) string {
	var (
		jsonBody string
		err      error
	)
	for k, orgParam := range req.params {
		if orgParam.Position != ParamPositionBody {
			continue
		}
		value := orgParam.Value
		if k == key {
			value = newParam.Prefix + newParam.Value + newParam.Suffix
		}
		if orgParam.ValueIsJsonRaw && value != "" {
			jsonBody, err = sjson.Set(jsonBody, orgParam.Key, json.RawMessage(value))
			if err != nil {
				jsonBody, err = sjson.Set(jsonBody, orgParam.Key, value)
			}
		} else {
			jsonBody, err = sjson.Set(jsonBody, orgParam.Key, value)
			if err != nil {
				jsonBody, err = sjson.Set(jsonBody, orgParam.Key, json.RawMessage(value))
			}
		}
		if err != nil {
			slog.Error("build new json body sjson.set fail", slogext.Error(err), "jsonBody", jsonBody, "key", orgParam.Key, "value", value)
		}
	}
	return jsonBody
}

// BuildNewMultipart 获取请求的body中Multipart表单格式的数据，并将key对应的参数的值设置为newParam的value或Multipart的content值
func (req *Request) BuildNewMultipart(key string, newParam Param, body *bytes.Buffer) *multipart.Writer {
	mpWriter := multipart.NewWriter(body)
	defer func() {
		_ = mpWriter.Close()
	}()
	replaceFun := func(orgStr string) string {
		newString := strings.ReplaceAll(orgStr, "\\", "\\\\")
		newString = strings.ReplaceAll(newString, `"`, "\\\"")
		return newString
	}
	for k, orgParam := range req.params {
		if orgParam.Position != ParamPositionBody {
			continue
		}
		mHeader := make(textproto.MIMEHeader)

		if orgParam.Multipart.Filename != "" {
			filename := orgParam.Multipart.Filename
			if k == key {
				filename = newParam.Multipart.Filename
			}
			mHeader.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"; filename="%s"`, replaceFun(orgParam.Key), replaceFun(filename)))
		} else {
			mHeader.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, replaceFun(orgParam.Key)))
		}
		if orgParam.Multipart.ContentType != "" {
			contentType := orgParam.Multipart.ContentType
			if k == key {
				contentType = newParam.Multipart.ContentType
			}
			mHeader.Set("Content-Type", contentType)
		}
		part, err := mpWriter.CreatePart(mHeader)
		if err != nil {
			slog.Error("part.create fail", slogext.Error(err))
			return mpWriter
		}
		data := []byte(orgParam.Value)

		if len(data) == 0 {
			data = orgParam.Multipart.Content
		}
		if k == key {
			newData := []byte(newParam.Value)
			if len(newData) == 0 {
				newData = newParam.Multipart.Content
			}
			data = newData
			data = append([]byte(newParam.Prefix), data...)
			data = append(data, []byte(newParam.Suffix)...)
		}
		_, err = part.Write(data)
		if err != nil {
			slog.Error("part.write fail", slogext.Error(err))
			return mpWriter
		}
	}
	return mpWriter
}

func (req *Request) Dump() (string, error) {
	rd := bytes.NewReader(req.Body)
	var requestURL string
	// 添加了不进行URL编码时处理Path的逻辑
	if req.DisableEncoding {
		var err error
		requestURL, err = url.PathUnescape(req.URL.String())
		if err != nil {
			return "", err
		}
	} else {
		requestURL = req.URL.String()
	}
	rawReq, err := http.NewRequest(req.Method, requestURL, rd)
	if err != nil {
		return "", err
	}
	rawReq.Header = req.Header
	reqStr, err := httputil.DumpRequest(rawReq, true)
	if err != nil {
		return "", err
	}
	return string(reqStr), nil
}

func (req *Request) ToCurlStr() (string, error) {
	var curlCmd string
	curlCmd = "curl -i -s -k "
	// 添加请求方法
	curlCmd = fmt.Sprintf("%s -X $'%s' \\\r\n   ", curlCmd, req.Method)

	// 添加请求头
	for key, values := range req.Header {
		for _, value := range values {
			curlCmd = fmt.Sprintf("%s -H $'%s: %s'", curlCmd, key, value)
		}
	}
	curlCmd = fmt.Sprintf("%s \\\r\n", curlCmd)

	cookieStr := req.Header.Get("Cookie")
	if cookieStr != "" {
		curlCmd = fmt.Sprintf("%s    -b $'%s' \\\r\n", curlCmd, cookieStr)
	}

	bodyString := string(req.Body)
	if strings.Contains(bodyString, "\n") && !strings.Contains(bodyString, "\r\n") {
		bodyString = strings.ReplaceAll(bodyString, "\n", "\r\n")
	}
	bodyString = strings.ReplaceAll(bodyString, "\r\n", "\\x0d\\x0a")
	// bodyString = strings.ReplaceAll(bodyString, "\"", "\\\"")

	if bodyString != "" {
		curlCmd = fmt.Sprintf("%s    --data-binary $'%s' \\\r\n", curlCmd, bodyString)
	}
	curlCmd = fmt.Sprintf("%s    $'%s'", curlCmd, req.URL.String())
	return curlCmd, nil
}

func (req *Request) GetCookies() []*http.Cookie {
	var cookies []*http.Cookie
	cookieStr := req.Header.Get("Cookie")
	if cookieStr == "" {
		return nil
	}
	cookieParts := strings.Split(cookieStr, ";")
	for _, cookiePart := range cookieParts {
		cookiePart = strings.TrimSpace(cookiePart)
		parts := strings.Split(cookiePart, "=")
		if len(parts) != 2 {
			continue
		}
		cookie := &http.Cookie{
			Name:  parts[0],
			Value: parts[1],
		}
		cookies = append(cookies, cookie)
	}
	return cookies
}

func (req *Request) AddCookie(key, value string) {
	rawCookie := req.Header.Get("Cookie")
	var cookieList []string
	newCookieStr := fmt.Sprintf("%s=%s", key, value)
	if rawCookie != "" {
		cookieList = strings.Split(rawCookie, ";")
		cookieList = append(cookieList, newCookieStr)
		newCookie := strings.Join(cookieList, ";")
		req.Header.Set("Cookie", newCookie)
	} else {
		req.Header.Set("Cookie", newCookieStr)
	}
}

func (req *Request) ResetCookie(key, value string) {
	rawCookie := req.Header.Get("Cookie")
	var newCookieList []string
	if rawCookie != "" {
		cookieParts := strings.Split(rawCookie, ";")
		for _, part := range cookieParts {
			part = strings.TrimSpace(part)
			cookie := strings.Split(part, "=")
			if len(cookie) == 2 && cookie[0] == key {
				newCookie := fmt.Sprintf("%s=%s", key, value)
				newCookieList = append(newCookieList, newCookie)
			} else {
				newCookieList = append(newCookieList, part)
			}
		}
		newCookie := strings.Join(newCookieList, ";")
		req.Header.Set("Cookie", newCookie)
	} else {
		newCookieStr := fmt.Sprintf("%s=%s", key, value)
		req.Header.Set("Cookie", newCookieStr)
	}
}

func (req *Request) IsRoot() bool {
	return req.URL.Path == "" || req.URL.Path == "/"
}

func (req *Request) ConvertRequest(request *http.Request) {
	req.URL = request.URL
	req.Header = request.Header
	req.Method = request.Method
	if request.Body != nil {
		body, err := io.ReadAll(io.LimitReader(request.Body, 1024*1024))
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				slog.Error("convert request body close", slogext.Error(err))
			}
		}(request.Body)
		if err != nil {
			slog.Error("convert request io.ReadAll", slogext.Error(err))
		}
		req.Body = body
	}
}
