package cmdexec

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
)

func RunContext(ctx context.Context, binary string, arg ...string) (string, error) {
	output := bytes.Buffer{}
	cmd := exec.CommandContext(ctx, binary, arg...)
	// 启动命令
	cmd.Stdout = &output
	cmd.Stderr = &output
	if err := cmd.Start(); err != nil {
		return "", fmt.Errorf("command start failed, command: %s, error: %w, output: %s", cmd.String(), err, output.String())
	}
	err := cmd.Wait()
	if err != nil {
		return "", fmt.Errorf("command exec failed, command: %s, error: %w, output: %s", cmd.String(), err, output.String())
	}
	return output.String(), nil
}
