package telemetry

import (
	"context"
	"log/slog"
	"runtime"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

type errorType string
type appType string

var (
	labels = []string{"app_type", "error_type"}
	// 定义一个 gauge 类型的指标，用于记录异常情况
	intelliErrorAlertCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Namespace: "intelli_sec",
		Name:      "error_alert_total",
		Help:      "intelli_sec的项目统计需要告警的情况",
	},
		labels,
	)
	PanicType = errorType("panic")
	// DnslogServerPollErrorType  = errorType("dnslog_poll_error")
	// DnslogServerCreatErrorType = errorType("dnslog_creat_error")

	DefaultType    = appType("default")
	NpocType       = appType("npoc")
	BruteforceType = appType("bruteforce")
)

func AlertError(appType appType, errorType errorType) {
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 1536) //nolint:mnd // need indeed
			n := runtime.Stack(buf, true)
			slog.ErrorContext(context.Background(), "recovered from panic", "error", r, "stack trace", buf[:n])
		}
	}()
	intelliErrorAlertCounter.WithLabelValues(string(appType), string(errorType)).Add(1)
}
