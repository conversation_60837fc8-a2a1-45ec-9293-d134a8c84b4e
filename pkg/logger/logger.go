package logger

import (
	"context"
	"io"
	"log/slog"
	"os"

	"gopkg.in/natefinch/lumberjack.v2"
)

type contextKey struct {
	name string
}

var attrsKey = &contextKey{}

// WithAttrs 向 context 中添加日志属性
func WithAttrs(ctx context.Context, attrs ...slog.Attr) context.Context {
	existing, _ := ctx.Value(attrsKey).([]slog.Attr)
	return context.WithValue(ctx, attrsKey, append(existing, attrs...))
}

// WithAttr 添加单个键值对
func WithAttr(ctx context.Context, key string, value any) context.Context {
	return WithAttrs(ctx, slog.Any(key, value))
}

// ContextHandler 从 context 中提取属性
type ContextHandler struct {
	next slog.Handler
}

func NewContextHandler(next slog.Handler) *ContextHandler {
	return &ContextHandler{next: next}
}
func (h *ContextHandler) Handle(ctx context.Context, r slog.Record) error {
	if attrs, ok := ctx.Value(attrsKey).([]slog.Attr); ok {
		for _, attr := range attrs {
			r.AddAttrs(attr)
		}
	}
	return h.next.Handle(ctx, r)
}
func (h *ContextHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.next.Enabled(ctx, level)
}
func (h *ContextHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return NewContextHandler(h.next.WithAttrs(attrs))
}
func (h *ContextHandler) WithGroup(name string) slog.Handler {
	return NewContextHandler(h.next.WithGroup(name))
}

type Config struct {
	LogPath       string
	LogLevel      string
	LogMaxSize    int
	LogMaxBackups int
	LogMaxAge     int
	LogOutConsole bool
}

// SetupLogger 设置全局 logger
func SetupLogger(cf Config) {
	// 配置日志轮转
	logRotator := &lumberjack.Logger{
		Filename:   cf.LogPath,
		MaxSize:    cf.LogMaxSize,    // 每个日志文件最大尺寸（MB）
		MaxBackups: cf.LogMaxBackups, // 保留的旧日志文件最大数量
		MaxAge:     cf.LogMaxAge,     // 保留日志文件的最大天数
		Compress:   true,             // 是否压缩旧日志文件
		LocalTime:  true,             // 使用本地时间命名备份文件
	}
	// // 创建处理器 - 使用文本格式
	// handler := slog.NewTextHandler(logRotator, &slog.HandlerOptions{
	// Level: slog.LevelDebug,
	// })
	var logLevel slog.Level
	switch cf.LogLevel {
	case "debug":
		logLevel = slog.LevelDebug
	case "info":
		logLevel = slog.LevelInfo
	case "warn":
		logLevel = slog.LevelWarn
	case "error":
		logLevel = slog.LevelError
	}
	var logHandler io.Writer
	if cf.LogOutConsole {
		logHandler = os.Stdout
	} else {
		logHandler = logRotator
	}
	// 或者使用JSON格式
	handler := slog.NewJSONHandler(logHandler, &slog.HandlerOptions{
		Level:     logLevel,
		AddSource: true,
	})
	newHandler := NewContextHandler(handler)
	logger := slog.New(newHandler)
	slog.SetDefault(logger)
}
