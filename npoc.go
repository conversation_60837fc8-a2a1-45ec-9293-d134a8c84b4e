package npoc

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/dnslog/pkg/server"
	"golang.org/x/sync/semaphore"

	"github.acme.red/intelli-sec/npoc/utils"
)

type (
	Severity   string
	Confidence string
	Category   string
	PocType    string
)

const (
	SeverityInfo      Severity   = "info"
	SeverityLow       Severity   = "low"
	SeverityMedium    Severity   = "medium"
	SeverityHigh      Severity   = "high"
	SeverityCritical  Severity   = "critical"
	ConfidenceHigh    Confidence = "high"
	ConfidenceMedium  Confidence = "medium"
	ConfidenceLow     Confidence = "low"
	ConfidenceUnknown Confidence = "unknown"

	pocSqliVulnMax     = 15
	dnsOrHTTPOOBVulMax = 20
)

const ( // 新增漏洞类型需要在后面的GenericVulnCategories中添加，也需要与将新的类型给前端同步, 当前gitlab和fortigate的还没和前端对
	WebWeakPasswordType Category = "web-weak-password" // web弱口令爆破
	SqlInjectType       Category = "sqli"              // SQL注入
	FileReadType        Category = "file-read"         // 文件读取
	FileUploadType      Category = "file-upload"       // 任意文件上传
	DirScanType         Category = "dirsearch"         // 敏感文件泄露
	DeserializeType     Category = "deserialize"       // 反序列化
	RCEType             Category = "rce"               // 命令执行
	Log4jType           Category = "log4j"             // log4j
	XSSType             Category = "xss"               // XSS
	XXEType             Category = "xxe"               // XXE
	SSRFType            Category = "ssrf"              // SSRF
	CSRFType            Category = "csrf"              // CSRF
	FastjsonType        Category = "fastjson"          // fastjson反序列化
	ShiroType           Category = "shiro"             // shiro反序列化
	ThinkphpType        Category = "thinkphp"          // thinkphp漏洞
	Struts2Type         Category = "struts2"           // struts2漏洞
	XStream             Category = "xstream"           // xstream漏洞
	ExchangeType        Category = "exchange"          // exchange漏洞
	GitlabType          Category = "gitlab"            // gitlab漏洞
	Fortigate           Category = "fortigate"         // fortigate漏洞

	GenericPocType PocType = "generic_poc_type"
	YamlPocType    PocType = "yaml_poc_type"

	ExtraKeyCheckRule      = "check_rule" // 响应中校验的规则，可能是正则也可能是cel表达式
	ExtraKeyCheckStr       = "check_str"  // 响应中校验的字符串
	ExtraKeyDes            = "补充描述"
	ExtraKeySubCategory    = "漏洞子类型"
	ExtraKeyShellURL       = "shell_url"
	ExtraKeyUser           = "user"
	ExtraKeyPass           = "password"
	ExtraKeyUserParamName  = "用户名参数"
	ExtraKeyPassParamName  = "密码参数" // nolint:gosec // 1
	ExtraKeyOOBUrl         = "oob_url"
	ExtraDoubleCheckID     = "double_check_id"
	ExtraDoubleCheckValue  = "double_check_value"
	ExtraCodeInjCheckValue = "code_inj_check_value"
	ExtraRCEPayloadType    = "rce_payload_type"
	ExtraKeyDefaultExtra   = "extractor_result"
	ExtraKeyParamType      = "参数类型"
	ExtraKeyDBM            = "数据库类型"

	ExtraValueOOBCheck = "oob_check"

	DefaultGenVulnPocParallel = 20
)

var GenericVulnCategories = []string{
	string(WebWeakPasswordType), string(SqlInjectType), string(FileReadType), string(FileUploadType), string(DirScanType), string(RCEType),
	string(Log4jType), string(XSSType), string(XXEType), string(SSRFType), string(CSRFType), string(FastjsonType), string(ShiroType),
	string(ThinkphpType), string(Struts2Type), string(XStream), string(ExchangeType), string(GitlabType), string(Fortigate), string(DeserializeType),
}

type Metadata struct {
	Name           string
	PocType        PocType
	Category       Category
	Tags           []string
	Description    string
	Product        string
	ProductVersion []string
	Reference      []string
	Severity       Severity
	Extra          map[string]string
	CVE            []string
	CPE            string
	Confidence     Confidence
}

type PoC interface {
	ID() string       // PoC ID 唯一区分
	Protocol() string // PoC所工作的协议，只有相关协议的请求响应会过来。 类型参见sprobe
	Metadata() Metadata
}

type Task interface {
	TaskProtocol() string
}

type TaskResult struct {
	VulnMu          sync.Mutex
	Vulnerabilities []*Vulnerability
	ErrorMu         sync.Mutex
	PoCErrors       []*PoCError
}

type OOBDetail struct {
	Timestamp     time.Time
	RemoteAddress string
	Protocol      string
	// FullId OOB unique RequestID
	FullId  string
	Request string
}

type Vulnerability struct {
	PoC         string
	PocType     PocType
	Name        string
	Category    Category
	Severity    Severity
	Method      string
	Param       string
	Payload     string
	Description string
	URL         string
	HTTP        *VulnerabilityHTTP
	Extra       map[string]string
	OOBUrl      *client.URL
	OOBDetails  []*OOBDetail
	Confidence  Confidence
}

type protocolEntry struct {
	PoCs       []PoC
	ExecutePoC func(ctx context.Context, poc PoC, task Task, result *TaskResult) error
}

type Option struct {
	IncludeYamlIds        []string
	ExcludeYamlIds        []string
	IncludeYamlSeverities []string
	IncludeCommonIds      []string
	PocParallel           int64
	FingerprintMode       bool
	FingerprintNames      []string
	DisableLoadPoc        bool
}

type NPoC struct {
	mProtocolEntries map[string]*protocolEntry
	pocParallel      int64
}

func New(allPoc []PoC, opt Option) (*NPoC, error) {
	if opt.PocParallel < 1 {
		return nil, fmt.Errorf("poc parallel must be greater than 0")
	}
	np := &NPoC{
		pocParallel: opt.PocParallel,
	}
	if err := np.initProtocolEntries(allPoc, opt); err != nil {
		return nil, err
	}
	return np, nil
}

func (np *NPoC) DoTask(ctx context.Context, task Task) (*TaskResult, error) {
	tr := &TaskResult{}
	httpTask, ok := task.(*HTTPTask)
	if !ok {
		return tr, nil
	}

	entry := np.mProtocolEntries[task.TaskProtocol()]
	if entry == nil {
		return tr, fmt.Errorf("protocol %s not found", task.TaskProtocol())
	}

	err := np.executePocs(ctx, entry, task, tr)

	// wait oob deadline
	np.waitForOOBDeadline(ctx, tr)

	np.processVulnerabilities(ctx, tr, httpTask)
	// slog.InfoContext(ctx, "result", "wait", wait, "count", len(tr.Vulnerabilities))
	return tr, err
}

// executePocs 针对任务运行所有 POC，控制并发
func (np *NPoC) executePocs(ctx context.Context, entry *protocolEntry, task Task, tr *TaskResult) error {
	sema := semaphore.NewWeighted(np.pocParallel)
	var (
		wg     sync.WaitGroup
		errsMu sync.Mutex
		errs   []error
	)

	for _, poc := range entry.PoCs {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := sema.Acquire(ctx, 1); err != nil {
			break
		}

		wg.Add(1)
		go func(p PoC) {
			defer utils.RecoverFun(ctx)
			defer wg.Done()
			defer sema.Release(1)

			select {
			case <-ctx.Done():
				return
			default:
			}
			err := entry.ExecutePoC(ctx, p, task, tr)
			if errors.Is(err, ErrSkip) {
				return
			}
			if err != nil {
				errsMu.Lock()
				errs = append(errs, err)
				errsMu.Unlock()
			}
		}(poc)
	}

	// 等待所有任务完成。这是必须的，保证 tr.Vulnerabilities 的完整性。
	wg.Wait()

	return errors.Join(errs...)
}

// waitForOOBDeadline 等待到最新的 OOB 截止时间
func (np *NPoC) waitForOOBDeadline(ctx context.Context, tr *TaskResult) {
	var (
		lastDeadline = time.Now()
		needWait     bool
	)

	for _, vul := range tr.Vulnerabilities {
		if vul == nil {
			slog.WarnContext(ctx, "npoc got unexpected nil vul result", "vul_result_len", len(tr.Vulnerabilities))
			continue
		}
		if vul.PocType == YamlPocType && vul.OOBUrl != nil && vul.OOBUrl.Deadline().After(lastDeadline) {
			lastDeadline = vul.OOBUrl.Deadline()
			needWait = true
		}
	}

	if needWait {
		<-time.After(time.Until(lastDeadline))
	}
}

// processVulnerabilities 处理和过滤漏洞
func (np *NPoC) processVulnerabilities(ctx context.Context, tr *TaskResult, httpTask *HTTPTask) {
	var (
		vuls             []*Vulnerability
		dnsOrHTTPOOBVuls []*Vulnerability
		pocSqliVuls      []*Vulnerability
	)

	vulnFilter := make(map[string]struct{})

	for _, vul := range tr.Vulnerabilities {
		// TODO: 添加一个vuln字段的check（防御性编程）
		if !np.addToVulnerabilityList(ctx, vul, httpTask, vulnFilter, &vuls, &dnsOrHTTPOOBVuls, &pocSqliVuls) {
			continue
		}
	}

	// Filter and combine results
	finalVuls := np.filterAndCombineResults(ctx, vuls, dnsOrHTTPOOBVuls, pocSqliVuls)
	tr.Vulnerabilities = finalVuls
}

// addToVulnerabilityList 处理漏洞并将其添加到相应的列表中
func (np *NPoC) addToVulnerabilityList(ctx context.Context, vul *Vulnerability, httpTask *HTTPTask, vulnFilter map[string]struct{},
	vuls, dnsOrHTTPOOBVuls, pocSqliVuls *[]*Vulnerability) bool {
	if vul.URL == "" {
		vul.URL = httpTask.Request.URL.String()
	}

	vulnInfo := fmt.Sprintf("%s%s%s%s", vul.PoC, vul.Param, vul.Extra[ExtraKeySubCategory], vul.URL)
	if vul.PoC == string(DirScanType) || vul.PoC == string(WebWeakPasswordType) {
		vulnInfo = vulnInfo + vul.Payload
	}

	if httpTask.Client != nil && httpTask.Client.RdnsClient != nil && vul.OOBUrl != nil {
		httpTask.Client.RdnsClient.RemoveURL(vul.OOBUrl)
	}

	if _, exit := vulnFilter[vulnInfo]; exit {
		return false
	}
	vulnFilter[vulnInfo] = struct{}{}

	// 不需要 OOB 的 PoC
	if vul.OOBUrl == nil {
		if vul.Category == SqlInjectType && vul.Param == "" { // poc类型(通用漏洞的会有参数字段)的sql注入
			*pocSqliVuls = append(*pocSqliVuls, vul)
		} else {
			*vuls = append(*vuls, vul)
		}
		return true
	}

	// OOB 被触发的 PoC
	np.processOOBVulnerability(ctx, vul, dnsOrHTTPOOBVuls, vuls)

	return true
}

// processOOBVulnerability 处理OOB漏洞
func (np *NPoC) processOOBVulnerability(ctx context.Context, vul *Vulnerability, dnsOrHTTPOOBVuls, vuls *[]*Vulnerability) bool {
	// 检查OOB是否被触发
	var triggered bool
	interactions := vul.OOBUrl.GetInteractions()
	if vul.PocType == YamlPocType {
		triggered = vul.OOBUrl.Triggered()
	} else {
		triggered = len(vul.OOBDetails) > 0
	}

	if !triggered {
		return false
	}

	// 对于YAML类型的PoC，提取OOB详情
	if vul.PocType == YamlPocType {
		np.extractOOBDetails(ctx, vul, vul.OOBUrl.GetInteractions())
		if len(vul.OOBDetails) <= 0 {
			slog.ErrorContext(ctx, "oob success, but no interactions found", "oobUrl", vul.OOBUrl)
			return false
		}
	}

	// 根据OOB类型添加到不同列表
	if vul.OOBUrl.OOBType == client.LogTypeDNS || vul.OOBUrl.OOBType == client.LogTypeHTTP {
		if len(vul.OOBUrl.TriggerTypes) == 1 && vul.OOBUrl.TriggerTypes[0] == client.LogTypeDNS { // 只有dns协议被触发了
			if len(interactions) > 15 || vul.OOBUrl.OOBType == client.LogTypeHTTP { //nolint:mnd // 大概率是waf触发的误报，正常ping、curl、nslookup命令的日志大概是3-10条记录
				vul.Confidence = ConfidenceLow
			}
		}
		// 检查 OOB 是否在截止时间后触发
		np.checkOOBTriggerTime(vul)
		*dnsOrHTTPOOBVuls = append(*dnsOrHTTPOOBVuls, vul)
	} else {
		*vuls = append(*vuls, vul)
	}

	return true
}

// extractOOBDetails 从interactions中提取 OOB 细节
func (np *NPoC) extractOOBDetails(ctx context.Context, vul *Vulnerability, interactions []*server.Interaction) {
	vul.OOBDetails = make([]*OOBDetail, 0)
	limit := 5
	if len(interactions) < limit {
		limit = len(interactions)
	}

	for _, interaction := range interactions[:limit] {
		detail := &OOBDetail{
			Timestamp:     interaction.Timestamp,
			RemoteAddress: interaction.RemoteAddress,
			Protocol:      interaction.Protocol,
			FullId:        interaction.FullID,
		}

		switch interaction.Protocol {
		case client.LogTypeDNS, client.LogTypeHTTP, client.LogTypeLDAP:
			detail.Request = interaction.RawRequest
			vul.OOBDetails = append(vul.OOBDetails, detail)
		case client.LogTypeRMI:
			detail.Request = vul.OOBUrl.URL()
			vul.OOBDetails = append(vul.OOBDetails, detail)
		default:
			slog.WarnContext(ctx, "invalid OOB protocol", "interaction", interaction)
		}
	}
}

// checkOOBTriggerTime 检查 OOB 是否在截止时间后触发
func (np *NPoC) checkOOBTriggerTime(vul *Vulnerability) {
	if len(vul.OOBDetails) == 0 {
		return
	}

	oobReceiveTime := vul.OOBDetails[0].Timestamp.UTC()
	oobDeadline := vul.OOBUrl.Deadline().Add(2 * time.Second).UTC() //nolint:mnd // 加的两秒为可能oobURL生成了没有立即使用，可能存在一定的误差

	if oobReceiveTime.After(oobDeadline) { // 如果oob触发在url创建12s后的则视为无效，部分waf的触发会比较延后
		vul.Confidence = ConfidenceLow
	}
}

// filterAndCombineResults 过滤并合并所有漏洞结果
func (np *NPoC) filterAndCombineResults(ctx context.Context, vuls, dnsOrHTTPOOBVuls, pocSqliVuls []*Vulnerability) []*Vulnerability {
	finalVuls := vuls

	if len(dnsOrHTTPOOBVuls) > dnsOrHTTPOOBVulMax {
		slog.WarnContext(ctx, "this target has more than 20 vulnerabilities related to dnslog, these results will be removed",
			"oob_vuln_num", len(dnsOrHTTPOOBVuls))

		for _, v := range dnsOrHTTPOOBVuls {
			slog.WarnContext(ctx, "filtered vulnerabilities with dnslog", "vuln_id", v.PoC, "target", v.URL)
		}
	} else {
		if len(dnsOrHTTPOOBVuls) > 10 {
			for _, vul := range dnsOrHTTPOOBVuls {
				slog.WarnContext(ctx, "original confidence level",
					"confidence", string(vul.Confidence),
					"vuln_id", vul.PoC,
					"target", vul.URL,
				)
				vul.Confidence = ConfidenceLow
			}
		}
		finalVuls = append(finalVuls, dnsOrHTTPOOBVuls...)
	}

	if len(pocSqliVuls) > pocSqliVulnMax {
		slog.WarnContext(ctx, "this target has more than 15 vulnerabilities related to sqli, these results will be removed",
			"poc_sql_vuln_num", len(pocSqliVuls))

		for _, v := range pocSqliVuls {
			slog.WarnContext(ctx, "filtered vulnerabilities with sqli", "vuln_id", v.PoC, "target", v.URL)
		}
	} else {
		if len(pocSqliVuls) > 5 {
			for _, vul := range pocSqliVuls {
				vul.Confidence = ConfidenceLow
			}
		}
		finalVuls = append(finalVuls, pocSqliVuls...)
	}

	return finalVuls
}

type Context interface {
	OutputVulnerability(ctx context.Context, vulnerability *Vulnerability) error
	OutputError(ctx context.Context, cError *PoCError) error
}
