package generate

import (
	"fmt"
	"strconv"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

func HttpGetMap(req *httpv.Request, resp *httpv.Response, index int) map[string]any {
	indexStr := "_" + strconv.Itoa(index)
	result := make(map[string]any)
	result[EnvKeyBody] = string(resp.Body)
	result[EnvKeyBody+indexStr] = string(resp.Body)

	for _, cookie := range resp.Cookies() {
		cookieName := strings.ToLower(cookie.Name)
		result[cookieName+indexStr] = cookie.Value
		result[cookieName] = cookie.Value
	}

	var headerStr string
	for key, header := range resp.Header {
		headerStr += fmt.Sprintf("%s: %s\n", key, strings.Join(header, "; "))
		key = strings.ToLower(strings.ReplaceAll(strings.TrimSpace(key), "-", "_"))
		result[key] = strings.Join(header, " ")
		result[key+indexStr] = result[key]
	}
	result[EnvKeyHeader+indexStr] = headerStr
	result[EnvKeyHeader] = headerStr

	result[EnvKeySetCookie] = resp.Header.Get("Set-Cookie")
	result[EnvKeySetCookie+indexStr] = resp.Header.Get("Set-Cookie")

	result[EnvKeyLocation] = resp.Header.Get("Location")
	result[EnvKeyLocation+indexStr] = resp.Header.Get("Location")

	respStr, err := resp.Dump()
	if err != nil {
		respStr = headerStr + "\n" + string(resp.Body)
	}
	result[EnvKeyAll+indexStr] = respStr
	result[EnvKeyAll] = respStr
	result[EnvKeyResponse] = respStr
	result[EnvKeyResponse+indexStr] = respStr

	result[EnvKeyStatusCode+indexStr] = resp.Status
	result[EnvKeyStatusCode] = resp.Status

	// reqStr, _ := req.Dump()
	// result[RequestExpr] = reqStr
	// result[RequestExpr+indexStr] = reqStr
	//
	// // 原nuclei的oob域名与现在我们的格式不一致，有些是校验请求中是否由oob域名的，这里使其默认能校验到
	// result[InteractshRequest] = reqStr + "888888.zxzxzx.zxzxzx.zxzxzx.zxzxzx.zxzxzx"
	// result[InteractshRequest+indexStr] = reqStr + "888888.zxzxzx.zxzxzx.zxzxzx.zxzxzx.zxzxzx"

	// 与nuclei行为一致
	host := req.URL.String()
	result[EnvKeyHost] = host
	result[EnvKeyHost+indexStr] = host

	duration := int(resp.TimeTrace.GetServerHandledTime().Seconds())
	result[EnvKeyDuration] = duration
	result[EnvKeyDuration+indexStr] = duration

	result[EnvKeyContentLength] = len(resp.Body)
	result[EnvKeyContentLength+indexStr] = len(resp.Body)

	return result
}
