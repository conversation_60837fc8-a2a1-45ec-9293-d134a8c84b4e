package generate

import "errors"

const (
	EnvKeyBody          = "body"
	EnvKeyAll           = "all"
	EnvKeyHeader        = "header"
	EnvKeyStatusCode    = "status_code"
	EnvKeyHost          = "host"
	EnvKeyResponse      = "response"
	EnvKeyDuration      = "duration"
	EnvKeyContentLength = "content_length"
	EnvKeySetCookie     = "set_cookie"
	EnvKeyLocation      = "location"
	EnvKeyTemplateId    = "template_id"
	EnvKeyReCookies     = "re_cookies"
	EnvKeyNowIndex      = "now_index"
	EnvKeyExistOOB      = "exist_oob"
	EnvKeyOOBUrl        = "interactsh_url"
)

var StopError = errors.New("stop")
