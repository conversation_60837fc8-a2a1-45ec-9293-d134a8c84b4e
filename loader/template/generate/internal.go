package generate

import (
	"encoding/json"
	"fmt"
	"net/url"
	"path"
	"regexp"
	"strings"

	"github.com/segmentio/ksuid"
	"golang.org/x/net/publicsuffix"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

// HostAnnotation @Host:target overrides the input target with the annotated one (similar to self-contained requests)
var HostAnnotation = regexp.MustCompile(`(?m)^@Host:\s*(.+)\s*\n`)

type Compiler interface {
	Compile() error
}
type domain struct {
	FQDN, RDN, DN, SD string
}

func InitEnvironment(u *url.URL) map[string]any {
	root := fmt.Sprintf("%s://%s", u.Scheme, u.Host)
	escapedPath := u.EscapedPath()
	host := u.Hostname()
	d := newDomain(host)
	return map[string]any{
		"BaseURL":             root,
		"RootURL":             root,
		"Hostname":            u.Host,
		"Host":                host,
		"Port":                u.Port(),
		"FQDN":                d.FQDN,
		"RDN":                 d.RDN,
		"DN":                  d.DN,
		"SD":                  d.SD,
		"Path":                path.Dir(escapedPath),
		"File":                path.Base(escapedPath),
		"Scheme":              u.Scheme,
		"randstr":             ksuid.New().String(),
		"interactsh_protocol": "http dns", // 暂时将所有需要oob_check的都默认先让其check成功，在外部单独做check
		"ip":                  "",
		"username":            "admin",
		"password":            "admin",
	}
}

func MarshalExtractor(mp []map[string]any) string {
	buf := &strings.Builder{}
	encode := json.NewEncoder(buf)
	encode.SetEscapeHTML(false)
	err := encode.Encode(mp)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(text.ToString(buf))
}

func newDomain(host string) domain {
	var d domain
	if text.IsIp(host) {
		return d
	}

	topDomain, err := publicsuffix.EffectiveTLDPlusOne(host)
	if err != nil {
		return d
	}

	i := strings.Index(topDomain, ".")
	if i == -1 {
		return d
	}
	// www.baidu.com
	d.FQDN = host
	// baidu.com
	d.RDN = topDomain
	// baidu
	d.DN = d.RDN[:i]
	i = strings.Index(host, d.DN)
	if i == -1 || i-1 < 0 {
		return d
	}

	// www
	d.SD = host[:i-1]

	return d
}
