package template

import "github.acme.red/intelli-sec/npoc/loader/template/internal"

type Info struct {
	Name           string                 `yaml:"name,omitempty"`
	Description    string                 `yaml:"description,omitempty"`
	Impact         string                 `yaml:"impact,omitempty"`
	Remediation    string                 `yaml:"remediation,omitempty"`
	SeverityHolder string                 `yaml:"severity,omitempty"`
	Authors        internal.StringSlice   `yaml:"author,omitempty"`
	Tags           internal.StringSlice   `yaml:"tags,omitempty"`
	Reference      internal.StringSlice   `yaml:"reference,omitempty"`
	Classification Classification         `yaml:"classification,omitempty"`
	Metadata       map[string]interface{} `yaml:"metadata,omitempty"`
}

type Classification struct {
	CVEID          internal.StringSlice `yaml:"cve-id,omitempty"`
	CWEID          internal.StringSlice `yaml:"cwe-id,omitempty"`
	CVSSMetrics    string               `yaml:"cvss-metrics,omitempty"`
	CVSSScore      float64              `yaml:"cvss-score,omitempty"`
	EPSSScore      float64              `yaml:"epss-score,omitempty"`
	EPSSPercentile float64              `yaml:"epss-percentile,omitempty"`
	CPE            string               `yaml:"cpe,omitempty"`
}
