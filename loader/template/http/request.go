package http

import (
	"errors"
	"fmt"
	"log/slog"
	"net"
	rawHttp "net/http"
	"net/url"
	"regexp"
	"slices"
	"strconv"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/pictor/foundation/slogext"
	"golang.org/x/exp/maps"
	"gopkg.in/yaml.v3"

	"github.acme.red/intelli-sec/common/expression"
	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/loader/template/generate"
	"github.acme.red/intelli-sec/npoc/loader/template/operators"
)

var (

	// target: overrides with the specific value
	reSniAnnotation = regexp.MustCompile(`(?m)^@tls-sni:\s*(.+)\s*\n`)
	// @timeout:duration overrides the input timeout with a custom duration
	reTimeoutAnnotation = regexp.MustCompile(`(?m)^@timeout(:)?\s*(.+)\s*\n`)
	// @once sets the Operators to be executed only once for a specific URL
	reOnceAnnotation = regexp.MustCompile(`(?m)^@once\s*\n`)
	reBaseUrl        = regexp.MustCompile(`(?im)\{\{BaseUrl}}|\{\{RootUrl}}`)
)

type Request struct {
	Path        []string
	Name        string
	Method      string
	Body        string
	Headers     map[string]string
	RawRequests []string
	Operator    operators.Operators
}

func (r *Request) Compile() error {
	if len(r.RawRequests) != 0 {
		for i, rawRequest := range r.RawRequests {
			rawRequest = reOnceAnnotation.ReplaceAllString(rawRequest, "")
			rawRequest = reSniAnnotation.ReplaceAllString(rawRequest, "")
			rawRequest = reTimeoutAnnotation.ReplaceAllString(rawRequest, "")
			r.RawRequests[i] = rawRequest
		}
	} else if len(r.Path) != 0 {
		for _, path := range r.Path {
			builder := strings.Builder{}
			path = reBaseUrl.ReplaceAllString(path, "")
			if path == "" {
				path = "/"
			}
			builder.WriteString(fmt.Sprintf("%s %s HTTP/1.1\n", r.Method, path))
			if _, ok := r.Headers["Host"]; !ok {
				builder.WriteString(fmt.Sprintf("Host: %s\n", "{{Hostname}}"))
			}
			for key, value := range r.Headers {
				builder.WriteString(fmt.Sprintf("%s: %s\n", key, value))
			}
			builder.WriteString("\n")
			builder.WriteString(r.Body)
			r.RawRequests = append(r.RawRequests, builder.String())
		}
	}
	for i, raw := range r.RawRequests {
		raw = strings.TrimSpace(raw)
		if raw == "" {
			return &yaml.TypeError{Errors: []string{"must contain at least one request"}}
		}
		raw = strings.ReplaceAll(raw, "{{interactsh-url}}", "{{interactsh_url}}") // 有-会被当作减法运算，造成歧义
		r.RawRequests[i] = raw
	}

	return r.Operator.Compile()
}

func (r *Request) Execute(c *npoc.HTTPContext, env map[string]any, follows *[]npoc.HTTPFollow) error {
	cookies, ok := env[generate.EnvKeyReCookies].(map[string]*rawHttp.Cookie)
	if !ok {
		cookies = make(map[string]*rawHttp.Cookie)
		env[generate.EnvKeyReCookies] = cookies
	}

	index, ok := env[generate.EnvKeyNowIndex].(int)
	if !ok {
		return errors.New("no index")
	}
	hvReq, ok := buildHttpvReq(c, r, index, env, cookies)
	if !ok { // 某一个请求生成失败(可能包含提取器中的变量提取失败)则终止该poc的扫描
		return generate.StopError
	}
	resp, err := c.Task().Client.Do(c.Context, hvReq)
	if err != nil {
		// 对于oob类型的请求有可能响应会获取不到，但是对是否存在漏洞不造成直接影响
		_, existOOB := env[generate.EnvKeyOOBUrl]
		if !existOOB {
			pocID, ok := env[generate.EnvKeyTemplateId].(string)
			if !ok {
				slog.ErrorContext(c.Context, "failed to parse template id", "data", fmt.Sprintf("%#v", env[generate.EnvKeyTemplateId]))
			}
			c.OutputPoCError(&npoc.PoCError{
				PoC: pocID,
				Err: err,
			})
			return generate.StopError
		}
	}
	// 将请求塞入follows里
	*follows = append(*follows, npoc.HTTPFollow{
		Request:  hvReq,
		Response: resp,
	})
	if resp != nil {
		if !r.Operator.DisableCookie {
			for _, cookie := range resp.Cookies() {
				cookies[cookie.Name] = cookie
			}
			env[generate.EnvKeyReCookies] = cookies
		}
		maps.Copy(env, generate.HttpGetMap(hvReq, resp, index+1))
	}
	return nil
}

func (r *Request) Operators() operators.Operators {
	return r.Operator
}

func (r *Request) GetRawRequests() []string {
	return r.RawRequests
}

func String2Request(s, schema string) (*httpv.Request, error) {
	// 不为nil的时候用于替换hvreq中的URL
	var uri *url.URL
	if hosts := generate.HostAnnotation.FindStringSubmatch(s); len(hosts) > 0 {
		s = generate.HostAnnotation.ReplaceAllString(s, "")
		host := strings.TrimSpace(hosts[1])
		u, err := url.Parse(host)
		if err != nil {
			return nil, err
		}

		if !slices.Contains(httpv.HTTPSchemes, u.Scheme) {
			return nil, fmt.Errorf("invalid URL scheme %q", u.Scheme)
		}

		port := u.Port()
		if port == "" {
			if u.Scheme == "http" {
				u.Host = net.JoinHostPort(u.Host, "80")
			} else {
				u.Host = net.JoinHostPort(u.Host, "443")
			}
		} else if p, err := strconv.Atoi(port); err != nil || p < 0 || p > 65536 {
			return nil, fmt.Errorf("invalid port %q", port)
		}

		uri = u
	}

	hvreq, err := httpv.String2Request(s, schema)
	if err != nil {
		return nil, err
	}

	if uri != nil {
		hvreq.URL.Scheme = uri.Scheme
		hvreq.URL.Host = uri.Host
	}

	return hvreq, nil
}

func buildHttpvReq(c *npoc.HTTPContext, request *Request, index int, env map[string]any, cookies map[string]*rawHttp.Cookie) (*httpv.Request, bool) {
	var (
		err      error
		pocID, _ = env[generate.EnvKeyTemplateId].(string)
	)
	raw := request.RawRequests[index]
	rawRequest, errs := expression.Eval(raw, env)
	if len(errs) != 0 && !request.Operator.SkipVariablesCheck {
		for _, e := range errs {
			if !strings.HasPrefix(e.Error(), "extractor") { // 过滤掉因提取器提取没有提取到数据导致的错误,不应该被记录
				if pocID != "CVE-2023-34124" { // 对于一些特定目标该poc不适用的时候会有报错，过滤报错输出
					slog.ErrorContext(c.Context, "build httpv request failed", slogext.Error(e))
				}
			}
		}
		return nil, false
	}
	var hvReq *httpv.Request
	if request.Operator.Unsafe {
		hvReq = c.Task().Request.Clone()
		if hvReq.URL == nil {
			slog.ErrorContext(c.Context, "request url is empty", slogext.Error(err))
		}
		hvReq.UnsafeRawHTTP = []byte(rawRequest)
	} else {
		hvReq, err = String2Request(rawRequest, c.Task().Request.URL.Scheme)
		if err != nil {
			slog.ErrorContext(c.Context, "build httpv request with strings to request failed", slogext.Error(err))
			return nil, false
		}
		if request.Operator.EnableURLEncode { // 如果没有设置禁用url编码则对url的query进行url编码
			hvReq.URL.RawQuery = hvReq.URL.Query().Encode()
		}
		exitCookie := hvReq.GetCookies()
	nextCookie:
		for _, cookie := range cookies {
			for _, ck := range exitCookie { // 本来请求中就有cookie 的时候，不对重复的cookie参数赋值
				if ck.Name == cookie.Name {
					continue nextCookie
				}
			}
			hvReq.AddCookie(cookie.Name, cookie.Value)
		}
		if hvReq.Header.Get("User-Agent") == "" {
			hvReq.Header.Set("User-Agent", c.Task().Request.Header.Get("User-Agent"))
		}
	}
	if hvReq.URL == nil {
		slog.ErrorContext(c.Context, "request url is empty", slogext.Error(err), "isUnsafe", request.Operator.Unsafe)
	}
	hvReq.FollowRedirects = request.Operator.Redirects
	return hvReq, true
}
