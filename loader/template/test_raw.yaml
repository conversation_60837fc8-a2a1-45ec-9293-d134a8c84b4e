id: CVE-2009-0347
info:
  name: CVE-2009-0347  Autonomy Ultraseek 'cs.html' URI重定向漏洞
  author: ctflearner
  severity: medium
  description: >-
    Autonomy(之前的Verity)Ultraseek搜索引擎中的cs.html存在开放重定向漏洞。远程攻击者可以借助url参数，重新定向用户到任意网站和执行钓鱼攻击。
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2009-0347
    - https://www.exploit-db.com/exploits/32766
    - https://www.kb.cert.org/vuls/id/202753
    - https://exchange.xforce.ibmcloud.com/vulnerabilities/48336
    - >-
      http://sunbeltblog.blogspot.com/2009/01/constant-stream-of-ultraseek-redirects.html
  classification:
    cvss-metrics: CVSS:2.0/AV:N/AC:M/Au:N/C:N/I:P/A:P
    cvss-score: 5
    cwe-id: CWE-59
    cve-id: CVE-2009-0347
    cpe: cpe:2.3:a:autonomy:ultraseek
  metadata:
    advice: >-
      配置文件：在配置文件中限制访问的文件目录，比如 PHP 中 php.ini 配置
      open_basedir。特殊字符过滤：检查用户输入，过滤或转义含有“../”、“..\”、“%00”，“..”，“./”，“#”等跳转目录或字符终止符、截断字符的输入。合法性判断：严格过滤用户输入字符的合法性，比如文件类型、文件地址、文件内容等。对传入的文件名参数进行过滤，并且判断是否是允许获取的文件类型，过滤回溯符
      ../白名单：白名单限定访问文件的路径、名称及后缀名。
    product: autonomy
    version: |
      ultraseek = _nil_
    vendor: autonomy
    type: redirect
  tags: cve,cve2009,redirect,autonomy
http:
  - path:
      - '{{BaseURL}}/cs.html?url=http://www.interact.sh'
    headers:
      Accept: '*/*'
      Content-Type: application/x-www-form-urlencoded
      Connection: keep-alive
    body: |
      <?php echo md5({{data1}});exit();?>
    method: GET
    matchers:
      - type: regex
        part: header
        regex:
          - >-
            (?m)^(?:Location\s*?:\s*?)(?:http?://|//)(?:[a-zA-Z0-9\-_\.@]*)interact\.sh.*$
