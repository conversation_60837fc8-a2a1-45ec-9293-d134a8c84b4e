package template

import (
	"cmp"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	rawHttp "net/http"
	"regexp"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/pkg/logger"
	"golang.org/x/exp/maps"
	"golang.org/x/sync/semaphore"
	"gopkg.in/yaml.v3"

	"github.acme.red/intelli-sec/common/expression"
	"github.acme.red/intelli-sec/common/yamlrule"
	yamlrulehttp "github.acme.red/intelli-sec/common/yamlrule/http"
	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/lib/dnslog"
	"github.acme.red/intelli-sec/npoc/loader/template/code"
	"github.acme.red/intelli-sec/npoc/loader/template/generate"
	"github.acme.red/intelli-sec/npoc/loader/template/http"
	"github.acme.red/intelli-sec/npoc/loader/template/operators"
	matcher2 "github.acme.red/intelli-sec/npoc/loader/template/operators/matcher"
	"github.acme.red/intelli-sec/npoc/loader/template/request"
	"github.acme.red/intelli-sec/npoc/loader/template/tcp"
	"github.acme.red/intelli-sec/npoc/utils/text"

	"github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/foundation/slogext"

	"github.acme.red/intelli-sec/npoc/utils"
)

type Template struct {
	yamlrule.Information
	Requests []request.Requester
}

func (t *Template) UnmarshalYAML(node *yaml.Node) error {
	var tt yamlrule.Template
	if err := node.Decode(&tt); err != nil {
		return err
	}
	for _, nd := range node.Content { // 保持原来的执行顺序将不同的requester添加到[]request.Requester中去
		if nd.Value == "" {
			continue
		}
		switch nd.Value {
		case "code":
			for i, cd := range tt.Codes {
				op := operators.NewOperators(cd.Operators)
				t.Requests = append(t.Requests, &code.Request{
					RequestID:      fmt.Sprintf("%s_code_%d", tt.Information.ID, i),
					TemplateID:     tt.Information.ID,
					Engine:         cd.Engine,
					PythonPackages: cd.PythonPackages,
					Args:           cd.Args,
					Pattern:        cd.Pattern,
					Source:         cd.Source,
					Timeout:        cd.Timeout,
					Operator:       op,
				})
			}
		case "http":
			for _, req := range tt.Requests {
				op := operators.NewOperators(req.Operators)
				switch r := req.Request.(type) {
				case *yamlrulehttp.Raw:
					t.Requests = append(t.Requests, &http.Request{
						Path:        nil,
						Name:        "",
						Method:      "",
						Body:        "",
						Headers:     nil,
						RawRequests: r.RawRequests,
						Operator:    op,
					})
				case *yamlrulehttp.Basic:
					t.Requests = append(t.Requests, &http.Request{
						Path:     r.Path,
						Name:     r.Name,
						Method:   r.Method,
						Headers:  r.Headers,
						Body:     r.Body,
						Operator: op,
					})
				}
			}
		case "tcp":
			for _, req := range tt.TCPs {
				op := operators.NewOperators(req.Operators)
				t.Requests = append(t.Requests, &tcp.Request{
					Operator: op,
					Address:  req.Address,
					Inputs:   req.Inputs,
				})
			}
		}
	}
	t.Information = tt.Information

	return nil
}

func (t *Template) Compile() error {
	if t.Flow != "" {
		if strings.Contains(t.Flow, "&&") {
			t.RequestCondition = "and"
		} else {
			t.RequestCondition = "or"
		}
	}
	t.RequestCondition = cmp.Or(t.RequestCondition, "or")
	for _, requester := range t.Requests {
		if err := requester.Compile(); err != nil {
			return fmt.Errorf("template compile: %s", err)
		}
	}

	return nil
}

func (t *Template) ID() string {
	return t.Information.ID
}

func (t *Template) Protocol() string {
	return npoc.ProtocolHTTP
}

func (t *Template) Metadata() npoc.Metadata {
	if t.Info == nil || t.Info.Metadata == nil {
		return npoc.Metadata{}
	}
	metadata := npoc.Metadata{
		Name:        t.Info.Name,
		PocType:     npoc.YamlPocType,
		Category:    npoc.Category(text.ToString(t.Info.Metadata["type"])),
		Tags:        t.Info.Tags.GetValue(),
		Severity:    npoc.Severity(t.Info.Severity),
		Confidence:  npoc.Confidence(t.Info.Confidence),
		Description: t.Info.Description,
		Product:     text.ToString(t.Info.Metadata["product"]),
		Reference:   t.Info.Reference,
		CPE:         t.Info.Classification.CPE,
	}

	if t.Info.Classification.CVEID != "" {
		metadata.CVE = []string{t.Info.Classification.CVEID}
	}
	if t.Info.Metadata == nil {
		metadata.ProductVersion = nil
	} else if version, ok := t.Info.Metadata["version"].([]string); ok {
		metadata.ProductVersion = version
	} else {
		metadata.ProductVersion = []string{text.ToString(t.Info.Metadata["version"])}
	}

	return metadata
}

func (t *Template) Execute(c *npoc.HTTPContext) error {
	c.Context = logger.WithAttr(c.Context, "poc_id", t.ID())
	if t.OnlyDomain && text.IsIp(c.Task().Request.URL.Hostname()) {
		return nil
	}
	env := generate.InitEnvironment(c.Task().Request.URL)
	env[generate.EnvKeyTemplateId] = t.ID()
	for key, value := range c.Task().Env {
		exprValue, _ := expression.Eval(value, env) // 解决自定义变量多层嵌套的问题
		env[key] = exprValue
	}
	// 将variables的内容注入到环境变量中
	injectVariables(env, t.Variables)
	var (
		success       bool
		isLastRequest bool
	)
	for i, requester := range t.Requests {
		select {
		case <-c.Context.Done():
			return c.Context.Err()
		default:
		}
		success = false
		if i == len(t.Requests)-1 {
			isLastRequest = true
		}
		if requester.Operators().LoadCache {
			data, ok := c.Task().TaskCache.Load(t.ID())
			if ok {
				dataMap, y := data.(map[string]any)
				if y {
					maps.Copy(env, dataMap)
				}
			}
		}
		opts := requester.Operators()
		if len(opts.Payloads) > 0 && opts.Threads > 0 {
			scanQPS := semaphore.NewWeighted(int64(opts.Threads))
			var wg sync.WaitGroup
			for _, payloads := range opts.LoadPayloads(c.Context) {
				if success {
					break
				}
				err := scanQPS.Acquire(c.Context, 1)
				if err != nil {
					return err
				}
				newEnv := maps.Clone(env)
				maps.Copy(newEnv, payloads)
				// 上面的maps.clone只会克隆第一层key-value，当value时指针类型的时候，会出现多个并发同时使用同一个指针
				// 这里cookies是map，在多个并发里面使用到的是同一个map，在两个并发同时读写的时候就会panic，暂时将cookies直接重新复制一遍来临时解决该问题
				// todo 这里可能有其他value是指针类型的时候也会存在同样问题
				if cookies, ok := env[generate.EnvKeyReCookies]; ok {
					ck, ok2 := cookies.(map[string]*rawHttp.Cookie)
					if ok2 {
						newEnv[generate.EnvKeyReCookies] = maps.Clone(ck)
					}
				}
				wg.Add(1)
				go func() {
					defer utils.RecoverFun(c.Context)
					defer scanQPS.Release(1)
					defer wg.Done()
					t.requestExecute(c, requester, newEnv, &success, &isLastRequest)
				}()
			}
			wg.Wait()
			if (t.RequestCondition == "and" && !success) || (t.RequestCondition == "or" && success) {
				break
			} else {
				continue
			}
		}
		t.requestExecute(c, requester, env, &success, &isLastRequest)
		if (t.RequestCondition == "and" && !success) || (t.RequestCondition == "or" && success) {
			break
		}
	}
	return nil
}

func (t *Template) requestExecute(c *npoc.HTTPContext, request request.Requester, env map[string]any, success, isLastRequest *bool) {
	var (
		oobTracker *dnslog.OOBURLTracker
		err        error
		extraRes   = make(map[string]string)
	)
	if c.Task().Client.RdnsClient != nil {
		oobTracker, err = dnslog.NewOOBURLTracker(c.Task().Client.RdnsClient)
		if err != nil {
			slog.ErrorContext(c.Context, "fail to create oob tracker", slogext.Error(err))
		}
		defer func() {
			// 释放OOB URL
			if oobTracker != nil {
				oobTracker.Clean()
			}
		}()
	}

	oobType := getOOBType(request)
	rawRequests := request.GetRawRequests()
	var (
		oobURL  *client.URL
		oobNum  int
		follows = make([]npoc.HTTPFollow, 0, len(rawRequests))
	)
	// 判断该poc是否需要用到oob
	envJson, err := json.Marshal(env)
	if err != nil {
		slog.ErrorContext(c.Context, "json encode error", slogext.Error(err))
	}
	if strings.Contains(string(envJson), "interactsh-url") { // 部分用variable或者payload等方式定义的oob则可能存放到env中
		newEnvJson := strings.ReplaceAll(string(envJson), "interactsh-url", "interactsh_url")
		_ = json.Unmarshal([]byte(newEnvJson), &env)
		if c.Task().Client.RdnsClient == nil {
			c.OutputPoCError(&npoc.PoCError{
				PoC: t.ID(),
				Err: fmt.Errorf("rdns client is nil"),
			})
			return
		}
		oobURL = dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, oobType)
		env[generate.EnvKeyOOBUrl] = oobURL.URL()
		oobNum++
		if oobTracker != nil && oobURL != nil {
			oobTracker.Track(oobURL)
		}
	}
	for _, raw := range rawRequests {
		if strings.Contains(raw, generate.EnvKeyOOBUrl) {
			oobNum++
		}
	}
	// 提取器的变量使用空结构体占位
	for _, ex := range request.Operators().Extractors {
		if ex.VarName() == "" {
			continue
		}
		env[ex.VarName()] = struct{}{}
	}
	// 提取器每次请求所提取出来的数据
	var dynVar map[string][]string
	for i, raw := range rawRequests {
		env[generate.EnvKeyNowIndex] = i
		// 根据提取器的数据生成响应的变量列表
		vars := generateVars(dynVar, request.Operators().IterateAll)
	nextVar:
		for _, variable := range vars {
			select {
			case <-c.Context.Done():
				return
			default:
			}
			if strings.Contains(raw, generate.EnvKeyOOBUrl) {
				if c.Task().Client.RdnsClient == nil {
					c.OutputPoCError(&npoc.PoCError{
						PoC: t.ID(),
						Err: fmt.Errorf("rdns client is nil"),
					})
					return
				}
				oobURL = dnslog.CreateURLAdapter(c.Task().Client.RdnsClient, oobType)
				env[generate.EnvKeyOOBUrl] = oobURL.URL()
				if oobTracker != nil && oobURL != nil {
					oobTracker.Track(oobURL)
				}
			}
			maps.Copy(env, variable)
			err = request.Execute(c, env, &follows)
			if err != nil {
				if !errors.Is(err, generate.StopError) {
					slog.ErrorContext(c.Context, "request execute failed", slogext.Error(err))
				}
				continue
			}

			if len(request.Operators().Extractors) > 0 {
				for _, et := range request.Operators().Extractors {
					part := et.GetPart() // 如果提取器的part指定的不是当前模块，那么这里就不对当前请求结果做提取
					if strings.Contains(part, "_") && !strings.Contains(part, fmt.Sprintf("_%d", i+1)) {
						continue
					}
					if e := et.Extract(env); len(e) > 0 {
						if dynVar == nil {
							dynVar = make(map[string][]string)
						}
						for _, vs := range e {
							results := GetDynVars(vs.(map[string]any))
							if et.VarName() != "" {
								dynVar[et.VarName()] = append(dynVar[et.VarName()], results...)
								if data, ok := extraRes[et.VarName()]; ok {
									extraRes[et.VarName()] = data + "," + strings.Join(results, ",")
								} else {
									extraRes[et.VarName()] = strings.Join(results, ",")
								}
							} else {
								if data, ok := extraRes[npoc.ExtraKeyDefaultExtra]; ok {
									extraRes[npoc.ExtraKeyDefaultExtra] = data + "," + strings.Join(results, ",")
								} else {
									extraRes[npoc.ExtraKeyDefaultExtra] = strings.Join(results, ",")
								}
							}
						}
					}
				}
			}
			// if len(extraRes) > 0 {
			// 	data, _ := json.Marshal(extraRes)
			// 	slog.DebugContext(c.Context, "extractor success", "extractor_result", string(data))
			// }
			for _, mt := range request.Operators().Matchers {
				matched, _ := mt.Match(env)
				if mt.Negative() {
					matched = !matched
				}
				if !matched {
					// 如果matchers-condition 为 and 存在有校验不通过的则不存在漏洞
					if request.Operators().MatchersCondition == matcher2.AndCondition {
						continue nextVar
					}
				}
				if matched && request.Operators().MatchersCondition == matcher2.OrCondition {
					if oobNum > 0 && oobURL == nil { // 是oob的poc但是涉及到oob的请求还没发包，暂不能说明漏洞存在
						continue
					}
					*success = true
					if *isLastRequest || t.RequestCondition == "or" {
						vulnerability := t.makeVuln(extraRes, oobURL, follows, request, env)
						c.OutputVulnerability(vulnerability)
						if oobTracker != nil && oobURL != nil {
							oobTracker.Mark(oobURL)
						}
					} else if len(request.Operators().Payloads) > 0 { // 当前请求matchers校验成功中，有payload且后面还有raw需要执行，将payload中的各个变量值存储到缓存中，方便后面raw使用
						cacheMap := make(map[string]any)
						for key := range request.Operators().Payloads {
							cacheMap[key] = env[key]
						}
						c.Task().TaskCache.Store(t.ID(), cacheMap)
					}
					// 非oob的出现漏洞就提交然后终止改poc的扫描了，oob的不能打断后续发包
					if oobNum == 0 {
						return
					}
					if oobNum > 1 { // 如果涉及到多个请求存在oobURL则不会将所有请求都保存
						follows = follows[:0:0]
					}
				}
			}
			// 如果matchers-condition 为 and 走到这里说明所有的matcher都校验成功了
			if request.Operators().MatchersCondition == matcher2.AndCondition {
				if oobNum > 0 && oobURL == nil { // 是oob的poc但是还没有生成过oobURL，暂不能说明漏洞存在
					continue
				}
				*success = true
				if *isLastRequest || t.RequestCondition == "or" {
					vulnerability := t.makeVuln(extraRes, oobURL, follows, request, env)
					c.OutputVulnerability(vulnerability)
					if oobTracker != nil && oobURL != nil {
						oobTracker.Mark(oobURL)
					}
				} else if len(request.Operators().Payloads) > 0 { // 当前请求matchers校验成功中，有payload且后面还有raw需要执行，将payload中的各个变量值存储到缓存中，方便后面raw使用
					cacheMap := make(map[string]any)
					for key := range request.Operators().Payloads {
						cacheMap[key] = env[key]
					}
					c.Task().TaskCache.Store(t.ID(), cacheMap)
				}
				// oob的不能打断后续发包
				if oobNum == 0 {
					return
				}
				if oobNum > 1 { // 如果涉及到多个请求存在oobURL则不会将所有请求都保存
					follows = follows[:0:0]
				}
			}
		}
	}
}

func (t *Template) makeVuln(extraRes map[string]string, oobURL *client.URL, follows []npoc.HTTPFollow, request request.Requester, env map[string]any) *npoc.Vulnerability {
	if request.Operators().StopAtFirstMatch && len(follows) > 0 { // 第一个校验成功就退出的一般出现在fuzz的poc中，fuzz的poc中只需要保留最后一个请求包
		follows = follows[len(follows)-1:]
	}
	var method, vulnUrl string
	followsLen := len(follows)
	if followsLen > 0 && follows[followsLen-1].Request != nil {
		method = follows[followsLen-1].Request.Method
		vulnUrl = follows[followsLen-1].Request.URL.String()
	}
	vulnerability := &npoc.Vulnerability{
		PoC:         t.ID(),
		Name:        t.Info.Name,
		Category:    t.Metadata().Category,
		Severity:    t.Metadata().Severity,
		Confidence:  t.Metadata().Confidence,
		Method:      method,
		Description: t.Info.Description,
		URL:         vulnUrl,
		Extra:       make(map[string]string),
	}
	if len(extraRes) > 0 {
		for key, value := range extraRes {
			vulnerability.Extra[key] = value
		}
	}
	for key := range request.Operators().Payloads {
		if _, ok := env[key]; ok {
			value, ok := env[key].(string)
			if !ok {
				continue
			}
			vulnerability.Extra[key] = value
		}
	}
	if oobURL != nil {
		vulnerability.OOBUrl = oobURL
		vulnerability.Extra[npoc.ExtraKeyOOBUrl] = oobURL.URL()
	}
	vulnerability.HTTP = &npoc.VulnerabilityHTTP{
		// 计算follows的下标，最多只取最后10个请求
		Follows: follows[max(len(follows)-10, 0):],
	}

	return vulnerability
}

func generateVars(dynVar map[string][]string, iterateAll bool) []map[string]any {
	// 保证没有提取器的时候也能至少循环一次
	if len(dynVar) == 0 {
		return make([]map[string]any, 1)
	}

	// 为了与nuclei行为一致的功能，当没开启iterate-all功能的时候
	// 也能根据提取器变量来获取值
	if !iterateAll {
		result := make(map[string]any, len(dynVar))
		for k, vs := range dynVar {
			if len(vs) > 0 {
				result[k] = vs[0]
			}
		}

		return []map[string]any{result}
	}

	// 以长度最长的切片为生成次数
	n := 0
	for _, v := range dynVar {
		if len(v) > n {
			n = len(v)
		}
	}

	results := make([]map[string]any, 0, n)

	for i := range n {
		result := make(map[string]any)
		for k, vs := range dynVar {
			// i大于vs就取vs最后一个值
			if len(vs) <= i {
				result[k] = vs[len(vs)-1]
			}

			if len(vs) > i {
				result[k] = vs[i]
			}
		}
		results = append(results, result)
	}

	return results
}

func GetDynVars(xs map[string]any) []string {
	result := make([]string, 0)
	for _, x := range xs {
		switch v := x.(type) {
		case map[string]any:
			result = append(result, GetDynVars(v)...)
		case string:
			result = append(result, v)
		case []string:
			return v
		default:
			return nil
		}
	}

	return result
}

var dslOOBRegex = regexp.MustCompile(`contains\(interactsh_protocol,\s?["|'](.*)["|']\)`)

func getOOBType(request request.Requester) string {
	oobType := client.LogTypeDNS
	for _, mt := range request.Operators().Matchers {
		switch m := mt.Matcher.(type) {
		case *matcher2.Words:
			if m.Part == "interactsh_protocol" {
				switch len(m.Words) {
				case 1:
					oobType = m.Words[0]
				case 2: //nolint:mnd // 2
					if m.Words[0] == client.LogTypeHTTP || m.Words[1] == client.LogTypeHTTP {
						oobType = client.LogTypeHTTP
					}
				}
				return oobType
			}
		case *matcher2.DSL:
			for _, dsl := range m.Dsl {
				res := dslOOBRegex.FindStringSubmatch(dsl)
				if len(res) == 2 {
					return res[1]
				}
			}
		}
	}
	return oobType
}

func injectVariables(env map[string]any, vars *yamlrule.Variables) {
	if vars == nil {
		return
	}
	for range 3 { // 避免variables的顺序错误导致eval获取不到后面定义的变量
		var stop = true
		for _, v := range *vars {
			name, err := expression.Eval(text.ToString(v.Value), env)
			if len(err) != 0 {
				env[text.ToString(v.Name)] = v.Value
				stop = false
			} else {
				env[text.ToString(v.Name)] = name
			}
		}
		if stop {
			break
		}
	}

}
