package code

import (
	"bytes"
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/internal/config"
	"github.acme.red/intelli-sec/npoc/loader/template/generate"
	"github.acme.red/intelli-sec/npoc/loader/template/operators"
	"github.acme.red/intelli-sec/npoc/pkg/gozero"
	gozerotypes "github.acme.red/intelli-sec/npoc/pkg/gozero/types"
	"github.acme.red/pictor/foundation/slogext"
)

const (
	binaryToolsDir = "/opt/npoc/binary-tools"
	binaryEnvKey   = "BinaryToolDir"
	defaultTimeout = 30
)

type Request struct {

	// RequestID is the optional id of the request
	RequestID  string `yaml:"request-id,omitempty" json:"request-id,omitempty" jsonschema:"title=request-id of the request,description=RequestID is the optional RequestID of the Request"`
	TemplateID string `yaml:"template-id,omitempty" json:"template-id,omitempty" jsonschema:"title=template-id of the template"`
	// description: |
	//   Engine type
	Engine         []string `yaml:"engine,omitempty" json:"engine,omitempty" jsonschema:"title=engine,description=Engine"`
	PythonPackages []string `yaml:"python-packages,omitempty" json:"python-packages,omitempty"`
	// description: |
	//   PreCondition is a condition which is evaluated before sending the request.
	PreCondition string `yaml:"pre-condition,omitempty" json:"pre-condition,omitempty" jsonschema:"title=pre-condition for the request,description=PreCondition is a condition which is evaluated before sending the request"`
	// description: |
	//   Engine Arguments
	Args []string `yaml:"args,omitempty" json:"args,omitempty" jsonschema:"title=args,description=Args"`
	// description: |
	//   Pattern preferred for file name
	Pattern string `yaml:"pattern,omitempty" json:"pattern,omitempty" jsonschema:"title=pattern,description=Pattern"`
	// description: |
	//   Source File/Snippet
	Source string `yaml:"source,omitempty" json:"source,omitempty" jsonschema:"title=source file/snippet,description=Source snippet"`
	// 指定脚本运行超时时间,单位秒s
	Timeout  int                 `yaml:"timeout,omitempty" json:"timeout,omitempty" jsonschema:"title=timeout,description=Timeout"`
	Operator operators.Operators `yaml:"-" json:"-"`

	gozero *gozero.Gozero
	src    *gozero.Source
}

func (r *Request) Compile() error {
	if *config.APPEnv == config.ASMAPPENV {
		return fmt.Errorf("code protoclo is disabled in the %s app env", config.ASMAPPENV)
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(defaultTimeout*20)*time.Second)
	defer cancel()
	engines, err := getPyVenv(ctx, r.Engine, r.TemplateID)
	if err != nil {
		return fmt.Errorf("get python venv failed, error: %w", err)
	}
	gozeroOptions := &gozero.Options{
		Engines:                  engines,
		Args:                     r.Args,
		EarlyCloseFileDescriptor: true,
	}

	engine, err := gozero.New(gozeroOptions)
	if err != nil {
		return fmt.Errorf("[%s] engines '%s' not available on host, error: %w", r.RequestID, strings.Join(gozeroOptions.Engines, ","), err)
	}
	r.gozero = engine
	var src *gozero.Source

	src, err = gozero.NewSourceWithString(r.Source, r.Pattern, "")
	if err != nil {
		return err
	}
	r.src = src
	if r.Timeout == 0 {
		r.Timeout = defaultTimeout
	}
	if len(r.PythonPackages) > 0 {
		r.PythonPackages = append(r.PythonPackages, "PySocks")
	}
	for _, pythonPackage := range r.PythonPackages { // 对于未安装的依赖进行安装
		err = ensurePythonPackageInstalled(engines, pythonPackage)
		if err != nil {
			return fmt.Errorf("python install pkg failed,package: %s, error: %w", pythonPackage, err)
		}
	}
	return r.Operator.Compile()
}

func (r *Request) Execute(c *npoc.HTTPContext, env map[string]any, follows *[]npoc.HTTPFollow) error {
	metaSrc, err := gozero.NewSourceWithString("", "", "") // 临时环境变量文件
	if err != nil {
		return err
	}
	defer func() {
		if err := metaSrc.Cleanup(); err != nil {
			slog.ErrorContext(c.Context, "error cleaning up meta", slogext.Error(err))
		}
	}()
	ctx, cancel := context.WithTimeout(c.Context, time.Duration(r.Timeout)*time.Second) // 设置脚本最多运行时间
	defer cancel()
	var addedBinaryEnvKey bool
	for key, value := range env {
		if key == binaryEnvKey {
			addedBinaryEnvKey = true
		}
		metaSrc.AddVariable(gozerotypes.Variable{Name: key, Value: fmt.Sprintf("%v", value)})
	}
	if !addedBinaryEnvKey {
		metaSrc.AddVariable(gozerotypes.Variable{Name: binaryEnvKey, Value: binaryToolsDir})
	}
	gOutput, err := r.gozero.Eval(ctx, r.src, metaSrc)
	if err != nil {
		slog.ErrorContext(c.Context, "error evaluating Go output", slogext.Error(err))
	}
	if gOutput == nil {
		// write error to stderr buff
		var buff bytes.Buffer
		if err != nil {
			buff.WriteString(err.Error())
		} else {
			buff.WriteString("no output something went wrong")
		}
		gOutput = &gozerotypes.Result{
			Stderr: buff,
		}
	}
	if errorOutput := gOutput.Stderr.String(); errorOutput != "" {
		slog.ErrorContext(c.Context, "exec run exist error", "output", errorOutput)
	}
	dataOutputString := strings.Trim(gOutput.Stdout.String(), " \n\r\t")
	slog.DebugContext(c.Context, "exec run output", "result", dataOutputString)
	env[generate.EnvKeyResponse] = dataOutputString
	env[generate.EnvKeyAll] = dataOutputString
	index, ok := env[generate.EnvKeyNowIndex].(int)
	if ok {
		env[fmt.Sprintf("%s_%d", generate.EnvKeyResponse, index+1)] = dataOutputString
	}
	return nil
}

func (r *Request) Operators() operators.Operators {
	return r.Operator
}

func (r *Request) GetRawRequests() []string {
	return []string{r.Source}
}
