package code

import (
	"bytes"
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/exec"
	"path"
	"regexp"
	"runtime"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/cmdexec"
	"github.acme.red/pictor/foundation/slogext"
	ver "github.com/hashicorp/go-version"
)

var (
	pythonVenvDir        = "/tmp/npoc/pyvenv"
	pythonBinaryReg      = regexp.MustCompile("python([0-9.]*)")
	pythonInstallVersion = regexp.MustCompile("Installed Python-([0-9.]*) to")
	pythonVersion        = regexp.MustCompile("Python ([0-9.]*)")
	notExistEnvVersion   *ver.Version
)

func init() {
	notExistEnvVersion, _ = ver.NewVersion("3.3")
	tmpDir := os.TempDir()
	pythonVenvDir = path.Join(tmpDir, "npoc", "pyvenv")
	err := os.MkdirAll(pythonVenvDir, 0777)
	if err != nil {
		slog.Error("mkdir pyvenv dir failed", slogext.Error(err))
	}

}

// 处理python的版本问题，以及创建虚拟环境用于运行对应的poc
func getPyVenv(ctx context.Context, engines []string, dirName string) ([]string, error) {
	for _, engine := range engines {
		if strings.HasPrefix(engine, "py") {
			rawPath, err := exec.LookPath(engine)
			if err != nil {
				// 没有对应版本的python则主动安装指定版本的python，目前只支持linux系统中的下载
				if runtime.GOOS != "linux" {
					continue
				}
				groups := pythonBinaryReg.FindStringSubmatch(engine)
				if len(groups) > 1 {
					rawPath, err = installPyEnv(ctx, groups[1])
					slog.Info("install successfully", slog.String("rawPath", rawPath))
					if err != nil {
						return engines, err
					}
				}
			}
			if rawPath != "" {
				// 有对应的python版本后创建虚拟运行环境供当前requester使用
				venvPath := path.Join(pythonVenvDir, dirName)
				err = createVirtualEnv(ctx, rawPath, venvPath)
				if err != nil {
					return engines, err
				}
				return []string{path.Join(venvPath, "bin", "python")}, nil
			}
		}
	}
	return engines, nil
}

// 安装对应的运行程序并返回安装好的程序地址
func installPyEnv(ctx context.Context, version string) (string, error) {
	// 使用pyenv安装对应的python版本
	output, err := cmdexec.RunContext(ctx, "pyenv", "install", version)
	if err != nil {
		return "", fmt.Errorf("run  pyenv failed, err: %w,", err)

	}
	var installedVersion string
	groups := pythonInstallVersion.FindStringSubmatch(output)
	if len(groups) <= 1 {
		return "", fmt.Errorf("pyenv install failed, version: %s, output: %s", version, output)
	}
	installedVersion = groups[1]
	// 将安装的python版本使用链接形式添加到环境变量中
	output, err = cmdexec.RunContext(ctx, "ln", "-sf", fmt.Sprintf("/root/.pyenv/versions/%s/bin/python", installedVersion), "/usr/bin/python"+version)
	if err != nil {
		return "", err
	}
	if output != "" {
		return "", fmt.Errorf("install failed, version: %s, output: %s", version, output)
	}
	binary := "python" + version
	rawPath, err := exec.LookPath(binary)
	if err != nil {
		return "", fmt.Errorf("get binary failed: %s, error: %w", binary, err)
	}
	return rawPath, nil
}

// 创建 Python 虚拟环境
func createVirtualEnv(ctx context.Context, binary, venvPath string) error {
	// 检查虚拟环境是否已存在
	if _, err := os.Stat(venvPath); err == nil {
		return nil
	}
	var args = []string{"-m", "venv", venvPath}
	output, err := cmdexec.RunContext(ctx, binary, "-V")
	if err != nil {
		return fmt.Errorf("python -V run failed,binary: %s, output: %s, err: %w", binary, output, err)
	}
	groups := pythonVersion.FindStringSubmatch(output)
	if len(groups) <= 1 {
		return fmt.Errorf("python get version failed, output: %s", output)
	}
	pyVersion := groups[1]

	v1, err := ver.NewVersion(pyVersion)
	if err != nil {
		return fmt.Errorf("parse version failed: %s, error: %w", pyVersion, err)
	}
	// 如果py版本小于3.3则需要安装virtualenv，然后由该工具安装虚拟环境
	if v1.LessThan(notExistEnvVersion) {
		virtualenvBinary, err := installVirtualenv(ctx, binary, pyVersion)
		if err != nil {
			return fmt.Errorf("install virtualenv failed, error: %w", err)
		}
		binary = virtualenvBinary
		args = []string{venvPath}
	}
	_, err = cmdexec.RunContext(ctx, binary, args...)
	if err != nil {
		return fmt.Errorf("python creat virtualEnv failed, binary: %s, args: %s , err: %w", args, binary, err)
	}
	return nil
}

func installVirtualenv(ctx context.Context, binary, version string) (string, error) {
	output := bytes.Buffer{}
	cmd := exec.CommandContext(ctx, binary, "-m", "pip", "install", "virtualenv")
	cmd.Stdout = &output
	cmd.Stderr = &output
	// 启动命令
	if err := cmd.Start(); err != nil {
		return "", fmt.Errorf("creat venv cmd start failed, command: %s, error: %w, output: %s", cmd.String(), err, output.String())
	}
	err := cmd.Wait()
	if err != nil {
		return "", fmt.Errorf("creat venv cmd wait failed, command: %s, error: %w, output: %s", cmd.String(), err, output.String())
	}

	return fmt.Sprintf("/root/.pyenv/versions/%s/bin/virtualenv", version), nil
}
