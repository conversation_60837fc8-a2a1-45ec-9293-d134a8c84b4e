package code

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"regexp"
	"strings"
	"time"
)

// 检查 Python 包是否已安装
func isPythonPackageInstalled(pythonPath, packageName string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if strings.Contains(packageName, "==") { // 如果是带版本号的，则只判断是否存在任意版本的包
		packageName = strings.Split(packageName, "==")[0]
	}
	// 使用 Python 的 importlib 来检查包是否可以导入
	checkCmd := fmt.Sprintf("import importlib.util; print(importlib.util.find_spec('%s') is not None)", packageName)
	cmd := exec.CommandContext(ctx, pythonPath, "-c", checkCmd)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		return false, fmt.Errorf("error checking Python package: %v, stderr: %s", err, stderr.String())
	}

	// 解析输出结果
	result := strings.TrimSpace(stdout.String())
	return result == "True", nil
}

// 安装 Python 包
func installPythonPackage(pythonPath, packageName string) error {

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 使用 pip 安装包
	pipArgs := []string{"-m", "pip", "install", packageName}
	cmd := exec.CommandContext(ctx, pythonPath, pipArgs...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 执行安装命令
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to install Python package: %v\nStderr: %s", err, stderr.String())
	}

	return nil
}

// 查找 Python 解释器路径
func findPythonPath(engines []string) (string, error) {
	for _, candidate := range engines {
		path, err := exec.LookPath(candidate)
		if err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("could not find Python interpreter (python3 or python)")
}

func isValidPackageName(name string) bool {
	// 简单的包名验证，防止命令注入
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9_\-=.]+$`)
	return validPattern.MatchString(name)
}

// 检查并安装 Python 包
func ensurePythonPackageInstalled(engines []string, packageName string) error {
	// 验证包名是否有效
	if !isValidPackageName(packageName) {
		return fmt.Errorf("invalid package name: %s", packageName)
	}
	// 查找 Python 解释器的路径
	pythonPath, err := findPythonPath(engines)
	if err != nil {
		return err
	}
	// 检查包是否已安装
	installed, err := isPythonPackageInstalled(pythonPath, packageName)
	if err != nil {
		return err
	}

	if installed {
		return nil
	}

	// 安装包
	return installPythonPackage(pythonPath, packageName)
}
