package extractor

import (
	"strings"

	"golang.org/x/exp/maps"

	"github.acme.red/intelli-sec/common/expression"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

type DSL struct {
	Dsl    []string
	dslExp []*expression.Expr
	Operator
}

func (d *DSL) Compile() error {
	d.dslExp = make([]*expression.Expr, len(d.Dsl))
	var err error
	for i := range d.Dsl {
		d.dslExp[i] = expression.Compile(d.Dsl[i])
		if d.dslExp[i].Err() != nil {
			return err
		}
	}

	return d.Operator.Compile()
}

func (d *DSL) Extract(env map[string]any) map[string]any {
	results := make(map[string]map[string]struct{})
	for _, expr := range d.dslExp {
		result, err := expr.Evaluate(env)
		if err != nil {
			if strings.Contains(err.<PERSON><PERSON><PERSON>(), "No parameter") {
				continue
			} else {
				return nil
			}
		}

		if _, ok := results[expr.String()]; !ok {
			results[expr.String()] = make(map[string]struct{})
		}

		s := text.ToString(result)
		if _, ok := results[expr.String()][s]; !ok {
			results[expr.String()][s] = struct{}{}
		}
	}

	var vars []string
	for _, v := range results {
		vars = append(vars, maps.Keys(v)...)
	}
	d.InjectVariable(env, vars)
	return d.GenerateResult(results)
}
