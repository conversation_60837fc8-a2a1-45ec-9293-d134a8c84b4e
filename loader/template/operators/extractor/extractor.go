package extractor

import (
	"strconv"

	"golang.org/x/exp/maps"

	yamlruleextract "github.acme.red/intelli-sec/common/yamlrule/opreators/extractor"
	"github.acme.red/intelli-sec/npoc/loader/template/generate"
)

// extractor 的 part 类型
const allPart = "all"

// Extractors 对外实际暴露的Extractors对象
type Extractors struct {
	Extracter
}

type Extracter interface {
	generate.Compiler
	Extract(map[string]any) map[string]any
	VarName() string
	GetPart() string
}

type Operator struct {
	yamlruleextract.Operator
}

func (e *Operator) Compile() error {
	if e.Part == "" {
		e.Part = allPart
	}

	return nil
}

func (e *Operator) InjectVariable(env map[string]any, vars []string) {
	if e.Name == "" || len(vars) == 0 {
		return
	}

	if len(vars) == 1 {
		env[e.Name] = vars[0]
		return
	}

	// 为了与nuclei行为一致的功能，用来支持提取到的内容是列表的情况下在变量名上加上数字
	// 来进行索引，比如提取器的变量名是v，提取到的内容是["1", "2", "3"]，使用v0变量
	// 则是"1"，使用v1变量则是"2"，v2是"3"
	for i, v := range vars {
		env[e.Name+strconv.Itoa(i)] = v
	}

	if value, ok := env[e.Name]; ok {
		switch v := value.(type) {
		case string:
			env[e.Name] = append([]string{v}, vars...)
		case struct{}:
			env[e.Name] = vars
		case []string:
			env[e.Name] = append(v, vars...)
		}
	} else {
		env[e.Name] = vars
	}
}

func (e *Operator) VarName() string {
	return e.Name
}

func (e *Operator) GetPart() string {
	return e.Part
}

func (e *Operator) GenerateResult(exprs map[string]map[string]struct{}) map[string]any {
	if len(exprs) == 0 {
		return nil
	}

	results := make(map[string]any, len(exprs))
	for k, v := range exprs {
		vs := maps.Keys(v)
		if len(vs) == 1 {
			results[k] = vs[0]
		} else {
			results[k] = vs
		}
	}

	if e.Name != "" {
		results = map[string]any{e.Name: results}
	}

	return map[string]any{
		e.Type: results,
	}
}
