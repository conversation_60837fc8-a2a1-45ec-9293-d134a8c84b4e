package extractor

import (
	"regexp"

	"golang.org/x/exp/maps"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Regexp struct {
	Regex  []string
	regexp []*regexp.Regexp
	Group  int
	Operator
}

func (r *Regexp) Compile() error {
	r.regexp = make([]*regexp.Regexp, len(r.Regex))

	var err error
	for i := range r.Regex {
		r.regexp[i], err = regexp.Compile(r.Regex[i])
		if err != nil {
			return err
		}
	}

	return r.Operator.Compile()
}

func (r *Regexp) Extract(env map[string]any) map[string]any {
	data, ok := env[r.Part]
	if !ok {
		return nil
	}

	exprs := make(map[string]map[string]struct{})
	for i, regex := range r.regexp {
		matches := regex.FindAllStringSubmatch(text.ToString(data), -1)

		for _, match := range matches {
			if len(match) <= r.Group {
				continue
			}

			if _, ok := exprs[r.Regex[i]]; !ok {
				exprs[r.Regex[i]] = make(map[string]struct{})
			}

			// 将提取到的结果与对应的表达式结合
			if _, ok := exprs[r.Regex[i]][match[r.Group]]; !ok {
				exprs[r.Regex[i]][match[r.Group]] = struct{}{}
			}
		}
	}

	var vars []string
	for _, v := range exprs {
		vars = append(vars, maps.Keys(v)...)
	}
	r.InjectVariable(env, vars)
	return r.GenerateResult(exprs)
}
