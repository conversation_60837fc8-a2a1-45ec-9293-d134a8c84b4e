package extractor

import (
	"encoding/json"

	"github.com/itchyny/gojq"
	"golang.org/x/exp/maps"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type JSON struct {
	JSON []string
	jq   []*gojq.Code
	Operator
}

func (j *JSON) Compile() error {
	j.jq = make([]*gojq.Code, len(j.JSON))
	for i, query := range j.JSON {
		q, err := gojq.Parse(query)
		if err != nil {
			return err
		}

		j.jq[i], err = gojq.Compile(q)
		if err != nil {
			return err
		}
	}

	return j.Operator.Compile()
}

func (j *JSON) Extract(env map[string]any) map[string]any {
	data, ok := env[j.Part]
	if !ok {
		return nil
	}

	var jsonObj any

	if err := json.Unmarshal([]byte(text.ToString(data)), &jsonObj); err != nil {
		return nil
	}

	exprs := make(map[string]map[string]struct{})
	for i, k := range j.jq {
		iter := k.Run(jsonObj)
		for {
			v, ok := iter.Next()
			if !ok {
				break
			}
			if _, ok := v.(error); ok {
				break
			}
			var result string
			if res, err := text.JSONScalarToString(v); err == nil {
				result = res
			} else if res, err := json.Marshal(v); err == nil {
				result = string(res)
			} else {
				result = text.ToString(v)
			}
			if result == "" {
				break
			}
			if _, ok := exprs[j.JSON[i]]; !ok {
				exprs[j.JSON[i]] = make(map[string]struct{})
			}

			// 将提取到的结果与对应的表达式结合
			if _, ok := exprs[j.JSON[i]][result]; !ok {
				exprs[j.JSON[i]][result] = struct{}{}
			}
		}
	}

	var vars []string
	for _, v := range exprs {
		vars = append(vars, maps.Keys(v)...)
	}
	j.InjectVariable(env, vars)
	return j.GenerateResult(exprs)
}
