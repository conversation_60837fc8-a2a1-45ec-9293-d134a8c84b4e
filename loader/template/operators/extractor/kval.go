package extractor

import (
	"strings"

	"golang.org/x/exp/maps"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Kval struct {
	CaseInsensitive bool
	Kval            []string
	Operator
}

func (k *Kval) Compile() error {
	if k.CaseInsensitive {
		for i := range k.Kval {
			k.Kval[i] = strings.ToLower(k.Kval[i])
		}
	}

	return k.Operator.Compile()
}

func (k *Kval) Extract(env map[string]any) map[string]any {
	if k.CaseInsensitive {
		newMp := env
		env = make(map[string]any, len(newMp))
		for key, value := range newMp {
			if s, ok := value.(string); ok {
				value = strings.ToLower(s)
			}
			env[key] = value
		}
	}

	exprs := make(map[string]map[string]struct{})
	for i, kv := range k.Kval {
		item, ok := env[kv]
		if !ok {
			continue
		}

		if _, ok = item.(struct{}); ok {
			continue
		}
		itemString := text.ToString(item)

		if _, ok := exprs[k.Kval[i]]; !ok {
			exprs[k.Kval[i]] = make(map[string]struct{})
		}

		// 将提取到的结果与对应的表达式结合
		if _, ok := exprs[k.Kval[i]][itemString]; !ok {
			exprs[k.Kval[i]][itemString] = struct{}{}
		}
	}
	var vars []string
	for _, v := range exprs {
		vars = append(vars, maps.Keys(v)...)
	}
	k.InjectVariable(env, vars)
	return k.GenerateResult(exprs)
}
