package extractor

import (
	"strings"

	"github.com/antchfx/htmlquery"
	"github.com/antchfx/xmlquery"
	"golang.org/x/exp/maps"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Xpath struct {
	Xpath     []string
	Attribute string
	Operator
}

func (x *Xpath) Compile() error {
	return x.Operator.Compile()
}

func (x *Xpath) Extract(env map[string]any) map[string]any {
	data, ok := env[x.Part]
	if !ok {
		return nil
	}

	var exprs map[string]map[string]struct{}
	s := text.ToString(data)
	if strings.HasPrefix(s, "<?xml") {
		exprs = x.ExtractXML(s)
	} else {
		exprs = x.ExtractHTML(s)
	}

	if exprs == nil {
		return nil
	}

	var vars []string
	for _, v := range exprs {
		vars = append(vars, maps.Keys(v)...)
	}
	x.InjectVariable(env, vars)
	return x.GenerateResult(exprs)
}

func (x *Xpath) ExtractHTML(html string) map[string]map[string]struct{} {
	doc, err := htmlquery.Parse(strings.NewReader(html))
	if err != nil {
		return nil
	}

	exprs := make(map[string]map[string]struct{})
	for i, k := range x.Xpath {
		nodes, err := htmlquery.QueryAll(doc, k)
		if err != nil {
			continue
		}
		for _, node := range nodes {
			var value string

			if x.Attribute != "" {
				value = htmlquery.SelectAttr(node, x.Attribute)
			} else {
				value = htmlquery.InnerText(node)
			}
			if _, ok := exprs[x.Xpath[i]]; !ok {
				exprs[x.Xpath[i]] = make(map[string]struct{})
			}
			if _, ok := exprs[x.Xpath[i]][value]; !ok {
				exprs[x.Xpath[i]][value] = struct{}{}
			}
		}
	}
	return exprs
}

func (x *Xpath) ExtractXML(xml string) map[string]map[string]struct{} {
	doc, err := xmlquery.Parse(strings.NewReader(xml))
	if err != nil {
		return nil
	}

	exprs := make(map[string]map[string]struct{})
	for _, k := range x.Xpath {
		nodes, err := xmlquery.QueryAll(doc, k)
		if err != nil {
			continue
		}
		for i, node := range nodes {
			var value string

			if x.Attribute != "" {
				value = node.SelectAttr(x.Attribute)
			} else {
				value = node.InnerText()
			}
			if _, ok := exprs[x.Xpath[i]]; !ok {
				exprs[x.Xpath[i]] = make(map[string]struct{})
			}
			if _, ok := exprs[x.Xpath[i]][value]; !ok {
				exprs[x.Xpath[i]][value] = struct{}{}
			}
		}
	}
	return exprs
}
