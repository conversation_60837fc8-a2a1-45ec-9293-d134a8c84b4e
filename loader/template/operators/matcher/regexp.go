package matcher

import (
	"regexp"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Regexp struct {
	Regex  []string
	regexp []*regexp.Regexp
	Operator
}

func (r *Regexp) Compile() error {
	r.regexp = make([]*regexp.Regexp, len(r.Regex))

	var err error
	for i := range r.Regex {
		r.regexp[i], err = regexp.Compile(r.Regex[i])
		if err != nil {
			return err
		}
	}

	return r.Operator.Compile()
}

func (r *Regexp) Match(mp map[string]any) (bool, []string) {
	data, ok := mp[r.Part]
	if !ok {
		return false, nil
	}
	dataStr := text.ToString(data)
	var matchedRegexes []string
	for _, regex := range r.regexp {
		if !regex.MatchString(dataStr) {
			switch r.Condition {
			case AndCondition:
				return false, []string{}
			case OrCondition:
				continue
			}
		}

		currentMatches := regex.FindAllString(dataStr, -1)
		// If the condition was an OR, return on the first match.
		if r.Condition == OrCondition {
			return true, currentMatches
		}

		matchedRegexes = append(matchedRegexes, currentMatches...)
	}
	if len(matchedRegexes) > 0 {
		return true, matchedRegexes
	}
	return false, []string{}
}
