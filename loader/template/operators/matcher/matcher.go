package matcher

import (
	yamlrulematch "github.acme.red/intelli-sec/common/yamlrule/opreators/matcher"
	"github.acme.red/intelli-sec/npoc/loader/template/generate"
)

const (
	AndCondition = "and"
	OrCondition  = "or"

	// matcher 的 part 类型
	// headersPart = "headers"
	// bodyPart    = "body"
	// allPart     = "all"
)

type Matcher interface {
	generate.Compiler
	Match(map[string]any) (bool, []string)
	Negative() bool
}

// Matchers 反序列化后对外暴露的对象
type Matchers struct {
	Matcher
}

type Operator struct {
	yamlrulematch.Operator
}

func (o *Operator) Compile() error {
	if o.Part == "" {
		o.Part = generate.EnvKeyAll
	}

	if o.Condition == "" {
		o.Condition = OrCondition
	}

	return nil
}

func (o *Operator) Negative() bool {
	return o.Neg
}
