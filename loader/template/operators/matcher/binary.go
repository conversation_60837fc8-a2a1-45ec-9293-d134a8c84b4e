package matcher

import (
	"encoding/hex"
	"strings"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Binary struct {
	Binary        []string
	binaryDecoded []string
	Operator
}

func (b *Binary) Compile() error {
	b.binaryDecoded = make([]string, len(b.Binary))
	for i := range b.Binary {
		bt, err := hex.DecodeString(b.Binary[i])
		if err != nil {
			return err
		}

		b.binaryDecoded[i] = string(bt)
	}

	return b.Operator.Compile()
}

func (b *Binary) Match(mp map[string]any) (bool, []string) {
	var matchedBinary []string
	data, ok := mp[b.Part]
	if !ok {
		return false, nil
	}
	dataStr := text.ToString(data)
	for _, binary := range b.binaryDecoded {
		if !strings.Contains(dataStr, binary) {
			switch b.Condition {
			case AndCondition:
				return false, []string{}
			case OrCondition:
				continue
			}
		}

		if b.Condition == OrCondition {
			return true, []string{binary}
		}

		matchedBinary = append(matchedBinary, binary)
	}
	if len(matchedBinary) > 0 {
		return true, matchedBinary
	}
	return false, []string{}
}
