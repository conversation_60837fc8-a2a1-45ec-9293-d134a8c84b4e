package matcher

import (
	"log/slog"

	"github.acme.red/intelli-sec/common/expression"
)

type DSL struct {
	Dsl    []string
	dslExp []*expression.Expr
	Operator
}

func (d *DSL) Compile() error {
	d.dslExp = make([]*expression.Expr, len(d.Dsl))
	for i := range d.Dsl {
		d.dslExp[i] = expression.Compile(d.Dsl[i])
		if d.dslExp[i].Err() != nil {
			return d.dslExp[i].Err()
		}
	}

	return d.Operator.Compile()
}

func (d *DSL) Match(env map[string]any) (bool, []string) {
	for i, expr := range d.dslExp {
		result, err := expr.Evaluate(env)
		if err != nil {
			switch d.Condition {
			case AndCondition:
				return false, nil
			case OrCondition:
				continue
			}
		}

		r, ok := result.(bool)
		if !ok {
			slog.Error("MatchDSL", "id", env["template-id"], "msg",
				"matchers return value of a DSL statement must return a boolean value")
			continue
		}

		if !r {
			switch d.Condition {
			case AndCondition:
				return false, nil
			case OrCondition:
				continue
			}
		}

		if d.Condition == OrCondition || len(d.dslExp)-1 == i {
			return true, nil
		}
	}

	return false, nil
}
