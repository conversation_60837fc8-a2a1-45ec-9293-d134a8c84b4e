package matcher

import (
	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Size struct {
	Size []int
	Operator
}

func (s *Size) Compile() error {
	return s.Operator.Compile()
}

func (s *Size) Match(mp map[string]any) (bool, []string) {
	data, ok := mp[s.Part]
	if !ok {
		return false, nil
	}
	dataStr := text.ToString(data)
	for _, size := range s.Size {
		if size == len(dataStr) {
			return true, nil
		}
	}
	return false, nil
}
