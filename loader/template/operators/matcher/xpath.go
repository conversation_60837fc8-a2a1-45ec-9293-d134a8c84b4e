package matcher

import (
	"strings"

	"github.com/antchfx/htmlquery"
	"github.com/antchfx/xmlquery"

	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Xpath struct {
	Xpath []string
	Operator
}

func (x *Xpath) Compile() error {
	return x.Operator.Compile()
}

func (x *Xpath) Match(mp map[string]any) (bool, []string) {
	data, ok := mp[x.Part]
	if !ok {
		return false, nil
	}
	dataStr := text.ToString(data)
	if strings.HasPrefix(dataStr, "<?xml") {
		return x.MatchHTML(dataStr), nil
	}
	return false, nil
}

func (x *Xpath) MatchHTML(corpus string) bool {
	doc, err := htmlquery.Parse(strings.NewReader(corpus))
	if err != nil {
		return false
	}

	matches := 0

	for _, k := range x.Xpath {
		nodes, err := htmlquery.QueryAll(doc, k)
		if err != nil {
			continue
		}

		if len(nodes) == 0 {
			switch x.Condition {
			case AndCondition:
				return false
			case OrCondition:
				continue
			}
		}

		if x.Condition == OrCondition {
			return true
		}

		matches = matches + len(nodes)
	}
	return matches > 0
}

func (x *Xpath) MatchXML(corpus string) bool {
	doc, err := xmlquery.Parse(strings.NewReader(corpus))
	if err != nil {
		return false
	}

	matches := 0

	for _, k := range x.Xpath {
		nodes, err := xmlquery.QueryAll(doc, k)
		if err != nil {
			continue
		}

		if len(nodes) == 0 {
			switch x.Condition {
			case AndCondition:
				return false
			case OrCondition:
				continue
			}
		}

		if x.Condition == OrCondition {
			return true
		}
		matches = matches + len(nodes)
	}

	return matches > 0
}
