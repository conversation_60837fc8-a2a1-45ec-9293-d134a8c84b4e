package matcher

import (
	"encoding/hex"
	"strings"

	"github.acme.red/intelli-sec/common/expression"
	"github.acme.red/intelli-sec/npoc/utils/text"
)

type Words struct {
	CaseInsensitive bool
	Words           []string
	Encoding        string
	Operator
}

func (w *Words) Compile() error {
	if w.Encoding == "hex" {
		for i := range w.Words {
			b, err := hex.DecodeString(w.Words[i])
			if err != nil {
				return err
			}

			w.Words[i] = string(b)
		}
	}

	if w.CaseInsensitive {
		for i := range len(w.Words) {
			w.Words[i] = strings.ToLower(w.Words[i])
		}
	}

	return w.Operator.Compile()
}

func (w *Words) Match(env map[string]any) (bool, []string) {
	var matchedWords []string
	data, ok := env[w.Part]
	if !ok {
		return false, nil
	}
	dataStr := text.ToString(data)
	if w.CaseInsensitive {
		dataStr = strings.ToLower(dataStr)
	}
	for _, word := range w.Words {
		wd, err := expression.Eval(word, env)
		if len(err) != 0 {
			// slog.Warn("Matchers Words", "id", env["template-id"], "msg", err, "word", word)
			if w.Condition == AndCondition {
				return false, nil
			}
			wd = word
		}
		if !strings.Contains(dataStr, wd) {
			switch w.Condition {
			case AndCondition:
				return false, nil
			case OrCondition:
				continue
			}
		}
		matchedWords = append(matchedWords, wd)

		if w.Condition == OrCondition {
			return true, matchedWords
		}
	}
	if len(matchedWords) > 0 {
		return true, matchedWords
	}
	return false, nil
}
