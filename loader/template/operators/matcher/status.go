package matcher

import "github.acme.red/intelli-sec/npoc/loader/template/generate"

type Status struct {
	Status []int
	Operator
}

func (s *Status) Compile() error {
	return s.Operator.Compile()
}

func (s *Status) Match(mp map[string]any) (bool, []string) {
	// status 类型的不存在condition和part
	// 当有多个status校验的时候，默认只要满足一个则匹配成功
	statusCode, ok := mp[generate.EnvKeyStatusCode]
	if !ok {
		return false, nil
	}
	for _, status := range s.Status {
		if statusCode != status {
			continue
		}
		// Return on the first match.
		return true, nil
	}
	return false, nil
}
