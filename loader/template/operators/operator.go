package operators

import (
	"context"
	"encoding/json"
	"log/slog"
	"strings"

	"github.acme.red/pictor/foundation/slogext"
	"github.com/thoas/go-funk"

	"github.acme.red/intelli-sec/common/yamlrule/opreators"
	yamlruleextract "github.acme.red/intelli-sec/common/yamlrule/opreators/extractor"
	yamlrulematch "github.acme.red/intelli-sec/common/yamlrule/opreators/matcher"
	"github.acme.red/intelli-sec/npoc/lib/cache"
	extractor2 "github.acme.red/intelli-sec/npoc/loader/template/operators/extractor"
	matcher2 "github.acme.red/intelli-sec/npoc/loader/template/operators/matcher"
)

const (
	AttackClusterBomb  = "clusterbomb"
	AttackPitchfork    = "pitchfork"
	AttackBatteringRam = "batteringram"
)

type Operators struct {
	opreators.Supported
	Matchers   []matcher2.Matchers
	Extractors []extractor2.Extractors
}

func (o *Operators) Compile() error {
	if o.Threads == 0 { // 默认25个并发
		o.Threads = 25
	}
	if o.AttackType == "" {
		o.AttackType = AttackClusterBomb
	}

	if o.MatchersCondition == "" {
		o.MatchersCondition = matcher2.OrCondition
	}

	for _, mt := range o.Matchers {
		err := mt.Compile()
		if err != nil {
			return err
		}
	}
	for _, ext := range o.Extractors {
		err := ext.Compile()
		if err != nil {
			return err
		}
	}

	return nil
}

func (o *Operators) LoadPayloads(ctx context.Context) []map[string]any { //nolint:gocognit // 1
	payloads := make(map[string][]string)
	for key, values := range o.Payloads {
		var newValues []string
		for _, value := range values.GetValue() {
			if strings.Contains(value, "helpers/") {
				dicValues, err := cache.LoadDict(value)
				if err != nil {
					slog.ErrorContext(ctx, "failed to load helpers values", "file_path", value, slogext.Error(err))
					continue
				}
				newValues = append(newValues, dicValues...)
			} else {
				newValues = append(newValues, value)
			}
		}
		payloads[key] = newValues
	}
	// clusterbomb n * m， 													--- 设置为默认值
	// pitchfork  n 对 m 取最短次
	// batteringram 默认，只允许有一组payloads
	var rawPayloads []map[string]any
	switch o.AttackType {
	case AttackPitchfork:
		rawPayloads = pitchfork(payloads)
	case AttackClusterBomb:
		rawPayloads = clusterbomb(payloads)
	case AttackBatteringRam:
		rawPayloads = clusterbomb(payloads)
	default:
		slog.ErrorContext(ctx, "this attack type is not supported")
	}
	if rawPayloads != nil {
		payloadBytes, err := json.Marshal(rawPayloads)
		if err != nil {
			slog.ErrorContext(ctx, "LoadPayloads() marshal payloads to json: "+err.Error())
		}
		if strings.Contains(string(payloadBytes), "{{user}}") {
			for _, payload := range rawPayloads {
				// 确保 username 和 password 的类型是 string
				username, ok1 := payload["username"].(string)
				password, ok2 := payload["password"].(string)
				if !ok1 || !ok2 {
					slog.ErrorContext(ctx, "LoadPayloads() Invalid payload structure")
					continue
				}
				if strings.Contains(password, "{{user}}") {
					newPass := strings.Replace(password, "{{user}}", username, -1)
					payload["password"] = newPass
				}
			}
		}
	}
	return rawPayloads
}

func pitchfork(payloads map[string][]string) []map[string]any {
	var lenSlice []int
	for _, values := range payloads {
		lenSlice = append(lenSlice, len(values))
	}
	if len(lenSlice) == 0 {
		slog.Error("load payloads failed,lenSlice.length is zero ")
		return []map[string]any{}
	}
	minLen := funk.MinInt(lenSlice)
	if minLen == 0 {
		return nil
	}
	result := make([]map[string]any, 0, minLen)
	for i := range minLen {
		mp := make(map[string]any)
		for key := range payloads {
			mp[key] = payloads[key][i]
		}
		result = append(result, mp)
	}
	return result
}

func clusterbomb(payloads map[string][]string) []map[string]any {
	// 获取所有键
	keys := make([]string, 0, len(payloads))
	for k := range payloads {
		keys = append(keys, k)
	}

	// 初始化结果列表
	var result []map[string]any

	// 递归函数，用于生成笛卡尔积
	var generate func(index int, current map[string]any, paths [][]string)
	generate = func(index int, current map[string]any, paths [][]string) {
		// 如果已经遍历完所有键
		if index == len(keys) {
			// 将当前组合添加到结果中
			result = append(result, current)
			return
		}

		// 当前键
		key := keys[index]
		// 当前键的值列表
		values := payloads[key]

		// 遍历当前键的所有值
		for _, value := range values {
			// 复制当前组合，避免在递归中修改
			next := make(map[string]any, len(current))
			for k, v := range current {
				next[k] = v
			}
			// 添加当前键和值到组合中
			next[key] = value

			// 如果不是最后一个键，则递归
			if index+1 < len(keys) {
				generate(index+1, next, paths)
			} else {
				// 最后一个键，使用paths来遍历其他键的剩余值（但在这个简单例子中，我们不需要paths）
				// 直接添加当前组合到结果中
				result = append(result, next)
			}
		}
	}

	// 初始化第一个组合为空
	initial := make(map[string]any)
	generate(0, initial, nil) // paths参数在此示例中未使用

	return result
}

func NewOperators(n opreators.Operators) Operators {
	ms := make([]matcher2.Matchers, len(n.Matchers))
	for i, m := range n.Matchers {
		ms[i] = NewMatch(m.Match, m.Operator)
	}

	es := make([]extractor2.Extractors, len(n.Extractors))
	for i, e := range n.Extractors {
		es[i] = NewExtract(e.Extractor, e.Operator)
	}

	return Operators{
		Matchers:   ms,
		Extractors: es,
		Supported:  n.Supported,
	}
}

func NewExtract(e any, operator yamlruleextract.Operator) extractor2.Extractors {
	var extract extractor2.Extracter
	switch v := e.(type) {
	case *yamlruleextract.Regexp:
		extract = &extractor2.Regexp{
			Regex: v.Regex,
			Operator: extractor2.Operator{
				Operator: operator,
			},
			Group: v.Group,
		}
	case *yamlruleextract.DSL:
		extract = &extractor2.DSL{
			Dsl: v.Dsl,
			Operator: extractor2.Operator{
				Operator: operator,
			},
		}
	case *yamlruleextract.Kval:
		extract = &extractor2.Kval{
			Kval: v.Kval,
			Operator: extractor2.Operator{
				Operator: operator,
			},
		}
	case *yamlruleextract.JSON:
		extract = &extractor2.JSON{
			JSON: v.JSON,
			Operator: extractor2.Operator{
				Operator: operator,
			},
		}
	case *yamlruleextract.Xpath:
		extract = &extractor2.Xpath{
			Xpath: v.Xpath,
			Operator: extractor2.Operator{
				Operator: operator,
			},
		}
	}

	return extractor2.Extractors{Extracter: extract}
}

func NewMatch(m any, operator yamlrulematch.Operator) matcher2.Matchers {
	var match matcher2.Matcher
	switch v := m.(type) {
	case *yamlrulematch.DSL:
		match = &matcher2.DSL{
			Dsl: v.DSL,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	case *yamlrulematch.Words:
		match = &matcher2.Words{
			Words:           v.Words,
			CaseInsensitive: v.CaseInsensitive,
			Encoding:        v.Encoding,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	case *yamlrulematch.Status:
		match = &matcher2.Status{
			Status: v.Status,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	case *yamlrulematch.Regexp:
		match = &matcher2.Regexp{
			Regex: v.Regexp,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	case *yamlrulematch.Binary:
		match = &matcher2.Binary{
			Binary: v.Binary,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	case *yamlrulematch.Xpath:
		match = &matcher2.Xpath{
			Xpath: v.Xpath,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	case *yamlrulematch.Size:
		match = &matcher2.Size{
			Size: v.Size,
			Operator: matcher2.Operator{
				Operator: operator,
			},
		}
	}
	return matcher2.Matchers{Matcher: match}
}
