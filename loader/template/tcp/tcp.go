package tcp

import (
	"errors"
	"fmt"
	"log/slog"
	"strings"

	"github.acme.red/pictor/foundation/slogext"

	"github.acme.red/intelli-sec/common/expression"
	"github.acme.red/intelli-sec/common/yamlrule/tcp"
	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/loader/template/generate"
	"github.acme.red/intelli-sec/npoc/loader/template/operators"
)

const (
	defaultReadLen = 4096
)

type Request struct {
	Operator operators.Operators
	Address  string
	Datas    []string
	Inputs   []*tcp.Input
	UseTLS   bool
}

func (r *Request) Compile() error {
	if strings.HasPrefix(r.Address, "tls://") {
		r.UseTLS = true
		r.Address = strings.TrimPrefix(r.Address, "tls://")
	}
	r.Datas = make([]string, len(r.Inputs))
	for i, input := range r.Inputs {
		input.Data = strings.ReplaceAll(input.Data, "{{interactsh-url}}", "{{interactsh_url}}") // 有-会被当作减法运算，造成歧义
		if input.Read == 0 {
			input.Read = defaultReadLen
		}
		r.Datas[i] = input.Data
	}
	return r.Operator.Compile()
}

func (r *Request) Execute(c *npoc.HTTPContext, env map[string]any, follows *[]npoc.HTTPFollow) error {
	if c.Task().Client.TCPClient == nil {
		return errors.New("no tcp client")
	}
	index, ok := env[generate.EnvKeyNowIndex].(int)
	if !ok {
		slog.ErrorContext(c.Context, "no index")
		return errors.New("no index")
	}
	pocID, _ := env[generate.EnvKeyTemplateId].(string)
	input := r.Inputs[index]
	rawData := input.Data
	rawRequest, errs := expression.Eval(rawData, env)
	if len(errs) != 0 && !r.Operator.SkipVariablesCheck {
		for _, e := range errs {
			if !strings.HasPrefix(e.Error(), "extractor") { // 过滤掉因提取器提取没有提取到数据导致的错误,不应该被记录
				slog.ErrorContext(c.Context, "expression eval failed", slogext.Error(e))
			}
		}
		return generate.StopError
	}
	address, errs := expression.Eval(r.Address, env)
	if len(errs) > 0 {
		return nil
	}
	useTLS := r.UseTLS
	if !strings.Contains(address, ":") {
		if c.Task().Request.URL.Scheme == "https" {
			address += ":443"
			useTLS = true // 443默认要使用tls
		} else if c.Task().Request.URL.Scheme == "http" {
			address += ":80"
		}
	}
	result, err := c.Task().Client.TCPClient.DoWithRawData(c.Context, []byte(rawRequest), address, useTLS, input.Read)
	if err != nil {
		c.OutputPoCError(&npoc.PoCError{
			PoC: pocID,
			Err: fmt.Errorf("sent raw data failed: %v", err),
		})
		return generate.StopError
	}
	resultStr := string(result)
	env[generate.EnvKeyResponse] = result
	env[generate.EnvKeyAll] = resultStr
	env[fmt.Sprintf("%s_%d", generate.EnvKeyResponse, index+1)] = resultStr
	return nil
}

func (r *Request) Operators() operators.Operators {
	return r.Operator
}

func (r *Request) GetRawRequests() []string {
	return r.Datas
}
