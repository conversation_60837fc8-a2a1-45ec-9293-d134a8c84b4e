package template

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"slices"
	"testing"

	"gopkg.in/yaml.v2"
)

func TestTemplate(t *testing.T) {
	home := os.Getenv("HOME")
	var template []string
	err := filepath.Walk(home+"/nuclei-templates/template", func(path string, info fs.FileInfo, _ error) error {
		if info.IsDir() {
			return nil
		}

		template = append(template, path)
		return nil
	})
	if err != nil {
		fmt.Println(err)
	}

	err = filepath.Walk(home+"/nuclei-templates/http/", func(path string, info fs.FileInfo, _ error) error {
		if info.IsDir() {
			return nil
		}

		b, err := os.ReadFile(path)
		if err != nil {
			t.Log(err)
			return err
		}

		var tp Template
		err = yaml.Unmarshal(b, &tp)
		if err != nil {
			if slices.Contains(template, path) {
				t.Log(path)
			}
			// } else {
			// 	t.Log(tp.HTTP, path)
		}

		return nil
	})
	if err != nil {
		fmt.Println(err)
	}
}
