package internal

import (
	"errors"
	"strings"

	"github.com/thoas/go-funk"
)

// StringSlice NOTE: 此类型为template的特殊类型，应该只使用在template序列化上
//
// 仿照nuclei源码中的StringSlice类型用于template中某些字段，
// 这些字段可能是字符串或数组类型，比如：tags，author等字段，
// 此类型将它们统一处理成字符串切片
type StringSlice struct {
	Value []string
}

func (s *StringSlice) UnmarshalYAML(unmarshal func(any) error) error {
	var strs []string
	if err := unmarshal(&strs); err == nil {
		s.Value = strs
		return nil
	}

	var str string
	if err := unmarshal(&str); err != nil {
		return err
	}

	if strings.TrimSpace(str) == "" {
		return errors.New("empty fields")
	}

	strs = strings.Split(str, ",")
	s.Value = funk.FilterString(strs, func(s string) bool {
		if strings.TrimSpace(s) == "" || strings.TrimSpace(s) == " " {
			return false
		}
		return true
	})

	return nil
}
