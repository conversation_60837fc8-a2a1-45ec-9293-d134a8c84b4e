package npoc

import (
	"context"
	"fmt"
	"log/slog"
	"slices"
	"strings"

	"github.com/thoas/go-funk"
)

type executePoCFunc func(ctx context.Context, poc PoC, task Task, result *TaskResult) error

func (np *NPoC) initProtocolEntries(pocs []PoC, opt Option) error {
	np.mProtocolEntries = make(map[string]*protocolEntry)
	mPoC := map[string]PoC{}
	mFunc := np.getProtocolExecuteFuncMap()
	for _, poc := range pocs {
		metadata := poc.Metadata()
		if metadata.Name == "" {
			return fmt.Errorf("invalid poc metadata, metadata.Name is empty")
		}

		if metadata.PocType == YamlPocType { // yaml poc的过滤逻辑
			// 按照id来筛选，如果没有传入包含的也没有传入排除的则默认全扫
			if len(opt.IncludeYamlIds) > 0 { // 如果有传入包含的id则认为只扫描这包含的部分id
				if !slices.Contains(opt.IncludeYamlIds, poc.ID()) {
					continue
				}
			} else if len(opt.ExcludeYamlIds) > 0 { // 如果只输入了排除的id则只排除这部分id
				if slices.Contains(opt.ExcludeYamlIds, poc.ID()) {
					continue
				}
			}
			// 按照风险等级来筛选
			if len(opt.IncludeYamlSeverities) > 0 { // 如果有输入包含的风险等级则排除非包含的风险等级
				if !slices.Contains(opt.IncludeYamlSeverities, string(metadata.Severity)) {
					continue
				}
			}
			// 指纹匹配模式，根据指纹来筛选
			if opt.FingerprintMode {
				var (
					tagInFingerprint bool
					newFingerprints  []string
				)
				// defaultTags := []string{"takeover", "exposure", "misc"} // 指纹匹配模式默认也要扫描信息类poc,暂时该逻辑去除
				for _, name := range opt.FingerprintNames {
					name = strings.ToLower(name)
					newFingerprints = append(newFingerprints, name)
					parts := strings.Split(name, " ")
					newFingerprints = append(newFingerprints, parts...)
					parts2 := strings.Split(name, "-")
					newFingerprints = append(newFingerprints, parts2...)
					parts3 := strings.Split(name, "_")
					newFingerprints = append(newFingerprints, parts3...)
				}
				newFingerprints = funk.UniqString(newFingerprints)
				tags := metadata.Tags
				for _, tag := range tags {
					if slices.Contains(newFingerprints, strings.ToLower(tag)) {
						tagInFingerprint = true
						break
					}
				}
				if !tagInFingerprint { // 所有tags都不在指纹内，则过滤掉该poc
					continue
				}
			}
		} else if metadata.PocType == GenericPocType { // 通用漏洞过滤逻辑
			if len(opt.IncludeCommonIds) > 0 { // 如果有传入包含的id则认为只扫描这包含的部分id
				if !slices.Contains(opt.IncludeCommonIds, poc.ID()) {
					continue
				}
			} else { // 如果没有传入就全都不扫
				continue
			}
		} else {
			slog.Error("unknown poc type", "poc id", poc.ID(), "type", metadata.PocType)
			continue
		}

		if mPoC[poc.ID()] != nil {
			return &PoCError{
				PoC: poc.ID(),
				Err: ErrExist,
			}
		}
		entry := np.mProtocolEntries[poc.Protocol()]
		if entry == nil {
			fn := mFunc[poc.Protocol()]
			if fn == nil {
				return &ProtocolError{
					Protocol: poc.Protocol(),
					Err:      ErrNotExist,
				}
			}
			entry = &protocolEntry{
				PoCs:       nil,
				ExecutePoC: fn,
			}
			np.mProtocolEntries[poc.Protocol()] = entry
		}
		mPoC[poc.ID()] = poc
		entry.PoCs = append(entry.PoCs, poc)
	}
	return nil
}

func (np *NPoC) getProtocolExecuteFuncMap() map[string]executePoCFunc {
	return map[string]executePoCFunc{
		ProtocolHTTP: np.executeHTTPPoC,
	}
}
