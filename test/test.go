package test

import (
	"context"
	"net/http"
	"net/url"
	"strings"

	oobClient "github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/internal/config"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

var (
	OOBHost  = "dnsx.cc"
	OOBToken = "wzPkZlQcthJQv2IyLW7eXCAfAOQteb0E" // nolint:gosec // 测试
	OOBUrl   = "http://dnsx.cc:8080"
)

func CreateNewTestClient(oob bool) (*httpv.Client, error) {
	*config.Proxy = "socks5://127.0.0.1:8083"
	taskClient, err := httpv.NewClient(context.Background(), httpv.ClientOpt{
		HostReqMaxParallel: 10,
		HostErrMaxNum:      30,
		FailRetries:        1,
		Proxy:              *config.Proxy,
	})
	if err != nil {
		return nil, err
	}
	if oob {
		*config.OOBHost = OOBHost
		*config.OOBToken = OOBToken
		*config.OOBURL = OOBUrl
		rdnsClient, err := httpv.NewRdnsClient(oobClient.Options{
			OobHost:   *config.OOBHost,
			ServerURL: *config.OOBURL,
			Token:     *config.OOBToken,
		}, *config.Proxy)
		if err != nil {
			return nil, err
		}
		taskClient.RdnsClient = rdnsClient
	}
	return taskClient, nil
}

func GetURLParam(rawURL, paramName string) string {
	u, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}

	query := u.Query()
	if values, exists := query[paramName]; exists && len(values) > 0 {
		return values[0]
	}

	if u.RawQuery == "" && strings.Contains(u.Path, "=") {
		parts := strings.Split(u.Path, "?")
		if len(parts) > 1 {
			query, _ = url.ParseQuery(parts[1])
			if values, exists := query[paramName]; exists && len(values) > 0 {
				return values[0]
			}
		}
	}

	return ""
}

// CreateTestHTTPContext Helper function to create HTTPContext for tests
func CreateTestHTTPContext(targetURL string, method string, body []byte, contentType string) (*npoc.HTTPContext, error) {
	ctx := context.Background()
	client, err := CreateNewTestClient(true)
	if err != nil {
		return nil, err
	}

	var req *httpv.Request
	if method == http.MethodPost || method == http.MethodPut || method == http.MethodPatch { // Added PATCH
		req, err = httpv.NewRequest(method, targetURL, body)
		if err == nil && contentType != "" {
			req.Header.Set("Content-Type", contentType)
		}
	} else {
		req, err = httpv.NewRequest(method, targetURL, nil)
	}

	if err != nil {
		return nil, err
	}
	req.ParseParam()

	resp, err := client.Do(ctx, req)
	if err != nil {
		return nil, err
	}
	return npoc.CreateNewTestHTTPContext(ctx, req, resp, client), nil
}
