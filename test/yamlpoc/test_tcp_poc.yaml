id: test_tcp_poc
info:
  name: test code poc
  author: default
  severity: high
  description: test code poc
  metadata:
    type: info-leak
  tags: tcp

tcp:
  # 目标地址也可以是{{Host}}:3389 指定目标主机的其他地址
  - address: '{{Hostname}}'
    inputs:
      # tcp请求发送的数据包
      - data: "GET /test_tcp_poc1 HTTP/1.1\r\nHost: www.example.com\r\nConnection: close\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\r\nAccept-Language: zh-CN,zh;q=0.9,en;q=0.8\r\n\r\n"
        # 读取响应包的长度
        read: 1024
      - data: "GET /test_tcp_poc2 HTTP/1.1\r\nHost: www.example.com\r\nConnection: close\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\r\nAccept-Language: zh-CN,zh;q=0.9,en;q=0.8\r\n\r\n"
        read: 1024
    matchers:
      - type: dsl
        dsl:
          - contains_all(response_1, "404", "Example") || contains_all(response_2, "404", "Example")
