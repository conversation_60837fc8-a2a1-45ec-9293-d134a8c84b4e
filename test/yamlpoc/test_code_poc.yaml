id: test_code_poc
info:
  name: test code poc
  author: default
  severity: high
  description: test code poc
  metadata:
    type: info-leak
  tags: code

code:
  - engine:
      #      - py
      - python3.9
    # python脚本依赖的三方包名，不提供版本号则下载默认版本
    python-packages:
      - requests
      - pySock
    # 将source中的脚本生成的临时文件名称，*代表随机名字，.py代表特定文件后缀
    pattern: "*.py"
    # 脚本允许运行的最长时间
    timeout: 30
    source: |
      import os

      import requests

      if __name__ == "__main__":
          try:
              # 获取环境变量中的目标地址
              base_url = os.getenv("BaseURL")
              # 获取环境变量中的代理地址
              proxies ={
                  'http': os.getenv("proxy"),
                  'https': os.getenv("proxy"),
              }
              # 发送GET请求
              response = requests.get(base_url + '/test_code_poc', verify=False, proxies=proxies)
              # 获取响应内容
              content = response.text

              # 检查是否包含指定字符串
              if 'Example Domain' in content:
                  print('success, exist vuln')
              else:
                  print('failed')


          except requests.exceptions.RequestException as e:
              # 处理请求异常
              print('fail')
              print(f'Error: {e}')
    matchers:
      - type: word
        # 脚本中打印到控制台中的数据都会被存到response字段中，作为校验器数据源
        part: response
        words:
          - success, exist vuln