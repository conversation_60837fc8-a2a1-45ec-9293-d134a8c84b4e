id: test_unsafe_http_poc
info:
  name: test code poc
  author: default
  severity: high
  description: test code poc
  # 按照指定的漏洞类型赋值，请不要填任意值
  metadata:
    type: info-leak
  tags: unsafe

http:
  - raw:
      - |-
        GET /test_unsafe_http_poc HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
    # 使用tcp对{{Hostname}}目标原封不动的发送上面的请求，主要用于请求不符合标准http请求格式的时候使用
    unsafe: true
    matchers:
      - type: dsl
        dsl:
          - contains_all(response, "Example")
