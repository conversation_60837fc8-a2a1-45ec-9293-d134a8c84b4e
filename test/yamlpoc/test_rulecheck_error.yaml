id: test_raw_http_poc
info:
  name: test raw http poc
  author: default
  severity: high
  description: test raw http poc
  tags: raw

http:
  # 原始格式的http请求
  - raw:
      - |-
        GET /test_raw_http_poc HTTP/1.1
        Host: {{Hostname}}
        Accept: */*
    matchers:
      - type: dsl
        dsl:
          # 校验响应中是否包含Example和Domain
          - contains_all(response, "Example", "Domain")
    extractors:
      - type: regex
        name: data
        group: 1
        regex:
          - This domain is for use in (.*) examples in documents
