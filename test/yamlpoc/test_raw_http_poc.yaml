id: test_raw_http_poc
info:
  name: test raw http poc
  author: default
  severity: high
  confidence: high
  description: test raw http poc
  metadata:
    # 按照指定的漏洞类型赋值，请不要填任意值
    product: test_raw_http_poc
    vendor: test
    fingerprint-id: 'test:::test_raw_http_poc'
    type: info-leak
  tags: raw

http:
  # 原始格式的http请求
  - raw:
      - |-
        GET /test_raw_http_poc/ HTTP/1.1
        Host: {{Hostname}} 
        Accept: */*
    matchers:
      - type: dsl
        dsl:
          # 校验响应中是否包含Example和Domain
          - contains_all(response, "Example", "Domain")
