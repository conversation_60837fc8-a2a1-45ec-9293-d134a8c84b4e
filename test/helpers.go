package test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

// OOBTestData OOB测试数据结构
type OOBTestData struct {
	Name         string
	TestCase     TestCase
	HTTPContext  *npoc.HTTPContext
	OOBWaitGroup *sync.WaitGroup
	Checker      interface{} // 具体的checker类型由各模块定义
}

// PayloadInfo 载荷信息结构
type PayloadInfo struct {
	Name     string
	Value    string
	Type     string
	Encoding string
	OOB      bool
	Enabled  bool
}

// TestEnvironmentBuilder 测试环境构建器
type TestEnvironmentBuilder struct {
	testCase TestCase
}

// NewTestCase 创建新的测试用例构建器
func NewTestCase(name string) *TestEnvironmentBuilder {
	return &TestEnvironmentBuilder{
		testCase: TestCase{
			Name:            name,
			Method:          httpv.MethodGet,
			Enabled:         true,
			FollowRedirects: true,
			OOBRequired:     false,
		},
	}
}

// WithURL 设置URL
func (b *TestEnvironmentBuilder) WithURL(url string) *TestEnvironmentBuilder {
	b.testCase.URL = url
	return b
}

// WithMethod 设置HTTP方法
func (b *TestEnvironmentBuilder) WithMethod(method string) *TestEnvironmentBuilder {
	b.testCase.Method = method
	return b
}

// WithBody 设置请求体
func (b *TestEnvironmentBuilder) WithBody(body []byte) *TestEnvironmentBuilder {
	b.testCase.Body = body
	return b
}

// WithContentType 设置Content-Type
func (b *TestEnvironmentBuilder) WithContentType(contentType string) *TestEnvironmentBuilder {
	b.testCase.ContentType = contentType
	return b
}

// WithPayload 设置载荷信息
func (b *TestEnvironmentBuilder) WithPayload(name, payloadType string) *TestEnvironmentBuilder {
	b.testCase.PayloadName = name
	b.testCase.PayloadType = payloadType
	return b
}

// WithTargetParameter 设置目标参数
func (b *TestEnvironmentBuilder) WithTargetParameter(param string) *TestEnvironmentBuilder {
	b.testCase.TargetParameter = param
	return b
}

// WithExpectedVulns 设置期望漏洞数量
func (b *TestEnvironmentBuilder) WithExpectedVulns(count int) *TestEnvironmentBuilder {
	b.testCase.ExpectVulnNum = count
	return b
}

// WithExpectedCategory 设置期望漏洞分类
func (b *TestEnvironmentBuilder) WithExpectedCategory(category npoc.Category) *TestEnvironmentBuilder {
	b.testCase.ExpectVulnCategory = category
	return b
}

// WithDescription 设置描述
func (b *TestEnvironmentBuilder) WithDescription(desc string) *TestEnvironmentBuilder {
	b.testCase.Description = desc
	return b
}

// WithEnabled 设置是否启用
func (b *TestEnvironmentBuilder) WithEnabled(enabled bool) *TestEnvironmentBuilder {
	b.testCase.Enabled = enabled
	return b
}

// WithFingerprint 添加指纹信息
func (b *TestEnvironmentBuilder) WithFingerprint(name string) *TestEnvironmentBuilder {
	if b.testCase.Fingerprints == nil {
		b.testCase.Fingerprints = make([]httpv.FingerprintInfo, 0)
	}
	b.testCase.Fingerprints = append(b.testCase.Fingerprints, httpv.FingerprintInfo{Name: name})
	return b
}

// WithLanguage 添加语言指纹
func (b *TestEnvironmentBuilder) WithLanguage(lang string) *TestEnvironmentBuilder {
	return b.WithFingerprint(lang)
}

// WithCMS 添加CMS指纹
func (b *TestEnvironmentBuilder) WithCMS(cms string) *TestEnvironmentBuilder {
	return b.WithFingerprint(cms)
}

// WithTimeout 设置超时时间
func (b *TestEnvironmentBuilder) WithTimeout(timeout time.Duration) *TestEnvironmentBuilder {
	b.testCase.Timeout = timeout
	return b
}

// WithHeaders 设置自定义请求头
func (b *TestEnvironmentBuilder) WithHeaders(headers map[string]string) *TestEnvironmentBuilder {
	if b.testCase.CustomHeaders == nil {
		b.testCase.CustomHeaders = make(map[string]string)
	}
	for k, v := range headers {
		b.testCase.CustomHeaders[k] = v
	}
	return b
}

// WithHeader 添加单个请求头
func (b *TestEnvironmentBuilder) WithHeader(key, value string) *TestEnvironmentBuilder {
	if b.testCase.CustomHeaders == nil {
		b.testCase.CustomHeaders = make(map[string]string)
	}
	b.testCase.CustomHeaders[key] = value
	return b
}

// WithOOB 设置是否需要OOB
func (b *TestEnvironmentBuilder) WithOOB(required bool) *TestEnvironmentBuilder {
	b.testCase.OOBRequired = required
	return b
}

// WithExtra 设置额外参数
func (b *TestEnvironmentBuilder) WithExtra(key string, value interface{}) *TestEnvironmentBuilder {
	if b.testCase.Extra == nil {
		b.testCase.Extra = make(map[string]interface{})
	}
	b.testCase.Extra[key] = value
	return b
}

// Build 构建测试用例
func (b *TestEnvironmentBuilder) Build() TestCase {
	return b.testCase
}

// CreateXXETestCase 创建XXE测试用例
func CreateXXETestCase(name, url, payloadName string, method string, body []byte) TestCase {
	return NewTestCase(name).
		WithURL(url).
		WithMethod(method).
		WithBody(body).
		WithPayload(payloadName, "xxe").
		WithExpectedCategory(npoc.XXEType).
		WithDescription(fmt.Sprintf("XXE漏洞测试: %s", name)).
		Build()
}

// CreateDeserializeWithGetTestCase 创建反序列化测试用例(GET)
func CreateDeserializeWithGetTestCase(name, url, lang string, expectVulns int) TestCase {
	if lang == "" {
		lang = detector.LangUnknown
	}
	return NewTestCase(name).
		WithURL(url).
		WithLanguage(lang).
		WithMethod(httpv.MethodGet).
		WithExpectedVulns(expectVulns).
		WithExpectedCategory(npoc.DeserializeType).
		WithDescription(fmt.Sprintf("反序列化漏洞测试: %s", name)).
		Build()
}

// CreateDeserializeWithPostTestCase 创建反序列化测试用例(POST)
func CreateDeserializeWithPostTestCase(name, url, lang, contentType string, body []byte, expectVulns int) TestCase {
	if lang == "" {
		lang = detector.LangUnknown
	}
	if contentType == "" {
		contentType = "application/x-www-form-urlencoded"
	}
	return NewTestCase(name).
		WithURL(url).
		WithBody(body).
		WithLanguage(lang).
		WithMethod(httpv.MethodPost).
		WithContentType(contentType).
		WithExpectedVulns(expectVulns).
		WithExpectedCategory(npoc.DeserializeType).
		WithDescription(fmt.Sprintf("反序列化漏洞测试: %s", name)).
		Build()
}

// CreateSSRFTestCase 创建SSRF测试用例
func CreateSSRFTestCase(name, url, targetParam, customPayload, payloadType string, expectVulns int) TestCase {
	return NewTestCase(name).
		WithURL(url).
		WithTargetParameter(targetParam).
		WithExpectedCategory(npoc.SSRFType).
		WithExpectedVulns(expectVulns).
		WithOOB(true).
		WithPayload(fmt.Sprintf("ssrf_%s", payloadType), payloadType).
		WithExtra("custom_payload", customPayload).
		WithDescription(fmt.Sprintf("SSRF漏洞测试: %s", name)).
		Build()
}

// 工具函数

// ExecuteVulnerabilityTest 执行漏洞测试的通用函数
func ExecuteVulnerabilityTest(t *testing.T, testCases []TestCase, pocExecutor func(*npoc.HTTPContext) error) {
	ExecuteVulnerabilityTestWithConfig(t, testCases, pocExecutor, DefaultTestConfig())
}

// ExecuteVulnerabilityTestWithConfig 使用配置执行漏洞测试
func ExecuteVulnerabilityTestWithConfig(t *testing.T, testCases []TestCase, pocExecutor func(*npoc.HTTPContext) error, config TestConfig) {
	framework, err := NewTestFramework(t, config)
	if err != nil {
		t.Fatalf("创建测试框架失败: %v", err)
	}

	executeFunc := func(hc *npoc.HTTPContext, tc TestCase) error {
		return pocExecutor(hc)
	}

	results := framework.ExecuteTestCases(testCases, executeFunc)
	framework.ValidateResults(results)
}

// ExecuteOOBTest 执行OOB测试
func ExecuteOOBTest(t *testing.T, testCases []TestCase, pocExecutor func(*npoc.HTTPContext) error, oobWaitFunc func()) {
	config := DefaultTestConfig()
	config.EnableOOB = true

	framework, err := NewTestFramework(t, config)
	if err != nil {
		t.Fatalf("创建测试框架失败: %v", err)
	}

	executeFunc := func(hc *npoc.HTTPContext, tc TestCase) error {
		err := pocExecutor(hc)
		if err != nil {
			return err
		}
		// 等待OOB结果
		if oobWaitFunc != nil {
			oobWaitFunc()
		}
		return nil
	}

	results := framework.ExecuteTestCases(testCases, executeFunc)
	framework.ValidateResults(results)
}

// WaitForOOBWithTimeout 带超时的OOB等待
func WaitForOOBWithTimeout(wg *sync.WaitGroup, timeout time.Duration) bool {
	done := make(chan bool, 1)
	go func() {
		wg.Wait()
		done <- true
	}()

	select {
	case <-done:
		return true
	case <-time.After(timeout):
		return false
	}
}

// CreateTestCasesFromEnvironments 从环境配置创建测试用例 (兼容旧格式)
func CreateTestCasesFromEnvironments(environments interface{}, payloads []PayloadInfo) []TestCase {
	var testCases []TestCase

	// 这里可以根据具体的环境类型进行转换
	// 由于每个模块的环境结构不同，这个函数提供基础框架
	// 具体的转换逻辑需要在各模块中实现

	return testCases
}

// FindPayloadByName 根据名称查找载荷
func FindPayloadByName(name string, payloads []PayloadInfo) (PayloadInfo, bool) {
	for _, payload := range payloads {
		if payload.Name == name {
			return payload, true
		}
	}
	return PayloadInfo{}, false
}

// FilterEnabledPayloads 过滤启用的载荷
func FilterEnabledPayloads(payloads []PayloadInfo) []PayloadInfo {
	var enabled []PayloadInfo
	for _, payload := range payloads {
		if payload.Enabled {
			enabled = append(enabled, payload)
		}
	}
	return enabled
}

// PrepareTestEnvironments 准备测试环境 (兼容函数)
func PrepareTestEnvironments(t *testing.T, testCases []TestCase, payloads []PayloadInfo, testType string) []TestCase {
	t.Helper()

	if len(testCases) == 0 {
		t.Logf("未指定%s测试环境，跳过测试", testType)
		return nil
	}

	var validCases []TestCase
	for _, tc := range testCases {
		if !tc.Enabled {
			continue
		}

		if tc.PayloadName != "" {
			payload, found := FindPayloadByName(tc.PayloadName, payloads)
			if !found {
				t.Logf("⚠️ 在%s测试环境 %s 中未找到载荷 %s，跳过此测试用例", testType, tc.URL, tc.PayloadName)
				continue
			}
			// 可以将payload信息存储到Extra中
			if tc.Extra == nil {
				tc.Extra = make(map[string]interface{})
			}
			tc.Extra["payload"] = payload
		}

		validCases = append(validCases, tc)
	}

	return validCases
}

// LogTestProgress 记录测试进度
func LogTestProgress(t *testing.T, current, total int, name string) {
	t.Helper()
	percentage := float64(current) / float64(total) * 100
	t.Logf("📈 测试进度: %d/%d (%.1f%%) - 当前: %s", current, total, percentage, name)
}

// LogTestSummary 记录测试总结
func LogTestSummary(t *testing.T, results []TestResult, testType string) {
	t.Helper()

	successCount := 0
	totalVulns := 0
	var totalDuration time.Duration
	var totalRequests int64

	for _, result := range results {
		if result.Success {
			successCount++
		}
		totalVulns += result.VulnCount
		totalDuration += result.Duration
		totalRequests += result.RequestCount
	}

	t.Logf("\n🎯 %s测试完成:", testType)
	t.Logf("   总测试数: %d", len(results))
	t.Logf("   成功数: %d", successCount)
	t.Logf("   失败数: %d", len(results)-successCount)
	t.Logf("   发现漏洞: %d", totalVulns)
	t.Logf("   总耗时: %v", totalDuration)
	t.Logf("   总发包数: %d", totalRequests)

	if len(results) > 0 {
		t.Logf("   平均耗时: %v", totalDuration/time.Duration(len(results)))
		t.Logf("   平均发包数: %.2f", float64(totalRequests)/float64(len(results)))
	}
}
