# 通用测试框架使用指南

## 概述

新的测试框架提供了以下功能：
- 统一的测试用例结构
- 并发执行支持
- 性能指标收集
- OOB (Out-of-Band) 测试支持
- 灵活的配置选项
- 构建器模式的测试用例创建
- 结果验证和报告

## 核心组件

### 1. TestCase 结构

`TestCase` 是测试用例的核心数据结构：

```go
type TestCase struct {
    Name                string                    // 测试用例名称
    URL                 string                    // 目标URL
    Method              string                    // HTTP方法
    Body                []byte                    // 请求体
    ContentType         string                    // Content-Type
    PayloadName         string                    // 载荷名称
    PayloadType         string                    // 载荷类型
    TargetParameter     string                    // 目标参数名
    ExpectVulnNum       int                       // 期望漏洞数量
    ExpectVulnCategory  string                    // 期望漏洞分类
    Enabled             bool                      // 是否启用
    Description         string                    // 测试描述
    Fingerprints        []httpv.FingerprintInfo   // 指纹信息
    CustomHeaders       map[string]string         // 自定义请求头
    DisableEncoding     bool                      // 是否禁用编码
    FollowRedirects     bool                      // 是否跟随重定向
    OOBRequired         bool                      // 是否需要OOB
    Timeout             time.Duration             // 超时时间
    Extra               map[string]interface{}    // 额外参数
}
```

### 2. TestFramework

`TestFramework` 是测试执行的核心：

```go
type TestFramework struct {
    t      *testing.T
    config TestConfig
    client *httpv.Client
}
```

### 3. TestConfig

测试配置选项：

```go
type TestConfig struct {
    EnableOOB           bool          // 是否启用OOB
    Concurrent          bool          // 是否并发执行
    MaxConcurrency      int           // 最大并发数
    Timeout             time.Duration // 默认超时时间
    DetailedLogging     bool          // 是否详细日志
    SkipDisabled        bool          // 是否跳过禁用的测试
    ContinueOnError     bool          // 遇到错误是否继续
    PerformanceReport   bool          // 是否生成性能报告
}
```

## 快速开始

### 1. 基础用法

```go
func TestBasicExample(t *testing.T) {
    // 1. 创建测试用例
    testCases := []TestCase{
        NewTestCase("PHP反序列化测试").
            WithURL("http://example.com/test.php?data=test").
            WithLanguage(detector.LangPHP).
            WithExpectedVulns(1).
            Build(),
    }

    // 2. 创建PoC实例
    poc := &YourPoC{}

    // 3. 执行测试
    ExecuteVulnerabilityTest(t, testCases, poc.Execute)
}
```

### 2. 使用构建器模式创建测试用例

```go
testCase := NewTestCase("测试名称").
    WithURL("http://example.com/test").
    WithMethod(httpv.MethodPost).
    WithBody([]byte("test=data")).
    WithContentType("application/x-www-form-urlencoded").
    WithLanguage(detector.LangPHP).
    WithExpectedVulns(1).
    WithExpectedCategory(npoc.VulnCategoryXXE).
    WithTimeout(30 * time.Second).
    WithHeader("User-Agent", "test-agent").
    WithOOB(true).
    WithEnabled(true).
    WithDescription("这是一个测试用例").
    Build()
```

### 3. 使用预定义函数

```go
// 创建常见类型的测试用例
phpTest := CreatePHPTestCase("PHP测试", "http://example.com/php", 1)
javaTest := CreateJavaTestCase("Java测试", "http://example.com/java", 2)
pythonTest := CreatePythonTestCase("Python测试", "http://example.com/python", 1)
xxeTest := CreateXXETestCase("XXE测试", "http://example.com/xxe", "payloadName", httpv.MethodPost, []byte("xml=test"))
ssrfTest := CreateSSRFTestCase("SSRF测试", "http://example.com/ssrf?url=test", "url")
```

## 高级用法

### 1. 自定义配置

```go
func TestWithCustomConfig(t *testing.T) {
    config := TestConfig{
        EnableOOB:         true,
        Concurrent:        true,
        MaxConcurrency:    5,
        Timeout:           10 * time.Second,
        DetailedLogging:   true,
        SkipDisabled:      true,
        ContinueOnError:   true,
        PerformanceReport: true,
    }

    ExecuteVulnerabilityTestWithConfig(t, testCases, poc.Execute, config)
}
```

### 2. OOB测试

```go
func TestOOB(t *testing.T) {
    testCases := []TestCase{
        NewTestCase("SSRF OOB测试").
            WithURL("http://example.com/ssrf?url=test").
            WithOOB(true).
            WithExpectedCategory(npoc.VulnCategorySSRF).
            Build(),
    }

    ExecuteOOBTest(t, testCases, poc.Execute, nil)
}
```

### 3. 过滤和筛选测试用例

```go
func TestWithFiltering(t *testing.T) {
    framework, _ := NewTestFramework(t, DefaultTestConfig())

    // 过滤启用的测试用例
    enabledCases := framework.FilterTestCases(allTestCases, FilterByEnabled(true))
    
    // 过滤特定载荷类型
    phpCases := framework.FilterTestCases(allTestCases, FilterByPayloadType("php"))
    
    // 过滤特定漏洞分类
    xxeCases := framework.FilterTestCases(allTestCases, FilterByVulnCategory(npoc.VulnCategoryXXE))

    // 执行过滤后的测试用例
    results := framework.ExecuteTestCases(enabledCases, executeFunc)
    framework.ValidateResults(results)
}
```

### 4. 手动控制执行流程

```go
func TestManualControl(t *testing.T) {
    framework, _ := NewTestFramework(t, DefaultTestConfig())

    executeFunc := func(hc *npoc.HTTPContext, tc TestCase) error {
        // 自定义执行逻辑
        return poc.Execute(hc)
    }

    results := framework.ExecuteTestCases(testCases, executeFunc)
    
    // 自定义结果验证
    for _, result := range results {
        if !result.Success {
            t.Errorf("测试失败: %s, 错误: %v", result.Name, result.Error)
        }
    }
}
```

## 从旧代码迁移

### 1. 转换旧的测试环境结构

```go
// 旧的结构
type OldTestEnvironment struct {
    Name    string
    URL     string
    Payload string
    Enabled bool
}

// 转换函数
func convertToTestCases(oldEnvs []OldTestEnvironment) []TestCase {
    var testCases []TestCase
    for _, env := range oldEnvs {
        testCase := NewTestCase(env.Name).
            WithURL(env.URL).
            WithEnabled(env.Enabled).
            WithExtra("old_payload", env.Payload).
            Build()
        testCases = append(testCases, testCase)
    }
    return testCases
}
```

### 2. 保持兼容性

```go
// 使用兼容性函数
validCases := PrepareTestEnvironments(t, testCases, payloads, "测试类型")
```

## 最佳实践

### 1. 测试用例组织

```go
// 按功能分组
var (
    phpTestCases = []TestCase{
        CreatePHPTestCase("PHP测试1", "url1", 1),
        CreatePHPTestCase("PHP测试2", "url2", 1),
    }
    
    javaTestCases = []TestCase{
        CreateJavaTestCase("Java测试1", "url1", 2),
        CreateJavaTestCase("Java测试2", "url2", 1),
    }
)

func TestPHP(t *testing.T) {
    ExecuteVulnerabilityTest(t, phpTestCases, poc.Execute)
}

func TestJava(t *testing.T) {
    ExecuteVulnerabilityTest(t, javaTestCases, poc.Execute)
}
```

### 2. 错误处理

```go
func TestWithErrorHandling(t *testing.T) {
    config := DefaultTestConfig()
    config.ContinueOnError = true // 遇到错误继续执行

    ExecuteVulnerabilityTestWithConfig(t, testCases, func(hc *npoc.HTTPContext) error {
        // 在这里处理特定的错误情况
        err := poc.Execute(hc)
        if err != nil {
            // 记录详细错误信息
            t.Logf("执行失败，但继续测试: %v", err)
        }
        return err
    }, config)
}
```

### 3. 性能优化

```go
func TestPerformanceOptimized(t *testing.T) {
    config := DefaultTestConfig()
    config.MaxConcurrency = 20        // 根据目标服务器调整
    config.Timeout = 5 * time.Second  // 较短的超时时间
    config.PerformanceReport = true   // 启用性能报告

    ExecuteVulnerabilityTestWithConfig(t, testCases, poc.Execute, config)
}
```

### 4. 调试和日志

```go
func TestWithDebugging(t *testing.T) {
    config := DefaultTestConfig()
    config.DetailedLogging = true

    // 添加进度日志
    framework, _ := NewTestFramework(t, config)
    
    executeFunc := func(hc *npoc.HTTPContext, tc TestCase) error {
        t.Logf("正在执行测试: %s", tc.Name)
        return poc.Execute(hc)
    }

    results := framework.ExecuteTestCases(testCases, executeFunc)
    
    // 详细的结果分析
    for _, result := range results {
        t.Logf("测试结果 [%s]: 成功=%v, 耗时=%v, 发包=%d, 漏洞=%d",
            result.Name, result.Success, result.Duration, 
            result.RequestCount, result.VulnCount)
    }
    
    framework.ValidateResults(results)
}
```

## 常见问题

### Q: 如何处理需要特殊设置的测试用例？

A: 使用 `Extra` 字段存储特殊参数，然后在执行函数中读取：

```go
testCase := NewTestCase("特殊测试").
    WithExtra("special_setting", "value").
    WithExtra("custom_payload", customPayload).
    Build()

executeFunc := func(hc *npoc.HTTPContext, tc TestCase) error {
    if setting, ok := tc.Extra["special_setting"]; ok {
        // 根据特殊设置调整执行逻辑
    }
    return poc.Execute(hc)
}
```

### Q: 如何处理依赖外部服务的测试？

A: 在测试开始前检查服务可用性：

```go
func TestWithExternalDependency(t *testing.T) {
    // 检查外部服务
    if !isServiceAvailable("http://external-service.com") {
        t.Skip("外部服务不可用，跳过测试")
    }
    
    ExecuteVulnerabilityTest(t, testCases, poc.Execute)
}
```

### Q: 如何处理需要不同配置的测试组？

A: 将测试分组并使用不同的配置：

```go
func TestGroup1(t *testing.T) {
    config := DefaultTestConfig()
    config.EnableOOB = false
    ExecuteVulnerabilityTestWithConfig(t, group1Cases, poc.Execute, config)
}

func TestGroup2(t *testing.T) {
    config := DefaultTestConfig()
    config.EnableOOB = true
    config.Timeout = 60 * time.Second
    ExecuteVulnerabilityTestWithConfig(t, group2Cases, poc.Execute, config)
}
```