package test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

// TestCase 通用测试用例结构
type TestCase struct {
	Name               string                  // 测试用例名称
	URL                string                  // 目标URL
	Method             string                  // HTTP方法
	Body               []byte                  // 请求体
	ContentType        string                  // Content-Type
	PayloadName        string                  // 载荷名称
	PayloadType        string                  // 载荷类型 (php, java, python, etc.)
	TargetParameter    string                  // 目标参数名
	ExpectVulnNum      int                     // 期望漏洞数量
	ExpectVulnCategory npoc.Category           // 期望漏洞分类
	Enabled            bool                    // 是否启用
	Description        string                  // 测试描述
	Fingerprints       []httpv.FingerprintInfo // 指纹信息
	CustomHeaders      map[string]string       // 自定义请求头
	DisableEncoding    bool                    // 是否禁用编码
	FollowRedirects    bool                    // 是否跟随重定向
	OOBRequired        bool                    // 是否需要OOB
	Timeout            time.Duration           // 超时时间
	Extra              map[string]interface{}  // 额外参数
}

// TestResult 通用测试结果结构
type TestResult struct {
	Name            string                // 测试用例名称
	TestCase        TestCase              // 原始测试用例
	HTTPContext     *npoc.HTTPContext     // HTTP上下文
	Duration        time.Duration         // 执行时间
	RequestCount    int64                 // 发包数量
	VulnCount       int                   // 发现漏洞数量
	Vulnerabilities []*npoc.Vulnerability // 漏洞详情
	Success         bool                  // 是否成功
	Error           error                 // 错误信息
	StartTime       time.Time             // 开始时间
	EndTime         time.Time             // 结束时间
}

// TestConfig 测试配置
type TestConfig struct {
	EnableOOB         bool          // 是否启用OOB
	Concurrent        bool          // 是否并发执行
	MaxConcurrency    int           // 最大并发数
	Timeout           time.Duration // 默认超时时间
	DetailedLogging   bool          // 是否详细日志
	SkipDisabled      bool          // 是否跳过禁用的测试
	ContinueOnError   bool          // 遇到错误是否继续
	PerformanceReport bool          // 是否生成性能报告
}

// DefaultTestConfig 默认测试配置
func DefaultTestConfig() TestConfig {
	return TestConfig{
		EnableOOB:         true,
		Concurrent:        true,
		MaxConcurrency:    10,
		Timeout:           30 * time.Second,
		DetailedLogging:   true,
		SkipDisabled:      true,
		ContinueOnError:   true,
		PerformanceReport: true,
	}
}

// TestFramework 通用测试框架
type TestFramework struct {
	t      *testing.T
	config TestConfig
	client *httpv.Client
}

// NewTestFramework 创建新的测试框架
func NewTestFramework(t *testing.T, config TestConfig) (*TestFramework, error) {
	client, err := CreateNewTestClient(config.EnableOOB)
	if err != nil {
		return nil, fmt.Errorf("创建测试客户端失败: %w", err)
	}

	return &TestFramework{
		t:      t,
		config: config,
		client: client,
	}, nil
}

// CreateHTTPContextFromTestCase 从测试用例创建HTTP上下文
func (tf *TestFramework) CreateHTTPContextFromTestCase(tc TestCase) (*npoc.HTTPContext, error) {
	// 设置默认值
	method := tc.Method
	if method == "" {
		method = httpv.MethodGet
	}

	contentType := tc.ContentType
	if contentType == "" && (method == httpv.MethodPost || method == httpv.MethodPut || method == httpv.MethodPatch) {
		contentType = "application/x-www-form-urlencoded"
	}

	// 创建HTTP上下文
	hc, err := CreateTestHTTPContext(tc.URL, method, tc.Body, contentType)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP上下文失败: %w", err)
	}

	// 设置指纹信息
	if len(tc.Fingerprints) > 0 {
		hc.Task().FingerPrint = tc.Fingerprints
	}

	// 设置自定义请求头
	if len(tc.CustomHeaders) > 0 {
		req := hc.Task().Request
		for key, value := range tc.CustomHeaders {
			req.Header.Set(key, value)
		}
	}

	// 设置编码选项
	if tc.DisableEncoding {
		hc.Task().Request.DisableEncoding = true
	}

	// 设置重定向选项
	if !tc.FollowRedirects {
		hc.Task().Request.FollowRedirects = false
	}

	return hc, nil
}

// ExecuteTestCases 执行测试用例
func (tf *TestFramework) ExecuteTestCases(testCases []TestCase, executeFunc func(*npoc.HTTPContext, TestCase) error) []TestResult {
	if tf.config.Concurrent {
		return tf.executeConcurrently(testCases, executeFunc)
	}
	return tf.executeSequentially(testCases, executeFunc)
}

// executeConcurrently 并发执行测试用例
func (tf *TestFramework) executeConcurrently(testCases []TestCase, executeFunc func(*npoc.HTTPContext, TestCase) error) []TestResult {
	var (
		results []TestResult
		wg      sync.WaitGroup
		mu      sync.Mutex
	)

	// 使用带缓冲的通道控制并发数
	semaphore := make(chan struct{}, tf.config.MaxConcurrency)

	for _, tc := range testCases {
		if tf.config.SkipDisabled && !tc.Enabled {
			continue
		}

		wg.Add(1)
		go func(testCase TestCase) {
			defer wg.Done()

			// 获取并发许可
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			result := tf.executeTestCase(testCase, executeFunc)

			mu.Lock()
			results = append(results, result)
			mu.Unlock()
		}(tc)
	}

	wg.Wait()
	return results
}

// executeSequentially 顺序执行测试用例
func (tf *TestFramework) executeSequentially(testCases []TestCase, executeFunc func(*npoc.HTTPContext, TestCase) error) []TestResult {
	var results []TestResult

	for _, tc := range testCases {
		if tf.config.SkipDisabled && !tc.Enabled {
			continue
		}

		result := tf.executeTestCase(tc, executeFunc)
		results = append(results, result)

		// 如果不继续错误且发生错误，则停止
		if !tf.config.ContinueOnError && result.Error != nil {
			break
		}
	}

	return results
}

// executeTestCase 执行单个测试用例
func (tf *TestFramework) executeTestCase(tc TestCase, executeFunc func(*npoc.HTTPContext, TestCase) error) TestResult {
	startTime := time.Now()
	result := TestResult{
		Name:      tc.Name,
		TestCase:  tc,
		StartTime: startTime,
	}

	// 创建HTTP上下文
	hc, err := tf.CreateHTTPContextFromTestCase(tc)
	if err != nil {
		result.Error = fmt.Errorf("创建HTTP上下文失败: %w", err)
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(startTime)
		return result
	}

	result.HTTPContext = hc

	// 记录执行前的请求数量
	requestCountBefore := hc.Task().Client.RequestCounter.Load()

	// 设置超时
	timeout := tc.Timeout
	if timeout == 0 {
		timeout = tf.config.Timeout
	}

	// 执行测试函数
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	done := make(chan error, 1)
	go func() {
		done <- executeFunc(hc, tc)
	}()

	select {
	case err = <-done:
		if err != nil {
			result.Error = fmt.Errorf("执行测试函数失败: %w", err)
		}
	case <-ctx.Done():
		result.Error = fmt.Errorf("测试用例超时 (超时时间: %v)", timeout)
	}

	// 记录结果
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(startTime)
	result.RequestCount = hc.Task().Client.RequestCounter.Load() - requestCountBefore

	// 获取漏洞信息
	if hc.GetResult() != nil && hc.GetResult().Vulnerabilities != nil {
		result.Vulnerabilities = hc.GetResult().Vulnerabilities
		result.VulnCount = len(result.Vulnerabilities)
	}

	result.Success = result.Error == nil && result.VulnCount >= tc.ExpectVulnNum

	return result
}

// ValidateResults 验证测试结果
func (tf *TestFramework) ValidateResults(results []TestResult) {
	successCount := 0
	totalCount := len(results)
	var totalDuration time.Duration
	var totalRequests int64
	var totalVulns int

	for _, result := range results {
		totalDuration += result.Duration
		totalRequests += result.RequestCount
		totalVulns += result.VulnCount

		if result.Success {
			successCount++
			if tf.config.DetailedLogging {
				tf.t.Logf("✅ [%s] 测试成功 - 耗时: %v, 发包: %d, 漏洞: %d",
					result.Name, result.Duration, result.RequestCount, result.VulnCount)
			}
		} else {
			tf.t.Errorf("❌ [%s] 测试失败 - 耗时: %v, 错误: %v",
				result.Name, result.Duration, result.Error)
		}

		// 验证期望的漏洞数量
		if result.TestCase.ExpectVulnNum > 0 && result.VulnCount != result.TestCase.ExpectVulnNum {
			tf.t.Errorf("❌ [%s] 漏洞数量不符合预期 - 期望: %d, 实际: %d",
				result.Name, result.TestCase.ExpectVulnNum, result.VulnCount)
		}

		// 验证期望的漏洞分类
		if result.TestCase.ExpectVulnCategory != "" {
			categoryMatched := false
			for _, vuln := range result.Vulnerabilities {
				if vuln != nil && vuln.Category == result.TestCase.ExpectVulnCategory {
					categoryMatched = true
					break
				}
			}
			if !categoryMatched {
				tf.t.Errorf("❌ [%s] 未发现期望分类的漏洞 - 期望分类: %s",
					result.Name, string(result.TestCase.ExpectVulnCategory))
			}
		}
	}

	// 输出总体测试结果
	tf.t.Logf("\n📊 测试总结:")
	tf.t.Logf("   总测试数: %d", totalCount)
	tf.t.Logf("   成功测试: %d", successCount)
	tf.t.Logf("   失败测试: %d", totalCount-successCount)
	tf.t.Logf("   总耗时: %v", totalDuration)
	tf.t.Logf("   总发包数: %d", totalRequests)
	tf.t.Logf("   发现漏洞总数: %d", totalVulns)

	if tf.config.PerformanceReport && totalCount > 0 {
		avgDuration := totalDuration / time.Duration(totalCount)
		avgRequests := float64(totalRequests) / float64(totalCount)
		tf.t.Logf("   平均耗时: %v", avgDuration)
		tf.t.Logf("   平均发包数: %.2f", avgRequests)
	}

	if successCount == totalCount {
		tf.t.Log("🎉 所有测试用例均通过!")
	} else {
		tf.t.Errorf("⚠️ 有 %d 个测试用例失败", totalCount-successCount)
	}
}

// FilterTestCases 过滤测试用例
func (tf *TestFramework) FilterTestCases(testCases []TestCase, filters ...func(TestCase) bool) []TestCase {
	var filtered []TestCase

	for _, tc := range testCases {
		include := true
		for _, filter := range filters {
			if !filter(tc) {
				include = false
				break
			}
		}
		if include {
			filtered = append(filtered, tc)
		}
	}

	return filtered
}

// 常用过滤器函数

// FilterByEnabled 按启用状态过滤
func FilterByEnabled(enabled bool) func(TestCase) bool {
	return func(tc TestCase) bool {
		return tc.Enabled == enabled
	}
}

// FilterByPayloadType 按载荷类型过滤
func FilterByPayloadType(payloadType string) func(TestCase) bool {
	return func(tc TestCase) bool {
		return tc.PayloadType == payloadType
	}
}

// FilterByVulnCategory 按漏洞分类过滤
func FilterByVulnCategory(category npoc.Category) func(TestCase) bool {
	return func(tc TestCase) bool {
		return tc.ExpectVulnCategory == category
	}
}

// FilterByName 按名称模式过滤 (支持通配符)
func FilterByName(pattern string) func(TestCase) bool {
	return func(tc TestCase) bool {
		// 简单的通配符匹配，可以根据需要扩展
		if pattern == "*" {
			return true
		}
		return tc.Name == pattern
	}
}
