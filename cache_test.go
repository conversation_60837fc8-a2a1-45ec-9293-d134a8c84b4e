package npoc

import (
	"strconv"
	"testing"
)

func TestSizedCache(t *testing.T) {
	const maxSize = 128
	c := NewSizedCache[int](maxSize)
	for i := 0; i < 2*maxSize; i++ {
		c.Set(strconv.Itoa(i), i)
	}
	for i := 0; i < maxSize; i++ {
		_, ok := c.Get(strconv.Itoa(i))
		if ok {
			t.Failed()
		}
	}
	for i := maxSize; i < 2*maxSize; i++ {
		val, ok := c.Get(strconv.Itoa(i))
		if !ok || val != i {
			t.Failed()
		}
	}
}
