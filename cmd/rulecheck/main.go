package main

import (
	"fmt"
	"log/slog"
	"os"

	"github.acme.red/intelli-sec/common/rulecheck"
	"github.acme.red/intelli-sec/npoc/utils/file"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/alecthomas/kingpin/v2"
)

var (
	path = kingpin.Flag("path", "需要检查的poc路径，如果是目录则加载目录下所有poc，如果是文件地址则加载指定的poc").Short('p').String()
)

func main() {
	// 或者使用JSON格式
	handler := slog.NewJSONHandler(os.Stderr, &slog.HandlerOptions{
		Level: slog.LevelError,
	})
	logger := slog.New(handler)
	slog.SetDefault(logger)
	kingpin.Parse()
	if *path == "" {
		slog.Error("请使用-p参数指定需要检查的poc文件路径")
		return
	}
	files, err := file.GetYamlFilesInDir(*path)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, pocFileName := range files {
		fileBytes, err := os.ReadFile(pocFileName)
		if err != nil {
			slog.Error("Error reading file", "filename", pocFileName)
			continue
		}
		_, err = rulecheck.CheckTemplateRule(fileBytes, false)
		if err != nil {
			slog.Error("There are errors in the rules", "file_name", pocFileName, slogext.Error(err))
			continue
		}
	}
}
