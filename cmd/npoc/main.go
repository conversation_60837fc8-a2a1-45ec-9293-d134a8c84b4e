package main

import (
	"cmp"
	"context"
	"fmt"
	"log/slog"
	"net/url"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.acme.red/intelli-sec/common/rulecheck"
	taskv1 "github.acme.red/intelli-sec/idl/gen/go/task/v1"
	"github.com/alecthomas/kingpin/v2"
	"github.com/labstack/gommon/log"
	"github.com/schollz/progressbar/v3"
	"golang.org/x/sync/semaphore"
	"gopkg.in/yaml.v3"

	"github.acme.red/intelli-sec/npoc/internal/config"
	"github.acme.red/intelli-sec/npoc/lib/sdk"
	"github.acme.red/intelli-sec/npoc/pkg/logger"
	"github.acme.red/intelli-sec/npoc/utils/file"
)

var (
	targetUrl  = kingpin.Flag("url", "目标url").Short('u').String()
	targetFile = kingpin.Flag("url_file", "目标url文件,一行一个url地址").Short('l').String()
	templates  = kingpin.Flag("templates", "扫描的poc文件，如果是目录则加载目录下所有poc，如果是文件地址则加载指定的poc").Short('t').String()
	hostThread = kingpin.Flag("ht", "并发目标数").Default("20").Int() //nolint:gochecknoglobals // 1
	onlyCheck  = kingpin.Flag("oc", "仅静态检测poc规则是否合法，不进行真实的扫描").Bool()
	envFile    = kingpin.Flag("ef", "自定义传入poc中需要用到的变量key-value键值对文件地址，yaml格式").Default("env.yaml").Short('e').String()
	configFile = kingpin.Flag("cf", "传入运行需要配置文件地址，yaml格式").Default("config.yaml").Short('c').String()
	outFile    = kingpin.Flag("of", "结果输出到指定文件中").Short('o').String()
)

type Config struct {
	OOBHost             string `yaml:"oob-host"`
	OOBToken            string `yaml:"oob-token"`
	OOBUrl              string `yaml:"oob-url"`
	TaskParallel        int    `yaml:"host-parallel"`
	TaskRequestParallel int    `yaml:"host-request-parallel"`
	Proxy               string `yaml:"proxy"`
}

func main() {
	config.Init()
	logger.SetupLogger(logger.Config{
		LogPath:       *config.LogPath,
		LogLevel:      *config.LogLevel,
		LogMaxSize:    *config.LogMaxSize,
		LogMaxBackups: *config.LogMaxBackup,
		LogMaxAge:     *config.LogMaxAge,
		LogOutConsole: *config.LogOutConsole,
	})
	slog.Debug("start load poc ...")
	var (
		allPocs [][]byte
		err     error
	)
	if *templates != "" {
		allPocs, err = loadPocFile()
		if err != nil {
			slog.Error("load poc file error:", err)
			return
		}
	}
	slog.Debug("poc load completed")
	if *onlyCheck {
		return
	}

	var targets []string
	if *targetUrl != "" {
		targets = append(targets, *targetUrl)
	}
	if *targetFile != "" {
		ts, err := file.ReadFileLines(*targetFile)
		if err != nil {
			slog.Error("Error reading file", "targetFile", *targetFile)
		} else {
			targets = append(targets, ts...)
		}
	}
	bar := progressbar.Default(int64(len(targets))) // 进度条
	var (
		resultCounter atomic.Int64
		writeResultMu sync.Mutex
	)
	var env = make(map[string]string)
	if *envFile != "" {
		envFileData, err := os.ReadFile(*envFile)
		if err == nil {
			err = yaml.Unmarshal(envFileData, &env)
			if err != nil {
				log.Warn("未检测到配置文件，无法使用oob进行验证")
			}
		}
	}
	var configData Config
	if *configFile != "" {
		configFileData, err := os.ReadFile(*configFile)
		if err != nil {
			slog.Warn("未检测到配置文件,也未使用参数指定oob域名和token,无法正常使用oob进行验证")
		} else {
			err = yaml.Unmarshal(configFileData, &configData)
			if err != nil {
				log.Warn("未检测到配置文件，无法使用oob进行验证")
			}
		}
	}
	if *config.OOBHost == "" && configData.OOBToken != "" {
		*config.OOBHost = configData.OOBHost
		*config.OOBURL = configData.OOBUrl
		*config.OOBToken = configData.OOBToken
	}
	if *config.Proxy != "" {
		configData.Proxy = *config.Proxy
	} else if configData.Proxy != "" {
		*config.Proxy = configData.Proxy
	}
	if configData.TaskParallel != 0 && *hostThread == 20 {
		*hostThread = configData.TaskParallel
	}
	if *config.TaskReqParallel == 25 {
		*config.TaskReqParallel = configData.TaskRequestParallel
	}
	sdk.InitSdk()
	ctx, cancel := context.WithTimeout(context.Background(), 60*24*time.Minute)
	defer cancel()
	hostLimit := semaphore.NewWeighted(int64(*hostThread))
	var wg sync.WaitGroup
	slog.Debug("start scan ...")
	for _, target := range targets {
		_, err := url.Parse(target)
		if err != nil {
			slog.Error("url parse error", "err", err, "url", target)
			continue
		}
		err = hostLimit.Acquire(ctx, 1)
		if err != nil {
			panic(err)
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer hostLimit.Release(1)
			results, err := sdk.RunPocTask(ctx, &taskv1.PocScanTaskData{
				Variables:          env,
				FailRetries:        1,
				HostErrMaxNum:      50,
				HostReqMaxParallel: cmp.Or(int64(*config.TaskReqParallel), 25),
				Pocs:               allPocs,
				DisableLoadPoc:     true,
				Target:             target,
			})

			_ = bar.Add(1)
			for _, result := range results {
				outStr := fmt.Sprintf("[+] url: %s, poc_id: %s", result.Url, result.Id)
				var defaultExtra []string
				for key, value := range result.Variables {
					outStr += fmt.Sprintf(", [%s: %s]", key, value)
				}
				if len(defaultExtra) > 0 {
					outStr += fmt.Sprintf(", [%s]", strings.Join(defaultExtra, ","))
				}
				slog.Info("[SUCCESS]", "info", outStr)
				if *outFile != "" {
					writeResultMu.Lock()
					err = writeResult(*outFile, outStr+"\n")
					if err != nil {
						slog.ErrorContext(ctx, "[ERROR] write result error", "err", err)
					}
					writeResultMu.Unlock()
				}
				resultCounter.Add(1)
			}
		}()
	}
	wg.Wait()
	if resultCounter.Load() > 0 {
		slog.Info("[end] ", "success number", resultCounter.Load())
	} else {
		slog.Info("[end] not successful, good luck!")
	}
}

func loadPocFile() ([][]byte, error) {
	var allPocs [][]byte
	files, err := file.GetYamlFilesInDir(*templates)
	if err != nil {
		return nil, fmt.Errorf("get poc file failed, path: %s, err: %w", *templates, err)
	}
	for _, pocFileName := range files {
		fileBytes, err := os.ReadFile(pocFileName)
		if err != nil {
			slog.Error("Error reading file", "filename", pocFileName)
			continue
		}
		warning, err := rulecheck.CheckTemplateRule(fileBytes, false)
		if err != nil {
			slog.Error("poc is error, file_name: %s, error: %v", pocFileName, err)
			continue
		} else if warning != nil {
			slog.Warn("poc check warning, file_name: %s, error: %v", pocFileName, warning)
		}
		allPocs = append(allPocs, fileBytes)
	}
	return allPocs, nil
}

func writeResult(filename, data string) error {

	// 以追加模式打开文件，如果文件不存在则创建
	f, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("无法打开文件: %w", err)
	}
	defer func() {
		_ = f.Close()
	}()

	// 写入内容
	if _, err := f.WriteString(data); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}
