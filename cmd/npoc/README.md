
## npoc 二进制poc扫描程序
- 当前目录使用task build-all 会将npoc扫描程序分别打包成适用于mac、win、linux三种平台的二进制程序，输出到output目录中

- 使用 /npoc-linux -u http://www.example.com -t /{{poc_path}}/ 可以进行简易测试（{{poc_path}}替换为poc所在路径）

- config.yaml文件作为常用参数的配置文件，在命令行传入参数与该文件的参数重复时会优先使用命令行参数传递的值 
注意这里设置的proxy代理对code协议中发送的请求无效，code中需要自行设置代理，可以通过env.yaml将代理地址传入

    ```
    oob-host: example.com // dnslog相关配置（如有需要请联系 吴嘉耀）
    oob-token: 123456789 // dnslog相关配置
    oob-url: http://example.com:8080 // dnslog相关配置
    proxy: socks5://127.0.0.1:8080 // 只能是socks5的代理
    task-parallel: 10 // 目标并发数
    task-request-parallel: 10 // 单目标请求并发数
    ```



- env.yaml 键值对形式，主要用于将一些自定义变量传入poc中使用

  比如有一个poc中需要用到{{reverse_address}}，那这里就需要在env.yaml中定义

  ```
  reverse_address: '*******:7777'
  ```

  

