version: "3"

dotenv:
  - .env

env:
  CGO_ENABLED: 0

vars:
  BINARY_NAME: npoc-agent
  # 需要提前在本地的~/.ssh/config配置好跳板机的认证信息
  JUMP_SERVER: jump
  # 已经在跳板机上配置好扫描节点的认证信息
  AGENT_SERVER: npoc-agent1

tasks:
  build:
    cmds:
      - GOOS=linux GOARCH=amd64 go build -trimpath -o ./output/{{.BINARY_NAME}}
  update:
    deps:
      - build
    cmds:
      # 先将容器停止
      - ssh {{.JUMP_SERVER}} "ssh {{.AGENT_SERVER}} \"cd /opt/npoc && docker compose stop\""
      - scp -r ./output/{{.BINARY_NAME}} ./.env {{.JUMP_SERVER}}:/tmp/npoc/
      - ssh {{.JUMP_SERVER}} "cd /tmp/npoc/ && scp  ./{{.BINARY_NAME}} {{.AGENT_SERVER}}:/opt/npoc/"
      - ssh {{.JUMP_SERVER}} "ssh {{.AGENT_SERVER}} \"cd /opt/npoc && docker compose start\""
  deploy:
    deps:
      - build
    cmds:
      # 在跳板机上停止节点运行
      - ssh {{.JUMP_SERVER}} "cd /opt/npoc && docker compose down"
      # 在跳板机上创建npoc到临时目录
      - ssh {{.JUMP_SERVER}} "mkdir /tmp/npoc"
      # 将需要用到的文件上传到跳板机临时目录
      - scp -r ./output/* ./Dockerfile ./docker-compose.yml ./.env ./app.yaml {{.JUMP_SERVER}}:/tmp/npoc/
      # 在跳板机上使用ssh命令让节点创建npoc工作目录
      - ssh {{.JUMP_SERVER}} "ssh {{.AGENT_SERVER}} \"mkdir -p /opt/npoc/logs/\""
      # 在跳板机上使用scp将文件传到扫描节点上
      - ssh {{.JUMP_SERVER}} "scp -r /tmp/npoc/*  {{.AGENT_SERVER}}:/opt/npoc/"
      # 在跳板机上重新运行扫描器
      - ssh {{.JUMP_SERVER}} "ssh {{.AGENT_SERVER}} \"cd /opt/npoc && docker compose up -d\""
