# 基于Ubuntu镜像
FROM debian:11

# 避免交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 安装Python3.9
RUN apt-get update && apt-get install -y \
    wget \
    git \
    fish \
    vim \
    build-essential \
    python3 \
    python3-dev \
    python3-pip \
    python3-venv \
    libssl-dev  \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev  \
    curl \
    llvm \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libffi-dev \
    liblzma-dev \
    python3-openssl \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建软链接确保python命令指向python3
RUN ln -sf /usr/bin/python3 /usr/bin/py && \
    ln -sf /usr/bin/python3 /usr/bin/py3 && \
    ln -sf /usr/bin/python3 /usr/bin/python


# 安装pyenv以及配置环境变量
RUN curl https://pyenv.run | bash && \
    echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc && \
    echo 'export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc && \
    echo 'eval "$(pyenv init --path)"' >> ~/.bashrc && \
    echo 'eval "$(pyenv init -)"' >> ~/.bashrc && \
    . ~/.bashrc

# 安装Go 1.23.4
# 请根据实际情况调整版本号
RUN wget https://golang.org/dl/go1.23.4.linux-amd64.tar.gz -O go.tar.gz && \
    tar -C /usr/local -xzf go.tar.gz && \
    rm go.tar.gz

# 设置Go环境变量
ENV PATH=$PATH:/usr/local/go/bin
ENV GOPATH=/go
ENV GOBIN=$GOPATH/bin
ENV PATH=$PATH:$GOBIN

# 创建应用目录
RUN mkdir -p /opt/npoc/logs && \
    mkdir /opt/npoc/binary-tools && \
    mkdir /opt/npoc/oss


# 创建启动脚本，包含自动重启逻辑
RUN echo '#!/bin/bash\n\
while true; do\n\
    echo "Starting npoc-agent..."\n\
    /opt/npoc/npoc-agent\n\
    echo "Agent exited with code $?. Restarting in 5 seconds..."\n\
    sleep 5\n\
done' > /opt/npoc/start.sh && \
    chmod +x /opt/npoc/start.sh

# 设置工作目录
WORKDIR /opt/npoc

# # 暴露可能需要的端口（根据你的应用需要修改）
# EXPOSE 8989

# 使用启动脚本作为容器的入口点
CMD ["/opt/npoc/start.sh"]