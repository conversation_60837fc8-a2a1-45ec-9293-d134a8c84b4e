volumes:
  otel: { }
services:
  agent:
    build: .
    image: npoc-agent-image:v0.0.4  # 自定义镜像名称
    container_name: npoc-agent-v2
    restart: always
    environment:
      # 调度服务器地址
      - SCHEDULER_ADDRESS=${SCHEDULER_ADDRESS}
      # 调度服务器token
      - NPOC_SCHEDULER_TOKEN=${NPOC_SCHEDULER_TOKEN}
      - OSS_ADDRESS=${OSS_ADDRESS}
      - OSS_TOKEN=${OSS_TOKEN}
      # dnslog地址
      - NPOC_OOB_HOST=${NPOC_OOB_HOST}
      # dnslog api url
      - NPOC_OOB_URL=${NPOC_OOB_URL}
      # dnslog token
      - NPOC_OOB_TOKEN=${NPOC_OOB_TOKEN}
      # 日志输出到控制台
      - NPOC_LOG_OUT_CONSOLE=${NPOC_LOG_OUT_CONSOLE}
      # 扫描器每秒的请求数量限制
      - NPOC_TASK_REQUEST_PARALLEL=${NPOC_TASK_REQUEST_PARALLEL}
      # 扫描器的任务并发数量限制
      - NPOC_TASK_PARALLEL=${NPOC_TASK_PARALLEL}
      # 扫描器的日志记录等级
      - NPOC_LOG_LEVEL=${NPOC_LOG_LEVEL}
    ports:
      - "127.0.0.1:6363:6363"
    volumes:
      - ./logs:/opt/npoc/logs
      - ./binary-tools:/opt/npoc/binary-tools
      - ./npoc-agent:/opt/npoc/npoc-agent:ro
      - ./oss:/opt/npoc/oss
  otel:
    restart: unless-stopped
    network_mode: host
    user: "0:0"  #  Error: cannot start pipelines: storage client: open /var/lib/otelcol/file_storage/receiver_filelog_app: permission denied
    image: "otel/opentelemetry-collector-contrib:0.113.0" # 这个是自己打的一个镜像，正常使用官方提供的镜像也没有太大问题，官方的镜像需要注意是否有日志读取的权限
    volumes:
      - ./logs:/opt/npoc/logs # 日志目录映射
      - ./app.yaml:/etc/otelcol-contrib/config.yaml
      - otel:/var/lib/otelcol/file_storage/logs # 保存日志处理状态，防止重复消费
    environment:
      APP_NAME: ${OTEL_APP_NAME}
      DEPLOY_ENV: ${OTEL_DEPLOY_ENV}
      INSTANCE_ID: ${OTEL_INSTANCE_ID}
      METRIC_ENDPOINT: 127.0.0.1:6363
      NODE_EXPORTER_ENDPOINT: 127.0.0.1:9100
      OTLP_ENDPOINT: otel.0xsec.cc:443
      OTLP_AUTH_USERNAME: dev
      OTLP_AUTH_PASSWORD: "(newpc*mA,h02}/+%jt{JO7bW3^sF'q)"

  node_exporter:
    image: quay.io/prometheus/node-exporter:v1.8.2
    command:
      - "--path.rootfs=/host"
      - "--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|run)($|/)" # wsl需要配置--no-collector.filesystem
      - "--web.listen-address=127.0.0.1:9100"
    network_mode: host
    pid: host
    restart: unless-stopped
    volumes:
      - "/:/host:ro,rslave"