package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	_ "net/http/pprof"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	schedv1 "github.acme.red/backendhub/idl/gen/go/mapper/sched/v1"
	schedulersdkv2 "github.acme.red/backendhub/schedulersdk/v2"
	taskv1 "github.acme.red/intelli-sec/idl/gen/go/task/v1"
	"github.acme.red/mapper/schedulersdk/v2/oss"
	"github.acme.red/pictor/foundation/slogext"
	"github.com/mholt/archiver/v3"
	fileutil "github.com/projectdiscovery/utils/file"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/protobuf/types/known/anypb"

	"github.acme.red/intelli-sec/npoc/internal/config"
	"github.acme.red/intelli-sec/npoc/lib/cache"
	"github.acme.red/intelli-sec/npoc/lib/sdk"
	"github.acme.red/intelli-sec/npoc/pkg/logger"
	"github.acme.red/intelli-sec/npoc/pkg/telemetry"
	"github.acme.red/intelli-sec/npoc/pocs"
	"github.acme.red/intelli-sec/npoc/utils"
)

var (
	// 定义一个 gauge 类型的指标，用于记录当前正在运行的任务数量
	intelliSecRunningTasksGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name: "intelli_sec_running_tasks_total",
		Help: "intelli_sec当前正在运行的任务总数",
	},
		[]string{"task_type"},
	)
	// 定义一个 gauge 类型的指标，用于记录当前正在运行的任务数量
	intelliSecCompletedTasksCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "intelli_sec_completed_tasks_total",
		Help: "intelli_sec已完成的任务总数",
	},
		[]string{"task_type"},
	)
)

const (
	ossLocalDir          = "/opt/npoc/oss/"
	ossPrefix            = "npoc"
	templateOSSObject    = "templates.zip"
	helpersOSSObject     = "helpers.zip"
	webPassDictOSSObject = "web_pass_dict.zip" // nolint:gosec // 1
	templateDir          = "templates"
	helpersDir           = "helpers"
	webPassDictDir       = "web_pass_dict" // nolint:gosec // 1
	ossDataVersionFile   = "version.json"
	pocScanTaskType      = "poc_scan_task"
	genVulnScanTaskType  = "gen_vuln_scan_task"

	pocScanDeadLine     = 20 * time.Minute // 设置poc扫描的最大允许时间
	genVulnScanDeadLine = 50 * time.Minute // 设置通用漏洞扫描的最大允许时间
)

type OSSDataVersion struct {
	Template    string `json:"template"`
	Helpers     string `json:"helpers"`
	WebPassDict string `json:"web_pass_dict"`
}

var (
	ossDataVersion OSSDataVersion
	templateLoadMu sync.Mutex
)

func main() {
	go func() {
		err := runDebugServer()
		if err != nil {
			panic(err)
		}
	}()
	go func() {
		slog.Info("Starting pprof server on :6060")
		if err := http.ListenAndServe(":6060", nil); err != nil {
			slog.Info("pprof server failed", slog.String("error", err.Error()))
		}
	}()
	config.Init()
	logger.SetupLogger(logger.Config{
		LogPath:       *config.LogPath,
		LogLevel:      *config.LogLevel,
		LogMaxSize:    *config.LogMaxSize,
		LogMaxBackups: *config.LogMaxBackup,
		LogMaxAge:     *config.LogMaxAge,
		LogOutConsole: *config.LogOutConsole,
	})
	slog.Info("agent starting")
	defer slog.Info("agent stopped")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()
	defer func() {
		if r := recover(); r != nil {
			// 获取调用栈信息
			buf := make([]byte, 1536) //nolint:mnd // need indeed
			n := runtime.Stack(buf, true)
			slog.ErrorContext(ctx, "recovered from panic", "error", r, "stack trace", buf[:n])
			telemetry.AlertError(telemetry.NpocType, telemetry.PanicType)
			time.Sleep(6 * time.Second) // 延迟退出，让采集器能采集到数据
		}
	}()
	sdk.InitSdk()
	var (
		ossClient *oss.Client
		err       error
	)
	if *config.APPEnv == config.ASMAPPENV { // 只有asm需要加载系统poc和相关的字典
		ossClient, err = oss.NewClient(
			oss.WithAddr(*config.SchedulerAddress),
			oss.WithToken(*config.SchedulerTOKEN),
			oss.WithPrefix(ossPrefix),
		)
		if err != nil {
			slog.Error("agent failed to init oss client", slogext.Error(err), "scheduler_address", *config.SchedulerAddress, "scheduler_token", *config.SchedulerTOKEN)
			panic(err)
		}
		err = updateOSSObject(ctx, ossClient)
		if err != nil {
			slog.Error("load oss object failed", slogext.Error(err))
			panic(err)
		}
		err = pocs.LoadSystemPocs(ctx, filepath.Join(ossLocalDir, templateDir))
		if err != nil {
			slog.Error("load system pocs failed", slogext.Error(err))
			panic(err)
		}
		cache.DictDir = ossLocalDir
	}

	if *config.SchedulerAddress == "" {
		err := errors.New("scheduler address is required, please set env SCHEDULER_ADDRESS")
		slog.Error("scheduler address is required, please set env SCHEDULER_ADDRESS")
		panic(err)
	}
	if *config.SchedulerTOKEN == "" {
		err := errors.New("scheduler token is required, please set env NPOC_SCHEDULER_TOKEN")
		slog.Error("scheduler token is required, please set env NPOC_SCHEDULER_TOKEN")
		panic(err)
	}
	sdkLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	var workerOpts = []schedulersdkv2.Option{
		schedulersdkv2.WithSchedulerAddr(*config.SchedulerAddress),
		schedulersdkv2.WithToken(*config.SchedulerTOKEN),
		schedulersdkv2.WithHandler(fakeHandler{
			ossClient: ossClient,
		}),
		schedulersdkv2.WithMaxTasks(*config.TaskParallel),
		schedulersdkv2.WithLogger(sdkLogger),
	}
	if *config.SchedulerSecure {
		workerOpts = append(workerOpts, schedulersdkv2.WithInsecure())
	}
	worker, err := schedulersdkv2.NewWorker(workerOpts...)
	if err != nil {
		slog.Error("new npoc worker error", slog.String("error", err.Error()))
		panic(err)
	}
	if err := worker.Run(context.Background()); err != nil {
		slog.Error("worker run failed", slog.String("error", err.Error()))
		panic(err)
	}
	panic(errors.New("agent stopped"))
}

type fakeHandler struct {
	ossClient *oss.Client
}

func (h fakeHandler) Handle(ctx context.Context, task *schedv1.Task) (*anypb.Any, error) {
	var (
		pocScanTaskArgs     taskv1.PocScanTaskData
		genVulnScanTaskArgs taskv1.GenVulnScanTaskData
		taskType            string
		startTime           = time.Now()
	)
	switch {
	case task.Args.MessageIs(&pocScanTaskArgs):
		taskType = pocScanTaskType
	case task.Args.MessageIs(&genVulnScanTaskArgs):
		taskType = genVulnScanTaskType
	}
	intelliSecRunningTasksGauge.WithLabelValues(taskType).Inc()
	defer func() {
		intelliSecRunningTasksGauge.WithLabelValues(taskType).Dec()
		intelliSecCompletedTasksCounter.WithLabelValues(taskType).Add(1)
	}()
	label := task.GetLabels()
	for key, value := range label {
		ctx = logger.WithAttr(ctx, key, value)
	}
	ctx = logger.WithAttr(ctx, "unique_id", task.GetUniqueId())
	ctx = logger.WithAttr(ctx, "task_type", taskType)
	defer utils.RecoverFun(ctx)
	switch taskType {
	case pocScanTaskType:
		if err := task.Args.UnmarshalTo(&pocScanTaskArgs); err != nil {
			slog.ErrorContext(ctx, "failed to unmarshal task args", slog.String("task_args_type", task.Args.GetTypeUrl()), slog.String("args_data", string(task.Args.GetValue())))
			return nil, err
		}
		ctx = logger.WithAttr(ctx, "target", pocScanTaskArgs.GetTarget())
		ctx, cancel := context.WithTimeout(ctx, pocScanDeadLine)
		defer cancel()
		slog.DebugContext(ctx, "start task")
		if !pocScanTaskArgs.DisableLoadPoc && h.ossClient != nil {
			h.updateSysPocs(ctx)
		}
		results, err := sdk.RunPocTask(ctx, &pocScanTaskArgs)
		if errors.Is(ctx.Err(), context.DeadlineExceeded) && time.Since(startTime) > pocScanDeadLine { // 因为超时导致的终止扫描
			err = errors.Join(fmt.Errorf("poc scan is skipped,the running time is %f seconds,exceeding the limit of %f seconds", time.Since(startTime).Seconds(), pocScanDeadLine.Seconds()))
			slog.WarnContext(ctx, "run poc task failed", slogext.Error(err))
		}
		if len(results) > 0 {
			pbResult, err2 := sdk.CreateProtobufResult(ctx, results)
			if err2 != nil {
				slog.ErrorContext(ctx, "create protobuf result failed", "task_results", results, slogext.Error(err2))
				return nil, err2
			}
			return pbResult, err
		}
		return nil, err
	case genVulnScanTaskType:
		if err := task.Args.UnmarshalTo(&genVulnScanTaskArgs); err != nil {
			slog.ErrorContext(ctx, "failed to unmarshal task args", slog.String("task_args_type", task.Args.GetTypeUrl()), slog.String("args_data", string(task.Args.GetValue())))
			return nil, err
		}
		ctx = logger.WithAttr(ctx, "target", genVulnScanTaskArgs.GetTarget())
		ctx, cancel := context.WithTimeout(ctx, genVulnScanDeadLine)
		defer cancel()
		slog.DebugContext(ctx, "start task", "args", base64.StdEncoding.EncodeToString([]byte(genVulnScanTaskArgs.String())))
		results, err := sdk.RunGenVulnTask(ctx, &genVulnScanTaskArgs)
		if errors.Is(ctx.Err(), context.DeadlineExceeded) && time.Since(startTime) > genVulnScanDeadLine { // 因为超时导致的终止扫描
			err = errors.Join(fmt.Errorf("gen vuln scan is skipped,the running time is %f seconds,exceeding the limit of %f seconds", time.Since(startTime).Seconds(), pocScanDeadLine.Seconds()))
			slog.WarnContext(ctx, "run gen vuln scan task failed", slogext.Error(err))
		}
		if len(results) > 0 {
			pbResult, err2 := sdk.CreateProtobufResult(ctx, results)
			if err2 != nil {
				slog.ErrorContext(ctx, "create protobuf result failed", "task_results", results, slogext.Error(err2))
				return nil, err2
			}
			return pbResult, err
		}
		return nil, err
	}
	return nil, nil

}

func (h fakeHandler) updateSysPocs(ctx context.Context) {
	objectInfo, err := h.ossClient.StatObject(ctx, templateOSSObject)
	if err != nil {
		slog.ErrorContext(ctx, "get oss object stat failed", slog.String("object_name", templateOSSObject))
		return
	}
	templateLoadMu.Lock()
	defer templateLoadMu.Unlock()
	if ossDataVersion.Template != objectInfo.LastModified.String() {
		if err := loadObject(ctx, h.ossClient, templateOSSObject, filepath.Join(ossLocalDir, templateDir)); err != nil {
			slog.Error("load oss object failed", slogext.Error(err), "oss_object", templateOSSObject)
		} else {
			err = pocs.LoadSystemPocs(ctx, filepath.Join(ossLocalDir, templateDir))
			if err != nil {
				slog.Error("load system pocs failed", slogext.Error(err))
			}
			ossDataVersion.Template = objectInfo.LastModified.String()
		}
	}
}

func runDebugServer() error {
	http.Handle("/metrics", promhttp.Handler())

	//nolint:gosec // 禁用 G114: Use of net/http serve function that has no support for setting timeouts
	if err := http.ListenAndServe("0.0.0.0:6363", nil); err != nil {
		return fmt.Errorf("http server: %w", err)
	}

	return nil
}

// 加载oss中的静态资源
func updateOSSObject(ctx context.Context, ossClient *oss.Client) error {
	versionPath := filepath.Join(ossLocalDir, ossDataVersionFile)
	tplObjectInfo, err := ossClient.StatObject(ctx, templateOSSObject)
	if err != nil {
		return fmt.Errorf("get template oss object failed,oss_object: %s, err:%w", templateOSSObject, err)
	}
	helpersObjectInfo, err := ossClient.StatObject(ctx, helpersOSSObject)
	if err != nil {
		return fmt.Errorf("get helpers oss object failed,oss_object: %s, err: %w", helpersOSSObject, err)
	}
	wpdObjectInfo, err := ossClient.StatObject(ctx, webPassDictOSSObject)
	if err != nil {
		return fmt.Errorf("get web_pass_dict oss object failed,oss_object: %s, err: %w", webPassDictOSSObject, err)
	}
	var needUpdateVersion bool
	defer func() {
		if needUpdateVersion {
			versionContent, _ := json.Marshal(ossDataVersion)
			err = os.WriteFile(versionPath, versionContent, 0644) // nolint:gosec // 1
			if err != nil {
				slog.Error("write oss version file failed", slogext.Error(err))
			}
		}
	}()
	if fileutil.FileExists(versionPath) { // 版本文件存在，判断各字典是否需要更新
		data, err := os.ReadFile(versionPath)
		if err != nil {
			return fmt.Errorf("read oss version file failed: %w", err)
		}
		if err = json.Unmarshal(data, &ossDataVersion); err != nil {
			return fmt.Errorf("unmarshal oss version file failed: %w", err)
		}
		if ossDataVersion.Template != tplObjectInfo.LastModified.String() {
			if err := loadObject(ctx, ossClient, templateOSSObject, filepath.Join(ossLocalDir, templateDir)); err != nil {
				return fmt.Errorf("load oss object failed: %w, object: %s", err, templateOSSObject)
			}

			ossDataVersion.Template = tplObjectInfo.LastModified.String()
			needUpdateVersion = true
		}
		if ossDataVersion.WebPassDict != wpdObjectInfo.LastModified.String() {
			if err := loadObject(ctx, ossClient, webPassDictOSSObject, filepath.Join(ossLocalDir, webPassDictDir)); err != nil {
				return fmt.Errorf("load oss object failed: %w, object: %s", err, webPassDictOSSObject)
			}
			ossDataVersion.WebPassDict = wpdObjectInfo.LastModified.String()
			needUpdateVersion = true
		}
		if ossDataVersion.Helpers != helpersObjectInfo.LastModified.String() {
			if err := loadObject(ctx, ossClient, helpersOSSObject, filepath.Join(ossLocalDir, helpersDir)); err != nil {
				return fmt.Errorf("load oss object failed: %w, object: %s", err, helpersOSSObject)
			}
			ossDataVersion.Helpers = helpersObjectInfo.LastModified.String()
			needUpdateVersion = true
		}
	} else { // 版本文件不存在，生成版本文件并加载所有object
		_ = os.RemoveAll(ossLocalDir)
		_ = os.MkdirAll(ossLocalDir, 0755)
		if err := loadObject(ctx, ossClient, templateOSSObject, filepath.Join(ossLocalDir, templateDir)); err != nil {
			return fmt.Errorf("load oss object failed: %w, oss_object: %s", err, templateOSSObject)
		}
		err = pocs.LoadSystemPocs(ctx, filepath.Join(ossLocalDir, templateDir))
		if err != nil {
			return fmt.Errorf("load system pocs failed: %w", err)
		}
		if err := loadObject(ctx, ossClient, helpersOSSObject, filepath.Join(ossLocalDir, helpersDir)); err != nil {
			return fmt.Errorf("load oss object failed: %w, oss_object: %s", err, helpersOSSObject)
		}
		if err := loadObject(ctx, ossClient, webPassDictOSSObject, filepath.Join(ossLocalDir, webPassDictDir)); err != nil {
			return fmt.Errorf("load oss object failed: %w, oss_object: %s", err, webPassDictOSSObject)
		}
		needUpdateVersion = true
		ossDataVersion = OSSDataVersion{
			Template:    tplObjectInfo.GetLastModified().String(),
			Helpers:     helpersObjectInfo.GetLastModified().String(),
			WebPassDict: wpdObjectInfo.GetLastModified().String(),
		}
	}

	return nil
}

func loadObject(ctx context.Context, ossClient *oss.Client, objectName, objectDir string) error {
	// 清理本地老版本的文件
	err := os.RemoveAll(objectDir)
	if err != nil {
		return fmt.Errorf("clean file failed: %w", err)
	}
	// 获取oss文件
	ossReader, err := ossClient.GetObject(ctx, objectName)
	if err != nil {
		return fmt.Errorf("get template oss object failed: %w", err)
	}
	// 创建本地文件
	localFile, err := os.Create(filepath.Join(ossLocalDir, objectName))
	if err != nil {
		return fmt.Errorf("create local file failed: %w", err)
	}
	defer func() { _ = localFile.Close() }()
	// 将oss文件克隆到本地文件
	_, err = io.Copy(localFile, ossReader)
	if err != nil {
		_ = localFile.Close()
		return fmt.Errorf("写入本地文件失败: %w", err)
	}
	// 将压缩包解压到指定目录
	err = archiver.Unarchive(localFile.Name(), ossLocalDir)
	if err != nil {
		return fmt.Errorf("unarchive file failed: %w", err)
	}
	return nil
}
