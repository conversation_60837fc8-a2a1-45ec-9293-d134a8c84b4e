package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/alecthomas/kingpin/v2"
	"github.com/elazarl/goproxy"
	"github.com/thoas/go-funk"
	"golang.org/x/sync/semaphore"
	"gopkg.in/yaml.v3"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/internal/config"
	"github.acme.red/intelli-sec/npoc/lib/sdk"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	nsync "github.acme.red/intelli-sec/npoc/pkg/sync"
	"github.acme.red/intelli-sec/npoc/pocs"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

type Config struct {
	OOBHost             string `yaml:"oob-host"`
	OOBToken            string `yaml:"oob-token"`
	OOBUrl              string `yaml:"oob-url"`
	TaskRequestParallel int    `yaml:"host-request-parallel"`
	Proxy               string `yaml:"proxy"`
}

// VulnerabilityReport 漏洞报告结构
type VulnerabilityReport struct {
	ScanInfo        ScanInfo                `json:"scan_info"`
	Vulnerabilities []VulnerabilityDetail   `json:"vulnerabilities"`
	Statistics      VulnerabilityStatistics `json:"statistics"`
}

// ScanInfo 扫描信息
type ScanInfo struct {
	ScanTime    time.Time `json:"scan_time"`
	ScanMode    string    `json:"scan_mode"`
	Target      string    `json:"target,omitempty"`
	RequestPath string    `json:"request_path,omitempty"`
	FastScan    bool      `json:"fast_scan"`
	PocIds      []string  `json:"poc_ids"`
}

// VulnerabilityDetail 漏洞详细信息
type VulnerabilityDetail struct {
	PoC           string                 `json:"poc"`
	PocType       string                 `json:"poc_type"`
	Name          string                 `json:"name"`
	Category      string                 `json:"category"`
	Severity      string                 `json:"severity"`
	Confidence    string                 `json:"confidence"`
	Method        string                 `json:"method"`
	Param         string                 `json:"param,omitempty"`
	Payload       string                 `json:"payload,omitempty"`
	Description   string                 `json:"description,omitempty"`
	URL           string                 `json:"url"`
	Extra         map[string]string      `json:"extra,omitempty"`
	OOBURL        string                 `json:"ooburl,omitempty"`
	OOBDetails    []OOBDetailJSON        `json:"oob_details,omitempty"`
	HTTP          *VulnerabilityHTTPJSON `json:"http,omitempty"`
	DiscoveryTime time.Time              `json:"discovery_time"`
}

// VulnerabilityHTTPJSON HTTP相关信息
type VulnerabilityHTTPJSON struct {
	Follows []HTTPFollowJSON `json:"follows,omitempty"`
}

// HTTPFollowJSON HTTP请求响应信息
type HTTPFollowJSON struct {
	Request  *RequestJSON  `json:"request,omitempty"`
	Response *ResponseJSON `json:"response,omitempty"`
}

// RequestJSON 请求信息
type RequestJSON struct {
	Method          string              `json:"method"`
	URL             string              `json:"url"`
	Header          map[string][]string `json:"header,omitempty"`
	Body            string              `json:"body,omitempty"`
	FollowRedirects bool                `json:"follow_redirects"`
}

// ResponseJSON 响应信息
type ResponseJSON struct {
	Status    int                 `json:"status"`
	StatusMsg string              `json:"status_msg"`
	Proto     string              `json:"proto"`
	Header    map[string][]string `json:"header,omitempty"`
	Body      string              `json:"body,omitempty"`
}

// OOBDetailJSON OOB详情
type OOBDetailJSON struct {
	// 根据实际的OOBDetail结构来定义
	Data interface{} `json:"data,omitempty"`
}

// VulnerabilityStatistics 漏洞统计信息
type VulnerabilityStatistics struct {
	TotalVulnerabilities int            `json:"total_vulnerabilities"`
	BySeverity           map[string]int `json:"by_severity"`
	ByCategory           map[string]int `json:"by_category"`
	ByConfidence         map[string]int `json:"by_confidence"`
	ScanDuration         string         `json:"scan_duration"`
	RequestsProcessed    int64          `json:"requests_processed"`
}

var (
	app               = kingpin.New("iscan", "通用漏洞扫描器")
	proxyMode         = app.Command("proxy", "代理模式，通过被动代理传入请求进行扫描")
	listenAddr        = proxyMode.Flag("listening", "代理服务监听地址").Default("127.0.0.1:10086").Short('l').String()
	target            = proxyMode.Flag("target", "指定扫描的域名,获取到非该域名的请求不进行扫描").Short('t').String()
	assignMode        = app.Command("assign", "指定请求包模式，通过指定请求包目录地址/文件地址，扫描内容为raw格式的http请求包")
	reqPath           = assignMode.Flag("path", "代理服务监听地址").Default("./requests").Short('p').String()
	targetSchema      = assignMode.Flag("schema", "指定目标服务类型http/https").Default("http").Short('s').String()
	maxRecordBodySize = app.Flag("r", "max record body size per request").Default("1000000").Int()
	requestQueueSize  = app.Flag("q", "task queue size").Default("3000").Int()
	pocId             = app.Flag("id", "指定想要扫描的漏洞类型，多个用逗号隔开，不指定默认全扫").Short('i').Enum(npoc.GenericVulnCategories...)
	configFile        = app.Flag("cf", "传入运行需要配置文件地址，yaml格式").Default("config.yaml").Short('c').String()
	fastScan          = app.Flag("fs", "是否开启快速扫描模式，默认关闭").Short('f').Bool()
	// 新增JSON输出参数
	jsonOutput = app.Flag("json", "将扫描结果保存为JSON文件").Short('j').String()
)

var (
	httpFollowQueue chan *npoc.HTTPFollow
	staticSuffix    = []string{
		"png", "gif", "jpg", "mp4", "mp3", "mng", "pct", "bmp", "jpeg", "pst", "psp", "ttf",
		"tif", "tiff", "ai", "drw", "wma", "ogg", "wav", "ra", "aac", "mid", "au", "aiff",
		"dxf", "eps", "ps", "svg", "3gp", "asf", "asx", "avi", "mov", "mpg", "qt", "rm",
		"wmv", "m4a", "bin", "xls", "xlsx", "ppt", "pptx", "doc", "docx", "odt", "ods", "odg",
		"odp", "exe", "zip", "rar", "tar", "gz", "iso", "rss", "pdf", "txt", "dll", "ico",
		"gz2", "apk", "crt", "woff", "map", "woff2", "webp", "less", "dmg", "bz2", "otf", "swf",
		"flv", "mpeg", "dat", "xsl", "csv", "cab", "exif", "wps", "m4v", "rmvb", "js", "css", "scss",
		"html", "htm", "xml",
	}
	staticContentType = []string{"application/javascript", "image/jpeg", "image/gif"}

	// 全局变量用于收集漏洞信息
	vulnerabilityReport = VulnerabilityReport{
		Vulnerabilities: make([]VulnerabilityDetail, 0),
		Statistics: VulnerabilityStatistics{
			BySeverity:   make(map[string]int),
			ByCategory:   make(map[string]int),
			ByConfidence: make(map[string]int),
		},
	}
	reportMutex   sync.Mutex
	scanStartTime time.Time

	jsonOutputFile       *os.File
	jsonOutputMutex      sync.Mutex // 专用于保护文件写入
	isFirstVulnerability = true     // 用于处理逗号分隔
)

func main() {
	scanStartTime = time.Now()
	cmd := kingpin.MustParse(app.Parse(os.Args[1:]))

	// 初始化扫描信息
	initScanInfo(cmd)

	// 如果指定了 -j 参数，立即初始化流式文件
	if *jsonOutput != "" {
		err := initJSONStream(*jsonOutput)
		if err != nil {
			log.Fatalf("无法初始化JSON报告文件: %v", err)
		}
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // Ensure cancel is called on exit

	shutdownSignal := make(chan os.Signal, 1)
	signal.Notify(shutdownSignal, os.Interrupt, syscall.SIGTERM)

	httpFollowQueue = make(chan *npoc.HTTPFollow, *requestQueueSize)

	switch cmd {
	case proxyMode.FullCommand():
		go proxyServer()
	case assignMode.FullCommand():
		loadRequestFile(httpFollowQueue)
		close(httpFollowQueue)
	}

	var configData Config
	if *configFile != "" {
		configFileData, err := os.ReadFile(*configFile)
		if err != nil {
			slog.Warn("未检测到配置文件,也未使用参数指定oob域名和token,无法正常使用oob进行验证")
		} else {
			err = yaml.Unmarshal(configFileData, &configData)
			if err != nil {
				slog.Warn("未检测到配置文件，无法使用oob进行验证")
			}
		}
	}

	if *config.Proxy != "" {
		configData.Proxy = *config.Proxy
	} else if configData.Proxy != "" {
		*config.Proxy = configData.Proxy
	}
	if *config.TaskReqParallel == 25 {
		*config.TaskReqParallel = configData.TaskRequestParallel
	}
	if *config.OOBHost == "" || *config.OOBToken == "" || *config.OOBURL == "" {
		if configData.OOBUrl != "" && configData.OOBToken != "" && configData.OOBHost != "" {
			*config.OOBHost = configData.OOBHost
			*config.OOBToken = configData.OOBToken
			*config.OOBURL = configData.OOBUrl
		}
	}
	sdk.InitSdk()
	opt := httpv.ClientOpt{
		HostReqMaxParallel: *config.TaskReqParallel,
		HostErrMaxNum:      30,
		FailRetries:        1,
		Proxy:              *config.Proxy,
	}
	reqFilter := make(map[string]struct{})
	vulnFilter := sync.Map{}
	taskClient, err := httpv.NewClient(context.Background(), opt)
	if err != nil {
		panic(err)
	}

	taskClient.RdnsClient = sdk.GetEngine().RdnsClient
	var includeGenIds []string
	if *pocId != "" {
		includeGenIds = strings.Split(*pocId, ",")
	} else {
		includeGenIds = npoc.GenericVulnCategories
	}
	// 处理poc相关，主要根据参数配置过滤部分poc
	filterOpt := npoc.Option{
		IncludeYamlIds:   []string{},
		ExcludeYamlIds:   []string{},
		IncludeCommonIds: includeGenIds,
		PocParallel:      10,
	}
	np, err := npoc.New(pocs.GenVulnPocs, filterOpt)
	if err != nil {
		panic(err)
	}

	taskCache := nsync.NewRWMutexMap[string, any](100)

	reqPools := semaphore.NewWeighted(10) // 10个请求并发扫描
	wg := sync.WaitGroup{}
	progress := sync.Map{}
	go func() {
		for {
			time.Sleep(2 * time.Second)
			startScan, ok := progress.Load("start")
			if !ok {
				continue
			}
			completeScan, ok := progress.Load("complete")
			if !ok {
				completeScan = 0
			}
			requestNum := taskClient.RequestCounter.Load()
			slog.Info("进度信息", "started", startScan, "completed", completeScan, "requestNum", requestNum)
		}
	}()

	go func() {
		<-shutdownSignal
		slog.Info("收到关闭信号 (Ctrl+C)，开始退出...")

		close(httpFollowQueue)

		cancel()
	}()

	for rawHTTPFollow := range httpFollowQueue {
		select {
		case <-ctx.Done():
			continue
		default:
		}
		lang := detector.DetectServerLanguage(rawHTTPFollow.Request, rawHTTPFollow.Response)

		rawHTTPFollow.ParseParam()
		baseReqInfo := fmt.Sprintf("%s %s://%s%s",
			rawHTTPFollow.Method,
			rawHTTPFollow.URL.Scheme,
			rawHTTPFollow.URL.Host,
			rawHTTPFollow.URL.Path)

		// 构建参数信息用于去重
		var paramKeys []string
		for _, param := range rawHTTPFollow.Params() {
			paramKeys = append(paramKeys, param.Key)
		}

		// 用于去重的完整请求标识
		var reqInfo string
		if len(paramKeys) > 0 {
			reqInfo = fmt.Sprintf("%s?%s", baseReqInfo, strings.Join(paramKeys, "&"))
		} else {
			reqInfo = baseReqInfo
		}

		// 用于日志显示的简洁信息
		displayInfo := baseReqInfo
		if len(paramKeys) > 0 {
			if len(paramKeys) <= 3 {
				displayInfo = fmt.Sprintf("%s [params: %s]", baseReqInfo, strings.Join(paramKeys, ", "))
			} else {
				displayInfo = fmt.Sprintf("%s [params: %s... (%d total)]",
					baseReqInfo,
					strings.Join(paramKeys[:3], ", "),
					len(paramKeys))
			}
		}

		if _, ok := reqFilter[reqInfo]; ok {
			continue
		} else {
			reqFilter[reqInfo] = struct{}{}
		}
		website := fmt.Sprintf("%s://%s", rawHTTPFollow.URL.Scheme, rawHTTPFollow.URL.Host)
		if exceeded, count := taskClient.FailExceededLimit(); exceeded {
			slog.Error("host fail exceeded limit, the host is not scanning anymore", "website", website, "err_count", count)
			continue
		}
		err = reqPools.Acquire(ctx, 1)
		if err != nil {
			slog.Info("无法获取任务池，可能正在关闭...", "error", err)
			continue
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer reqPools.Release(1)
			startScan, ok := progress.Load("start")
			if !ok {
				progress.Store("start", 1)
			} else {
				progress.Store("start", startScan.(int)+1)
			}
			slog.Info("start scan", "request_info", displayInfo)
			npTask := &npoc.HTTPTask{ // todo web弱口令爆破需要加载字典给WebLoginBruteDict赋值
				Request:   rawHTTPFollow.Request,
				Response:  rawHTTPFollow.Response,
				Client:    taskClient,
				TaskCache: taskCache,
				FullScan:  !*fastScan,
			}
			npTask.FingerPrint = append(npTask.FingerPrint, httpv.FingerprintInfo{Name: lang})
			tr, err := np.DoTask(ctx, npTask)
			slog.Info("completed scan", "request_info", displayInfo)
			if err != nil {
				slog.Error("Failed to do rawHTTPFollow", "error", err)
				return
			}
			completeScan, ok := progress.Load("complete")
			if !ok {
				progress.Store("complete", 1)
			} else {
				progress.Store("complete", completeScan.(int)+1)
			}
			if len(tr.Vulnerabilities) > 0 {
				for _, vuln := range tr.Vulnerabilities {
					vulnInfo := fmt.Sprintf("%s%s%s", vuln.PoC, vuln.Param, vuln.Extra[npoc.ExtraKeySubCategory])
					if vuln.PoC == "dirsearch" {
						vulnInfo = vulnInfo + vuln.Payload
					} else {
						vulnInfo = vulnInfo + vuln.URL
					}
					_, exist := vulnFilter.Load(vulnInfo)
					if exist { // 没有重复
						continue
					}
					vulnFilter.Store(vulnInfo, struct{}{})

					slog.Info("成功发现漏洞", "name", vuln.Name, "url", vuln.URL, "payload", vuln.Payload)
					if vuln.Param != "" {
						slog.Info("参数", "param", vuln.Param)
					}
					for key, value := range vuln.Extra {
						slog.Info("extra key", "key", key, "value", value)
					}

					// 如果指定了JSON输出，则收集漏洞信息
					if *jsonOutput != "" {
						streamVulnerabilityToReport(vuln)
					}
				}
			}
		}()
	}
	slog.Info("任务队列已关闭，等待所有正在运行的扫描完成...")
	wg.Wait()
	slog.Info("所有扫描任务已完成。")

	// 扫描完成后，如果指定了JSON输出，则保存报告
	if *jsonOutput != "" {
		finalizeJSONStream(taskClient)
	}

	slog.Info("程序已成功退出。")
}

// initJSONStream 初始化JSON流式文件
func initJSONStream(filename string) error {
	var err error
	// 获取可用文件名
	availableFilename := getAvailableFilename(filename)

	// 创建或清空文件
	jsonOutputFile, err = os.Create(availableFilename)
	if err != nil {
		return fmt.Errorf("创建JSON报告文件失败: %w", err)
	}

	// 复制一份 ScanInfo 用于写入
	reportMutex.Lock()
	scanInfoCopy := vulnerabilityReport.ScanInfo
	reportMutex.Unlock()

	scanInfoJSON, err := json.MarshalIndent(scanInfoCopy, "  ", "  ")
	if err != nil {
		return fmt.Errorf("序列化 scan_info 失败: %w", err)
	}

	// 写入JSON文件的开头部分
	// {
	//   "scan_info": { ... },
	//   "vulnerabilities": [
	initialContent := fmt.Sprintf("{\n  \"scan_info\": %s,\n  \"vulnerabilities\": [", string(scanInfoJSON))
	_, err = jsonOutputFile.WriteString(initialContent)
	if err != nil {
		return fmt.Errorf("写入JSON文件头失败: %w", err)
	}

	slog.Info("JSON 报告文件已创建，将流式写入漏洞", "filename", availableFilename)
	return nil
}

// finalizeJSONStream 完成JSON文件的写入
func finalizeJSONStream(taskClient *httpv.Client) {
	if jsonOutputFile == nil {
		return
	}

	// --- 开始收尾工作 ---
	jsonOutputMutex.Lock()
	defer jsonOutputMutex.Unlock()
	defer jsonOutputFile.Close()

	slog.Info("开始完成JSON报告的写入...")

	// --- 完善统计信息 ---
	reportMutex.Lock()
	vulnerabilityReport.Statistics.ScanDuration = time.Since(scanStartTime).String()
	vulnerabilityReport.Statistics.RequestsProcessed = taskClient.RequestCounter.Load()
	statsCopy := vulnerabilityReport.Statistics
	reportMutex.Unlock()

	statsJSON, err := json.MarshalIndent(statsCopy, "  ", "  ")
	if err != nil {
		slog.Error("序列化 statistics 失败", "error", err)
		// 即使序列化失败，也要尝试关闭JSON结构
		statsJSON = []byte("{}")
	}

	// --- 写入文件的结尾部分 ---
	var finalContent string
	// 如果写入过漏洞，需要先回退一个字符以覆盖最后的逗号
	if !isFirstVulnerability {
		//   ... { "vuln": "..." }
		// ], <--- 在这里结束
		finalContent = fmt.Sprintf("\n  ],\n  \"statistics\": %s\n}", string(statsJSON))
	} else {
		//   ... []
		// ], <--- 在这里结束
		finalContent = fmt.Sprintf("\n  ],\n  \"statistics\": %s\n}", string(statsJSON))
	}

	_, err = jsonOutputFile.WriteString(finalContent)
	if err != nil {
		slog.Error("写入JSON文件尾失败", "error", err)
		return
	}

	reportMutex.Lock()
	totalVulns := vulnerabilityReport.Statistics.TotalVulnerabilities
	reportMutex.Unlock()

	slog.Info("漏洞报告已成功保存", "filename", jsonOutputFile.Name(), "total_vulnerabilities", totalVulns)
}

// initScanInfo 初始化扫描信息
func initScanInfo(cmd string) {
	var includeGenIds []string
	if *pocId != "" {
		includeGenIds = strings.Split(*pocId, ",")
	} else {
		includeGenIds = npoc.GenericVulnCategories
	}

	vulnerabilityReport.ScanInfo = ScanInfo{
		ScanTime: scanStartTime,
		FastScan: *fastScan,
		PocIds:   includeGenIds,
	}

	switch cmd {
	case proxyMode.FullCommand():
		vulnerabilityReport.ScanInfo.ScanMode = "proxy"
		if *target != "" {
			vulnerabilityReport.ScanInfo.Target = *target
		}
	case assignMode.FullCommand():
		vulnerabilityReport.ScanInfo.ScanMode = "assign"
		vulnerabilityReport.ScanInfo.RequestPath = *reqPath
	}
}

// streamVulnerabilityToReport 将单个漏洞流式写入JSON文件
func streamVulnerabilityToReport(vuln *npoc.Vulnerability) {
	reportMutex.Lock()
	vulnerabilityReport.Statistics.BySeverity[string(vuln.Severity)]++
	vulnerabilityReport.Statistics.ByCategory[string(vuln.Category)]++
	vulnerabilityReport.Statistics.ByConfidence[string(vuln.Confidence)]++
	vulnerabilityReport.Statistics.TotalVulnerabilities++
	reportMutex.Unlock()

	oobURL := ""
	if vuln.OOBUrl != nil {
		oobURL = vuln.OOBUrl.URL()
	}
	httpFlow := &VulnerabilityHTTPJSON{[]HTTPFollowJSON{}}
	for _, h := range vuln.HTTP.Follows {
		httpFlow.Follows = append(httpFlow.Follows, HTTPFollowJSON{
			Request:  loadRequest(h.Request),
			Response: loadResponse(h.Response),
		})
	}

	vulnDetail := VulnerabilityDetail{
		PoC:           vuln.PoC,
		PocType:       string(vuln.PocType),
		Name:          vuln.Name,
		Category:      string(vuln.Category),
		Severity:      string(vuln.Severity),
		Confidence:    string(vuln.Confidence),
		Method:        vuln.Method,
		Param:         vuln.Param,
		Payload:       vuln.Payload,
		Description:   vuln.Description,
		URL:           vuln.URL,
		Extra:         vuln.Extra,
		DiscoveryTime: time.Now(),
		HTTP:          httpFlow,
		OOBURL:        oobURL,
	}
	vulnJSON, err := json.MarshalIndent(vulnDetail, "    ", "  ")
	if err != nil {
		slog.Error("序列化漏洞详情失败", "error", err, "poc", vuln.Name)
		return
	}

	// 使用专用锁保护文件写入操作
	jsonOutputMutex.Lock()
	defer jsonOutputMutex.Unlock()

	if jsonOutputFile == nil {
		return // 如果文件未初始化，则不执行任何操作
	}

	var contentToWrite string
	if isFirstVulnerability {
		// 第一个漏洞，不需要在前面加逗号
		contentToWrite = fmt.Sprintf("\n    %s", string(vulnJSON))
		isFirstVulnerability = false // 更新标志
	} else {
		// 后续的漏洞，需要在前面加上逗号和换行符
		contentToWrite = fmt.Sprintf(",\n    %s", string(vulnJSON))
	}

	if _, err := jsonOutputFile.WriteString(contentToWrite); err != nil {
		slog.Error("流式写入漏洞信息到文件失败", "error", err)
	}
}

// getAvailableFilename 获取可用的文件名，如果文件已存在则添加数字后缀
func getAvailableFilename(baseFilename string) string {
	if _, err := os.Stat(baseFilename); os.IsNotExist(err) {
		return baseFilename
	}

	// 分离文件名和扩展名
	ext := filepath.Ext(baseFilename)
	nameWithoutExt := strings.TrimSuffix(baseFilename, ext)

	// 如果没有扩展名，默认使用.json
	if ext == "" {
		ext = ".json"
	}

	// 尝试添加数字后缀
	for i := 1; ; i++ {
		newFilename := fmt.Sprintf("%s_%d%s", nameWithoutExt, i, ext)
		if _, err := os.Stat(newFilename); os.IsNotExist(err) {
			return newFilename
		}
	}
}

// 从一个函数，变成一个普通的同步函数
func loadRequestFile(queue chan<- *npoc.HTTPFollow) {
	var fileCount int // 用于计数成功入队的文件

	// 检查并记录 filepath.Walk 返回的错误
	walkErr := filepath.Walk(*reqPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err // 返回错误，可能会终止遍历
		}

		if !info.IsDir() {

			content, err := os.ReadFile(path)
			if err != nil {
				slog.Error("读取请求文件失败", "path", path, "error", err)
				return nil // 继续遍历其他文件
			}

			req, err := httpv.String2Request(string(content), *targetSchema)
			if err != nil {
				slog.Error("解析请求文件失败", "path", path, "error", err)
				return nil // 继续遍历其他文件
			}

			queue <- &npoc.HTTPFollow{Request: req}
			fileCount++ // 计数
		}
		return nil
	})

	if walkErr != nil {
		slog.Error("filepath.Walk 执行完成，但过程中遇到了错误", "error", walkErr)
	}

}

func proxyServer() {
	proxy := goproxy.NewProxyHttpServer()
	proxy.Logger = log.New(io.Discard, "", 0)
	// // 关键步骤1: 设置代理为 MITM 模式，允许拦截 HTTPS 请求
	// proxy.Verbose = true

	// 关键步骤2: 设置 TLS 配置，允许代理生成证书
	proxy.OnRequest().HandleConnect(goproxy.AlwaysMitm)

	proxy.OnRequest().DoFunc(func(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
		body := NewRecordReadCloser(req.Body, *maxRecordBodySize)
		req.Body = body
		ctx.UserData = body
		return req, nil
	})
	proxy.OnResponse().DoFunc(func(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {
		if !strings.Contains(ctx.Req.URL.Host, *target) {
			return resp
		}
		if resp == nil {
			return resp
		}
		clonedReq := ctx.Req.Clone(context.Background())
		if !httpFilter(clonedReq, resp) {
			return resp
		}
		r, ok := ctx.UserData.(*RecordReadCloser)
		if !ok {
			return nil
		}
		// 读取原始响应体
		limitedReader := io.LimitReader(resp.Body, int64(*maxRecordBodySize))
		respBodyBytes, err := io.ReadAll(limitedReader)
		var recordedBody []byte
		if err != nil {
			slog.Error("failed to read response body", "error", err)
			recordedBody = nil
		} else {
			recordedBody = respBodyBytes
		}
		resp.Body.Close()
		if recordedBody != nil {
			resp.Body = io.NopCloser(bytes.NewReader(recordedBody))
		} else {
			resp.Body = io.NopCloser(bytes.NewReader([]byte{}))
		}

		httpFlow := &npoc.HTTPFollow{
			Request: &httpv.Request{
				Method:          clonedReq.Method,
				URL:             clonedReq.URL,
				Header:          clonedReq.Header,
				Body:            r.Buffer().Bytes(),
				FollowRedirects: true,
			},
			Response: &httpv.Response{
				Status:    resp.StatusCode,
				StatusMsg: resp.Status,
				Proto:     resp.Proto,
				Header:    resp.Header,
				Body:      recordedBody,
			},
		}
		select {
		case httpFollowQueue <- httpFlow:
		default:
		}
		return resp
	})
	slog.Info("start proxy listening", "address", fmt.Sprintf("http://%s", *listenAddr))
	err := http.ListenAndServe(*listenAddr, proxy)
	if err != nil {
		panic(err)
	}
}

func httpFilter(req *http.Request, resp *http.Response) bool {
	siteExt := path.Ext(req.URL.String())
	if siteExt != "" {
		siteExt = strings.Trim(siteExt, ".")
	}
	if funk.ContainsString(staticSuffix, siteExt) {
		return false
	}
	respContent := resp.Header.Get("Content-Type")
	return !funk.ContainsString(staticContentType, respContent)
}

func NewRecordReadCloser(r io.ReadCloser, maxRecordSize int) *RecordReadCloser {
	return &RecordReadCloser{
		rc:            r,
		buf:           bytes.NewBuffer(nil),
		closed:        false,
		maxRecordSize: maxRecordSize,
	}
}

func loadRequest(request *httpv.Request) *RequestJSON {
	return &RequestJSON{
		Method:          request.Method,
		URL:             request.URL.String(),
		Header:          request.Header,
		Body:            string(request.Body),
		FollowRedirects: request.FollowRedirects,
	}
}

func loadResponse(resp *httpv.Response) *ResponseJSON {
	return &ResponseJSON{
		Status:    resp.Status,
		StatusMsg: resp.StatusMsg,
		Proto:     resp.Proto,
		Header:    resp.Header,
		Body:      string(resp.Body),
	}
}

type RecordReadCloser struct {
	rc            io.ReadCloser
	buf           *bytes.Buffer
	maxRecordSize int
	closed        bool
}

func (r *RecordReadCloser) Read(p []byte) (n int, err error) {
	n, err = r.rc.Read(p)
	remain := min(r.maxRecordSize-r.buf.Len(), n)
	if remain > 0 {
		r.buf.Write(p[:remain])
	}
	return
}

func (r *RecordReadCloser) Close() error {
	r.closed = true
	return r.rc.Close()
}

func (r *RecordReadCloser) Buffer() *bytes.Buffer {
	return r.buf
}
