version: "3"

dotenv:
  - .env

env:
  CGO_ENABLED: 0

vars:
  BINARY_NAME: iscan

tasks:
  build:
    cmds:
      - GOOS=linux GOARCH=amd64 go build -trimpath -o ./output/{{.BINARY_NAME}}-linux
  build-mac:
    cmds:
      - GOOS=darwin GOARCH=arm64 go build -trimpath -o ./output/{{.BINARY_NAME}}-darwin
  build-win:
    cmds:
      - GOOS=windows GOARCH=amd64 go build -trimpath -o ./output/{{.BINARY_NAME}}-win.exe
  build-all:
    deps:
      - build
      - build-mac
      - build-win