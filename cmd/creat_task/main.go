//nolint:mnd // e2e tests
package main

import (
	"context"
	"flag"
	"log/slog"

	workflowv2 "github.acme.red/mapper/idl/gen/go/mapper/workflow/v2"
	"github.acme.red/pictor/foundation/slogext"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	serverAddr := flag.String("server-addr", "**************:8080", "workflow server address")
	flag.Parse()

	conn, err := grpc.NewClient(*serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		slog.Error("new grpc client", slogext.Error(err))
		return
	}

	client := workflowv2.NewWorkflowServiceClient(conn)

	ctx := context.Background()

	resp, err := client.CreateWorkflow(ctx, generateAsmWorkflow())
	if err != nil {
		slog.Error("create workflow", slogext.Error(err))
		return
	}

	slog.Info("create workflow success", slog.String("workflow_id", resp.GetId()))
}

func generateAsmWorkflow() *workflowv2.CreateWorkflowRequest {
	// 	pocContent := `id: test_raw_http_poc_2
	// info:
	//   name: test raw http poc
	//   author: default
	//   severity: high
	//   description: test raw http poc
	//   metadata:
	//     # 按照指定的漏洞类型赋值，请不要填任意值
	//     type: info-leak
	//   tags: raw
	//
	// http:
	//   # 原始格式的http请求
	//   - raw:
	//       - |-
	//         GET /test_raw_http_poc_2 HTTP/1.1
	//         Host: {{Hostname}}
	//         Accept: */*
	//     matchers:
	//       - type: dsl
	//         dsl:
	//           # 校验响应中是否包含Example和Domain
	//           - contains_all(response, "Example", "Domain")
	//     extractors:
	//       - type: regex
	//         name: data
	//         group: 1
	//         regex:
	//           - This domain is for use in (.*) examples in documents
	// `
	return &workflowv2.CreateWorkflowRequest{
		Labels: map[string]string{"project_id": "example_project_id"},
		Seed: &workflowv2.Scope{
			// Domains: []string{"www.example.com"},
			Ips: []string{"*************"},
			// Urls: []string{"http://testphp.vulnweb.com/"},
		},
		Nodes: []*workflowv2.Node{
			{
				Kind: workflowv2.TaskKind_TASK_KIND_INPUT,
			},
			{
				Kind: workflowv2.TaskKind_TASK_KIND_SERVICE_PROBE,
				ServiceProbeOptions: &workflowv2.ServiceProbeOptions{
					Parallel:      1,
					EnabledProbes: nil,
					Ports:         []int32{3306},
					Fast:          false,
				},
			},
			// {
			// 	Kind: workflowv2.TaskKind_TASK_KIND_POC,
			// 	PocOptions: &workflowv2.PocOptions{
			// 		FailRetries:           2,
			// 		HostErrMaxNum:         2,
			// 		HostReqMaxParallel:    8,
			// 		IncludeYamlIds:        []string{"test_raw_http_poc", "test_raw_http_exp", "3Com-wireless-default-login"},
			// 		IncludeYamlSeverities: nil,
			// 		FingerprintMode:       false,
			// 		Variables:             map[string]string{"path": "value_path", "data": "value_data"},
			// 		DisableLoadPoc:        false,
			// 	},
			// },
			{
				Kind: workflowv2.TaskKind_TASK_KIND_PASS_BRUTE,
				PassBruteOptions: &workflowv2.PassBruteOptions{
					Parallel: 20,
					DictKey:  "",
				},
			},
			// {
			// 	Kind: workflowv2.TaskKind_TASK_KIND_DIR_PROBE,
			// 	DirProbeOptions: &workflowv2.DirProbeOptions{
			// 		ReqParallel: 20,
			// 		DictKey:     "top2k",
			// 	},
			// },
			// {
			// 	Kind: workflowv2.TaskKind_TASK_KIND_VULN,
			// 	VulnOptions: &workflowv2.VulnOptions{
			// 		FailRetries:        2,
			// 		HostReqMaxParallel: 2,
			// 		HostErrMaxNum:      2,
			// 		IncludeCommonIds:   nil,
			// 		UseDefaultDict:       "",
			// 		LoginOtherDict:     nil,
			// 	},
			// },
		},
		Edges: []*workflowv2.Edge{
			{Source: workflowv2.TaskKind_TASK_KIND_INPUT, Target: workflowv2.TaskKind_TASK_KIND_SERVICE_PROBE},
			{Source: workflowv2.TaskKind_TASK_KIND_SERVICE_PROBE, Target: workflowv2.TaskKind_TASK_KIND_PASS_BRUTE},
		},
	}
}
