package config

import (
	"github.com/alecthomas/kingpin/v2"
)

const (
	ASMAPPENV     = "asm"
	POCSCANAPPENV = "pocscan"
)

var (
	OOBHost          = kingpin.Flag("oob_host", "dnslog 地址, host:port").Envar("NPOC_OOB_HOST").String()
	OOBToken         = kingpin.Flag("oob_token", "dnslog token").Envar("NPOC_OOB_TOKEN").String()
	OOBURL           = kingpin.Flag("oob_url", "dnslog api url").Envar("NPOC_OOB_URL").String()
	SchedulerAddress = kingpin.Flag("sa", "调度器服务端地址, host:port").Envar("SCHEDULER_ADDRESS").String()
	SchedulerSecure  = kingpin.Flag("ss", "是否绕过证书校验，正式环境不用绕过").Envar("SCHEDULER_SECURE").Bool()
	SchedulerTOKEN   = kingpin.Flag("st", "调度器token").Envar("NPOC_SCHEDULER_TOKEN").String()
	TaskReqParallel  = kingpin.Flag("hrp", "对于同一任务/目标的请求并发数").Envar("NPOC_TASK_REQUEST_PARALLEL").Default("25").Short('r').Int()
	APPEnv           = kingpin.Flag("ae", "应用环境，asm/pocscan").Envar("NPOC_APP_ENV").Default("pocscan").Enum(ASMAPPENV, POCSCANAPPENV)
	TaskParallel     = kingpin.Flag("hp", "任务/目标并发数").Envar("NPOC_TASK_PARALLEL").Default("40").Int()
	Proxy            = kingpin.Flag("proxy", "配置全局的socks5代理").Envar("NPOC_PROXY").String()
	LogOutConsole    = kingpin.Flag("loc", "日志输出到控制台").Default("true").Envar("NPOC_LOG_OUT_CONSOLE").Bool()
	LogPath          = kingpin.Flag("lp", "Log Path").Default("/opt/npoc/logs/agent.log").Envar("NPOC_LOG_PATH").String()
	LogLevel         = kingpin.Flag("lv", "Log Level").Default("debug").Envar("NPOC_LOG_LEVEL").Enum("debug", "info", "warn", "error")
	LogMaxSize       = kingpin.Flag("lms", "单个日志文件最大容量 MB").Envar("NPOC_LOG_MAX_SIZE").Default("50").Int()
	LogMaxAge        = kingpin.Flag("lma", "日志文件最大保存时间 Day").Envar("NPOC_LOG_MAX_AGE").Default("30").Int()
	LogMaxBackup     = kingpin.Flag("lmb", "日志文件最多保存数量").Envar("NPOC_LOG_MAX_BACKUP").Default("100").Int()
)

func Init() {
	kingpin.Parse()
}
