package sdk

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"strings"

	taskv1 "github.acme.red/intelli-sec/idl/gen/go/task/v1"
	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/pictor/foundation/slogext"
	rpcHttp "google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/anypb"
)

// 过滤一些常见错误
var blankErrorStr = []string{"too many requests timeout, stop scan", "err: context canceled"} //nolint:gochecknoglobals // 1

// CreateProtobufResult 创建 protobuf 结果，使用 JSON 中转方案处理 UTF-8 问题
// 这个方案可以自动处理所有嵌套结构中的无效 UTF-8 字符，包括复杂的 httpFlow 结构
func CreateProtobufResult(ctx context.Context, results []*taskv1.VulnTaskResult) (*anypb.Any, error) {
	vulnResults := &taskv1.VulnTaskResults{VulnTaskResult: results}

	// 方案1：直接尝试创建 protobuf，这是最快的路径
	pbResult, err := anypb.New(vulnResults)
	if err == nil {
		return pbResult, nil
	}

	// 方案2：如果直接创建失败（通常是因为 UTF-8 问题），使用 JSON 中转
	// JSON 序列化会自动处理 UTF-8 编码问题，因为 JSON 规范要求所有字符串都是有效的 UTF-8

	jsonData, jsonErr := json.Marshal(vulnResults)
	if jsonErr != nil {
		return nil, fmt.Errorf("json marshal failed: %w, original anypb error: %w", jsonErr, err)
	}
	slog.WarnContext(ctx, "anypb.New failed, trying JSON fallback", "error", err, "data", string(jsonData))

	var cleanResults taskv1.VulnTaskResults
	if unmarshalErr := json.Unmarshal(jsonData, &cleanResults); unmarshalErr != nil {
		return nil, fmt.Errorf("json unmarshal failed: %w, original anypb error: %w", unmarshalErr, err)
	}

	// 现在尝试用清理后的数据创建 protobuf
	cleanPbResult, cleanErr := anypb.New(&cleanResults)
	if cleanErr != nil {
		return nil, fmt.Errorf("anypb.New failed even after JSON cleanup: %w, original error: %w", cleanErr, err)
	}

	return cleanPbResult, nil
}

// 处理npoc的返回结果成协议规定的类型，并打印错误日志
func handleTaskResult(ctx context.Context, tr *npoc.TaskResult) []*taskv1.VulnTaskResult {
	if tr == nil {
		return nil
	}
	var warError error
	for _, pocError := range tr.PoCErrors {
		if isNotPrintError(pocError) {
			continue
		}
		warError = errors.Join(warError, pocError)
	}
	if warError != nil {
		slog.WarnContext(ctx, "scan exist error", slogext.Error(warError))
	}
	if len(tr.Vulnerabilities) == 0 {
		return nil
	}
	var results = make([]*taskv1.VulnTaskResult, len(tr.Vulnerabilities))
	for i, vulnerability := range tr.Vulnerabilities {
		results[i] = vulnInfoConversion(vulnerability)
	}
	return results
}

func isNotPrintError(err error) bool {
	for _, s := range blankErrorStr {
		if strings.Contains(err.Error(), s) {
			return true
		}
	}
	return false
}

// 漏洞信息转换
func vulnInfoConversion(vuln *npoc.Vulnerability) *taskv1.VulnTaskResult {
	var (
		httpFlow           []*taskv1.VulnRequestResponsePair
		lastRespBodyLength int
	)
	if vuln.HTTP != nil && len(vuln.HTTP.Follows) > 0 {
		httpFlow = httpFlowConversion(vuln.HTTP)
		if len(httpFlow) > 0 && httpFlow[len(httpFlow)-1].Response != nil {
			lastRespBodyLength = len(httpFlow[len(httpFlow)-1].Response.Body)
		}
	}
	var UrlNoQuery string // 用于后端对漏洞去重
	vUrl, err2 := url.Parse(vuln.URL)
	if err2 != nil {
		UrlNoQuery = vuln.URL
	} else {
		UrlNoQuery = fmt.Sprintf("%s://%s%s", vUrl.Scheme, vUrl.Host, vUrl.Path)
	}
	if vuln.Confidence == "" {
		vuln.Confidence = npoc.ConfidenceUnknown
	}
	d := taskv1.VulnTaskResult{
		Id:                     vuln.PoC,
		Name:                   vuln.Name,
		Category:               string(vuln.Category),
		Severity:               string(vuln.Severity),
		Param:                  vuln.Param,
		Payload:                vuln.Payload,
		Description:            vuln.Description,
		Url:                    vuln.URL,
		UrlNoQuery:             UrlNoQuery,
		HttpFlow:               httpFlow,
		Extra:                  vuln.Extra,
		Method:                 vuln.Method,
		LastResponseBodyLength: int32(lastRespBodyLength),
		Confidence:             string(vuln.Confidence),
		Variables:              make(map[string]string),
	}
	for key, value := range vuln.Extra { // 将提取器中带指定key的结果上报，可能后续任务需要用到（目前是poc任务结束后exp任务可能需要）
		if key == npoc.ExtraKeyDefaultExtra { // 对于没有key的提取器结果不进行上报
			continue
		}
		d.Variables[key] = value
	}
	if vuln.OOBDetails != nil {
		d.OobDetails = make([]*taskv1.OOBDetail, 0)
		for _, detail := range vuln.OOBDetails {
			if detail.Protocol != "" {
				d.OobDetails = append(d.OobDetails, &taskv1.OOBDetail{
					Timestamp:  detail.Timestamp.Unix(),
					RemoteAddr: detail.RemoteAddress,
					Protocol:   detail.Protocol,
					Id:         detail.FullId,
					Request:    detail.Request,
				})
			}
		}
	}
	return &d
}

// http请求和响应类型转换
func httpFlowConversion(vulnHTTP *npoc.VulnerabilityHTTP) []*taskv1.VulnRequestResponsePair {
	var (
		bpHTTPFlow = make([]*taskv1.VulnRequestResponsePair, len(vulnHTTP.Follows))
	)
	for i, follow := range vulnHTTP.Follows {
		var req *rpcHttp.HttpRequest
		if follow.Request != nil {
			if len(follow.Request.UnsafeRawHTTP) != 0 {
				req = rawRequestToRPCReq(string(follow.Request.UnsafeRawHTTP), follow.Request.URL)
			} else {
				var reqHeader []*rpcHttp.HttpHeader
				for k, values := range follow.Request.Header {
					for _, value := range values {
						reqHeader = append(reqHeader, &rpcHttp.HttpHeader{Key: k, Value: value})
					}
				}

				req = &rpcHttp.HttpRequest{
					Method:  follow.Request.Method,
					Uri:     follow.Request.URL.String(),
					Body:    follow.Request.Body,
					Headers: reqHeader,
				}
			}
		}
		var resp *rpcHttp.HttpResponse
		if follow.Response != nil {
			var respHeader []*rpcHttp.HttpHeader
			for k := range follow.Response.Header {
				respHeader = append(respHeader, &rpcHttp.HttpHeader{Key: k, Value: follow.Response.Header.Get(k)})
			}
			resp = &rpcHttp.HttpResponse{
				Status:  int32(follow.Response.Status),
				Reason:  http.StatusText(follow.Response.Status),
				Headers: respHeader,
				Body:    follow.Response.Body,
			}
		}
		bpHTTPFlow[i] = &taskv1.VulnRequestResponsePair{Request: req, Response: resp, Payload: follow.Payload}
	}
	return bpHTTPFlow
}

// 字符串转换为rpc的请求
func rawRequestToRPCReq(str string, u *url.URL) *rpcHttp.HttpRequest {
	var newlineChar = "\n"
	if strings.Contains(str, "\r\n") {
		newlineChar = "\r\n"
	}
	req := &rpcHttp.HttpRequest{
		Uri: fmt.Sprintf("%s://%s", u.Scheme, u.Host),
	}
	firstLine, afterLine, ok := strings.Cut(str, newlineChar)
	if !ok {
		return req
	}
	method, others, ok := strings.Cut(firstLine, " ")
	if ok {
		req.Method = strings.TrimSpace(method)
	} else {
		req.Method = "UNKNOWN"
	}
	if others != "" {
		var path string
		index := strings.Index(others, "HTTP/")
		if index != -1 {
			// Return the substring before "HTTP/"
			path = others[:index]
		} else {
			path = others
		}
		path = strings.TrimSpace(path)
		if len(path) > 0 && path[0] != '/' {
			path = "/" + path
		}
		req.Uri = req.Uri + path
	}
	// HTTP协议每行的结束符是\r\n。
	headerLine, body, ok := strings.Cut(afterLine, newlineChar+newlineChar)
	if !ok {
		return req
	}
	req.Body = []byte(body)
	headerLines := strings.Split(headerLine, newlineChar)
	for _, line := range headerLines {
		key, value, ok := strings.Cut(line, ":")
		if !ok {
			continue
		}
		req.Headers = append(req.Headers, &rpcHttp.HttpHeader{Key: strings.TrimSpace(key), Value: strings.TrimSpace(value)})
	}
	return req
}
