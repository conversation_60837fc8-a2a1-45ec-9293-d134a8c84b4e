package sdk

import (
	"cmp"
	"context"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"slices"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	taskv1 "github.acme.red/intelli-sec/idl/gen/go/task/v1"
	oobClient "github.acme.red/pictor/dnslog/pkg/client"
	"github.acme.red/pictor/foundation/slogext"
	"golang.org/x/sync/semaphore"
	httpproto "google.golang.org/genproto/googleapis/rpc/http"

	"github.acme.red/intelli-sec/npoc"
	"github.acme.red/intelli-sec/npoc/internal/config"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/pkg/nhttp"
	nsync "github.acme.red/intelli-sec/npoc/pkg/sync"
	"github.acme.red/intelli-sec/npoc/pocs"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

const (
	pocCacheSize  = 100
	requestThread = 20
	deadLineMin   = 40
	oobVulnMax    = 40
)

var engine Engine

type Engine struct {
	RdnsClient     *oobClient.Client
	HttpvClientOpt httpv.ClientOpt
}

type DetectOptions struct {
	OS     bool
	Lang   bool
	Server bool
	CMS    bool
}

func InitSdk() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second) // 加载
	defer cancel()
	if *config.OOBHost != "" && *config.OOBToken != "" && *config.OOBURL != "" {
		rdnsClient, err := httpv.NewRdnsClient(oobClient.Options{
			OobHost:   *config.OOBHost,
			ServerURL: *config.OOBURL,
			Token:     *config.OOBToken,
		}, *config.Proxy)
		if err != nil {
			slog.ErrorContext(ctx, "failed to new rdns client, oob is disabled", slogext.Error(err))
			panic(err) // rdnsClient创建失败不应该做任务，这里直接退出，容器会持续重启，重新尝试创建
		} else {
			engine.RdnsClient = rdnsClient
		}
	} else {
		slog.ErrorContext(ctx, "dnslog config is wrong, oob is disabled")
	}
}

func GetEngine() *Engine {
	return &engine
}

func SetRdnsClientWithEngine(rdnsClient *oobClient.Client) {
	engine.RdnsClient = rdnsClient
}

func RunPocTask(ctx context.Context, taskData *taskv1.PocScanTaskData) (results []*taskv1.VulnTaskResult, err error) {
	var (
		allPocs           []npoc.PoC
		pocIds            []string
		sendRequestNum    int64
		timeoutRequestNum int64
		startTime         = time.Now()
	)
	targetUrl, err := url.Parse(taskData.GetTarget())
	if err != nil {
		return nil, err
	}
	defer func() {
		duration := time.Since(startTime).Seconds()
		slog.InfoContext(ctx, "completed task", "result_num", len(results), "run_time", int(duration), "send_request_num", sendRequestNum, "timeout_request_num", timeoutRequestNum)
	}()

	for _, content := range taskData.Pocs {
		poc, err := pocs.LoadCustomPoc(ctx, content)
		if err != nil {
			slog.ErrorContext(ctx, "poc load failed", slogext.Error(err))
			continue
		}
		// 传入了完整poc的默认是需要扫描的
		if len(taskData.IncludeYamlIds) > 0 {
			taskData.IncludeYamlIds = append(taskData.IncludeYamlIds, poc.ID())
		}
		pocIds = append(pocIds, poc.ID())
		allPocs = append(allPocs, poc)
	}
	// 创建当前任务的缓存
	taskCache := nsync.NewRWMutexMap[string, any](pocCacheSize)
	if !taskData.DisableLoadPoc {
		sysPocs, ok := pocs.SysCache.Get(pocs.PocCacheKey)
		if !ok {
			slog.ErrorContext(ctx, "failed to load sysPocs", slogext.Error(err))
			return nil, errors.New("system poc cache get fail")
		}
		for _, poc := range sysPocs {
			if slices.Contains(pocIds, poc.ID()) { // 自定义poc和系统poc的id冲突时仅使用自定义poc进行扫描
				continue
			}
			allPocs = append(allPocs, poc)
		}
	}
	if len(allPocs) == 0 {
		slog.ErrorContext(ctx, "all pocs is empty")
		return nil, errors.New("all pocs is empty")
	}
	httpvClientOpt := httpv.ClientOpt{
		HostReqMaxParallel: *config.TaskReqParallel,
		Proxy:              cmp.Or(taskData.Proxy, *config.Proxy),
		HostErrMaxNum:      int(taskData.HostErrMaxNum),
		FailRetries:        int(taskData.FailRetries),
	}
	client, err := httpv.NewClient(ctx, httpvClientOpt)
	if err != nil {
		return nil, fmt.Errorf("failed to create httpv client,error: %w", err)
	}
	client.RdnsClient = engine.RdnsClient
	filterOpt := npoc.Option{
		IncludeYamlIds:        taskData.IncludeYamlIds,
		ExcludeYamlIds:        taskData.ExcludeYamlIds,
		IncludeYamlSeverities: taskData.IncludeYamlSeverities,
		FingerprintMode:       taskData.FingerprintMode,
		PocParallel:           cmp.Or(int64(*config.TaskReqParallel), 100),
		DisableLoadPoc:        taskData.DisableLoadPoc,
	}
	for _, fingerprint := range taskData.Fingerprints {
		filterOpt.FingerprintNames = append(filterOpt.FingerprintNames, fingerprint.Name)
	}
	taskPocs, err := npoc.New(allPocs, filterOpt)
	if err != nil {
		return nil, fmt.Errorf("poc scan new npoc failed, error: %w", err)
	}
	tr, err2 := taskPocs.DoTask(ctx, &npoc.HTTPTask{
		Request: &httpv.Request{
			Method: "GET",
			URL:    targetUrl,
			Header: http.Header{
				"User-Agent": []string{nhttp.GetRandomUserAgent()},
			},
		},
		Env:       taskData.Variables,
		TaskCache: taskCache,
		Client:    client,
	})
	if err2 != nil {
		slog.WarnContext(ctx, "poc scan do task have errors", slogext.Error(err2))
	}
	exceeded, count := client.FailExceededLimit()
	if exceeded {
		slog.WarnContext(ctx, "poc scan is skipped,too many timeout requests", "num", count)
		err = errors.Join(fmt.Errorf("poc scan is skipped, the number of timeout request is %d, exceeds the limit of %d", count, client.HostErrMaxNum))
	}
	timeoutRequestNum = int64(count)
	sendRequestNum = client.RequestCounter.Load()
	results = handleTaskResult(ctx, tr)
	for _, taskResult := range results {
		taskResult.Target = taskData.Target
	}
	return results, err
}

func RunGenVulnTask(ctx context.Context, taskData *taskv1.GenVulnScanTaskData) (results []*taskv1.VulnTaskResult, err error) {
	var (
		sendRequestNum    int64
		timeoutRequestNum int64
		startTime         = time.Now()
	)
	defer func() {
		duration := time.Since(startTime).Seconds()
		slog.InfoContext(ctx, "completed task", "result_num", len(results), "run_time", int(duration), "send_request_num", sendRequestNum, "timeout_request_num", timeoutRequestNum)
	}()
	// 创建当前任务的缓存
	taskCache := nsync.NewRWMutexMap[string, any](pocCacheSize)

	httpvClientOpt := httpv.ClientOpt{
		HostReqMaxParallel: *config.TaskReqParallel,
		Proxy:              cmp.Or(taskData.Proxy, *config.Proxy),
		HostErrMaxNum:      int(taskData.HostErrMaxNum),
		FailRetries:        int(taskData.FailRetries),
	}
	client, err := httpv.NewClient(ctx, httpvClientOpt)
	if err != nil {
		return nil, fmt.Errorf("gen vuln scan failed to create httpv client,error: %w", err)
	}
	client.RdnsClient = engine.RdnsClient

	filterOpt := npoc.Option{
		IncludeCommonIds: taskData.IncludeCommonIds,
		PocParallel:      npoc.DefaultGenVulnPocParallel,
	}
	taskPocs, err := npoc.New(pocs.GenVulnPocs, filterOpt)
	if err != nil {
		return nil, fmt.Errorf("gen vuln scan new npoc failed, error: %w", err)
	}
	exceeded, count := client.FailExceededLimit()
	if exceeded {
		slog.WarnContext(ctx, "gen vuln scan is skipped,too many timeout requests", "num", count)
		err = errors.Join(fmt.Errorf("gen vuln scan is skipped, the number of timeout request is %d, exceeds the limit of %d", count, client.HostErrMaxNum))
	}
	timeoutRequestNum = int64(count)
	detectOpts := newDetectOptions(taskData.IncludeCommonIds)
	results = runWithRequests(ctx, client, taskPocs, taskCache, taskData, detectOpts)
	sendRequestNum = client.RequestCounter.Load()
	for _, taskResult := range results {
		taskResult.Target = taskData.Target
	}
	return results, err
}

func runWithRequests(ctx context.Context, client *httpv.Client, pocs *npoc.NPoC, taskCache *nsync.RWMutexMap[string, any], taskData *taskv1.GenVulnScanTaskData, detectOpts DetectOptions) []*taskv1.VulnTaskResult {
	var (
		wg               sync.WaitGroup
		vulns            []*taskv1.VulnTaskResult
		vulnsMu          sync.Mutex
		onceCancelLog    sync.Once
		fingerprintInfos []httpv.FingerprintInfo
	)
	requests := taskData.GetRequests()
	pools, requestPools := initializeSemaphores(len(requests), requestThread)

	httpFlows, languageCounts, serverCounts, validPathFlowAtomic := processInitialRequests(
		ctx, client, requests, requestPools, detectOpts.Lang, detectOpts.Server,
	)

	cmsCounts := detectCMSIfNeeded(ctx, detectOpts.CMS, httpFlows, languageCounts)

	os := detectOSIfNeeded(ctx, client, detectOpts.OS, languageCounts, serverCounts, validPathFlowAtomic)
	fingerprintInfos = aggregateFingerprints(languageCounts, serverCounts, os, cmsCounts)

	// 重置 wg 用于后续的 POC 检测
	wg = sync.WaitGroup{}
	scannedRequestCache := nsync.NewRWMutexMap[string, bool](pocCacheSize)
	for _, httpFlow := range httpFlows {
		select {
		case <-ctx.Done():
			return vulns
		default:
		}
		err := pools.Acquire(ctx, 1)
		if err != nil {
			continue
		}
		wg.Add(1)
		go func(httpFlow npoc.HTTPFollow) {
			defer utils.RecoverFun(ctx)
			defer wg.Done()
			defer pools.Release(1)
			select {
			case <-ctx.Done():
				return
			default:
			}
			httpvReq := httpFlow.Request
			httpvResp := httpFlow.Response
			if exceeded, _ := client.FailExceededLimit(); exceeded {
				return
			}
			httpvReq.ParseParam()
			if len(httpvReq.Params()) > 50 { //nolint:mnd // 正常请求一般参数不会这么多，太多的参数会导致发包量非常大，长时间无法完成影响其他正常请求的扫描，这里对这种请求不做扫描
				slog.WarnContext(ctx, "too many request params, scan skipped", "param_num", len(httpvReq.Params()))
				return
			}
			// 判断该请求之前是否已经扫描过
			_, scanned := scannedRequestCache.LoadOrStore(getReqOnlyKey(httpvReq), true)
			if scanned {
				return
			}
			task := &npoc.HTTPTask{
				Request:  httpvReq,
				Client:   client,
				Response: httpvResp,
				WebLoginBruteDict: &npoc.WebLoginBruteDict{
					UseDefaultDict:  taskData.WebLoginBruteDict.UseDefaultDict,
					CustomUsers:     taskData.WebLoginBruteDict.CustomUsers,
					CustomPasswords: taskData.WebLoginBruteDict.CustomPasswords,
					UserAndPassword: taskData.WebLoginBruteDict.UserAndPassword,
				},
				TaskCache:   taskCache,
				FullScan:    true,
				FingerPrint: fingerprintInfos,
			}
			tr, err2 := pocs.DoTask(ctx, task)
			if err2 != nil {
				if errors.Is(err2, context.Canceled) {
					onceCancelLog.Do(func() {
						slog.WarnContext(ctx, "npoc do task skipped", slogext.Error(err2))
					})
				} else {
					slog.WarnContext(ctx, "npoc do task have errors", slogext.Error(err2))
				}
			}
			vulnInfo := handleTaskResult(ctx, tr)
			if vulnInfo != nil {
				vulnsMu.Lock()
				vulns = append(vulns, vulnInfo...)
				vulnsMu.Unlock()
			}
		}(httpFlow)
	}
	wg.Wait()
	return vulns
}

func getRawResponse(ctx context.Context, request *httpv.Request, client *httpv.Client) (*httpv.Response, error) {
	var (
		resp *httpv.Response
		err  error
	)

	// 与爬虫一致，原始请求重放的时候禁止重定向
	req := request.Clone()
	req.FollowRedirects = false

	for range 3 {
		resp, err = client.Do(ctx, req)
		if err == nil && resp != nil {
			break
		}
	}

	if resp == nil {
		return nil, fmt.Errorf("npoc task fail,response get fail error: %w", err)
	}
	return resp, nil
}

// 为请求生成一个用于判断重复的唯一值，由请求(url、header、body)中的参数key组成.
func getReqOnlyKey(req *httpv.Request) string {
	onlyKeySlice := []string{req.URL.Path}
	for _, param := range req.Params() {
		onlyKeySlice = append(onlyKeySlice, param.Key)
	}
	for key := range req.Header {
		onlyKeySlice = append(onlyKeySlice, key)
	}
	// 参数是无序的，这里进行一下统一排序保证生成的字符串的一致性
	sort.Strings(onlyKeySlice)
	return strings.Join(onlyKeySlice, "")
}

// initializeSemaphores 初始化用于并发控制的信号量
func initializeSemaphores(numRequests, threadLimit int) (*semaphore.Weighted, *semaphore.Weighted) {
	var pools, requestPools *semaphore.Weighted
	if numRequests < threadLimit {
		pools = semaphore.NewWeighted(int64(numRequests))
		requestPools = semaphore.NewWeighted(int64(numRequests))
	} else {
		pools = semaphore.NewWeighted(int64(threadLimit))
		requestPools = semaphore.NewWeighted(int64(threadLimit))
	}
	return pools, requestPools
}

// processInitialRequests 处理初始HTTP请求，进行语言和服务探测
func processInitialRequests(
	ctx context.Context,
	client *httpv.Client,
	requests []*httpproto.HttpRequest,
	requestPools *semaphore.Weighted,
	needDetectLang, needDetectServer bool,
) ([]npoc.HTTPFollow, map[string]int, map[string]int, *atomic.Value) {
	var (
		wg             sync.WaitGroup
		httpFlows      []npoc.HTTPFollow
		httpFlowsMu    sync.Mutex
		languageCounts = make(map[string]int)
		languageMu     sync.Mutex
		serverCounts   = make(map[string]int)
		serverMu       sync.Mutex
		validPathFlow  atomic.Value
	)

	for _, request := range requests {
		select {
		case <-ctx.Done():
			break
		default:
		}
		if err := requestPools.Acquire(ctx, 1); err != nil {
			slog.WarnContext(ctx, "Failed to acquire request pool semaphore", slogext.Error(err))
			continue
		}
		wg.Add(1)
		go func(request *httpproto.HttpRequest) {
			defer utils.RecoverFun(ctx)
			defer wg.Done()
			defer requestPools.Release(1)

			select {
			case <-ctx.Done():
				return
			default:
			}

			reqURL, err := url.Parse(request.Uri)
			if err != nil {
				slog.ErrorContext(ctx, "url parse failed", "url", request.Uri, slogext.Error(err))
				return
			}
			headers := make(http.Header)
			for _, header := range request.Headers {
				headers.Set(header.Key, header.Value)
			}
			httpvReq := &httpv.Request{
				URL:             reqURL,
				Method:          request.Method,
				Header:          headers,
				Body:            request.Body,
				FollowRedirects: true,
			}
			httpvResp, err := getRawResponse(ctx, httpvReq, client)
			if err != nil {
				slog.WarnContext(ctx, "get response failed", "url", httpvReq.URL.String(), slogext.Error(err))
				return
			}
			flow := npoc.HTTPFollow{Request: httpvReq, Response: httpvResp}

			if reqURL.Path != "" && reqURL.Path != "/" {
				if validPathFlow.Load() == nil {
					validPathFlow.Store(&flow)
				}
			}

			lang := detector.LangUnknown
			if needDetectLang {
				detectedLang := detector.DetectServerLanguage(httpvReq, httpvResp)
				if detectedLang != detector.LangUnknown {
					languageMu.Lock()
					languageCounts[detectedLang]++
					languageMu.Unlock()
					lang = detectedLang
				}
			}
			if needDetectServer {
				detectedServer := detector.DetectWebServer(httpvResp, lang)
				if detectedServer != detector.ServerUnknown {
					serverMu.Lock()
					serverCounts[detectedServer]++
					serverMu.Unlock()
				}
			}
			httpFlowsMu.Lock()
			httpFlows = append(httpFlows, flow)
			httpFlowsMu.Unlock()
		}(request)
	}
	wg.Wait()
	return httpFlows, languageCounts, serverCounts, &validPathFlow
}

// detectCMSIfNeeded 根据收集到的信息探测框架/cms
func detectCMSIfNeeded(
	ctx context.Context,
	needDetectCMS bool,
	httpFlows []npoc.HTTPFollow,
	languageCounts map[string]int,
) []string {
	if !needDetectCMS {
		return []string{detector.FrameworkUnknown}
	}

	mostCommonLang := getMostCommon(languageCounts)
	var (
		cmsSet sync.Map
		wg     sync.WaitGroup
	)

	for _, httpFlow := range httpFlows {
		select {
		case <-ctx.Done():
			break
		default:
		}

		wg.Add(1)
		go func(hf npoc.HTTPFollow) {
			defer utils.RecoverFun(ctx)
			defer wg.Done()

			select {
			case <-ctx.Done():
				return
			default:
			}

			frameworks := detector.DetectFramework(hf.Request, hf.Response, mostCommonLang)
			for _, framework := range frameworks {
				if framework != detector.FrameworkUnknown {
					cmsSet.Store(framework, true)
				}
			}
		}(httpFlow)
	}
	wg.Wait()

	var result []string
	cmsSet.Range(func(key, value interface{}) bool {
		if keyStr, ok := key.(string); ok {
			result = append(result, keyStr)
		}
		return true
	})

	if len(result) == 0 {
		return []string{detector.FrameworkUnknown}
	}

	return result
}

// detectOSIfNeeded 根据收集到的信息探测操作系统
func detectOSIfNeeded(
	ctx context.Context,
	client *httpv.Client,
	needDetectOS bool,
	languageCounts map[string]int,
	serverCounts map[string]int,
	validPathFlowAtomic *atomic.Value,
) string {
	if !needDetectOS {
		return detector.OSUnknown
	}

	mostCommonLang := getMostCommon(languageCounts)
	mostCommonServer := getMostCommon(serverCounts)

	loadedFlow := validPathFlowAtomic.Load()
	flow, ok := loadedFlow.(*npoc.HTTPFollow)
	if !ok || flow == nil {
		slog.InfoContext(ctx, "no valid path flow available for OS detection")
		return detector.OSUnknown
	}

	return detector.DetectOperatingSystem(ctx, flow.Request, flow.Response, client, mostCommonLang, mostCommonServer)
}

func getMostCommon(Counts map[string]int) string {
	var mostCommon string
	var maxCount int

	for lang, count := range Counts {
		if count > maxCount {
			maxCount = count
			mostCommon = lang
		}
	}
	return mostCommon
}

// aggregateFingerprints 聚合所有探测到的指纹信息
func aggregateFingerprints(languageCounts map[string]int, serverCounts map[string]int, os string, cmss []string) []httpv.FingerprintInfo {
	var fingerprintInfos []httpv.FingerprintInfo
	for langKey := range languageCounts {
		fingerprintInfos = append(fingerprintInfos, httpv.FingerprintInfo{Name: langKey})
	}
	for serverKey := range serverCounts {
		fingerprintInfos = append(fingerprintInfos, httpv.FingerprintInfo{Name: serverKey})
	}
	if os != detector.OSUnknown {
		fingerprintInfos = append(fingerprintInfos, httpv.FingerprintInfo{Name: os})
	}
	for _, cms := range cmss {
		fingerprintInfos = append(fingerprintInfos, httpv.FingerprintInfo{Name: cms})
	}
	return fingerprintInfos
}

func newDetectOptions(includeCommonIds []string) DetectOptions {
	return DetectOptions{
		Lang:   utils.ContainsAll(includeCommonIds, pocs.NeedDetectLanguage),
		Server: utils.ContainsAll(includeCommonIds, pocs.NeedDetectServer),
		OS:     utils.ContainsAll(includeCommonIds, pocs.NeedDetectOS),
		CMS:    utils.ContainsAll(includeCommonIds, pocs.NeedDetectCMS),
	}
}
