package cache

import (
	"bufio"
	"os"
	"path"
	"strings"
	"time"

	"github.com/hashicorp/golang-lru/v2/expirable"
)

const (
	ClearTime = time.Hour * 1
)

var DictDir = ""

var dictCache = expirable.NewLRU[string, []string](0, nil, ClearTime) //nolint:gochecknoglobals // 1

func LoadDict(fileName string) ([]string, error) {
	absAdd := path.Join(DictDir, fileName)
	var lines []string
	file, err := os.Open(absAdd)
	if err != nil {
		return nil, err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		line = strings.TrimSpace(line)
		lines = append(lines, line)
	}
	if err = scanner.Err(); err != nil {
		return nil, err
	}
	dictCache.Add(fileName, lines)
	return lines, nil
}
