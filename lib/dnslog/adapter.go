package dnslog

import (
	"errors"
	"slices"

	oobClient "github.acme.red/pictor/dnslog/pkg/client"
)

// OOBURLTracker track the usage of OOB URLs and release unused OOB URL on demand.
type OOBURLTracker struct {
	oobURLs     map[string]*oobClient.URL // unique id => url object
	oobURLUsage map[string]bool           // unique id => url used
	client      *oobClient.Client
}

func NewOOBURLTracker(client *oobClient.Client) (*OOBURLTracker, error) {
	if client == nil {
		return nil, errors.New("oob client is nil")
	}
	tracker := &OOBURLTracker{
		oobURLs:     make(map[string]*oobClient.URL),
		oobURLUsage: make(map[string]bool),
		client:      client,
	}
	return tracker, nil
}

// Track a OOB URL
func (t *OOBURLTracker) Track(u *oobClient.URL) {
	if u == nil {
		return
	}
	t.oobURLs[GetID(u)] = u
	t.oobURLUsage[GetID(u)] = false
}

// Mark a OOB URL as used
func (t *OOBURLTracker) Mark(u *oobClient.URL) {
	if u == nil {
		return
	}
	id := GetID(u)
	if _, ok := t.oobURLs[id]; !ok {
		t.oobURLs[id] = u
	}
	t.oobURLUsage[id] = true
}

// Clean unused OOB URLs
func (t *OOBURLTracker) Clean() {
	for id, used := range t.oobURLUsage {
		u, ok := t.oobURLs[id]
		if !ok {
			continue
		}
		if u != nil && !used {
			t.client.RemoveURL(u)
		}
	}
}

// CreateURLAdapter combine HTTP and DNS type.
func CreateURLAdapter(client *oobClient.Client, oobTypes ...string) *oobClient.URL {
	if len(oobTypes) > 0 && oobTypes[0] == oobClient.LogTypeHTTP && !slices.Contains(oobTypes, oobClient.LogTypeDNS) {
		oobTypes = append(oobTypes, oobClient.LogTypeDNS)
	}
	return client.CreateURL("", oobTypes...)
}

// CreateURLAdapterWithCustomData 创建DNS的URL结构体并插入自定义数据.
func CreateURLAdapterWithCustomData(custom string, client *oobClient.Client, oobTypes ...string) *oobClient.URL {
	if len(oobTypes) > 0 && oobTypes[0] == oobClient.LogTypeHTTP && !slices.Contains(oobTypes, oobClient.LogTypeDNS) {
		oobTypes = append(oobTypes, oobClient.LogTypeDNS)
	}
	return client.CreateURL(custom, oobTypes...)
}

// GetID return a distinct ID of oob url
func GetID(u *oobClient.URL) string {
	return u.OOBType + "_" + u.UniqueID
}
