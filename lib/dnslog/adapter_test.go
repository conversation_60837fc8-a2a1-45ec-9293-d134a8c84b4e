package dnslog

import (
	"github.acme.red/pictor/dnslog/pkg/client"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestOOBTracker(t *testing.T) {
	c, err := client.New(&client.Options{
		ServerURL: "http://dnsx.cc:8080",
		OobHost:   "dnsx.cc",
		Token:     "wzPkZlQcthJQv2IyLW7eXCAfAOQteb0E",
	})
	require.NoError(t, err)
	tracker, err := NewOOBURLTracker(c)
	require.NoError(t, err)
	u1 := c.CreateURL("", client.LogTypeDNS)
	u2 := c.CreateURL("", client.LogTypeDNS)
	require.Len(t, c.URLs(), 2)
	tracker.Track(u1)
	tracker.Track(u2)
	tracker.Mark(u1)
	tracker.Clean()
	require.Len(t, c.URLs(), 1)
	require.Equal(t, u1, c.URLs()[0])
}
