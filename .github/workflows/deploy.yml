name: Deploy
run-name: ${{ inputs.action || 'update' }} to ${{ inputs.environment || 'asm_dev' }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run '
        type: choice
        required: true
        options:
        - asm_dev
        - asm_saas
        - pocscan_dev
        - pocscan_prod
      action:
        description: 'action to run'
        type: choice
        required: true
        options:
          - deploy
          - update
  push:
    branches:
      - main

jobs:
  deploy:
    env:
      ENVIRONMENT: ${{ inputs.environment || 'asm_dev' }}
    runs-on: self-hosted
    concurrency:
      group: deploy-${{ inputs.environment || 'asm_dev' }}  # 给定组 ID，保证同一分支的同时运行数量限制
      cancel-in-progress: true  # 如果有新的实例被触发，取消当前正在运行的实例
    environment: ${{ inputs.environment || 'asm_dev' }}
    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: "0"

      - name: Install Task
        uses: arduino/setup-task@v2
        with:
          repo-token: ${{ secrets.PUBLIC_GITHUB_TOKEN }}
      
      - name: Install go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24'
          cache: false
          token: ${{ secrets.PUBLIC_GITHUB_TOKEN }}
      
      - name: Build Binary
        run: task build-agent

      - name: Build images
        if: ${{ inputs.action == 'deploy' }}
        env:
          DOCKER_TAG_PREFIX: ${{ vars.REGISTRY }}/${{ vars.REGISTRY_PROJECT }}
        run: |
          task build-image

      - name: Login docker registry
        if: ${{ inputs.action == 'deploy' }}
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.REGISTRY }}
          username: ${{ vars.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Push images
        if: ${{ inputs.action == 'deploy' }}
        env:
          DOCKER_TAG_PREFIX: ${{ vars.REGISTRY }}/${{ vars.REGISTRY_PROJECT }}
        run: |
          task push
          docker system prune -a -f --filter until=48h
      - name: Copy npoc binary
        run: |
          cp ./cmd/npoc_agent/output/npoc-agent ansible/files/npoc-agent
          chmod +x ansible/files/npoc-agent
          chmod +x ansible/files/binary-tools/*
      - name: Fix SSH key permissions
        run: |
          # 确保私钥文件只有所有者可读
          chmod 600 ansible/files/profiles/${{ env.ENVIRONMENT }}/id_ed25519
      - name: Deploy npoc
        run: |
          docker run --user $(id -u):$(id -g) --rm -v ./ansible:/work containers.github.acme.red/pictor/ansible:latest ansible-playbook --private-key files/profiles/${{ env.ENVIRONMENT }}/id_ed25519 -i files/profiles/${{ env.ENVIRONMENT }}/inventory.yml ${{ inputs.action || 'update' }}.yml
