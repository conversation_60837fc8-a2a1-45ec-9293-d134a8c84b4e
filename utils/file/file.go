package file

import (
	"bufio"
	"os"
	"path/filepath"
	"strings"
)

// GetYamlFilesInDir  获取指定目录下所有子目录中的yaml或yml后缀的文件
func GetYamlFilesInDir(root string) ([]string, error) {
	var files []string

	// 遍历目录
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 检查是否为文件且后缀为.yaml或.yml
		if !info.IsDir() && (strings.HasSuffix(strings.ToLower(info.Name()), ".yaml") || strings.HasSuffix(strings.ToLower(info.Name()), ".yml")) {
			files = append(files, path)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return files, nil
}

func ReadFileLines(filename string) ([]string, error) {
	f, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	lines := make([]string, 0)
	buf := bufio.NewScanner(f)
	for buf.Scan() {
		if buf.Text() == "" {
			continue
		}
		lines = append(lines, strings.TrimSpace(buf.Text()))
	}
	if err := buf.Err(); err != nil {
		return nil, err
	}

	return lines, err
}
