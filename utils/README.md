# Utils 工具库文档

本目录包含了 NPOC 项目中各种通用工具函数和模块，为安全扫描提供了基础功能支持。

## 📁 目录结构

### 🔍 检测器模块 (detector/)
负责自动识别和检测目标系统的技术栈信息。

#### language_detector.go - 语言检测
- **功能**: 自动检测 Web 应用使用的后端编程语言
- **支持语言**: PHP, Python, Java, Perl, ASP, Ruby, Node.js, Go, .NET Core, ColdFusion, Scala
- **检测方式**: 
  - URL 路径扩展名分析
  - HTTP 头部特征识别  
  - Cookie 模式匹配
  - 响应体内容模式识别（错误信息、框架特征等）
- **主要函数**: `DetectServerLanguage(req, resp) string`

#### cms_detector.go - 框架/CMS检测
- **功能**: 识别 Web 框架和内容管理系统
- **支持框架**: Laravel, Symfony, CodeIgniter, Djan<PERSON>, Flask, Spring, <PERSON>ru<PERSON>, Word<PERSON>ress, <PERSON><PERSON><PERSON>, Joomla 等 50+ 种
- **检测特征**: 
  - 响应体模式匹配
  - HTTP 头部分析
  - Cookie 识别
  - URL 路径特征
- **主要函数**: `DetectFramework(req, resp, language) []string`

#### server_detector.go - Web服务器检测
- **功能**: 识别 Web 服务器软件类型
- **支持服务器**: IIS, Apache, Nginx 及其衍生版本
- **检测方式**: Server 头分析、语言联动推理、特定头部组合
- **主要函数**: `DetectWebServer(resp, language) string`

#### os_detector.go - 操作系统检测
- **功能**: 推测目标服务器的操作系统
- **支持系统**: Windows, Linux, Unix 系列
- **检测方式**: 
  - 语言特征联动（ASP.NET → Windows）
  - HTTP 头部分析
  - 路径大小写敏感性测试
  - URL 路径风格分析
- **主要函数**: `DetectOperatingSystem(ctx, req, resp, client, language, server) string`

### 📄 HTML/DOM 处理模块

#### html.go - HTML文档解析
- **功能**: 提供丰富的 HTML 文档解析和操作功能
- **核心特性**:
  - **缓存机制**: 基于 LRU 缓存的 goquery 文档解析，提升性能
  - **jQuery 风格选择器**: 支持 CSS 选择器进行 DOM 元素选择
  - **内容提取**: 提取 title、script、meta 等标签内容
  - **属性操作**: 获取和匹配 DOM 元素属性
  - **正则匹配**: 对 DOM 文本内容进行正则表达式匹配
  - **内容清理**: 去除时间戳、UUID、注释等干扰数据
- **主要函数**:
  - `ParseTitle(body) string` - 解析页面标题
  - `ExtractAllScript(body) [][]byte` - 提取所有 script 标签内容
  - `ExistQuerySelectorAll(body, query) bool` - 检查选择器是否匹配元素
  - `DomText(body, query) []byte` - 获取选择器匹配元素的文本内容
  - `DomAttr(body, attrName, query) []byte` - 获取选择器匹配元素的属性值
  - `ClearContent(link, ua, content) []byte` - 清理响应内容中的干扰数据

### 🌐 HTTP 处理模块

#### http.go - HTTP请求处理
- **功能**: HTTP 请求和响应的处理工具
- **核心特性**:
  - **请求体类型检测**: 自动识别 JSON、Form、其他类型的请求体
  - **超时错误判断**: 识别网络超时相关的错误
- **主要函数**:
  - `DetectRequestBodyType(body, header) string` - 检测请求体类型
  - `IsTimeoutError(err) bool` - 判断是否为超时错误

### 📊 相似度计算模块 (similarity/)

#### similarity.go - 响应相似度计算
- **功能**: 计算两个 HTTP 响应的相似度，用于页面比较和变化检测
- **应用场景**: 
  - 目录扫描中判断页面是否为 404 页面
  - SQL 注入中判断响应变化
  - 反射型 XSS 检测中的页面对比
- **计算维度**:
  - **状态码比较**: 检查 HTTP 状态码是否一致
  - **Content-Type 检查**: 确保响应类型相同
  - **头部相似度**: 分析 HTTP 头部的相似性
  - **内容相似度**: 根据内容类型选择不同算法
    - HTML: 使用专门的 HTML 结构相似度算法
    - JSON: 使用 JSON 结构比较算法  
    - 纯文本: 使用文本相似度算法
- **主要函数**:
  - `SimiParam.Compute() int` - 计算相似度百分比
  - `RespsIsLike(resps, allowCodeDif, remove, simiMin) bool` - 批量响应相似性检查

### 📝 文本处理模块 (text/)

#### json.go - JSON安全处理
- **功能**: 将字符串安全地转换为 JSON 格式
- **应用场景**: 防止 JSON 注入攻击，确保数据安全传输
- **主要函数**: `ToJSONSafeString(input) string` - 安全的 JSON 字符串编码

### 🔧 通用工具模块

#### common.go - 通用函数
- **功能**: 提供异常恢复处理
- **主要函数**: `RecoverFun(ctx)` - 统一的 panic 恢复处理

#### file.go - 文件类型判断
- **功能**: 判断文件扩展名是否为动态文件类型
- **支持类型**: php, jsp, asp, cgi, py, rb 等动态脚本文件
- **主要函数**: `IsDynamicFileExt(ext) bool`

#### rand.go, regex.go, slice.go, version.go
- **功能**: 提供随机数生成、正则表达式处理、切片操作、版本比较等基础工具

### 🎯 其他专用模块

#### guess/ - 推测模块
- **功能**: 基于已知信息进行智能推测

#### template/ - 模板处理
- **功能**: 处理 payload 模板相关功能

#### oobutils/ - 带外检测工具
- **功能**: 支持带外（Out-of-Band）漏洞检测的工具函数

#### file/ - 文件操作增强
- **功能**: 扩展的文件操作功能

## 🚀 使用示例

### 检测目标技术栈
```go
// 检测编程语言
language := detector.DetectServerLanguage(request, response)

// 检测 Web 框架
frameworks := detector.DetectFramework(request, response, language)

// 检测 Web 服务器
server := detector.DetectWebServer(response, language)

// 检测操作系统
os := detector.DetectOperatingSystem(ctx, request, response, client, language, server)
```

### HTML 内容处理
```go
// 解析页面标题
title := utils.ParseTitle(responseBody)

// 检查页面是否包含特定元素
hasLogin := utils.ExistQuerySelectorAll(responseBody, "form[action*='login']")

// 提取所有链接
links := utils.DomAttr(responseBody, "href", "a")
```

### 相似度计算
```go
param := similarity.SimiParam{
    Resp1:        response1,
    Resp2:        response2,
    AllowCodeDif: false,
}
similarity, err := param.Compute()
```

## 📋 注意事项

1. **性能优化**: HTML 解析模块使用了 LRU 缓存，重复解析相同内容时性能更佳
2. **编码处理**: 所有文本处理都考虑了字符编码转换，支持多语言内容
3. **错误处理**: 各模块都提供了完善的错误处理机制
4. **扩展性**: 检测器模块采用规则驱动的设计，易于添加新的检测规则

## 🔄 模块依赖

- `detector/` 模块之间存在联动关系，语言检测结果会影响框架、服务器、操作系统的检测准确性
- `similarity/` 模块依赖 `html/`, `json/`, `text/` 子模块提供具体的相似度计算算法
- 多个模块共享 `httpv` 包进行 HTTP 请求和响应处理