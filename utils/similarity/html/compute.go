package html

import (
	"bytes"
	"strings"

	"golang.org/x/net/html"

	"github.acme.red/intelli-sec/npoc/utils/similarity/text"
)

type Assorted struct {
	Title    string
	HeadTag  []string
	OtherTag []string
	TagText  []string
}

func ComputeSim(as1, as2 *Assorted) int {
	headTagSim := text.Compute(as1.HeadTag, as2.HeadTag)
	if headTagSim != 1 {
		return int(headTagSim * 100)
	}
	otherTagSim := text.Compute(as1.OtherTag, as2.OtherTag)
	TagTextSim := text.Compute(as1.TagText, as2.TagText)

	return int(otherTagSim*40 + TagTextSim*60)
}

func GetAssorted(data, dataToRemove []byte) *Assorted {
	if dataToRemove != nil {
		data = bytes.ReplaceAll(data, dataToRemove, []byte{})
	}
	tokenizer := html.NewTokenizer(bytes.NewReader(data))
	result := Assorted{Title: "", HeadTag: []string{}, OtherTag: []string{}, TagText: []string{}}
	var (
		inTitle bool // 进入title标签，将text单独存一份到结构体中
		inStyle bool // 进入style标签，该标签不需要记录到标签slice中，其中的text也不需要记录
		inHead  bool // 进入head标签，该标签下的标签单独存，并且不存text
	)

	for n := tokenizer.Next(); n != html.ErrorToken; n = tokenizer.Next() {
		// 标签类型，text: 非标签内的字符串，self: 自闭和标签，start: 存在单独的开始和结束的标签
		// 标签由三部分组成   标签类型:标签名:id:xxxx:name:xxx
		tagDemo := ""
		token := tokenizer.Token()
		switch n {
		case html.SelfClosingTagToken:
			tagDemo = "self"
		case html.StartTagToken:
			if token.Data == "style" {
				inStyle = true
				continue
			}
			if token.Data == "head" {
				inHead = true
				continue
			}
			if token.Data == "title" {
				inTitle = true
			}
			tagDemo = "start"
		case html.EndTagToken:
			if token.Data == "style" {
				inStyle = false
			}
			if token.Data == "head" {
				inHead = false
			}
			continue
		case html.TextToken:
			if inStyle {
				continue
			}
			if inTitle {
				result.Title = strings.TrimSpace(token.Data)
				inTitle = false
			}
			tagDemo = "text"
		default:
			continue
		}

		tData := strings.TrimSpace(token.Data)
		if n == html.TextToken && tData != "" && !inHead {
			// 将字符串按空格切割后作为单词存储
			result.TagText = append(result.TagText, strings.Fields(tData)...)
		}

		if n != html.TextToken || tData != "" {
			tagDemo += ":" + token.DataAtom.String()
			// 对标签的id和name属性做记录，当这两个属性一样的标签代表相同标签
			for _, attr := range token.Attr {
				tagDemo += " " + attr.Key
				switch attr.Key {
				case "id":
					tagDemo += ":" + attr.Val
				case "name":
					tagDemo += ":" + attr.Val
				}
			}
			if inHead {
				result.HeadTag = append(result.HeadTag, tagDemo)
			} else {
				result.OtherTag = append(result.OtherTag, tagDemo)
			}
		}
	}
	return &result
}
