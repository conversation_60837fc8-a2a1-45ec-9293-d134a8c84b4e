package similarity

import (
	"bytes"
	"fmt"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/similarity/html"
	"github.acme.red/intelli-sec/npoc/utils/similarity/json"
	"github.acme.red/intelli-sec/npoc/utils/similarity/text"
)

type SimiParam struct {
	Resp1         *httpv.Response
	Resp2         *httpv.Response
	Remove1       []byte // 避免一些特殊字符的干扰，需要从resp1中剔除的字符
	Remove2       []byte
	AllowCodeDif  bool // 允许状态码不一样
	AllowJsonComp bool // 对于json的body使用json对应的相似度计算方式(暂时只有目录扫描采用该方式)
	OnlyBody      bool
}

// Compute todo 对于一些网页内容比较短的页面使用该方式是否合理，如果比较短的网页中出现了一些类似于md5/时间戳
// 这种长度一样但是值不一样的计算出来的相似度可能就会相差很大，实际上是相似的页面
func (sp *SimiParam) Compute() (int, error) {
	if !sp.AllowCodeDif {
		if sp.Resp1.Status != sp.Resp2.Status {
			// slog.Info("状态码不同，相似度为0")
			return 0, nil
		}
	}
	content1, content2 := sp.Resp1.Header.Get("Content-Type"), sp.Resp2.Header.Get("Content-Type")
	if content1 != content2 {
		return 0, nil
	}
	// 图片、二进制文件相关，无法计算相似度相关
	if strings.Contains(content1, "image") || strings.Contains(content1, "octet-stream") {
		// slog.Info("content-type 是 image or octet-stream,不支持计算相似度")
		return 0, nil
	}
	headerSim := sp.comHeaderSim()
	if CodeIs3xx(sp.Resp1.Status) && CodeIs3xx(sp.Resp2.Status) {
		return headerSim, nil
	}
	if sp.OnlyBody {
		headerSim = 1
	}
	// 剔除一些干扰字符对结果的影响
	sp.ClearSpeStr()
	body1, err := sp.Resp1.GetUTF8Body()
	if err != nil {
		return 0, fmt.Errorf("获取utf-8的响应包失败，err：%s", err)
	}
	body2, err := sp.Resp2.GetUTF8Body()
	if err != nil {
		return 0, fmt.Errorf("获取utf-8的响应包失败，err：%s", err)
	}

	if strings.Contains(content1, "html") {
		as1 := html.GetAssorted(body1, sp.Remove1)
		as2 := html.GetAssorted(body2, sp.Remove2)
		pageSim := html.ComputeSim(as1, as2)
		return int(float32(pageSim)*0.95 + float32(headerSim)*0.05), nil
	} else {
		if sp.AllowJsonComp && strings.Contains(content1, "json") {
			jsonSim := json.Compute(string(body1), string(body2))
			return int(jsonSim*0.95 + float32(headerSim)*0.05), nil
		}
		textSim := text.ComputeStr(string(body1), string(body2))
		return int(textSim*0.95 + float32(headerSim)*0.05), nil
	}
}

// 计算响应包header部分的相似度
func (sp *SimiParam) comHeaderSim() int {
	if sp.Resp1.Status != sp.Resp2.Status {
		return 0
	}
	// 状态码都是3xx，但是跳转的目的地址不同则相似度为0
	if CodeIs3xx(sp.Resp1.Status) {
		location1, location2 := sp.Resp1.Header.Get("Location"), sp.Resp2.Header.Get("Location")
		if location1 != location2 {
			return 0
		}
	}
	headerList1 := sp.Resp1.GetHeaderKeys()
	headerList2 := sp.Resp2.GetHeaderKeys()
	headerSim := text.Compute(headerList1, headerList2)

	return int(headerSim * 100)
}

func (sp *SimiParam) ClearSpeStr() {
	specialList := []string{
		"<!-- a padding to disable MSIE and Chrome friendly error page -->",
	}
	for _, sl := range specialList {
		bytes.ReplaceAll(sp.Resp1.Body, []byte(sl), []byte(""))
		bytes.ReplaceAll(sp.Resp2.Body, []byte(sl), []byte(""))
	}
}

func CodeIs3xx(code int) bool {
	return code > 299 && code < 400
}

func RespsIsLike(resps []*httpv.Response, AllowCodeDif bool, remove []byte, simiMin int) (bool, error) {
	// 两次循环将所有的请求两两计算相似度，如果有任意两个的相似度小于最小相似度，则说明这些请求不是完全相似的
	for i := 0; i < len(resps); i++ {
		for j := i + 1; j < len(resps); j++ {
			simiParam := SimiParam{
				Resp1:        resps[i],
				Resp2:        resps[j],
				Remove1:      remove,
				Remove2:      remove,
				AllowCodeDif: AllowCodeDif,
			}
			simi, err := simiParam.Compute()
			if err != nil {
				return false, err
			}
			if simi < simiMin {
				return false, nil
			}
		}
	}
	return true, nil
}
