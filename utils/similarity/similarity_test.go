package similarity

import (
	"fmt"
	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"log/slog"
	"testing"
)

func TestCompute(t *testing.T) {
	respStr1 := `HTTP/1.1 200 OK
Content-Type: text/html; charset=UTF-8
Date: Thu, 07 Nov 2024 12:41:55 GMT
X-Powered-By: PHP/5.5.38
Set-Cookie: PHPSESSID=jdivcjr2tvlkbp4l8sjbo2n3b1; path=/
Expires: Thu, 19 Nov 1981 08:52:00 GMT
Pragma: no-cache
Server: Apache/2.4.6 (CentOS) OpenSSL/1.0.2k-fips PHP/5.5.38
Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0

<!DOCTYPE html>
<!-- 
UI Version: 4.5.1
-->
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>MyRepublic Hosted PBX</title>
        <meta name="description" content="Login">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
        <!-- Call App Mode on ios devices -->
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <!-- Remove Tap Highlight on Windows Phone IE -->
        <meta name="msapplication-tap-highlight" content="no">
        <!-- base css -->
	           <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="themes/smartadmin/css/vendors.bundle.css">
           <link id="appbundle" rel="stylesheet" media="screen, print" href="themes/smartadmin/css/app.bundle.css">
           <link id="myskin" rel="stylesheet" media="screen, print" href="themes/smartadmin/css/skins/skin-master.css">
	        <link rel="stylesheet" media="screen, print" type="text/css" href="themes/smartadmin/css/formplugins/select2/select2.bundle.css">
        <!-- Place favicon.ico in the root directory -->
	            <link rel="apple-touch-icon" sizes="180x180" href="img/favicon/apple-touch-icon.png">
            <link rel="icon" type="image/png" sizes="32x32" href="img/favicon/favicon-32x32.png">
            <link rel="mask-icon" href="img/favicon/safari-pinned-tab.svg" color="#5bbad5">
                <link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/fa-brands.css">
        <link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/formplugins/select2/select2.bundle.css">

                  <link rel="stylesheet" media="screen" href="themes/smartadmin/css/themes/cust-theme-3.css">
        
	<link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/my-style.css">
        <!-- You can add your own stylesheet here to override any styles that comes before it-->
            <link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/your-style.css">
        
            </head>
    <body>
        <div class="page-wrapper auth">
            <div class="page-inner">
                <div class="page-content-wrapper bg-transparent m-0">
                    <div class="height-10 w-100 px-4">
                        <div class="d-flex align-items-center container p-0 d-md-none">
                            <div class="page-logo width-mobile-auto m-0 align-items-center justify-content-center p-0 bg-transparent bg-img-none shadow-0 height-9 border-0">
                                <a href="javascript:void(0)" class="page-logo-link press-scale-down d-flex align-items-center">
                                    <img src="https://cssync.sg.myrepublic.net/MyRepublic-Diapositive-Logo-Horizontal.png" height="40px">                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1" style="background: url(themes/smartadmin/img/svg/pattern-1.svg) no-repeat center bottom fixed; background-size: cover;">
                        <div class="container py-4 py-lg-5 my-lg-5 px-4 px-sm-0">
                            <div class="row">
                                <div class="col col-md-6 col-lg-7 hidden-sm-down">
                                    <h2 class="fs-xxl fw-500 mt-4">
                                        MyRepublic Cloud PBX
                                        <small class="h3 fw-300 mt-3 mb-5">
                                            Welcome to MyRepublic's Cloud PBX service
                                        </small>
                                    </h2>
                                    <div class="d-sm-flex flex-column align-items-center justify-content-center d-md-block">
                                        
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-6 col-lg-5 col-xl-4 ml-auto">
                                    <h1 class="fw-300 mb-3 d-sm-block d-md-none" id="form_title_text">
                                        Login
                                    </h1>
                                    <div class="card p-4 rounded-plus bg-faded">
                                        <form method="post" id="login-form">
                                                                                        <div class="alert alert-danger text-dark" role="alert">
                                                You are not allowed to connect right now, try later
                                            </div>
                                                                                        
                                            <div class="form-group">
                                                <label class="form-label" for="username">Username</label>
                                                <input type="text" required name="username" class="form-control form-control-lg" id="username" autofocus=true>
                                                <div class="invalid-feedback">No, you missed this one.</div>
                                                <div class="help-block">Enter your username</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="password">Password</label>
                                                <input type="password" name="password" class="form-control form-control-lg" required id="password" >
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter your password</div>
                                            </div>

                                            
                                            
                                            <div class="row no-gutters">
                                                <div class="col-lg-4 pl-lg-1 my-2">
                                                    <button type="submit" id="loginbutton" value="Login" class="btn btn-primary btn-block">Login</button>
                                                </div>
                                                <div class="col-lg-7 pr-lg-1 my-2 offset-lg-1">
                                                    <a href="#" onclick="showpasswordchange()" class="btn btn-secondary btn-block">Change Password</a>
                                                </div>
                                            </div>
                                        </form>
                                        <form method="post" id="changepassword-form" style="display:none">
                                                                                        <div class="alert alert-danger text-dark" role="alert">
                                                You are not allowed to connect right now, try later
                                            </div>
                                            
                                            <div class="form-group">
                                                <label class="form-label" for="username">Username</label>
                                                <input type="text" required name="usernametochange" class="form-control form-control-lg" id="usernametochange" autofocus=true>
                                                <div class="invalid-feedback">No, you missed this one.</div>
                                                <div class="help-block">Enter your username</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="oldpassword">Old Password</label>
                                                <input type="password" name="oldpassword" class="form-control form-control-lg" required id="oldpassword" value="">
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter your password</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="newpassword">New Password</label>
                                                <input type="password" name="newpassword" class="form-control form-control-lg" required id="newpassword">
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter your new password</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="newpassword2">Repeat New Password</label>
                                                <input type="password" name="newpassword2" class="form-control form-control-lg" required id="newpassword2">
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter again your new password</div>
                                            </div>

                                            
                                            <div class="row no-gutters">
                                                <div class="col-lg-7 pl-lg-1 my-2">
                                                    <button type="submit" id="changepasswordbutton" value="Change Password" class="btn btn-primary btn-block">Change Password</button>
                                                </div>
                                                <div class="col-lg-4 pr-lg-1 my-2 offset-lg-1" id="changepasswordlogin">
                                                    <a href="#" onclick="showlogin()" class="btn btn-secondary btn-block">Login</a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="clusterdialog" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-transparent" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title text-white">
                            Tenant select
                        </h4>
                    </div>
                    <form method="POST" class="form">
                    <div class="modal-body"> 
                        <div class="form-group">
                            <div class="col-md-3">
                                <input type="hidden" name="providedusername" value="">
                                <input type="hidden" name="providedpassword" value="">
                                <select id="clusterconnect" name="clusterconnect" class="">
                                    <option SELECTED value=''></option>
                                                                    </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" name="connect" value="Connect" class="btn btn-primary">Connect</button>
                    </div>
                </div>
                </form>
            </div>
        </div>

        <!-- BEGIN Color profile  -->
        <!-- this area is hidden and will not be seen on screens or screen readers -->
        <!-- we use this only for CSS color refernce for JS stuff -->
        <p id="js-color-profile" class="d-none">
            <span class="color-primary-50"></span>
            <span class="color-primary-100"></span>
            <span class="color-primary-200"></span>
            <span class="color-primary-300"></span>
            <span class="color-primary-400"></span>
            <span class="color-primary-500"></span>
            <span class="color-primary-600"></span>
            <span class="color-primary-700"></span>
            <span class="color-primary-800"></span>
            <span class="color-primary-900"></span>
            <span class="color-info-50"></span>
            <span class="color-info-100"></span>
            <span class="color-info-200"></span>
            <span class="color-info-300"></span>
            <span class="color-info-400"></span>
            <span class="color-info-500"></span>
            <span class="color-info-600"></span>
            <span class="color-info-700"></span>
            <span class="color-info-800"></span>
            <span class="color-info-900"></span>
            <span class="color-danger-50"></span>
            <span class="color-danger-100"></span>
            <span class="color-danger-200"></span>
            <span class="color-danger-300"></span>
            <span class="color-danger-400"></span>
            <span class="color-danger-500"></span>
            <span class="color-danger-600"></span>
            <span class="color-danger-700"></span>
            <span class="color-danger-800"></span>
            <span class="color-danger-900"></span>
            <span class="color-warning-50"></span>
            <span class="color-warning-100"></span>
            <span class="color-warning-200"></span>
            <span class="color-warning-300"></span>
            <span class="color-warning-400"></span>
            <span class="color-warning-500"></span>
            <span class="color-warning-600"></span>
            <span class="color-warning-700"></span>
            <span class="color-warning-800"></span>
            <span class="color-warning-900"></span>
            <span class="color-success-50"></span>
            <span class="color-success-100"></span>
            <span class="color-success-200"></span>
            <span class="color-success-300"></span>
            <span class="color-success-400"></span>
            <span class="color-success-500"></span>
            <span class="color-success-600"></span>
            <span class="color-success-700"></span>
            <span class="color-success-800"></span>
            <span class="color-success-900"></span>
            <span class="color-fusion-50"></span>
            <span class="color-fusion-100"></span>
            <span class="color-fusion-200"></span>
            <span class="color-fusion-300"></span>
            <span class="color-fusion-400"></span>
            <span class="color-fusion-500"></span>
            <span class="color-fusion-600"></span>
            <span class="color-fusion-700"></span>
            <span class="color-fusion-800"></span>
            <span class="color-fusion-900"></span>
        </p>
        <script src="themes/smartadmin/js/vendors.bundle.js"></script>
        <script src="themes/smartadmin/js/app.bundle.js"></script>
        <script src="themes/smartadmin/js/formplugins/select2/select2.bundle.js"></script>
        <script src="js/plugin/jquery-validate/jquery.validate.min.js"></script>
        <script src="js/plugin/jquery-validate/additional-methods.min.js"></script>
        <script>
        $(document).ready(function() {

            $('.select2').select2();

	    if (""=="yes") {
	       $("#changepasswordlogin").hide('fast');
	       showpasswordchange();
	    }
            
            $( "#clusterdialog" ).modal( {
                keyboard: false,
                backdrop: false,
                show: false 
            } );

            

            $( "#changepassword-form" ).validate ({
                rules: {
                    newpassword: {
	    
                        pattern: '(?=.*[A-Z].*[A-Z])(?=.*[0-9].*[0-9])(?=.*[a-z].*[a-z].*[a-z]).{8}',
	                        required: true
                    },
                    newpassword2: {
                        equalTo: "#newpassword",
	                required: true
                    }
                },
                messages: {
                    newpassword: {
	                pattern: jQuery.validator.format("Password needs to be at least 8 chars, 2 uppercase, 2 digits and 3 lowercase"),
                    },
                },
            });
        })

        function showlogin() {
            $( "#form_title_text").text("Login");
            $( "#changepassword-form").hide('fast');
            $( "#login-form").show('fast');
        };

        function showpasswordchange() {
            $( "#form_title_text").text("Change Password");
            $( "#changepassword-form").show('fast');
            $( "#login-form").hide('fast');
        };
        </script>
    </body>
    <!-- END Body -->
</html>
`
	respStr2 := `HTTP/1.1 200 OK
Server: Apache/2.4.6 (CentOS) OpenSSL/1.0.2k-fips PHP/5.5.38
X-Powered-By: PHP/5.5.38
Set-Cookie: PHPSESSID=o2bt5sl1gtjjpdblmbktoopsu6; path=/
Expires: Thu, 19 Nov 1981 08:52:00 GMT
Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0
Pragma: no-cache
Date: Thu, 07 Nov 2024 12:42:03 GMT
Content-Type: text/html; charset=UTF-8

<!DOCTYPE html>
<!-- 
UI Version: 4.5.1
-->
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>MyRepublic Hosted PBX</title>
        <meta name="description" content="Login">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
        <!-- Call App Mode on ios devices -->
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <!-- Remove Tap Highlight on Windows Phone IE -->
        <meta name="msapplication-tap-highlight" content="no">
        <!-- base css -->
	           <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="themes/smartadmin/css/vendors.bundle.css">
           <link id="appbundle" rel="stylesheet" media="screen, print" href="themes/smartadmin/css/app.bundle.css">
           <link id="myskin" rel="stylesheet" media="screen, print" href="themes/smartadmin/css/skins/skin-master.css">
	        <link rel="stylesheet" media="screen, print" type="text/css" href="themes/smartadmin/css/formplugins/select2/select2.bundle.css">
        <!-- Place favicon.ico in the root directory -->
	            <link rel="apple-touch-icon" sizes="180x180" href="img/favicon/apple-touch-icon.png">
            <link rel="icon" type="image/png" sizes="32x32" href="img/favicon/favicon-32x32.png">
            <link rel="mask-icon" href="img/favicon/safari-pinned-tab.svg" color="#5bbad5">
                <link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/fa-brands.css">
        <link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/formplugins/select2/select2.bundle.css">

                  <link rel="stylesheet" media="screen" href="themes/smartadmin/css/themes/cust-theme-3.css">
        
	<link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/my-style.css">
        <!-- You can add your own stylesheet here to override any styles that comes before it-->
            <link rel="stylesheet" media="screen, print" href="themes/smartadmin/css/your-style.css">
        
            </head>
    <body>
        <div class="page-wrapper auth">
            <div class="page-inner">
                <div class="page-content-wrapper bg-transparent m-0">
                    <div class="height-10 w-100 px-4">
                        <div class="d-flex align-items-center container p-0 d-md-none">
                            <div class="page-logo width-mobile-auto m-0 align-items-center justify-content-center p-0 bg-transparent bg-img-none shadow-0 height-9 border-0">
                                <a href="javascript:void(0)" class="page-logo-link press-scale-down d-flex align-items-center">
                                    <img src="https://cssync.sg.myrepublic.net/MyRepublic-Diapositive-Logo-Horizontal.png" height="40px">                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1" style="background: url(themes/smartadmin/img/svg/pattern-1.svg) no-repeat center bottom fixed; background-size: cover;">
                        <div class="container py-4 py-lg-5 my-lg-5 px-4 px-sm-0">
                            <div class="row">
                                <div class="col col-md-6 col-lg-7 hidden-sm-down">
                                    <h2 class="fs-xxl fw-500 mt-4">
                                        MyRepublic Cloud PBX
                                        <small class="h3 fw-300 mt-3 mb-5">
                                            Welcome to MyRepublic's Cloud PBX service
                                        </small>
                                    </h2>
                                    <div class="d-sm-flex flex-column align-items-center justify-content-center d-md-block">
                                        
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-6 col-lg-5 col-xl-4 ml-auto">
                                    <h1 class="fw-300 mb-3 d-sm-block d-md-none" id="form_title_text">
                                        Login
                                    </h1>
                                    <div class="card p-4 rounded-plus bg-faded">
                                        <form method="post" id="login-form">
                                                                                        <div class="alert alert-danger text-dark" role="alert">
                                                You are not allowed to connect right now, try later
                                            </div>
                                                                                        
                                            <div class="form-group">
                                                <label class="form-label" for="username">Username</label>
                                                <input type="text" required name="username" class="form-control form-control-lg" id="username" autofocus=true>
                                                <div class="invalid-feedback">No, you missed this one.</div>
                                                <div class="help-block">Enter your username</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="password">Password</label>
                                                <input type="password" name="password" class="form-control form-control-lg" required id="password" >
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter your password</div>
                                            </div>

                                            
                                            
                                            <div class="row no-gutters">
                                                <div class="col-lg-4 pl-lg-1 my-2">
                                                    <button type="submit" id="loginbutton" value="Login" class="btn btn-primary btn-block">Login</button>
                                                </div>
                                                <div class="col-lg-7 pr-lg-1 my-2 offset-lg-1">
                                                    <a href="#" onclick="showpasswordchange()" class="btn btn-secondary btn-block">Change Password</a>
                                                </div>
                                            </div>
                                        </form>
                                        <form method="post" id="changepassword-form" style="display:none">
                                                                                        <div class="alert alert-danger text-dark" role="alert">
                                                You are not allowed to connect right now, try later
                                            </div>
                                            
                                            <div class="form-group">
                                                <label class="form-label" for="username">Username</label>
                                                <input type="text" required name="usernametochange" class="form-control form-control-lg" id="usernametochange" autofocus=true>
                                                <div class="invalid-feedback">No, you missed this one.</div>
                                                <div class="help-block">Enter your username</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="oldpassword">Old Password</label>
                                                <input type="password" name="oldpassword" class="form-control form-control-lg" required id="oldpassword" value="">
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter your password</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="newpassword">New Password</label>
                                                <input type="password" name="newpassword" class="form-control form-control-lg" required id="newpassword">
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter your new password</div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label" for="newpassword2">Repeat New Password</label>
                                                <input type="password" name="newpassword2" class="form-control form-control-lg" required id="newpassword2">
                                                <div class="invalid-feedback">Sorry, you missed this one.</div>
                                                <div class="help-block">Enter again your new password</div>
                                            </div>

                                            
                                            <div class="row no-gutters">
                                                <div class="col-lg-7 pl-lg-1 my-2">
                                                    <button type="submit" id="changepasswordbutton" value="Change Password" class="btn btn-primary btn-block">Change Password</button>
                                                </div>
                                                <div class="col-lg-4 pr-lg-1 my-2 offset-lg-1" id="changepasswordlogin">
                                                    <a href="#" onclick="showlogin()" class="btn btn-secondary btn-block">Login</a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="clusterdialog" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-transparent" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title text-white">
                            Tenant select
                        </h4>
                    </div>
                    <form method="POST" class="form">
                    <div class="modal-body"> 
                        <div class="form-group">
                            <div class="col-md-3">
                                <input type="hidden" name="providedusername" value="">
                                <input type="hidden" name="providedpassword" value="">
                                <select id="clusterconnect" name="clusterconnect" class="">
                                    <option SELECTED value=''></option>
                                                                    </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" name="connect" value="Connect" class="btn btn-primary">Connect</button>
                    </div>
                </div>
                </form>
            </div>
        </div>

        <!-- BEGIN Color profile  -->
        <!-- this area is hidden and will not be seen on screens or screen readers -->
        <!-- we use this only for CSS color refernce for JS stuff -->
        <p id="js-color-profile" class="d-none">
            <span class="color-primary-50"></span>
            <span class="color-primary-100"></span>
            <span class="color-primary-200"></span>
            <span class="color-primary-300"></span>
            <span class="color-primary-400"></span>
            <span class="color-primary-500"></span>
            <span class="color-primary-600"></span>
            <span class="color-primary-700"></span>
            <span class="color-primary-800"></span>
            <span class="color-primary-900"></span>
            <span class="color-info-50"></span>
            <span class="color-info-100"></span>
            <span class="color-info-200"></span>
            <span class="color-info-300"></span>
            <span class="color-info-400"></span>
            <span class="color-info-500"></span>
            <span class="color-info-600"></span>
            <span class="color-info-700"></span>
            <span class="color-info-800"></span>
            <span class="color-info-900"></span>
            <span class="color-danger-50"></span>
            <span class="color-danger-100"></span>
            <span class="color-danger-200"></span>
            <span class="color-danger-300"></span>
            <span class="color-danger-400"></span>
            <span class="color-danger-500"></span>
            <span class="color-danger-600"></span>
            <span class="color-danger-700"></span>
            <span class="color-danger-800"></span>
            <span class="color-danger-900"></span>
            <span class="color-warning-50"></span>
            <span class="color-warning-100"></span>
            <span class="color-warning-200"></span>
            <span class="color-warning-300"></span>
            <span class="color-warning-400"></span>
            <span class="color-warning-500"></span>
            <span class="color-warning-600"></span>
            <span class="color-warning-700"></span>
            <span class="color-warning-800"></span>
            <span class="color-warning-900"></span>
            <span class="color-success-50"></span>
            <span class="color-success-100"></span>
            <span class="color-success-200"></span>
            <span class="color-success-300"></span>
            <span class="color-success-400"></span>
            <span class="color-success-500"></span>
            <span class="color-success-600"></span>
            <span class="color-success-700"></span>
            <span class="color-success-800"></span>
            <span class="color-success-900"></span>
            <span class="color-fusion-50"></span>
            <span class="color-fusion-100"></span>
            <span class="color-fusion-200"></span>
            <span class="color-fusion-300"></span>
            <span class="color-fusion-400"></span>
            <span class="color-fusion-500"></span>
            <span class="color-fusion-600"></span>
            <span class="color-fusion-700"></span>
            <span class="color-fusion-800"></span>
            <span class="color-fusion-900"></span>
        </p>
        <script src="themes/smartadmin/js/vendors.bundle.js"></script>
        <script src="themes/smartadmin/js/app.bundle.js"></script>
        <script src="themes/smartadmin/js/formplugins/select2/select2.bundle.js"></script>
        <script src="js/plugin/jquery-validate/jquery.validate.min.js"></script>
        <script src="js/plugin/jquery-validate/additional-methods.min.js"></script>
        <script>
        $(document).ready(function() {

            $('.select2').select2();

	    if (""=="yes") {
	       $("#changepasswordlogin").hide('fast');
	       showpasswordchange();
	    }
            
            $( "#clusterdialog" ).modal( {
                keyboard: false,
                backdrop: false,
                show: false 
            } );

            

            $( "#changepassword-form" ).validate ({
                rules: {
                    newpassword: {
	    
                        pattern: '(?=.*[A-Z].*[A-Z])(?=.*[0-9].*[0-9])(?=.*[a-z].*[a-z].*[a-z]).{8}',
	                        required: true
                    },
                    newpassword2: {
                        equalTo: "#newpassword",
	                required: true
                    }
                },
                messages: {
                    newpassword: {
	                pattern: jQuery.validator.format("Password needs to be at least 8 chars, 2 uppercase, 2 digits and 3 lowercase"),
                    },
                },
            });
        })

        function showlogin() {
            $( "#form_title_text").text("Login");
            $( "#changepassword-form").hide('fast');
            $( "#login-form").show('fast');
        };

        function showpasswordchange() {
            $( "#form_title_text").text("Change Password");
            $( "#changepassword-form").show('fast');
            $( "#login-form").hide('fast');
        };
        </script>
    </body>
    <!-- END Body -->
</html>`
	resp1, err := httpv.String2Response(respStr1)
	if err != nil {
		slog.Error("String2Response failed", "error", err)
		return
	}
	resp2, err := httpv.String2Response(respStr2)
	if err != nil {
		slog.Error("String2Response failed", "error", err)
		return
	}
	// resp相应包相似度计算
	param := SimiParam{
		Resp1:        resp1,
		Resp2:        resp2,
		Remove1:      []byte(""),
		Remove2:      []byte(""),
		AllowCodeDif: false,
	}
	respSim, err := param.Compute()
	if err != nil {
		slog.Error("计算相似度错误", "err: ", err)
	}
	fmt.Println(respSim)

	// x, err := comparer.CompareResponse(resp1, []byte(""), resp2, []byte(""), false)
	// if err != nil {
	//	slog.Error("计算相似度错误", "err: ", err)
	// }
	// fmt.Println(x)

	// html相似度计算
	// 新版
	// tt := html.GetAssorted([]byte(str), []byte(""))
	// yy := html.GetAssorted([]byte(str2), []byte(""))
	// sim := html.ComputeSim(tt, yy)
	// fmt.Println(sim)
	// // 旧版
	// p1 := htmlcompare.NewHTMLProcessor([]byte(str), []byte(""))
	// p2 := htmlcompare.NewHTMLProcessor([]byte(str2), []byte(""))
	// pageSimilarity := p1.CompareWith(p2)
	// fmt.Println(pageSimilarity)
}
