package text

import "strings"

var SplitMark = []rune("\" \n\r,，.。<>/()';{}[]；（）")

func StringSplit(rawStr string) []string {
	markMap := make(map[rune]bool)
	for _, mark := range SplitMark {
		markMap[mark] = true
	}
	fun := func(mark rune) bool {
		return markMap[mark]
	}
	list := strings.FieldsFunc(rawStr, fun)
	return list
}

func ComputeStr(strA, strB string) float32 {
	listA := StringSplit(strA)
	listB := StringSplit(strB)
	return Compute(listA, listB)
}

func Compute(listA, listB []string) float32 {
	if len(listA) == 0 && len(listB) == 0 {
		return 1
	}
	// 统计同一个元素分别在两个list中出现的次数
	aCount := map[string]int{}
	bCount := map[string]int{}
	sameCount := 0
	for _, s := range listA {
		aCount[s] += 1
	}
	for _, s := range listB {
		bCount[s] += 1
	}
	for str, i := range aCount {
		if n, exit := bCount[str]; exit {
			// 如果同时存在则取出现次数少的为共同出现次数
			// 计算两个list中相同的单词次数，再除以list长度即可得到一个相似度
			if i > n {
				sameCount += n
			} else {
				sameCount += i
			}
		}
	}
	if sameCount == 0 {
		return 0
	}

	if sameCount > 0 {
		return 2.0 * float32(sameCount) / float32(len(listA)+len(listB))
	} else {
		return 0
	}
}
