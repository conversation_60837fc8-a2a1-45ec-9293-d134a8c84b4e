package json

import (
	"fmt"
	"reflect"

	"github.com/tidwall/gjson"
)

func Compute(jsonStr1, jsonStr2 string) float32 {
	map1 := make(map[string]interface{})
	map2 := make(map[string]interface{})

	parserJson(jsonStr1, "", map1)
	parserJson(jsonStr2, "", map2)

	keySimi := computeKeySimi(map1, map2)
	valueSimi := computeValueSimi(map1, map2)
	// 暂时用于目录扫描判断响应是否相似，key的占比应该比较大
	return keySimi*0.7 + valueSimi*0.3
}

func computeKeySimi(map1, map2 map[string]interface{}) float32 {
	totalKeys := len(map1) + len(map2)
	commonKeys := 0
	for k := range map1 {
		if _, ok := map2[k]; ok {
			commonKeys++
		}
	}

	if totalKeys == 0 {
		return 100 // 如果两个对象都是空的，则视为完全相似
	}

	return float32(commonKeys*2) * 100 / float32(totalKeys)
}

func computeValueSimi(map1, map2 map[string]interface{}) float32 {
	totalKeys := len(map1) + len(map2)
	commonKeys := 0
	for k, v1 := range map1 {
		if v2, ok := map2[k]; ok && reflect.DeepEqual(v1, v2) {
			commonKeys++
		}
	}

	if totalKeys == 0 {
		return 100 // 如果两个对象都是空的，则视为完全相似
	}

	return float32(commonKeys*2) * 100 / float32(totalKeys)
}

// 将json格式数据平铺
func parserJson(rawStr string, jsonKey string, result map[string]interface{}) {
	h := gjson.Parse(rawStr)
	h.ForEach(func(key, value gjson.Result) bool {
		var newJsonKey string
		if jsonKey != "" {
			newJsonKey = fmt.Sprintf("%s.%s", jsonKey, value.Path(rawStr))
		} else {
			newJsonKey = value.Path(rawStr)
		}
		if value.IsObject() {
			// 当value值是json格式类型则循环解析其中的键值对
			parserJson(value.String(), newJsonKey, result)
		} else {
			result[newJsonKey] = value.Value()
		}
		return true
	})
}
