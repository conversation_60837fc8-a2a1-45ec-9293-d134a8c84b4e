package utils

import (
	"bytes"
	"fmt"
	"io"
	"net/url"
	"path"
	"regexp"
	"strings"

	"github.acme.red/intelli-sec/npoc/utils/text"
	"github.com/PuerkitoBio/goquery"
	"github.com/dlclark/regexp2"
	"github.com/hashicorp/golang-lru/v2/expirable"
	errorutil "github.com/projectdiscovery/utils/errors"
	"github.com/samber/lo"
)

var (
	titleReg       = regexp.MustCompile(`(?is)<title>(.*?)</title>`)
	charsetReg     = regexp.MustCompile("charset=(.+)$")
	unmarshalCache = expirable.NewLRU[string, *goquery.Document](1000, nil, 0)
)

const (
	regexOption = regexp2.IgnoreCase
)

type CachedDoc struct {
	UseCache  bool   // 是否使用缓存
	KeyOrBody []byte // 若使用缓存，该字段为缓存的键，否则，该字段同response.body
}

func CacheKey(taskID string, url string) string {
	return fmt.Sprintf("%s:%s", taskID, url)
}

func CachedGoqueryNewDocument(key string, body []byte) (*goquery.Document, error) {
	if key != "" {
		// use cache
		if v, ok := unmarshalCache.Get(key); ok {
			return v, nil
		}
	}
	doc, err := goquery.NewDocumentFromReader(io.NopCloser(bytes.NewBuffer(body)))
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("goquery parse")
	}
	if key != "" {
		unmarshalCache.Add(key, doc)
	}
	return doc, err
}

func AddCache(key string, doc *goquery.Document) {
	unmarshalCache.Add(key, doc)
}

func GetCache(key string) (*goquery.Document, bool) {
	return unmarshalCache.Get(key)
}

func DeleteCache(key string) bool {
	return unmarshalCache.Remove(key)
}

func Keys() []string {
	return unmarshalCache.Keys()
}

func Len() int {
	return unmarshalCache.Len()
}

// handleCachedDoc extract cached goquery document
func handleCachedDoc(cachedDoc *CachedDoc) (*goquery.Document, error) {
	if cachedDoc.UseCache {
		doc, ok := GetCache(string(cachedDoc.KeyOrBody))
		if !ok {
			return nil, errorutil.New("fail to get goquery document from cache, key: %s", cachedDoc.KeyOrBody)
		}
		return doc, nil
	} else {
		doc, err := goquery.NewDocumentFromReader(io.NopCloser(bytes.NewBuffer(cachedDoc.KeyOrBody)))
		if err != nil {
			return nil, errorutil.NewWithErr(err).Msgf("fail to run goquery")
		}
		return doc, nil
	}
}

func ParseTitle(body []byte) string {
	smallBody := body
	if len(body) > 2048 {
		smallBody = body[:2048]
	}

	allBs := titleReg.FindSubmatch(smallBody)
	var bs []byte
	if len(allBs) > 1 {
		bs = allBs[1]
	}

	title := string(bs)

	var charCoding string
	{
		bs := charsetReg.Find(smallBody)
		charCoding = string(bs)
		charCoding = strings.ToLower(charCoding)
	}
	if !lo.Contains([]string{"utf8", "utf-8"}, charCoding) {
		dst, err := text.ToUTF8(charCoding, title)
		if err == nil {
			title = dst
		}
	}

	return strings.TrimSpace(title)
}

var patterns = []*regexp.Regexp{
	regexp.MustCompile(`(?i)[a-f\d]{4}(?:[a-f\d]{4}-){4}[a-f\d]{12}`),             // uuid
	regexp.MustCompile(`[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}.\d\d:\d\d:\d\d(\.\d+)?Z?`), // 时间
	regexp.MustCompile(`[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}`),                          // 时间
	regexp.MustCompile(`[0-9]{4}/[0-9]{1,2}/[0-9]{1,2}`),                          // 时间
	regexp.MustCompile(`<!--.+-->`),                                               // 注释
	regexp.MustCompile(`"Mozilla/\d+.+?"`),                                        // ua
}

// ClearContent 去除掉返回包body中的一些id、时间等干扰数据
func ClearContent(link string, ua string, content []byte) []byte {
	// 默认做两次url解码,url解码报错后使用原始值做处理
	contentStr := urlDoubleUnDecode(string(content))
	for _, sampleRegexp := range patterns {
		contentStr = sampleRegexp.ReplaceAllString(contentStr, "")
	}
	// 如果返回包中在/前面加了转义字符则将转义字符去掉
	if strings.Contains(contentStr, `\/`) {
		contentStr = strings.ReplaceAll(contentStr, `\/`, "/")
	}
	unDecodedUrl := urlDoubleUnDecode(link)
	contentStr = strings.ReplaceAll(contentStr, unDecodedUrl, "")
	u, err := url.Parse(unDecodedUrl)
	if err == nil {
		baseUrl := u.Scheme + "://" + u.Host
		urlPath := strings.ReplaceAll(unDecodedUrl, baseUrl, "")
		contentStr = strings.ReplaceAll(contentStr, baseUrl, "")
		if u.Path != "" {
			contentStr = strings.ReplaceAll(contentStr, urlPath, "")

			file := path.Base(urlPath)

			if strings.Contains(file, ".") {
				if file != "" {
					contentStr = strings.ReplaceAll(contentStr, file, "")
				}
				// if strings.Contains(file, ".") {
				//	fileName := strings.Split(file, ".")[0]
				//	contentStr = strings.ReplaceAll(contentStr, fileName, "")
				// }
			}
		}
	}

	// 去除可能存在ua
	if ua != "" {
		contentStr = strings.ReplaceAll(contentStr, "（", `(`)
		contentStr = strings.ReplaceAll(contentStr, "）", `)`)
		contentStr = strings.ReplaceAll(contentStr, ua, "")
	}

	return []byte(contentStr)
}

func urlDoubleUnDecode(str string) string {
	newStr, err := url.QueryUnescape(str)
	if err != nil {
		newStr = str
		return newStr
	}
	newStr2, err := url.QueryUnescape(str)
	if err != nil {
		newStr2 = newStr
	}
	return newStr2
}

// ExtractAllScript extracts all the contents of <script> tag
func ExtractAllScript(body []byte) ([][]byte, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	scripts := make([][]byte, 0)
	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return nil, err
	}
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		scripts = append(scripts, []byte(s.Text()))
	})
	return scripts, nil
}

// ExtractAllScriptFromCache extracts all the contents of <script> tag
func ExtractAllScriptFromCache(cachedDoc *CachedDoc) ([][]byte, error) {
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	scripts := make([][]byte, 0)
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		scripts = append(scripts, []byte(s.Text()))
	})
	return scripts, nil
}

// ExtractAllScriptSrc extracts the src attribute of <script> tags, accepts response body
func ExtractAllScriptSrc(body []byte) ([][]byte, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return nil, err
	}
	return ExtractAllScriptSrcFromDoc(doc)
}

// ExtractAllScriptSrcFromCache extracts the src attribute of <script> tags, accepts cache key
func ExtractAllScriptSrcFromCache(cachedDoc *CachedDoc) ([][]byte, error) {
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	return ExtractAllScriptSrcFromDoc(doc)
}

// ExtractAllScriptSrcFromDoc extracts script:src attributes
func ExtractAllScriptSrcFromDoc(doc *goquery.Document) ([][]byte, error) {
	scriptSrcs := make([][]byte, 0)
	doc.Find("script").Each(func(i int, s *goquery.Selection) {
		val, ok := s.Attr("src")
		if ok {
			scriptSrcs = append(scriptSrcs, []byte(val))
		}
	})
	return scriptSrcs, nil
}

// ExistQuerySelectorAll execute jQuery expression and returns whether there is at least one node selected
// Deprecated: use ExistQuerySelectorAllFromCache instead
func ExistQuerySelectorAll(body []byte, query string) (bool, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return false, err
	}
	s := doc.Find(query).Length()
	if s <= 0 {
		return false, nil
	} else {
		return true, nil
	}
}

// ExistQuerySelectorAllFromCache execute jQuery expression and returns whether there is at least one node selected
func ExistQuerySelectorAllFromCache(cachedDoc *CachedDoc, query string) (bool, error) {
	var (
		doc *goquery.Document
		err error
	)
	doc, err = handleCachedDoc(cachedDoc)
	if err != nil {
		return false, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	s := doc.Find(query).Length()
	if s <= 0 {
		return false, nil
	} else {
		return true, nil
	}
}

// DomText execute jQuery expression, return text content of all DOM nodes selected
// Deprecated: use DomTextFromCache instead
func DomText(body []byte, query string) ([]byte, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)

	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return nil, err
	}
	b := strings.Builder{}
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		str := strings.TrimSpace(s.Text())
		b.WriteString(str + "\n")
	})
	return []byte(strings.TrimSpace(b.String())), nil
}

// DomTextFromCache execute jQuery expression, return text content of all DOM nodes selected
func DomTextFromCache(cachedDoc *CachedDoc, query string) ([]byte, error) {
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	b := strings.Builder{}
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		str := strings.TrimSpace(s.Text())
		b.WriteString(str + "\n")
	})
	return []byte(strings.TrimSpace(b.String())), nil
}

// DomTextMatch execute jQuery expression, match text content, and returns whether there is at least one node matched
// Deprecated: use DomTextMatchFromCache instead
func DomTextMatch(body []byte, regex string, query string) (bool, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	re, err := regexp2.Compile(regex, regexOption)
	if err != nil {
		return false, err
	}

	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return false, err
	}
	res := false
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		l, err := re.MatchString(s.Text())
		if err != nil {
			return
		}
		if l {
			res = true
		}
	})
	return res, nil
}

// DomTextMatchFromCache execute jQuery expression, match text content, and returns whether there is at least one node matched
func DomTextMatchFromCache(cachedDoc *CachedDoc, regex string, query string) (bool, error) {
	re, err := regexp2.Compile(regex, regexOption)
	if err != nil {
		return false, err
	}
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return false, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	res := false
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		l, err := re.MatchString(s.Text())
		if err != nil {
			return
		}
		if l {
			res = true
		}
	})
	return res, nil
}

// DomAttrMatch execute jQuery expression, match text content, and returns whether there is at least one node matched
// Deprecated: use DomAttrMatchFromCache instead
func DomAttrMatch(body []byte, attrName string, regex string, query string) (bool, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	re, err := regexp2.Compile(regex, regexOption)
	if err != nil {
		return false, err
	}

	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return false, err
	}
	res := false
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		content, ok := s.Attr(attrName)
		if !ok {
			return
		}
		l, err := re.MatchString(content)
		if err != nil {
			return
		}
		if l {
			res = true
		}
	})
	return res, nil
}

// DomAttrMatchFromCache execute jQuery expression, match text content, and returns whether there is at least one node matched
func DomAttrMatchFromCache(cachedDoc *CachedDoc, attrName string, regex string, query string) (bool, error) {
	re, err := regexp2.Compile(regex, regexOption)
	if err != nil {
		return false, err
	}
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return false, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	res := false
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		content, ok := s.Attr(attrName)
		if !ok {
			return
		}
		l, err := re.MatchString(content)
		if err != nil {
			return
		}
		if l {
			res = true
		}
	})
	return res, nil
}

// DomAttr execute jQuery expression, return the value of specified attribute of nodes, if there is more than one
// node selected, their attributes would be joined.
// Deprecated: use DomAttrFromCache instead.
func DomAttr(body []byte, attrName string, query string) ([]byte, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	b := strings.Builder{}

	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return nil, err
	}
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		content, ok := s.Attr(attrName)
		if !ok {
			return
		}
		b.WriteString(strings.TrimSpace(content) + "\n")
	})
	return []byte(strings.TrimSpace(b.String())), nil
}

// DomAttrFromCache execute jQuery expression, return the value of specified attribute of nodes, if there is more than one
// node selected, their attributes would be joined. Accept serialized goquery document.
func DomAttrFromCache(cachedDoc *CachedDoc, attrName string, query string) ([]byte, error) {
	b := strings.Builder{}
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	doc.Find(query).Each(func(i int, s *goquery.Selection) {
		content, ok := s.Attr(attrName)
		if !ok {
			return
		}
		b.WriteString(strings.TrimSpace(content) + "\n")
	})
	return []byte(strings.TrimSpace(b.String())), nil
}

// GetMetaContent returns the corresponding content of meta tag with name
// Deprecated: use GetMetaContentFromCache instead
func GetMetaContent(body []byte, name string) (string, error) {
	r := bytes.NewReader(body)
	rc := io.NopCloser(r)
	doc, err := goquery.NewDocumentFromReader(rc)
	if err != nil {
		return "", err
	}
	res := ""
	ss := doc.Find(fmt.Sprintf("meta[name='%s']", name))
	if ss.Length() > 0 {
		con, ok := ss.First().Attr("content")
		if ok {
			res = con
		}
	}
	return res, nil
}

// GetMetaContentFromCache returns the corresponding content of meta tag with name, accept cache key
func GetMetaContentFromCache(cachedDoc *CachedDoc, name string) (string, error) {
	doc, err := handleCachedDoc(cachedDoc)
	if err != nil {
		return "", errorutil.NewWithErr(err).Msgf("handleCachedDoc")
	}
	res := ""
	ss := doc.Find(fmt.Sprintf("meta[name='%s']", name))
	if ss.Length() > 0 {
		con, ok := ss.First().Attr("content")
		if ok {
			res = con
		}
	}
	return res, nil
}

func FetchFaviconUrl(target string, data []byte) (string, error) {
	targetURL, err := url.Parse(target)
	if err != nil {
		return "", fmt.Errorf("parse target: %w", err)
	}
	potentialURLs, err := extractPotentialFavIconsURLs(data)
	if err != nil {
		return "", err
	}

	faviconURL, err := url.Parse("/favicon.ico")
	if err != nil {
		return "", fmt.Errorf("parse default favicon: %w", err)
	}
	if len(potentialURLs) > 0 {
		faviconURL, err = url.Parse(potentialURLs[0])
		if err != nil {
			return "", err
		}
	}
	faviconURL = targetURL.ResolveReference(faviconURL)

	return faviconURL.String(), nil
}

func extractPotentialFavIconsURLs(body []byte) ([]string, error) {
	var potentialURLs []string
	document, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	document.Find("link").Each(func(i int, item *goquery.Selection) {
		href, okHref := item.Attr("href")
		rel, okRel := item.Attr("rel")
		isValidRel := okRel && equalFoldAny(rel, "icon", "shortcut icon", "mask-icon", "apple-touch-icon")
		if okHref && isValidRel {
			potentialURLs = append(potentialURLs, href)
		}
	})
	return potentialURLs, nil
}

func equalFoldAny(s string, ss ...string) bool {
	for _, sss := range ss {
		if strings.EqualFold(s, sss) {
			return true
		}
	}
	return false
}
