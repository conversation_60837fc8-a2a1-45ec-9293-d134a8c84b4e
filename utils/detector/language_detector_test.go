package detector

import (
	"context"
	"testing"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

func TestDetectServerLanguage(t *testing.T) {
	opt := httpv.ClientOpt{
		HostReqMaxParallel: 25,
		HostErrMaxNum:      30,
		FailRetries:        1,
	}

	ctx := context.Background()
	taskClient, err := httpv.NewClient(ctx, opt)
	if err != nil {
		t.Fatalf("Failed to create HTTP client: %v", err)
	}

	// TODO: 后续应该修改为内网靶场
	testCases := []struct {
		language     string
		expectedLang string
		urls         []string
	}{
		{
			language:     "PHP",
			expectedLang: LangPHP,
			urls: []string{
				"http://**********:9012/",
			},
		},
		{
			language:     "Python",
			expectedLang: LangPython,
			urls: []string{
				"http://**************",
				"http://**********:9013/",
			},
		},
		{
			language:     "Java",
			expectedLang: LangJava,
			urls: []string{
				"http://************:9000",
			},
		},
		{
			language:     "Perl",
			expectedLang: LangPerl,
			urls: []string{
				"https://koham21-intra.dhd.gr",
			},
		},
		{
			language:     "ASP",
			expectedLang: LangASP,
			urls: []string{
				"https://www.dovekirken.no",
			},
		},
		{
			language:     "Ruby",
			expectedLang: LangRuby,
			urls: []string{
				"https://************/",
			},
		},
		{
			language:     "Unknown",
			expectedLang: LangUnknown,
			urls: []string{
				"https://example.com",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.language, func(t *testing.T) {
			if len(tc.urls) == 0 {
				t.Skipf("No URLs provided for %s language test", tc.language)
				return
			}

			for _, url := range tc.urls {
				t.Run(url, func(t *testing.T) {
					req, err := httpv.NewRequest("GET", url, nil)
					if err != nil {
						t.Fatalf("Failed to create request: %v", err)
						return
					}

					req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
					req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
					req.Header.Set("Accept-Language", "en-US,en;q=0.5")

					resp, err := taskClient.Do(ctx, req)
					if err != nil {
						t.Fatalf("Failed to send request to %s: %v", url, err)
						return
					}

					detectedLang := DetectServerLanguage(req, resp)

					if detectedLang != tc.expectedLang {
						t.Errorf("URL %s: Expected language %s, but got %s", url, tc.expectedLang, detectedLang)

						t.Logf("Response status: %d", resp.Status)
						t.Logf("Response headers: %v", resp.Header)

						body, _ := resp.GetUTF8Body()
						if len(body) > 500 {
							t.Logf("Response body (first 500 bytes): %s", body[:500])
						} else {
							t.Logf("Response body: %s", body)
						}
					} else {
						t.Logf("URL %s: Successfully detected %s language", url, detectedLang)
					}
				})
			}
		})
	}
}

// 用于测试特定URL的语言检测
func TestSpecificURL(t *testing.T) {
	t.Skip("Skipping specific URL test. Unskip to test a specific URL.")

	opt := httpv.ClientOpt{
		HostReqMaxParallel: 25,
		HostErrMaxNum:      30,
		FailRetries:        1,
	}

	ctx := context.Background()
	taskClient, err := httpv.NewClient(ctx, opt)
	if err != nil {
		t.Fatalf("Failed to create HTTP client: %v", err)
	}

	url := ""

	req, err := httpv.NewRequest("GET", url, nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := taskClient.Do(ctx, req)
	if err != nil {
		t.Fatalf("Failed to send request: %v", err)
	}

	detectedLang := DetectServerLanguage(req, resp)

	t.Logf("URL: %s", url)
	t.Logf("Detected language: %s", detectedLang)
	t.Logf("Response status: %d", resp.Status)
	t.Logf("Response headers: %v", resp.Header)

	body, _ := resp.GetUTF8Body()
	if len(body) > 500 {
		t.Logf("Response body (first 500 bytes): %s", body[:500])
	} else {
		t.Logf("Response body: %s", body)
	}
}
