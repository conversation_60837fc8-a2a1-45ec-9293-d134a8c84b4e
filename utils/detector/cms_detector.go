package detector

import (
	"regexp"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

// 框架和CMS类型常量
const (
	// PHP框架和CMS
	FrameworkLaravel      = "Laravel"
	FrameworkSymfony      = "Symfony"
	FrameworkCodeIgniter  = "CodeIgniter"
	FrameworkCodeIgniter4 = "CodeIgniter4"
	FrameworkZend         = "ZendFramework"
	FrameworkCakePHP      = "CakePHP"
	FrameworkYii          = "Yii"
	FrameworkYii2         = "Yii2"
	FrameworkSlim         = "Slim"
	FrameworkThinkPHP     = "ThinkPHP"
	FrameworkSpiral       = "Spiral"

	// PHP库和组件
	LibraryDoctrine  = "Doctrine"
	LibraryGuzzle    = "Guzzle"
	LibraryMonolog   = "Monolog"
	LibraryPHPSecLib = "PHPSecLib"
	LibraryPlates    = "Plates"

	// PHP CMS
	CMSWordPress = "WordPress"
	CMSDrupal7   = "Drupal7"
	CMSDrupal9   = "Drupal9"
	CMSJoomla    = "Joomla"
	CMSBitrix    = "Bitrix"
	CMSOpenCart  = "OpenCart"
	CMSPydio     = "Pydio"
	CMSvBulletin = "vBulletin"
	CMSHorde     = "Horde"

	// Python框架
	FrameworkDjango  = "Django"
	FrameworkFlask   = "Flask"
	FrameworkFastAPI = "FastAPI"
	FrameworkPyramid = "Pyramid"
	FrameworkTornado = "Tornado"
	FrameworkBottle  = "Bottle"

	// Java框架
	FrameworkSpring   = "Spring"
	FrameworkStruts   = "Struts"
	FrameworkJSF      = "JSF"
	FrameworkPlayJava = "Play"

	// .NET框架
	FrameworkASPNET = "ASP.NET"
	FrameworkCore   = "ASP.NET Core"
	FrameworkMVC    = "ASP.NET MVC"

	// Node.js框架
	FrameworkExpress = "Express"
	FrameworkNextJS  = "Next.js"
	FrameworkNuxtJS  = "Nuxt.js"
	FrameworkKoa     = "Koa"
	FrameworkNestJS  = "NestJS"

	// Ruby框架
	FrameworkRails   = "Ruby on Rails"
	FrameworkSinatra = "Sinatra"

	// Go框架
	FrameworkGin   = "Gin"
	FrameworkEcho  = "Echo"
	FrameworkBeego = "Beego"
	FrameworkIris  = "Iris"

	// Scala框架
	FrameworkPlayScala = "Play Framework"
	FrameworkAkkaHTTP  = "Akka HTTP"

	// 前端框架
	FrameworkReact   = "React"
	FrameworkVue     = "Vue.js"
	FrameworkAngular = "Angular"

	// 未知框架
	FrameworkUnknown = "Unknown"
)

// frameworkDetectionRule 框架检测规则
type frameworkDetectionRule struct {
	framework   string
	language    string // 限定语言，空字符串表示不限定
	minMatches  int    // 最少匹配数量才认为检测成功
	patterns    []*regexp.Regexp
	headers     map[string][]string // header名 -> 可能的值列表
	cookies     []string
	paths       []string
	bodyContent []string // 必须出现的内容
}

var (
	frameworkRules    []frameworkDetectionRule
	frameworkOnceInit sync.Once
)

// 框架检测规则定义 - 只包含强特征规则
var frameworkRuleDefinitions = []struct {
	framework   string
	language    string
	minMatches  int
	patterns    []string
	headers     map[string][]string
	cookies     []string
	paths       []string
	bodyContent []string
}{
	// === PHP 框架和CMS ===
	{
		framework:  FrameworkLaravel,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="csrf-token" content="[^"]*"[^>]*>`,
			`Laravel v\d+\.\d+\.\d+`,
			`"laravel/framework":\s*"[^"]*"`,
			`Illuminate\\[A-Za-z\\]+`,
			`@csrf\s*<input[^>]*name="_token"`,
		},
		headers: map[string][]string{
			"Set-Cookie": {"laravel_session", "XSRF-TOKEN"},
		},
		cookies:     []string{"laravel_session", "XSRF-TOKEN"},
		paths:       []string{"/storage/logs/", "/vendor/laravel/"},
		bodyContent: []string{"csrf-token", "laravel_session"},
	},
	{
		framework:  FrameworkSymfony,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<div[^>]*id="sfwdt[^"]*"[^>]*class="sf-toolbar">`,
			`Symfony\\Component\\`,
			`_profiler/_wdt/[a-zA-Z0-9]+`,
			`<div[^>]*class="[^"]*sf-toolbar[^"]*">`,
		},
		headers: map[string][]string{
			"X-Debug-Token":      {""},
			"X-Debug-Token-Link": {""},
		},
		cookies:     []string{"sf_redirect", "symfony"},
		paths:       []string{"/_profiler/", "/_wdt/", "/bundles/framework/"},
		bodyContent: []string{"sf-toolbar", "_profiler"},
	},
	{
		framework:  FrameworkCodeIgniter,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`CI_Controller`,
			`CodeIgniter\s*\d+\.\d+\.\d+`,
			`application/config/`,
			`system/core/CodeIgniter\.php`,
		},
		cookies:     []string{"ci_session", "codeigniter_session"},
		paths:       []string{"/application/", "/system/core/"},
		bodyContent: []string{"CodeIgniter", "ci_session"},
	},
	{
		framework:  FrameworkCodeIgniter4,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`CodeIgniter\s*4\.\d+\.\d+`,
			`App\\Controllers\\`,
			`CodeIgniter\\[A-Za-z\\]+`,
			`<input[^>]*name="csrf_token_name"`,
		},
		headers: map[string][]string{
			"Set-Cookie": {"ci_session"},
		},
		paths:       []string{"/public/", "/app/Controllers/"},
		bodyContent: []string{"CodeIgniter", "csrf_token_name"},
	},
	{
		framework:  FrameworkCakePHP,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Cake\\[A-Za-z\\]+`,
			`CakePHP\s*\d+\.\d+\.\d+`,
			`<form[^>]*action="[^"]*/[^"]*.ctp"`,
			`cake\.generic\.css`,
		},
		cookies:     []string{"CAKEPHP", "cakephp"},
		paths:       []string{"/cake/", "/app/View/", "/webroot/"},
		bodyContent: []string{"CakePHP", "Cake\\"},
	},
	{
		framework:  FrameworkYii,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Powered by <a href="http://www\.yiiframework\.com/" rel="external">Yii Framework</a>`,
			`YiiBase::createWebApplication`,
			`class="[^"]*yii[^"]*"`,
			`jquery\.yii\.js`,
		},
		paths:       []string{"/protected/", "/assets/", "/framework/"},
		bodyContent: []string{"yiiframework.com", "jquery.yii.js"},
	},
	{
		framework:  FrameworkYii2,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`yii\\[a-zA-Z\\]+`,
			`Yii\s*2\.\d+\.\d+`,
			`<input[^>]*name="_csrf"[^>]*value="[^"]*">`,
			`yii\.js`,
			`yii\.validation\.js`,
		},
		headers: map[string][]string{
			"Set-Cookie": {"_csrf", "PHPSESSID"},
		},
		paths:       []string{"/assets/", "/web/assets/"},
		bodyContent: []string{"yii\\", "_csrf"},
	},
	{
		framework:  FrameworkSlim,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Slim\\[A-Za-z\\]+`,
			`SlimFramework`,
			`"slim/slim":\s*"[^"]*"`,
			`Slim\s*Framework\s*\d+\.\d+\.\d+`,
		},
		headers: map[string][]string{
			"X-Powered-By": {"Slim"},
		},
		bodyContent: []string{"Slim\\", "SlimFramework"},
	},
	{
		framework:  FrameworkThinkPHP,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`ThinkPHP\s*\d+\.\d+\.\d+`,
			`think\\[A-Za-z\\]+`,
			`ThinkPHP.*Runtime Error`,
			`<title>ThinkPHP[^<]*</title>`,
			`template_c`,
		},
		paths:       []string{"/ThinkPHP/", "/Application/", "/Runtime/"},
		bodyContent: []string{"ThinkPHP", "think\\"},
	},
	{
		framework:  FrameworkSpiral,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Spiral\\[A-Za-z\\]+`,
			`spiral/framework`,
			`Spiral\s*Framework\s*\d+\.\d+\.\d+`,
		},
		bodyContent: []string{"Spiral\\", "spiral/framework"},
	},
	{
		framework:  FrameworkZend,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Zend\\[A-Za-z\\]+`,
			`Zend_[A-Za-z_]+`,
			`ZendFramework`,
			`zend-[a-z-]+`,
		},
		paths:       []string{"/library/Zend/", "/vendor/zendframework/"},
		bodyContent: []string{"Zend\\", "Zend_", "zendframework"},
	},

	// === PHP 库和组件 ===
	{
		framework:  LibraryDoctrine,
		language:   LangPHP,
		minMatches: 1,
		patterns: []string{
			`Doctrine\\[A-Za-z\\]+`,
			`doctrine/orm`,
			`doctrine/dbal`,
		},
		bodyContent: []string{"Doctrine\\"},
	},
	{
		framework:  LibraryGuzzle,
		language:   LangPHP,
		minMatches: 1,
		patterns: []string{
			`GuzzleHttp\\[A-Za-z\\]+`,
			`guzzlehttp/guzzle`,
		},
		bodyContent: []string{"GuzzleHttp\\"},
	},
	{
		framework:  LibraryMonolog,
		language:   LangPHP,
		minMatches: 1,
		patterns: []string{
			`Monolog\\[A-Za-z\\]+`,
			`monolog/monolog`,
		},
		bodyContent: []string{"Monolog\\"},
	},
	{
		framework:  LibraryPHPSecLib,
		language:   LangPHP,
		minMatches: 1,
		patterns: []string{
			`phpseclib\\[A-Za-z\\]+`,
			`phpseclib/phpseclib`,
		},
		bodyContent: []string{"phpseclib\\"},
	},
	{
		framework:  LibraryPlates,
		language:   LangPHP,
		minMatches: 1,
		patterns: []string{
			`League\\Plates\\`,
			`league/plates`,
		},
		bodyContent: []string{"League\\Plates\\"},
	},

	// === PHP CMS ===
	{
		framework:  CMSWordPress,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="generator" content="WordPress[^"]*">`,
			`/wp-content/themes/`,
			`/wp-content/plugins/`,
			`/wp-admin/admin-ajax\.php`,
			`wp-json/wp/v2/`,
			`var wpApiSettings`,
		},
		headers: map[string][]string{
			"Link": {"wp-json"},
		},
		paths:       []string{"/wp-content/", "/wp-admin/", "/wp-includes/", "/wp-json/"},
		bodyContent: []string{"wp-content", "wp-admin"},
	},
	{
		framework:  CMSDrupal7,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="generator" content="Drupal 7[^"]*">`,
			`Drupal\.settings`,
			`/sites/default/files/`,
			`/sites/all/modules/`,
			`misc/drupal\.js`,
		},
		headers: map[string][]string{
			"X-Drupal-Cache": {""},
		},
		paths:       []string{"/sites/default/", "/sites/all/", "/misc/"},
		bodyContent: []string{"Drupal 7", "Drupal.settings"},
	},
	{
		framework:  CMSDrupal9,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="generator" content="Drupal 9[^"]*">`,
			`Drupal\.behaviors`,
			`/core/themes/`,
			`/core/modules/`,
			`drupalSettings`,
		},
		headers: map[string][]string{
			"X-Drupal-Dynamic-Cache": {""},
		},
		paths:       []string{"/core/", "/sites/default/"},
		bodyContent: []string{"Drupal 9", "drupalSettings"},
	},
	{
		framework:  CMSBitrix,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`/bitrix/templates/`,
			`/bitrix/components/`,
			`BX\.Ready`,
			`<script[^>]*>/bitrix/js/`,
			`bitrix_sessid`,
		},
		cookies:     []string{"BITRIX_SM_", "BX_SESSION_"},
		paths:       []string{"/bitrix/", "/upload/"},
		bodyContent: []string{"/bitrix/", "BX.Ready"},
	},
	{
		framework:  CMSOpenCart,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="generator" content="OpenCart[^"]*">`,
			`catalog/view/theme/`,
			`route=common/home`,
			`index\.php\?route=`,
		},
		cookies:     []string{"OCSESSID", "currency", "language"},
		paths:       []string{"/catalog/", "/admin/", "/system/"},
		bodyContent: []string{"OpenCart", "index.php?route="},
	},
	{
		framework:  CMSPydio,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Pydio\s*\d+\.\d+\.\d+`,
			`pydio\.js`,
			`AJXP_[A-Z_]+`,
			`ajaxplorer`,
		},
		paths:       []string{"/pydio/", "/data/"},
		bodyContent: []string{"Pydio", "ajaxplorer"},
	},
	{
		framework:  CMSvBulletin,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="generator" content="vBulletin[^"]*">`,
			`vbulletin_global\.js`,
			`showthread\.php\?`,
			`forumdisplay\.php\?`,
		},
		cookies:     []string{"bb_sessionhash", "bb_userid"},
		paths:       []string{"/admincp/", "/modcp/"},
		bodyContent: []string{"vBulletin", "showthread.php"},
	},
	{
		framework:  CMSHorde,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`Horde\s*\d+\.\d+\.\d+`,
			`/horde/themes/`,
			`Horde_[A-Za-z_]+`,
			`horde\.js`,
		},
		paths:       []string{"/horde/", "/imp/", "/kronolith/"},
		bodyContent: []string{"Horde", "/horde/"},
	},
	{
		framework:  CMSJoomla,
		language:   LangPHP,
		minMatches: 2,
		patterns: []string{
			`<meta name="generator" content="Joomla![^"]*">`,
			`/media/jui/`,
			`/media/system/`,
			`/administrator/index\.php`,
			`Joomla\.JText`,
			`var Joomla\s*=`,
			`/templates/[^/]+/index\.php`,
		},
		headers: map[string][]string{
			"Set-Cookie": {"joomla_user_state"},
		},
		cookies:     []string{"joomla_user_state", "jp_auth"},
		paths:       []string{"/administrator/", "/media/system/", "/templates/", "/components/"},
		bodyContent: []string{"Joomla!", "/media/system/", "administrator/index.php"},
	},

	// === Python 框架 ===
	{
		framework:  FrameworkDjango,
		language:   LangPython,
		minMatches: 2,
		patterns: []string{
			`<input[^>]*name='csrfmiddlewaretoken'[^>]*value='[^']*'>`,
			`django\.contrib\.admin`,
			`<div[^>]*id="django-debug-toolbar"`,
			`__admin_media_prefix__`,
		},
		headers: map[string][]string{
			"X-Django-Version": {""},
			"Vary":             {"Cookie"},
		},
		cookies:     []string{"django_session", "csrftoken", "sessionid"},
		paths:       []string{"/admin/", "/static/admin/", "/__debug__/"},
		bodyContent: []string{"csrfmiddlewaretoken", "django"},
	},
	{
		framework:  FrameworkFlask,
		language:   LangPython,
		minMatches: 2,
		patterns: []string{
			`<div[^>]*class="[^"]*flask-debug-toolbar[^"]*">`,
			`flask\.sessions`,
			`werkzeug\.debug`,
		},
		headers: map[string][]string{
			"Server": {"Werkzeug"},
		},
		cookies:     []string{"session", "flask_session"},
		bodyContent: []string{"flask", "werkzeug"},
	},
	{
		framework:  FrameworkFastAPI,
		language:   LangPython,
		minMatches: 2,
		patterns: []string{
			`<title>FastAPI</title>`,
			`"fastapi":\s*"\d+\.\d+\.\d+"`,
			`/docs.*swagger`,
			`/redoc`,
		},
		bodyContent: []string{"FastAPI", "swagger"},
	},
	{
		framework:  FrameworkPyramid,
		language:   LangPython,
		minMatches: 2,
		patterns: []string{
			`pyramid\.[a-zA-Z_]+`,
			`pyramid_debugtoolbar`,
			`<!-- Pyramid web framework -->`,
			`pyramid\.config`,
			`pyramid\.view`,
		},
		headers: map[string][]string{
			"Server": {"waitress", "pyramid"},
		},
		cookies:     []string{"pyramid_session"},
		bodyContent: []string{"pyramid", "pyramid_debugtoolbar"},
	},
	{
		framework:  FrameworkTornado,
		language:   LangPython,
		minMatches: 2,
		patterns: []string{
			`tornado\.[a-zA-Z_]+`,
			`<!-- Tornado web server -->`,
			`tornado\.web`,
			`tornado\.ioloop`,
			`TornadoServer`,
		},
		headers: map[string][]string{
			"Server":       {"TornadoServer", "tornado"},
			"X-Powered-By": {"Tornado"},
		},
		bodyContent: []string{"tornado", "TornadoServer"},
	},
	{
		framework:  FrameworkBottle,
		language:   LangPython,
		minMatches: 2,
		patterns: []string{
			`bottle\.[a-zA-Z_]+`,
			`<!-- Powered by Bottle -->`,
			`from bottle import`,
			`@bottle\.route`,
		},
		headers: map[string][]string{
			"Server":       {"bottle", "WSGIServer"},
			"X-Powered-By": {"Bottle"},
		},
		bodyContent: []string{"bottle", "Bottle"},
	},

	// === Node.js 框架 ===
	{
		framework:  FrameworkNextJS,
		language:   LangNodeJS,
		minMatches: 2,
		patterns: []string{
			`<meta name="next-head-count" content="\d+">`,
			`/_next/static/`,
			`<script[^>]*src="/_next/static/`,
			`__NEXT_DATA__`,
			`"buildId":\s*"[^"]*"`,
		},
		paths:       []string{"/_next/", "/_next/static/"},
		bodyContent: []string{"_next", "__NEXT_DATA__"},
	},
	{
		framework:  FrameworkExpress,
		language:   LangNodeJS,
		minMatches: 1,
		headers: map[string][]string{
			"X-Powered-By": {"Express"},
		},
		bodyContent: []string{"Express"},
	},
	{
		framework:  FrameworkNuxtJS,
		language:   LangNodeJS,
		minMatches: 2,
		patterns: []string{
			`window\.__NUXT__=`,
			`<div[^>]*id="__nuxt">`,
			`/_nuxt/`,
		},
		paths:       []string{"/_nuxt/"},
		bodyContent: []string{"__NUXT__", "_nuxt"},
	},
	{
		framework:  FrameworkKoa,
		language:   LangNodeJS,
		minMatches: 2,
		patterns: []string{
			`<!-- Powered by Koa -->`,
			`app\.use\(.*koa`,
			`const koa = require\('koa'\)`,
			`ctx\.body\s*=`,
			`koa-[a-zA-Z-]+`,
		},
		headers: map[string][]string{
			"X-Powered-By": {"koa", "Koa"},
			"Server":       {"koa"},
		},
		cookies:     []string{"koa.sess", "koa:sess"},
		bodyContent: []string{"koa", "Koa", "ctx.body"},
	},
	{
		framework:  FrameworkNestJS,
		language:   LangNodeJS,
		minMatches: 2,
		patterns: []string{
			`@nestjs/[a-zA-Z-]+`,
			`<!-- Powered by NestJS -->`,
			`NestFactory\.create`,
			`@Controller\(\)`,
			`@Injectable\(\)`,
			`nest\s*start`,
		},
		headers: map[string][]string{
			"X-Powered-By": {"NestJS", "Express"}, // NestJS 默认基于 Express
		},
		bodyContent: []string{"@nestjs", "NestJS", "NestFactory", "@Controller"},
	},

	// === Java 框架 ===
	{
		framework:  FrameworkSpring,
		language:   LangJava,
		minMatches: 2,
		patterns: []string{
			`org\.springframework`,
			`spring-security`,
			`<input[^>]*name="_csrf"[^>]*value="[^"]*">`,
		},
		headers: map[string][]string{
			"X-Application-Context": {""},
		},
		cookies:     []string{"JSESSIONID", "SPRING_SECURITY_REMEMBER_ME_COOKIE"},
		bodyContent: []string{"springframework", "_csrf"},
	},
	{
		framework:  FrameworkStruts,
		language:   LangJava,
		minMatches: 2,
		patterns: []string{
			`<form[^>]*action="[^"]*\.action[^"]*"`,
			`struts\.action\.Action`,
			`org\.apache\.struts`,
			`struts-[a-zA-Z]+-\d+\.\d+\.\d+\.jar`,
			`<s:[a-zA-Z]+[^>]*>`,
			`Struts\s*\d+\.\d+\.\d+`,
		},
		cookies:     []string{"JSESSIONID"},
		paths:       []string{"/struts/", "/*.action"},
		bodyContent: []string{".action", "struts", "org.apache.struts"},
	},
	{
		framework:  FrameworkJSF,
		language:   LangJava,
		minMatches: 2,
		patterns: []string{
			`<input[^>]*type="hidden"[^>]*name="javax\.faces\.ViewState"[^>]*value="[^"]*">`,
			`javax\.faces`,
			`jsf-[a-zA-Z]+-\d+\.\d+\.\d+\.jar`,
			`<h:[a-zA-Z]+[^>]*>`,
			`<f:[a-zA-Z]+[^>]*>`,
			`JSF\s*\d+\.\d+`,
		},
		cookies:     []string{"JSESSIONID"},
		paths:       []string{"/faces/", "/*.jsf", "/*.xhtml"},
		bodyContent: []string{"javax.faces.ViewState", "javax.faces", "JSF"},
	},
	{
		framework:  FrameworkPlayJava,
		language:   LangJava,
		minMatches: 2,
		patterns: []string{
			`play\.api\.[a-zA-Z\.]+`,
			`play\.mvc\.[a-zA-Z\.]+`,
			`<!-- Play Framework -->`,
			`Play\s*Framework\s*\d+\.\d+\.\d+`,
			`@play\.mvc\.`,
			`play-java-\d+\.\d+\.\d+`,
		},
		headers: map[string][]string{
			"Server":       {"Play Framework"},
			"X-Powered-By": {"Play Framework"},
		},
		cookies:     []string{"PLAY_SESSION"},
		paths:       []string{"/assets/"},
		bodyContent: []string{"Play Framework", "play.api", "play.mvc"},
	},

	// === .NET 框架 ===
	{
		framework:  FrameworkASPNET,
		language:   LangASP,
		minMatches: 2,
		patterns: []string{
			`<input[^>]*name="__VIEWSTATE"[^>]*value="[^"]*">`,
			`<input[^>]*name="__EVENTVALIDATION"[^>]*value="[^"]*">`,
			`WebResource\.axd\?d=[^&]*&t=\d+`,
			`ScriptResource\.axd\?d=[^&]*&t=\d+`,
		},
		headers: map[string][]string{
			"X-AspNet-Version":    {""},
			"X-AspNetMvc-Version": {""},
		},
		cookies:     []string{"ASP.NET_SessionId", "ASPSESSIONID"},
		bodyContent: []string{"__VIEWSTATE", "__EVENTVALIDATION"},
	},
	{
		framework:  FrameworkCore,
		language:   LangDotNetCore,
		minMatches: 2,
		patterns: []string{
			`<input[^>]*name="__RequestVerificationToken"[^>]*type="hidden"[^>]*value="[^"]*">`,
			`Microsoft\.AspNetCore`,
		},
		headers: map[string][]string{
			"Server": {"Kestrel"},
		},
		cookies:     []string{".AspNetCore.Session", ".AspNetCore.Antiforgery"},
		bodyContent: []string{"__RequestVerificationToken", "AspNetCore"},
	},
	{
		framework:  FrameworkMVC,
		language:   LangASP,
		minMatches: 2,
		patterns: []string{
			`System\.Web\.Mvc`,
			`@Html\.[A-Za-z]+\(`,
			`@model [A-Za-z0-9\.\_]+`,
			`Microsoft\.AspNet\.Mvc`,
			`<input[^>]*name="__RequestVerificationToken"[^>]*type="hidden">`,
			`mvc-\d+\.\d+\.\d+`,
		},
		headers: map[string][]string{
			"X-AspNetMvc-Version": {""},
		},
		cookies:     []string{"ASP.NET_SessionId", "__RequestVerificationToken"},
		bodyContent: []string{"@Html.", "@model", "System.Web.Mvc", "__RequestVerificationToken"},
	},

	// === Ruby 框架 ===
	{
		framework:  FrameworkRails,
		language:   LangRuby,
		minMatches: 2,
		patterns: []string{
			`<meta name="csrf-param" content="authenticity_token"[^>]*/>`,
			`<meta name="csrf-token" content="[^"]*"[^>]*/>`,
			`rails\.root:`,
		},
		cookies:     []string{"_rails_session", "_session_id"},
		paths:       []string{"/assets/", "/rails/"},
		bodyContent: []string{"csrf-param", "authenticity_token"},
	},
	{
		framework:  FrameworkSinatra,
		language:   LangRuby,
		minMatches: 2,
		patterns: []string{
			`<!-- Powered by Sinatra -->`,
			`Sinatra\s*\d+\.\d+\.\d+`,
			`require 'sinatra'`,
			`get\s*['"][^'"]*['"]\s*do`,
			`post\s*['"][^'"]*['"]\s*do`,
			`sinatra/base`,
		},
		headers: map[string][]string{
			"Server":       {"WEBrick", "Thin", "Puma"}, // Sinatra 常用服务器
			"X-Powered-By": {"Sinatra"},
		},
		cookies:     []string{"rack.session"},
		bodyContent: []string{"Sinatra", "sinatra", "WEBrick"},
	},

	// === Go 框架 ===
	{
		framework:  FrameworkGin,
		language:   LangGo,
		minMatches: 1,
		headers: map[string][]string{
			"Server": {"gin"},
		},
		bodyContent: []string{"gin"},
	},
	{
		framework:  FrameworkEcho,
		language:   LangGo,
		minMatches: 1,
		headers: map[string][]string{
			"Server": {"echo"},
		},
		bodyContent: []string{"echo"},
	},
	{
		framework:  FrameworkBeego,
		language:   LangGo,
		minMatches: 2,
		patterns: []string{
			`github\.com/beego/beego`,
			`<!-- Powered by Beego -->`,
			`beego\.[A-Za-z]+`,
			`Beego\s*\d+\.\d+\.\d+`,
			`beego\.Controller`,
			`beego\.Router`,
		},
		headers: map[string][]string{
			"Server":       {"beegoing"},
			"X-Powered-By": {"Beego"},
		},
		cookies:     []string{"beegosessionID"},
		bodyContent: []string{"Beego", "beego", "github.com/beego"},
	},
	{
		framework:  FrameworkIris,
		language:   LangGo,
		minMatches: 2,
		patterns: []string{
			`github\.com/kataras/iris`,
			`<!-- Powered by Iris -->`,
			`iris\.[A-Za-z]+`,
			`Iris\s*\d+\.\d+\.\d+`,
			`iris\.Application`,
			`iris\.Context`,
		},
		headers: map[string][]string{
			"Server":       {"iris"},
			"X-Powered-By": {"Iris"},
		},
		cookies:     []string{"irissessionid"},
		bodyContent: []string{"Iris", "iris", "github.com/kataras/iris"},
	},

	// === Scala 框架 ===
	{
		framework:  FrameworkPlayScala,
		language:   LangScala,
		minMatches: 2,
		patterns: []string{
			`play\.api\.[a-zA-Z\.]+`,
			`<!-- Play Framework -->`,
			`Play\s*Framework\s*\d+\.\d+\.\d+`,
			`import play\.api\.`,
			`play-scala-\d+\.\d+\.\d+`,
			`controllers\.[A-Za-z]+Controller`,
		},
		headers: map[string][]string{
			"Server":       {"Play Framework"},
			"X-Powered-By": {"Play Framework"},
		},
		cookies:     []string{"PLAY_SESSION"},
		paths:       []string{"/assets/"},
		bodyContent: []string{"Play Framework", "play.api", "import play.api"},
	},
	{
		framework:  FrameworkAkkaHTTP,
		language:   LangScala,
		minMatches: 2,
		patterns: []string{
			`akka\.http\.[a-zA-Z\.]+`,
			`<!-- Powered by Akka HTTP -->`,
			`Akka\s*HTTP\s*\d+\.\d+\.\d+`,
			`import akka\.http\.`,
			`akka-http-[a-zA-Z]+-\d+\.\d+\.\d+`,
			`HttpServer\.bind`,
		},
		headers: map[string][]string{
			"Server":       {"akka-http"},
			"X-Powered-By": {"Akka HTTP"},
		},
		bodyContent: []string{"Akka HTTP", "akka.http", "akka-http"},
	},

	// === 前端框架 (不限定后端语言) ===
	{
		framework:  FrameworkReact,
		language:   "", // 不限定语言
		minMatches: 2,
		patterns: []string{
			`<div[^>]*id="react-root">`,
			`<div[^>]*id="root"[^>]*>.*react`,
			`react\.development\.js`,
			`react\.production\.min\.js`,
		},
		bodyContent: []string{"react", "React"},
	},
	{
		framework:  FrameworkVue,
		language:   "",
		minMatches: 2,
		patterns: []string{
			`<div[^>]*id="app"[^>]*>.*[Vv]ue`,
			`vue\.js`,
			`vue\.min\.js`,
			`window\.Vue\s*=`,
		},
		bodyContent: []string{"vue.js", "Vue"},
	},
	{
		framework:  FrameworkAngular,
		language:   "",
		minMatches: 2,
		patterns: []string{
			`ng-app="[^"]*"`,
			`ng-controller="[^"]*"`,
			`angular\.js`,
			`angular\.min\.js`,
		},
		bodyContent: []string{"ng-app", "angular.js"},
	},
}

// initFrameworkRules 初始化预编译的正则表达式
func initFrameworkRules() {
	frameworkRules = make([]frameworkDetectionRule, 0, len(frameworkRuleDefinitions))
	for _, r := range frameworkRuleDefinitions {
		compiledPatterns := make([]*regexp.Regexp, 0, len(r.patterns))
		for _, pattern := range r.patterns {
			re, err := regexp.Compile(pattern)
			if err == nil {
				compiledPatterns = append(compiledPatterns, re)
			}
		}
		frameworkRules = append(frameworkRules, frameworkDetectionRule{
			framework:   r.framework,
			language:    r.language,
			minMatches:  r.minMatches,
			patterns:    compiledPatterns,
			headers:     r.headers,
			cookies:     r.cookies,
			paths:       r.paths,
			bodyContent: r.bodyContent,
		})
	}
}

// DetectFramework 探测框架和CMS - 与语言探测联动
func DetectFramework(req *httpv.Request, resp *httpv.Response, detectedLanguage string) []string {
	// 确保正则表达式只被编译一次
	frameworkOnceInit.Do(initFrameworkRules)

	var detectedFrameworks []string

	// 获取响应体字符串
	bodyStr := getResponseBodyString(resp)
	if bodyStr == "" && resp != nil {
		// 如果无法获取响应体，仅基于头部和路径检测
		bodyStr = ""
	}

	// 遍历所有框架规则
	for _, rule := range frameworkRules {
		// 语言联动：如果指定了语言限制，且检测到的语言不匹配，则跳过
		if rule.language != "" && detectedLanguage != LangUnknown && rule.language != detectedLanguage {
			continue
		}

		// 检测是否符合该框架的特征
		if isFrameworkMatch(req, resp, bodyStr, rule) {
			// 避免重复添加
			if !contains(detectedFrameworks, rule.framework) {
				detectedFrameworks = append(detectedFrameworks, rule.framework)
			}
		}
	}

	// 如果没有检测到任何框架，返回未知
	if len(detectedFrameworks) == 0 {
		return []string{FrameworkUnknown}
	}

	return detectedFrameworks
}

// isFrameworkMatch 检测是否匹配特定框架 - 只有强特征匹配才返回true
func isFrameworkMatch(req *httpv.Request, resp *httpv.Response, body string, rule frameworkDetectionRule) bool {
	matchCount := 0

	// 1. 检测响应体模式匹配
	for _, re := range rule.patterns {
		if re.MatchString(body) {
			matchCount++
		}
	}

	// 2. 检测响应体必需内容
	for _, content := range rule.bodyContent {
		if strings.Contains(body, content) {
			matchCount++
		}
	}

	// 3. 检测HTTP头
	if resp != nil && resp.Header != nil {
		for headerName, expectedValues := range rule.headers {
			if headerValue := resp.Header.Get(headerName); headerValue != "" {
				if len(expectedValues) == 0 || expectedValues[0] == "" {
					// 只要存在该头部就算匹配
					matchCount++
				} else {
					// 检查头部值是否匹配预期值
					headerLower := strings.ToLower(headerValue)
					for _, expectedValue := range expectedValues {
						if strings.Contains(headerLower, strings.ToLower(expectedValue)) {
							matchCount++
							break
						}
					}
				}
			}
		}
	}

	// 4. 检测Cookie
	if resp != nil {
		cookies := resp.Cookies()
		for _, cookie := range cookies {
			for _, expectedCookie := range rule.cookies {
				if strings.HasPrefix(strings.ToLower(cookie.Name), strings.ToLower(expectedCookie)) {
					matchCount++
					break
				}
			}
		}
	}

	// 5. 检测URL路径
	if req != nil {
		path := strings.ToLower(req.URL.Path)
		for _, expectedPath := range rule.paths {
			if strings.Contains(path, strings.ToLower(expectedPath)) {
				matchCount++
			}
		}
	}

	// 只有达到最小匹配数量才认为检测成功
	return matchCount >= rule.minMatches
}

func getResponseBodyString(resp *httpv.Response) string {
	if resp == nil {
		return ""
	}

	body, err := resp.GetUTF8Body()
	if err != nil {
		return string(resp.Body)
	}
	return string(body)
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
