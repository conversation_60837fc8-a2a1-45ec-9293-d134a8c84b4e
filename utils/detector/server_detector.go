package detector

import (
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

const (
	ServerIIS     = "IIS"
	ServerApache  = "Apache"
	ServerNginx   = "Nginx"
	ServerUnknown = "UnknownServer"
)

// DetectWebServer 根据给定的响应和已探测的语言推断 Web 服务器软件。
func DetectWebServer(resp *httpv.Response, language string) string {
	// 0. 基本检查
	if resp == nil {
		return ServerUnknown
	}

	// 1. 基于语言的强判断规则
	switch language {
	case LangASP:
		return ServerIIS // ASP.NET 主要运行在 IIS 上
	case LangDotNetCore:
		// .NET Core 可以在多个服务器上运行，需要进一步检查
		if resp.Header != nil {
			serverHeader := strings.ToLower(resp.Header.Get("Server"))
			if strings.Contains(serverHeader, "kestrel") {
				return ServerIIS // Kestrel 通常在 IIS 后面
			}
		}
	}

	// 2. 基于 Server 响应头进行判断
	var serverHeader string
	if resp.Header != nil {
		serverHeader = strings.ToLower(resp.Header.Get("Server"))
	} else {
		return ServerUnknown
	}

	if serverHeader == "" {
		return ServerUnknown
	}

	// 2.1. 检查 IIS
	iisIndicators := []string{
		"microsoft-iis", "microsoft-httpapi", "iisexpress",
		"asp.net", "iis", "microsoft/", "windows server",
	}
	for _, indicator := range iisIndicators {
		if strings.Contains(serverHeader, indicator) {
			return ServerIIS
		}
	}

	// 2.2. 检查 Nginx 及其衍生品
	nginxIndicators := []string{
		"nginx", "openresty", "tengine", "angie", "unit nginx",
	}
	for _, indicator := range nginxIndicators {
		if strings.Contains(serverHeader, indicator) {
			return ServerNginx
		}
	}

	// 2.3. 检查 Apache 及其衍生品
	apacheIndicators := []string{
		"apache", "apache-coyote", "apache tomcat", "oracle-http-server",
		"httpd", "ibm_http_server", "red hat", // Red Hat HTTP Server 基于 Apache
	}
	for _, indicator := range apacheIndicators {
		if strings.Contains(serverHeader, indicator) {
			return ServerApache
		}
	}

	// 3. 基于其他 HTTP 头进行判断
	if resp.Header != nil {
		// 检查 X-Powered-By 头
		if poweredBy := strings.ToLower(resp.Header.Get("X-Powered-By")); poweredBy != "" {
			// IIS 特定标识
			if strings.Contains(poweredBy, "asp.net") || strings.Contains(poweredBy, "iis") {
				return ServerIIS
			}

			// Apache 特定标识
			if strings.Contains(poweredBy, "apache") || strings.Contains(poweredBy, "php") ||
				strings.Contains(poweredBy, "mod_") {
				return ServerApache
			}

			// Nginx 在 PHP 环境下的标识
			if strings.Contains(poweredBy, "nginx") {
				return ServerNginx
			}
		}

		// 检查特定的服务器头组合
		if checkServerHeaderCombination(resp.Header, language) != ServerUnknown {
			return checkServerHeaderCombination(resp.Header, language)
		}
	}

	// 4. 基于语言和其他线索的推理判断
	return inferServerFromLanguage(language, serverHeader)
}

// checkServerHeaderCombination 基于HTTP头组合判断服务器类型
func checkServerHeaderCombination(headers map[string][]string, language string) string {
	// IIS 特定头组合
	iisHeaders := []string{"X-AspNet-Version", "X-AspNetMvc-Version", "X-Powered-By"}
	iisCount := 0
	for _, header := range iisHeaders {
		if _, exists := headers[header]; exists {
			iisCount++
		}
	}
	if iisCount >= 2 || (iisCount >= 1 && (language == LangASP || language == LangDotNetCore)) {
		return ServerIIS
	}

	// Apache 特定头组合
	apacheHeaders := []string{"X-Powered-By", "X-Mod-Pagespeed", "X-Apache"}
	for _, header := range apacheHeaders {
		if headerValues, exists := headers[header]; exists {
			headerValue := strings.ToLower(strings.Join(headerValues, " "))
			if strings.Contains(headerValue, "apache") ||
				strings.Contains(headerValue, "php") ||
				strings.Contains(headerValue, "mod_") {
				return ServerApache
			}
		}
	}

	// Nginx 特定头组合
	nginxHeaders := []string{"X-Accel-", "X-Upstream-", "X-Real-IP"}
	for _, header := range nginxHeaders {
		for headerName := range headers {
			if strings.HasPrefix(strings.ToLower(headerName), strings.ToLower(header)) {
				return ServerNginx
			}
		}
	}

	return ServerUnknown
}

// inferServerFromLanguage 基于语言特征推理服务器类型
func inferServerFromLanguage(language, serverHeader string) string {
	switch language {
	case LangPHP:
		// PHP 更多在 Apache 和 Nginx 上运行
		if strings.Contains(serverHeader, "apache") || strings.Contains(serverHeader, "httpd") {
			return ServerApache
		}
		if strings.Contains(serverHeader, "nginx") {
			return ServerNginx
		}
		// 默认 PHP 更多在 Apache 上
		return ServerApache

	case LangPython:
		// Python 应用更多在 Nginx 后面运行
		return ServerNginx

	case LangNodeJS:
		// Node.js 应用通常在 Nginx 后面做反向代理
		return ServerNginx

	case LangGo:
		// Go 应用通常在 Nginx 后面做反向代理
		return ServerNginx

	case LangJava:
		// Java 应用通常使用 Tomcat 等，归类为 Apache 系列
		return ServerApache

	case LangRuby:
		// Ruby 应用更多在 Nginx 后面运行
		return ServerNginx

	case LangPerl:
		// Perl 更多在 Apache 上运行
		return ServerApache

	case LangScala:
		// Scala 应用通常在 Nginx 后面运行
		return ServerNginx
	}

	return ServerUnknown
}
