package detector

import (
	"net/http" // 我们需要这个来创建 http.Header
	"testing"

	"github.acme.red/intelli-sec/npoc/pkg/httpv" // 假设 httpv.Response 在这里定义
)

func TestDetectWebServer(t *testing.T) {
	testCases := []struct {
		name           string
		language       string
		response       *httpv.Response // DetectWebServer 接收一个 *httpv.Response
		expectedServer string
	}{
		{
			name:           "Nil response",
			language:       LangPHP,
			response:       nil,
			expectedServer: ServerUnknown,
		},
		{
			name:     "ASP language implies IIS",
			language: LangASP,
			response: &httpv.Response{
				Header: http.Header{},
			},
			expectedServer: ServerIIS,
		},
		{
			name:           "Nil Header in response",
			language:       LangPHP,
			response:       &httpv.Response{Header: nil},
			expectedServer: ServerUnknown,
		},
		{
			name:     "Empty Server header value",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {""}},
			},
			expectedServer: ServerUnknown,
		},
		{
			name:     "No Server header key",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"X-Custom-Header": {"SomeValue"}},
			},
			expectedServer: ServerUnknown,
		},
		{
			name:     "IIS from Server header (microsoft-iis)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"Microsoft-IIS/10.0"}},
			},
			expectedServer: ServerIIS,
		},
		{
			name:     "IIS from Server header (microsoft-httpapi)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"Microsoft-HTTPAPI/2.0"}},
			},
			expectedServer: ServerIIS,
		},
		{
			name:     "Nginx from Server header (nginx)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"nginx/1.18.0"}},
			},
			expectedServer: ServerNginx,
		},
		{
			name:     "Nginx from Server header (openresty)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"openresty/1.19.9.1"}},
			},
			expectedServer: ServerNginx,
		},
		{
			name:     "Apache from Server header (apache-coyote)",
			language: LangJava,
			response: &httpv.Response{
				Header: http.Header{"Server": {"Apache-Coyote/1.1"}},
			},
			expectedServer: ServerApache,
		},
		{
			name:     "Apache from Server header (apache tomcat)",
			language: LangJava,
			response: &httpv.Response{
				Header: http.Header{"Server": {"Apache Tomcat/9.0.50"}},
			},
			expectedServer: ServerApache,
		},
		{
			name:     "Apache from Server header (oracle-http-server)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"Oracle-HTTP-Server"}},
			},
			expectedServer: ServerApache,
		},
		{
			name:     "Apache from Server header (apache generic)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"Apache/2.4.41 (Ubuntu)"}},
			},
			expectedServer: ServerApache,
		},
		{
			name:     "Unknown server from Server header",
			language: LangPython,
			response: &httpv.Response{
				Header: http.Header{"Server": {"MyCustomServer/1.0"}},
			},
			expectedServer: ServerUnknown,
		},
		{
			name:     "Server header check is case-insensitive (Nginx)",
			language: LangPHP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"NGINX/1.20.0"}},
			},
			expectedServer: ServerNginx,
		},
		{
			name:     "ASP language takes precedence over non-IIS Server header",
			language: LangASP,
			response: &httpv.Response{
				Header: http.Header{"Server": {"nginx/1.18.0"}},
			},
			expectedServer: ServerIIS,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 调用被测试的函数
			actualServer := DetectWebServer(tc.response, tc.language)

			// 检查结果是否符合预期
			if actualServer != tc.expectedServer {
				t.Errorf("对于案例 '%s', 语言 '%s': 期望服务器类型 '%s', 但得到 '%s'",
					tc.name, tc.language, tc.expectedServer, actualServer)
				if tc.response != nil && tc.response.Header != nil {
					t.Logf("响应头信息: %v", tc.response.Header)
				}
			} else {
				t.Logf("案例 '%s', 语言 '%s': 成功检测到服务器类型 '%s'",
					tc.name, tc.language, actualServer)
			}
		})
	}
}
