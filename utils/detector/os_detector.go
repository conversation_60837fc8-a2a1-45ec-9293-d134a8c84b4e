package detector

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"unicode"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/utils/similarity"
)

const (
	OSWindows = "Windows"
	OSLinux   = "Linux"
	OSUnknown = "UnknownOS"
)

var (
	apacheOsRegex *regexp.Regexp
	onceOsRegex   sync.Once
)

// initOsRegex 初始化用于检测 Apache Server 头中 OS 信息的正则表达式
func initOsRegexCompile() {
	apacheOsRegex = regexp.MustCompile(`\((?i)\s*(?:Unix|Ubuntu|CentOS|Debian|Fedora|Red Hat[^)]*|SUSE|Gentoo|AlmaLinux|Rocky Linux|Amazon Linux|Oracle Linux)\s*\)`)
}

// DetectOperatingSystem 根据提供的请求、响应、语言和服务器信息探测操作系统。
func DetectOperatingSystem(ctx context.Context, request *httpv.Request, resp *httpv.Response, client *httpv.Client, language, server string) string {
	onceOsRegex.Do(initOsRegexCompile)

	// 基于语言的强判断规则 - 新增更多语言的联动
	switch language {
	case LangASP, LangDotNetCore:
		return OSWindows // ASP.NET 和 .NET Core 在生产环境主要运行在 Windows 上
	case LangColdFusion:
		return OSWindows // ColdFusion 主要运行在 Windows 上
	case LangPython:
		// 基于Python特定的路径分析
		if request != nil && analyzePathStyle(request.URL.Path) == "windows" {
			return OSWindows
		}
		return OSLinux // Python 更多运行在 Linux 上
	case LangGo, LangNodeJS:
		// Go 和 Node.js 在 Linux 上更常见
		if request != nil && analyzePathStyle(request.URL.Path) == "windows" {
			return OSWindows
		}
		return OSLinux
	}

	// 基于服务器的强判断
	if server == ServerIIS {
		return OSWindows
	}

	if request == nil {
		return OSUnknown
	}
	req := request.Clone()

	// 增强的响应头分析
	if resp != nil && resp.Header != nil {
		// 检查更多操作系统相关的头信息
		if os := analyzeOSHeaders(resp.Header); os != OSUnknown {
			return os
		}

		// Apache 服务器特定检测
		serverHeaderFull := resp.Header.Get("Server")
		if server == ServerApache && serverHeaderFull != "" {
			if apacheOsRegex.MatchString(serverHeaderFull) {
				return OSLinux
			}
			// 检查 Windows 上的 Apache 特征
			if strings.Contains(strings.ToLower(serverHeaderFull), "win32") ||
				strings.Contains(strings.ToLower(serverHeaderFull), "win64") {
				return OSWindows
			}
		}

		// Nginx 特定检测
		if server == ServerNginx {
			// Nginx 在 Linux 上更常见，但也要检查特定标识
			if strings.Contains(strings.ToLower(serverHeaderFull), "ubuntu") ||
				strings.Contains(strings.ToLower(serverHeaderFull), "debian") ||
				strings.Contains(strings.ToLower(serverHeaderFull), "centos") {
				return OSLinux
			}
		}
	}

	// 路径大小写敏感性测试（原有逻辑保持）
	if req.URL.Path == "/" || client == nil {
		return OSUnknown
	}

	newReq, err := ModifyRequestPathCaseRandomly(req)
	if err != nil {
		return OSUnknown
	}
	newResp, err := client.Do(ctx, newReq)
	if err != nil {
		return OSUnknown
	}

	param := similarity.SimiParam{
		Resp1:        resp,
		Resp2:        newResp,
		Remove1:      []byte(""),
		Remove2:      []byte(""),
		AllowCodeDif: false,
	}
	respSim, err := param.Compute()
	if err != nil {
		return OSUnknown
	}

	if respSim < 70 {
		return OSLinux
	} else {
		return OSUnknown
	}
}

// analyzeOSHeaders 分析HTTP头中的操作系统信息
func analyzeOSHeaders(headers map[string][]string) string {
	// 检查各种可能包含OS信息的头
	osHeaders := []string{"Server", "X-Powered-By", "X-AspNet-Version", "X-Runtime"}

	for _, headerName := range osHeaders {
		if headerValues := headers[headerName]; len(headerValues) > 0 {
			headerValue := strings.ToLower(strings.Join(headerValues, " "))

			// Windows 特征标识
			windowsIndicators := []string{
				"win32", "win64", "windows", "microsoft-iis", "asp.net",
				"microsoft-httpapi", ".net framework", "iisexpress",
			}
			for _, indicator := range windowsIndicators {
				if strings.Contains(headerValue, indicator) {
					return OSWindows
				}
			}

			// Linux 特征标识
			linuxIndicators := []string{
				"ubuntu", "debian", "centos", "redhat", "suse", "fedora",
				"unix", "linux", "amazon linux", "oracle linux",
			}
			for _, indicator := range linuxIndicators {
				if strings.Contains(headerValue, indicator) {
					return OSLinux
				}
			}
		}
	}

	return OSUnknown
}

// analyzePathStyle 分析URL路径风格来推测操作系统
func analyzePathStyle(path string) string {
	// 注意：URL路径中不会包含反斜杠，因为URL标准规定使用正斜杠
	// 所以不应该检查 strings.Contains(path, "\\")

	// 检查典型的 Windows 特定路径模式
	windowsPatterns := []string{
		"/iisstart",      // IIS 默认页面
		"/aspnet_client", // ASP.NET 客户端脚本目录
		"/webdav",        // Windows WebDAV
		"/_vti_",         // FrontPage 服务器扩展（Windows 特有）
		"/exchange",      // Exchange Server
		"/owa",           // Outlook Web Access
		"/sharepoint",    // SharePoint
		"/msadc",         // Microsoft ActiveX Data Connector
		"/_layouts",      // SharePoint layouts 目录
		"/_catalogs",     // SharePoint catalogs 目录
	}

	pathLower := strings.ToLower(path)
	for _, pattern := range windowsPatterns {
		if strings.Contains(pathLower, pattern) {
			return "windows"
		}
	}

	// 检查典型的 Linux/Unix 特定路径模式
	linuxPatterns := []string{
		"/cgi-bin",     // 传统的 CGI 目录
		"/.well-known", // ACME/Let's Encrypt 等标准目录
		"/phpmyadmin",  // 常见的 Linux 下的 PHP 应用
		"/webmail",     // 常见的 Linux 下的邮件应用
		"/munin",       // 系统监控工具（主要在 Linux 上）
		"/nagios",      // 监控系统（主要在 Linux 上）
		"/cacti",       // 网络监控工具
		"/git",         // Git 仓库（更常见于 Linux）
	}

	for _, pattern := range linuxPatterns {
		if strings.Contains(pathLower, pattern) {
			return "linux"
		}
	}

	// 检查文件扩展名模式
	if strings.Contains(pathLower, ".asp") ||
		strings.Contains(pathLower, ".aspx") ||
		strings.Contains(pathLower, ".ashx") {
		return "windows"
	}

	if strings.Contains(pathLower, ".pl") && strings.Contains(pathLower, "/cgi-bin/") {
		return "linux" // Perl CGI 更常见于 Linux
	}

	return "unknown"
}

// ModifyRequestPathCaseRandomly 保持原有逻辑不变
func ModifyRequestPathCaseRandomly(originalReq *httpv.Request) (*httpv.Request, error) {
	if originalReq == nil {
		return nil, errors.New("original request is nil")
	}
	if originalReq.URL == nil {
		return nil, errors.New("original request URL is nil")
	}
	originalPath := originalReq.URL.Path
	if originalPath == "" || originalPath == "/" {
		return nil, errors.New("path is empty or root, no characters to modify case for")
	}
	pathRunes := []rune(originalPath)
	var letterIndices []int
	for i, r := range pathRunes {
		if unicode.IsLetter(r) {
			letterIndices = append(letterIndices, i)
		}
	}
	if len(letterIndices) == 0 {
		return nil, errors.New("path contains no letter characters to modify case for")
	}

	randomBytes := make([]byte, 1)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to generate random number: %w", err)
	}
	randomIndexInLetters := int(randomBytes[0]) % len(letterIndices)

	charIndexToModify := letterIndices[randomIndexInLetters]
	charToModify := pathRunes[charIndexToModify]
	if unicode.IsUpper(charToModify) {
		pathRunes[charIndexToModify] = unicode.ToLower(charToModify)
	} else {
		pathRunes[charIndexToModify] = unicode.ToUpper(charToModify)
	}
	modifiedPath := string(pathRunes)
	clonedReq := originalReq.Clone()
	clonedReq.URL.Path = modifiedPath
	return clonedReq, nil
}
