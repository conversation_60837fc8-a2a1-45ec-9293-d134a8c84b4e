package detector

import (
	"context"
	"net/http"
	"net/url"
	"testing"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

func TestDetectOperatingSystem(t *testing.T) {
	// 初始化测试环境
	ctx := context.Background()
	opt := httpv.ClientOpt{
		HostReqMaxParallel: 25,
		HostErrMaxNum:      30,
		FailRetries:        1,
	}
	taskClient, err := httpv.NewClient(ctx, opt)
	if err != nil {
		t.Fatalf("创建HTTP客户端失败: %v", err)
	}

	// 测试用例1: 基于语言检测 - ASP
	t.Run("基于ASP语言检测Windows", func(t *testing.T) {
		os := DetectOperatingSystem(ctx, nil, nil, nil, LangASP, "")
		if os != OSWindows {
			t.Errorf("期望检测到Windows，但得到了%s", os)
		}
	})

	// 测试用例2: 基于服务器检测 - IIS
	t.Run("基于IIS服务器检测Windows", func(t *testing.T) {
		os := DetectOperatingSystem(ctx, nil, nil, nil, "", ServerIIS)
		if os != OSWindows {
			t.Errorf("期望检测到Windows，但得到了%s", os)
		}
	})

	// 测试用例3: 基于路径大小写敏感性检测Linux - 使用实际HTTP请求
	t.Run("基于路径大小写敏感性检测Linux", func(t *testing.T) {
		// 这里使用一个已知的Linux服务器进行测试
		// 注意：这个URL需要替换为实际可用的测试URL
		testURL := "http://10.4.4.120:9014/index.php"

		parsedURL, err := url.Parse(testURL)
		if err != nil {
			t.Fatalf("解析URL失败: %v", err)
		}

		// 创建原始请求
		originalReq := &httpv.Request{
			URL:    parsedURL,
			Method: "GET",
			Header: http.Header{},
		}

		// 发送原始请求
		originalResp, err := taskClient.Do(ctx, originalReq)
		if err != nil {
			t.Fatalf("发送原始请求失败: %v", err)
		}

		// 使用实际的客户端和请求/响应进行操作系统检测
		os := DetectOperatingSystem(ctx, originalReq, originalResp, taskClient, "", "")

		if os != OSLinux {
			t.Errorf("检测失败！os：%s", os)
		} else {
			t.Logf("检测到的操作系统: %s", os)
		}
	})
}

// 测试ModifyRequestPathCaseRandomly函数
func TestModifyRequestPathCaseRandomly(t *testing.T) {
	// 测试用例1: 正常路径
	t.Run("正常路径修改大小写", func(t *testing.T) {
		parsedURL, _ := url.Parse("http://example.com/testPath")
		req := &httpv.Request{
			URL: parsedURL,
		}

		modifiedReq, err := ModifyRequestPathCaseRandomly(req)
		if err != nil {
			t.Errorf("修改路径大小写失败: %v", err)
		}

		if modifiedReq.URL.Path == req.URL.Path {
			t.Errorf("路径大小写未被修改: %s", modifiedReq.URL.Path)
		}

		t.Logf("原始路径: %s, 修改后路径: %s", req.URL.Path, modifiedReq.URL.Path)
	})

	// 测试用例2: 空路径
	t.Run("空路径返回错误", func(t *testing.T) {
		parsedURL, _ := url.Parse("http://example.com/")
		req := &httpv.Request{
			URL: parsedURL,
		}

		_, err := ModifyRequestPathCaseRandomly(req)
		if err == nil {
			t.Error("期望空路径返回错误，但没有")
		}
	})

	// 测试用例3: 无字母路径
	t.Run("无字母路径返回错误", func(t *testing.T) {
		parsedURL, _ := url.Parse("http://example.com/123456")
		req := &httpv.Request{
			URL: parsedURL,
		}

		_, err := ModifyRequestPathCaseRandomly(req)
		if err == nil {
			t.Error("期望无字母路径返回错误，但没有")
		}
	})

	// 测试用例4: nil请求
	t.Run("nil请求返回错误", func(t *testing.T) {
		_, err := ModifyRequestPathCaseRandomly(nil)
		if err == nil {
			t.Error("期望nil请求返回错误，但没有")
		}
	})
}
