package detector

import (
	"net/http"
	"regexp"
	"strings"
	"sync"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

// 定义语言类型常量 - 添加更多语言支持
const (
	LangPHP        = "php"
	LangPython     = "python"
	LangJava       = "java"
	LangPerl       = "perl"
	LangASP        = "asp"
	LangRuby       = "ruby"
	LangNodeJS     = "nodejs"
	LangGo         = "go"
	LangDotNetCore = "dotnetcore"
	LangColdFusion = "coldfusion"
	LangScala      = "scala"
	LangUnknown    = "UnknownLang"
)

var AllLanguages = []string{
	LangPHP, LangPython, LangJava, LangPerl, LangASP, LangRuby,
	LangNodeJS, LangGo, LangDotNetCore, LangColdFusion, LangScala,
}

// languagePattern 定义语言检测模式
type languagePattern struct {
	lang     string
	patterns []*regexp.Regexp
}

// 预编译的正则表达式模式
var (
	languagePatterns []languagePattern
	once             sync.Once
)

var languagePatternDefinitions = []struct {
	lang     string
	patterns []string
}{
	{
		lang: LangPHP,
		patterns: []string{
			// PHP错误信息 - 明确的错误格式
			`<b>Fatal error</b>:.*in <b>.*\.php</b> on line <b>\d+</b>`,
			`<b>Warning</b>:.*in <b>.*\.php</b> on line <b>\d+</b>`,
			`<b>Parse error</b>:.*in <b>.*\.php</b> on line <b>\d+</b>`,
			`<b>Notice</b>:.*in <b>.*\.php</b> on line <b>\d+</b>`,
			`PHP Fatal error:.*in .*\.php on line \d+`,
			`PHP Warning:.*in .*\.php on line \d+`,
			`PHP Notice:.*in .*\.php on line \d+`,
			`PHP Deprecated:.*in .*\.php on line \d+`,
			`PHP Parse error:.*in .*\.php on line \d+`,
			// PHP代码块 - 明确的PHP标签
			`<\?php\s+[^>]*\?>`,
			`<\?=.*\?>`,
			// PHP特定框架和CMS - 明确的meta标签
			`<meta name="generator" content="WordPress[^"]*">`,
			`<meta name="generator" content="Drupal[^"]*">`,
			`<meta name="generator" content="Joomla[^"]*">`,
			`<meta name="generator" content="Magento[^"]*">`,
			`<meta name="generator" content="PrestaShop[^"]*">`,
			`<meta name="generator" content="OpenCart[^"]*">`,
			// PHP特定标记
			`<!-- This is a PHP application -->`,
			`<!-- Generated by PHP \d+\.\d+\.\d+ -->`,
			// Composer特定文件
			`"require":\s*\{[^}]*"php":\s*"[^"]*"`,
			// Laravel特定标记
			`<meta name="csrf-token" content="[^"]*">`,
			`laravel_session=[^;]*`,
			// Symfony特定标记
			`<div id="sfwdt[^"]*" class="sf-toolbar">`,
			// CodeIgniter特定标记
			`<!-- CodeIgniter -->`,
		},
	},
	{
		lang: LangPython,
		patterns: []string{
			// Python错误信息 - 明确的Traceback格式
			`Traceback \(most recent call last\):\s*\n.*File ".*\.py", line \d+`,
			`<pre>Traceback \(most recent call last\):.*File ".*\.py", line \d+`,
			// Django特定错误和标记
			`<div id="django-debug-toolbar"[^>]*class="djdt-`,
			`<div[^>]*id=['"]djangoError['"]`,
			`<p>Django Version: \d+\.\d+\.\d+</p>`,
			`<!-- Generated by Django \d+\.\d+\.\d+ -->`,
			`<input type='hidden' name='csrfmiddlewaretoken'`,
			`django\.contrib\.admin`,
			// Flask特定错误和标记
			`<div class="flask-debug-toolbar">`,
			`Werkzeug Debugger</h1>`,
			`<title>Werkzeug Debugger</title>`,
			// FastAPI特定标记
			`<title>FastAPI</title>`,
			`"fastapi":\s*"\d+\.\d+\.\d+"`,
			// Pyramid特定标记
			`<!-- Pyramid web framework -->`,
			// Tornado特定标记
			`<!-- Tornado web server -->`,
			// Python特定标记
			`<meta name="generator" content="Django[^"]*">`,
			`<meta name="generator" content="Flask[^"]*">`,
			`<!-- Built with Python \d+\.\d+ -->`,
			// Python包管理器标记
			`"python_requires":\s*"[^"]*"`,
		},
	},
	{
		lang: LangJava,
		patterns: []string{
			// Java错误堆栈 - 明确的异常格式
			`java\.lang\.Exception:.*\n.*at .*\.java:\d+\)`,
			`java\.lang\.RuntimeException:.*\n.*at .*\.java:\d+\)`,
			`javax\.servlet\.ServletException:.*\n.*at .*\.java:\d+\)`,
			`java\.io\.IOException:.*\n.*at .*\.java:\d+\)`,
			`java\.sql\.SQLException:.*\n.*at .*\.java:\d+\)`,
			`at [a-zA-Z0-9\.$_]+\([a-zA-Z0-9\.$_]+\.java:\d+\)`,
			// Java服务器标识 - 明确的服务器标记
			`<h1>HTTP Status \d+ – .*</h1>.*Apache Tomcat`,
			`<h1>Apache Tomcat/\d+\.\d+\.\d+</h1>`,
			`<title>Apache Tomcat/\d+\.\d+\.\d+</title>`,
			// Spring框架特定标记
			`<input type="hidden" name="_csrf" value="[^"]*"/>`,
			`<!-- Spring Framework \d+\.\d+\.\d+ -->`,
			`<meta name="generator" content="Spring Boot[^"]*">`,
			// Struts框架特定标记
			`<form[^>]*action="[^"]*\.action[^"]*"`,
			`struts\.action\.Action`,
			// JSF特定标记
			`<input type="hidden" name="javax\.faces\.ViewState"`,
			// JSP特定标记 - 明确的JSP语法
			`<%@\s*page[^>]*%>`,
			`<%@\s*taglib[^>]*%>`,
			`<jsp:[a-zA-Z]+[^>]*>`,
			// Maven/Gradle特定标记
			`<groupId>[^<]+</groupId>`,
			`<artifactId>[^<]+</artifactId>`,
			// Java特定注释
			`<!-- Generated by Java \d+\.\d+ -->`,
			`<!-- Powered by Java EE -->`,
		},
	},
	{
		lang: LangPerl,
		patterns: []string{
			// Perl错误信息 - 明确的错误格式
			`at .* line \d+\.\s*Compilation failed in require`,
			`<b>.*\.pl line \d+\.</b>`,
			`<h1>Software error:</h1>\s*<pre>.*at .*\.pl line \d+`,
			`Global symbol ".*" requires explicit package name at .*\.pl line \d+`,
			`syntax error at .*\.pl line \d+`,
			`Bareword ".*" not allowed while "strict subs" in use at .*\.pl line \d+`,
			// Perl特定错误模式
			`Can't locate [^.]+(\.pm|\.pl) in @INC`,
			`BEGIN failed--compilation aborted at .*\.pl line \d+`,
			`died at .*\.pl line \d+`,
			`Use of uninitialized value .* at .*\.pl line \d+`,
			// Perl CGI特定标记
			`<meta name="generator" content="CGI::.*Perl[^"]*">`,
			`Content-Type: text/html.*charset=.*\n\n<!DOCTYPE`,
			// Perl框架特定标记
			`<!-- Powered by Mojolicious \d+\.\d+ -->`,
			`<!-- Powered by Catalyst \d+\.\d+ -->`,
			`<!-- Powered by Dancer2? \d+\.\d+ -->`,
			// Perl模块特定错误和标记
			`DBIx::Class::Schema::throw_exception`,
			`Template::Exception:.*at .*\.pm line \d+`,
			`Moose::Meta::Class::`,
			// CPAN模块标记
			`use strict;\s*use warnings;`,
			`#!/usr/bin/perl`,
			`#!/usr/local/bin/perl`,
		},
	},
	{
		lang: LangASP,
		patterns: []string{
			// ASP.NET错误信息 - 明确的错误页面格式
			`<span><H1>Server Error in '/' Application\.<hr width=100% size=1 color=silver></H1>`,
			`<title>ASP\.NET Error</title>`,
			`<title>Runtime Error</title>.*ASP\.NET`,
			`<h2>Description:</h2>.*\.aspx.*line \d+`,
			`<h2>Source Error:</h2>.*\.aspx.*line \d+`,
			// ASP.NET特定表单字段 - 明确的ViewState字段
			`<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="[^"]*"`,
			`<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="[^"]*"`,
			`<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="[^"]*"`,
			`<input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET"`,
			`<input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT"`,
			// ASP.NET特定标记
			`<meta name="generator" content="ASP\.NET[^"]*">`,
			`<!-- ASP\.NET \d+\.\d+ powered -->`,
			`WebResource\.axd\?d=[^&]*&t=\d+`,
			`ScriptResource\.axd\?d=[^&]*&t=\d+`,
			// Web Forms特定控件
			`<asp:[A-Za-z]+[^>]*runat="server"`,
			// MVC特定标记
			`@Html\.[A-Za-z]+\(`,
			`@model [A-Za-z0-9\.]+`,
		},
	},
	{
		lang: LangRuby,
		patterns: []string{
			// Rails错误信息 - 明确的Rails错误页面
			`<div class="rails-default-error-page">`,
			`<h1[^>]*>.*ActionView::Template::Error.*</h1>`,
			`<p><code>Rails\.root: .*</code></p>`,
			`<!-- Rails \d+\.\d+\.\d+ -->`,
			// Rails特定标记
			`<meta name="csrf-param" content="authenticity_token" />`,
			`<meta name="csrf-token" content="[^"]*" />`,
			`<meta name="generator" content="Ruby on Rails \d+\.\d+\.\d+">`,
			// Ruby错误堆栈
			`from .*\.rb:\d+ in`,
			// Sinatra特定标记
			`<!-- Powered by Sinatra \d+\.\d+\.\d+ -->`,
			// Rack特定标记
			`<!-- Rack \d+\.\d+\.\d+ -->`,
			// Gem相关标记
			`# -*- encoding: utf-8 -*-`,
			`Gem::Specification\.new do \|s\|`,
			// Ruby特定注释
			`<!-- Built with Ruby \d+\.\d+\.\d+ -->`,
		},
	},
	{
		lang: LangNodeJS,
		patterns: []string{
			// Node.js错误信息 - 明确的Node.js堆栈
			`at .*\.js:\d+:\d+`,
			`Error: .*\n.*at .*\.js:\d+:\d+`,
			`TypeError: .*\n.*at .*\.js:\d+:\d+`,
			`ReferenceError: .*\n.*at .*\.js:\d+:\d+`,
			// Express.js特定标记
			`<!-- Powered by Express \d+\.\d+\.\d+ -->`,
			`<meta name="generator" content="Express[^"]*">`,
			`express\.static\(`,
			// Next.js特定标记
			`<meta name="next-head-count" content="\d+">`,
			`/_next/static/`,
			`<script src="/_next/static/`,
			// Nuxt.js特定标记
			`<meta charset="utf-8"><meta name="viewport"[^>]*><meta data-n-head="ssr"`,
			`<script>window\.__NUXT__=`,
			// React SSR特定标记
			`<div id="react-root">`,
			`<script>window\.__INITIAL_STATE__=`,
			// Node.js包管理器标记
			`"engines":\s*\{\s*"node":\s*"[^"]*"`,
			`"main":\s*"[^"]*\.js"`,
			// Koa特定标记
			`<!-- Powered by Koa \d+\.\d+\.\d+ -->`,
		},
	},
	{
		lang: LangGo,
		patterns: []string{
			// Go错误信息 - 明确的Go panic格式
			`panic: .*\n\ngoroutine \d+ \[running\]:`,
			`.*\.go:\d+ \+0x[a-fA-F0-9]+`,
			// Gin框架特定标记
			`<!-- Powered by Gin \d+\.\d+\.\d+ -->`,
			// Echo框架特定标记
			`<!-- Powered by Echo \d+\.\d+\.\d+ -->`,
			// Go模块特定标记
			`module [a-zA-Z0-9\.\-/]+`,
			`go \d+\.\d+`,
			// Go特定HTTP头标记
			`<!-- Built with Go \d+\.\d+\.\d+ -->`,
			// Beego框架特定标记
			`<!-- Powered by Beego \d+\.\d+\.\d+ -->`,
		},
	},
	{
		lang: LangDotNetCore,
		patterns: []string{
			// .NET Core特定错误
			`An unhandled exception occurred while processing the request\..*Microsoft\.AspNetCore`,
			`Microsoft\.AspNetCore\.Diagnostics\.DeveloperExceptionPageMiddleware`,
			// .NET Core特定标记
			`<meta name="generator" content="\.NET Core[^"]*">`,
			`<!-- \.NET Core \d+\.\d+\.\d+ -->`,
			// ASP.NET Core特定标记
			`Microsoft\.AspNetCore\.Antiforgery`,
			`<input name="__RequestVerificationToken" type="hidden" value="[^"]*"`,
			// .NET Core项目文件标记
			`<TargetFramework>netcoreapp\d+\.\d+</TargetFramework>`,
			`<TargetFramework>net\d+\.\d+</TargetFramework>`,
		},
	},
	{
		lang: LangColdFusion,
		patterns: []string{
			// ColdFusion错误信息 - 明确的CF错误格式
			`<title>ColdFusion Error</title>`,
			`coldfusion\.runtime\.CFErrorPage`,
			`<h1>ColdFusion has encountered an error</h1>`,
			// ColdFusion特定标记
			`<cfoutput>.*</cfoutput>`,
			`<cfset .*>`,
			`<cfif .*>.*</cfif>`,
			`<cfloop .*>.*</cfloop>`,
			// ColdFusion服务器标记
			`<!-- ColdFusion \d+,\d+,\d+,\d+ -->`,
			`<meta name="generator" content="ColdFusion[^"]*">`,
			// CFML特定语法
			`<cf[a-zA-Z]+ [^>]*>`,
		},
	},
	{
		lang: LangScala,
		patterns: []string{
			// Scala错误信息 - 明确的Scala堆栈
			`at .*\.scala:\d+`,
			`scala\..*Exception:.*\n.*at .*\.scala:\d+`,
			// Play Framework特定标记
			`<!-- Play Framework \d+\.\d+\.\d+ -->`,
			`<meta name="generator" content="Play Framework[^"]*">`,
			// Akka特定标记
			`<!-- Powered by Akka HTTP \d+\.\d+\.\d+ -->`,
			// Scala特定构建工具标记
			`scalaVersion := "[^"]*"`,
			`<!-- Built with Scala \d+\.\d+\.\d+ -->`,
		},
	},
}

// 初始化预编译的正则表达式
func initPatterns() {
	languagePatterns = make([]languagePattern, 0, len(languagePatternDefinitions))
	for _, p := range languagePatternDefinitions {
		compiledPatterns := make([]*regexp.Regexp, 0, len(p.patterns))
		for _, pattern := range p.patterns {
			re, err := regexp.Compile(pattern)
			if err == nil {
				compiledPatterns = append(compiledPatterns, re)
			}
		}
		languagePatterns = append(languagePatterns, languagePattern{
			lang:     p.lang,
			patterns: compiledPatterns,
		})
	}
}

// 文件扩展名到语言的映射 - 添加更多扩展名
var extensionToLang = map[string]string{
	// PHP
	".php":   LangPHP,
	".phtml": LangPHP,
	".php3":  LangPHP,
	".php4":  LangPHP,
	".php5":  LangPHP,
	".php7":  LangPHP,
	".php8":  LangPHP,

	// Python
	".py":   LangPython,
	".pyc":  LangPython,
	".pyo":  LangPython,
	".pyw":  LangPython,
	".wsgi": LangPython,

	// Java
	".jsp":    LangJava,
	".jspx":   LangJava,
	".do":     LangJava,
	".action": LangJava,
	".jsf":    LangJava,
	".faces":  LangJava,

	// Perl
	".pl":   LangPerl,
	".pm":   LangPerl,
	".cgi":  LangPerl,
	".fcgi": LangPerl,

	// Ruby
	".rb":    LangRuby,
	".rhtml": LangRuby,
	".erb":   LangRuby,

	// ASP/ASP.NET
	".asp":  LangASP,
	".aspx": LangASP,
	".ashx": LangASP,
	".asmx": LangASP,
	".ascx": LangASP,

	// Node.js
	".js":   LangNodeJS,
	".mjs":  LangNodeJS,
	".node": LangNodeJS,

	// ColdFusion
	".cfm":  LangColdFusion,
	".cfml": LangColdFusion,
	".cfc":  LangColdFusion,

	// Scala
	".scala": LangScala,
	".sc":    LangScala,
}

// DetectServerLanguage 分析请求和响应,判断服务器可能使用的编程语言
func DetectServerLanguage(req *httpv.Request, resp *httpv.Response) string {
	// 确保正则表达式只被编译一次
	once.Do(initPatterns)

	// 分析URL路径 - 这是最快的检查,先执行
	if lang := analyzeURLPath(req.URL.Path); lang != LangUnknown {
		return lang
	}

	// 分析HTTP头 - 这也相对快速
	if lang := analyzeHeaders(resp.Header); lang != LangUnknown {
		return lang
	}

	// 分析Cookie - 相对简单的检查
	if lang := analyzeCookies(resp.Cookies()); lang != LangUnknown {
		return lang
	}

	// 分析响应体 - 这是最耗时的操作,最后执行
	bodyStr := ""
	body, err := resp.GetUTF8Body()
	if err != nil {
		bodyStr = string(resp.Body)
	} else {
		bodyStr = string(body)
	}

	if lang := analyzeResponseBody(bodyStr); lang != LangUnknown {
		return lang
	}

	// 没有足够的证据确定语言
	return LangUnknown
}

func analyzeURLPath(path string) string {
	for ext, lang := range extensionToLang {
		if strings.HasSuffix(path, ext) {
			return lang
		}
	}
	return LangUnknown
}

func analyzeHeaders(headers http.Header) string {
	// 检查X-Powered-By头 - 添加更多框架检测
	if powered := headers.Get("X-Powered-By"); powered != "" {
		poweredLower := strings.ToLower(powered)
		switch {
		case strings.Contains(poweredLower, "php"):
			return LangPHP
		case strings.Contains(poweredLower, "asp.net"):
			return LangASP
		case strings.Contains(poweredLower, ".net core"), strings.Contains(poweredLower, "aspnetcore"):
			return LangDotNetCore
		case strings.Contains(poweredLower, "jsp"), strings.Contains(poweredLower, "servlet"),
			strings.Contains(poweredLower, "tomcat"), strings.Contains(poweredLower, "jetty"):
			return LangJava
		case strings.Contains(poweredLower, "perl"):
			return LangPerl
		case strings.Contains(poweredLower, "ruby"), strings.Contains(poweredLower, "rails"),
			strings.Contains(poweredLower, "passenger"):
			return LangRuby
		case strings.Contains(poweredLower, "python"), strings.Contains(poweredLower, "django"),
			strings.Contains(poweredLower, "flask"), strings.Contains(poweredLower, "gunicorn"):
			return LangPython
		case strings.Contains(poweredLower, "express"), strings.Contains(poweredLower, "nodejs"),
			strings.Contains(poweredLower, "node.js"), strings.Contains(poweredLower, "next.js"):
			return LangNodeJS
		case strings.Contains(poweredLower, "coldfusion"), strings.Contains(poweredLower, "cfml"):
			return LangColdFusion
		case strings.Contains(poweredLower, "scala"), strings.Contains(poweredLower, "play"),
			strings.Contains(poweredLower, "akka"):
			return LangScala
		case strings.Contains(poweredLower, "go"), strings.Contains(poweredLower, "gin"),
			strings.Contains(poweredLower, "echo"), strings.Contains(poweredLower, "beego"):
			return LangGo
		}
	}

	// 检查特定框架的头 - 添加更多框架头检测
	headerChecks := map[string]string{
		"X-Django-Version":      LangPython,
		"X-Python-Version":      LangPython,
		"X-Flask-Version":       LangPython,
		"X-Ruby-Version":        LangRuby,
		"X-Rack-Cache":          LangRuby,
		"X-Runtime":             LangRuby,
		"X-AspNet-Version":      LangASP,
		"X-AspNetMvc-Version":   LangASP,
		"X-AspNetCore-Version":  LangDotNetCore,
		"X-Application-Context": LangJava,
		"X-Java-Version":        LangJava,
		"X-Nodejs-Version":      LangNodeJS,
		"X-Express-Version":     LangNodeJS,
		"X-Powered-CMS":         LangPHP, // 很多PHP CMS会设置这个头
	}

	for header, lang := range headerChecks {
		if headers.Get(header) != "" {
			return lang
		}
	}

	// 检查Server头 - 添加更多服务器检测
	if server := headers.Get("Server"); server != "" {
		serverLower := strings.ToLower(server)
		switch {
		case strings.Contains(serverLower, "php"):
			return LangPHP
		case strings.Contains(serverLower, "python"), strings.Contains(serverLower, "gunicorn"),
			strings.Contains(serverLower, "uwsgi"), strings.Contains(serverLower, "waitress"):
			return LangPython
		case strings.Contains(serverLower, "tomcat"), strings.Contains(serverLower, "jetty"),
			strings.Contains(serverLower, "websphere"), strings.Contains(serverLower, "jboss"),
			strings.Contains(serverLower, "wildfly"), strings.Contains(serverLower, "liberty"):
			return LangJava
		case strings.Contains(serverLower, "perl"), strings.Contains(serverLower, "starman"),
			strings.Contains(serverLower, "plack"):
			return LangPerl
		case strings.Contains(serverLower, "ruby"), strings.Contains(serverLower, "passenger"),
			strings.Contains(serverLower, "puma"), strings.Contains(serverLower, "unicorn"):
			return LangRuby
		case strings.Contains(serverLower, "iis"), strings.Contains(serverLower, "asp.net"):
			return LangASP
		case strings.Contains(serverLower, "kestrel"), strings.Contains(serverLower, ".net core"):
			return LangDotNetCore
		case strings.Contains(serverLower, "coldfusion"), strings.Contains(serverLower, "jrun"):
			return LangColdFusion
		case strings.Contains(serverLower, "scala"), strings.Contains(serverLower, "play framework"),
			strings.Contains(serverLower, "akka-http"):
			return LangScala
		}
	}

	return LangUnknown
}

func analyzeResponseBody(body string) string {
	for _, lp := range languagePatterns {
		for _, re := range lp.patterns {
			if re.MatchString(body) {
				return lp.lang
			}
		}
	}
	return LangUnknown
}

func analyzeCookies(cookies []*http.Cookie) string {
	for _, cookie := range cookies {
		name := strings.ToLower(cookie.Name)
		switch {
		// PHP相关cookie
		case name == "phpsessid", strings.HasPrefix(name, "phpsess"):
			return LangPHP
		// Java相关cookie
		case name == "jsessionid", strings.HasPrefix(name, "jsession"):
			return LangJava
		// ASP.NET相关cookie
		case strings.HasPrefix(name, "asp.net_sessionid"), strings.HasPrefix(name, "aspsessionid"):
			return LangASP
		// .NET Core相关cookie
		case strings.HasPrefix(name, ".aspnetcore."):
			return LangDotNetCore
		// Ruby相关cookie
		case strings.HasPrefix(name, "_rails"), strings.HasPrefix(name, "_session_id"),
			strings.Contains(name, "_session"):
			return LangRuby
		// Python相关cookie
		case strings.HasPrefix(name, "django_"), strings.HasPrefix(name, "sessionid"),
			strings.HasPrefix(name, "flask_session"):
			return LangPython
		// Node.js相关cookie
		case strings.HasPrefix(name, "connect.sid"), strings.HasPrefix(name, "express_session"),
			strings.HasPrefix(name, "next_session"):
			return LangNodeJS
		// Perl相关cookie
		case strings.HasPrefix(name, "perlsession"), strings.HasPrefix(name, "cgisess"):
			return LangPerl
		// ColdFusion相关cookie
		case strings.HasPrefix(name, "cfid"), strings.HasPrefix(name, "cftoken"),
			strings.HasPrefix(name, "jsessionid") && strings.Contains(cookie.Value, "CF"):
			return LangColdFusion
		}
	}
	return LangUnknown
}
