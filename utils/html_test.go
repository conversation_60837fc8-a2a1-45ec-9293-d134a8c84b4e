package utils

import (
	"io"
	"net/http"
	"testing"

	"github.com/PuerkitoBio/goquery"
	"github.com/stretchr/testify/require"
)

func TestCachedGoqueryNewDocument(t *testing.T) {
	u := "https://www.lichoin.com"
	resp, err := http.Get(u)
	require.NoError(t, err)
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err)
	doc1, err := CachedGoqueryNewDocument(u, body)
	require.NoError(t, err)
	doc2, err := CachedGoqueryNewDocument(u, body)
	require.NoError(t, err)
	require.Equal(t, doc1, doc2)
}

func BenchmarkExistQuerySelectorAll(b *testing.B) {
	// Mem: 1.62GB, CPU: 31.71s
	resp, err := http.Get("https://www.lichoin.com")
	require.NoError(b, err)
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	require.NoError(b, err)
	for i := 0; i < b.N; i++ {
		_, err = ExistQuerySelectorAll(body, "script[src='xxx']")
		require.NoError(b, err)
	}
}

func BenchmarkGoqueryNewDocument(b *testing.B) {
	// CPU: 110ms, Mem: 56.51MB
	u := "https://www.lichoin.com"
	resp, err := http.Get(u)
	require.NoError(b, err)
	defer resp.Body.Close()
	for i := 0; i < 1000; i++ {
		_, err = goquery.NewDocumentFromReader(io.NopCloser(resp.Body))
		require.NoError(b, err)
	}
}
