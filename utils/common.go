package utils

import (
	"context"
	"log/slog"
	"runtime"

	"github.acme.red/intelli-sec/npoc/pkg/telemetry"
)

func RecoverFun(ctx context.Context) {
	if r := recover(); r != nil {
		// 获取调用栈信息
		buf := make([]byte, 1536) //nolint:mnd // need indeed
		n := runtime.Stack(buf, true)
		slog.ErrorContext(ctx, "recovered from panic", "error", r, "stack trace", buf[:n])
		telemetry.AlertError(telemetry.DefaultType, telemetry.PanicType)
	}
}
