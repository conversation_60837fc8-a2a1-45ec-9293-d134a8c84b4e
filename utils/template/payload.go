package template

import (
	"bytes"
	"text/template"
)

type PayloadTemp struct {
	RawValue   string // 原始参数的值
	RandInt    int
	RandInt1   int
	RandInt2   int
	RandInt3   int
	RandInt4   int
	RandInt5   int
	RandInt6   int
	RandStr1   string
	RandStr2   string
	RandStr3   string
	SleepTime  int
	OOBHost    string // 带随机字符串的一个OOB的子域名
	OOBRmiUrl  string
	OOBHttpUrl string
	ParamName  string
	ParamValue string
}

func ArgEval(rawString string, temp PayloadTemp) (string, error) {
	tmpl, err := template.New("test").Parse(rawString)
	if err != nil {
		return "", err
	}
	var tplOutput bytes.Buffer
	err = tmpl.Execute(&tplOutput, temp)
	if err != nil {
		return "", err
	}
	resultString := tplOutput.String()
	return resultString, nil
}
