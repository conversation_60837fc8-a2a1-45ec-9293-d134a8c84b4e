package guess

import (
	"bytes"
	"net/http"
	"regexp"
	"strings"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
)

var (
	maybeXMLKey = regexp.MustCompile(`(?i)(xml)`)
	maybeXML    = regexp.MustCompile(`(?i)(<\?xml.*>)|(<.*>.*</.*>)`)
)

func IsXMLParam(param httpv.Param) bool {
	if maybeXMLKey.MatchString(param.Key) || IsXMLString(param.Value) {
		return true
	}
	return false
}

func IsXMLString(data string) bool {
	return maybeXML.MatchString(data)
}

func IsXMLBytes(data []byte) bool {
	return maybeXML.Match(data)
}

func IsXMLRequest(req *httpv.Request) bool {
	if req.Method != http.MethodPost && req.Method != http.MethodPut {
		return false
	}

	contentType := req.Header.Get("Content-Type")

	if strings.Contains(contentType, "xml") {
		return true
	}

	if len(req.Body) < 5 {
		return false
	}

	if IsXMLBytes(req.Body) {
		return true
	}
	return false
}

func IsHTMLResponse(resp *httpv.Response) bool {
	contentType := resp.Header.Get("Content-Type")
	if strings.Contains(contentType, "text/html") {
		return true
	}
	if resp.Header.Get("X-Content-Type-Options") == "nosniff" {
		return false
	}
	right := len(resp.Body) - 1
	if right <= 0 {
		return false
	}
	if right > 500 {
		right = 500
	}
	testBody := resp.Body[0:right]
	if bytes.Contains(testBody, []byte("<html")) ||
		bytes.Contains(testBody, []byte("<head")) ||
		bytes.Contains(testBody, []byte("<body")) {
		return true
	}
	return false
}

func ParamValueIsLink(param httpv.Param) bool {
	prefix := []string{"http://", "https://"}
	value := strings.ToLower(param.Value)
	for _, p := range prefix {
		if strings.HasPrefix(value, p) && len(value) > len(p) {
			return true
		}
	}
	return false
}

func IsURLEncoded(s string) bool {
	for i := 0; i < len(s)-2; i++ {
		if s[i] == '%' {
			c1, c2 := s[i+1], s[i+2]
			if isHexDigit(c1) && isHexDigit(c2) {
				return true
			}
		}
	}
	return false
}

func isHexDigit(c byte) bool {
	return (c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c >= 'f')
}
