package utils

import (
	"strings"
)

var DynamicFileExts = []string{"php", "jsp", "asp", "asa", "cer", "cdx", "action", "do", "cgi", "pl", "py", "rb"}

func IsDynamicFileExt(ext string) bool {
	if ext == "" {
		return false
	}
	if ext[0] == '.' {
		ext = ext[1:]
	}
	ext = strings.ToLower(ext)
	for _, fileExt := range DynamicFileExts {
		if strings.Contains(ext, fileExt) {
			return true
		}
	}
	return false
}
