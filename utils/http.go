package utils

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/url"
	"strings"
)

const (
	BodyTypeJSON   string = "json"
	BodyTypeParams string = "params"
	BodyTypeOther  string = "other"
)

var ReqTimeoutStr = []string{"i/o timeout", "context deadline exceeded (Client.Timeout"}

// DetectRequestBodyType 函数接收请求体 []byte 和 http.Header，
// 然后判断请求体的类型。
// 返回值: "json", "params", 或 "other"。
func DetectRequestBodyType(body []byte, header http.Header) string {
	contentType := header.Get("Content-Type")
	if contentType != "" {
		lowerContentType := strings.ToLower(contentType)
		if strings.Contains(lowerContentType, "application/json") {
			return BodyTypeJSON
		}
		if strings.Contains(lowerContentType, "application/x-www-form-urlencoded") {
			return BodyTypeParams
		}
	}

	trimmedBody := bytes.TrimSpace(body)

	if len(trimmedBody) == 0 {
		return BodyTypeOther
	}

	firstChar := trimmedBody[0]
	lastChar := trimmedBody[len(trimmedBody)-1]

	if (firstChar == '{' && lastChar == '}') || (firstChar == '[' && lastChar == ']') {
		if json.Valid(trimmedBody) {
			return BodyTypeJSON
		}
	}

	if bytes.Contains(trimmedBody, []byte("=")) {
		_, err := url.ParseQuery(string(trimmedBody))
		if err == nil {
			return BodyTypeParams
		}
	}

	return BodyTypeOther
}

func IsTimeoutError(err error) bool {
	errorString := err.Error()
	for _, errStr := range ReqTimeoutStr {
		if strings.Contains(errorString, errStr) {
			return true
		}
	}
	return false
}
