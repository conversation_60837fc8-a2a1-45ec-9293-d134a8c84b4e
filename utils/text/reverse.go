package text

import (
	"fmt"
	"net"
	"net/url"
	"slices"
	"strings"

	"github.com/miekg/dns"
)

// ReverseDomain reverses a domain name, www.miek.nl. -> nl.miek.www
// s must be a syntactically valid domain name, see IsDomainName.
func ReverseDomain(s string) string {
	if _, ok := dns.IsDomainName(s); !ok {
		return s
	}

	labels := dns.SplitDomainName(s)
	if labels == nil {
		return s
	}
	slices.Reverse(labels)
	return strings.Join(labels, ".")
}

func ReverseURL(u *url.URL) *url.URL {
	reversed := ReverseDomain(u.Host)
	if u.Port() == "" {
		u.Host = reversed
	} else {
		u.Host = net.JoinHostPort(reversed, u.Port())
	}
	return u
}

// ReverseURLStringScheme Scheme is required
// https://www.baidu.com/index.html -> https://com.baidu.www/index.html
func ReverseURLStringScheme(s string) string {
	u, err := url.Parse(s)
	if err != nil {
		return s
	}
	return ReverseURL(u).String()
}

// ReverseURLString 不接受带 scheme 的输入
func ReverseURLString(s string) (string, error) {
	if strings.Contains(s, "//") {
		return "", fmt.Errorf("url string should not contain scheme: %s", s)
	}

	reversed := ReverseURLStringScheme("//" + s)
	// best effort to remove the leading "//"
	if strings.HasPrefix(reversed, "//") {
		return reversed[2:], nil
	}
	return reversed, nil
}

// ReverseString 将字符串反转
func ReverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}
