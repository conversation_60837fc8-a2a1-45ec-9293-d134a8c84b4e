package text

import (
	"bytes"
	"encoding/json"
	"errors"
)

// ToJSONSafeString 将传入的字符串转为符合JSON格式的字符串
func ToJSONSafeString(input string) (string, error) {
	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	err := encoder.Encode(input)
	if err != nil {
		return "", errors.New("failed to encode string to JSON: " + err.Error())
	}

	// 去除编码添加的引号和末尾的换行符
	encoded := buf.String()
	if len(encoded) >= 1 && encoded[len(encoded)-1] == '\n' {
		encoded = encoded[:len(encoded)-1]
	}
	if len(encoded) >= 2 && encoded[0] == '"' && encoded[len(encoded)-1] == '"' {
		encoded = encoded[1 : len(encoded)-1]
	}

	// 验证解码
	var decoded string
	err = json.Unmarshal([]byte(`"`+encoded+`"`), &decoded)
	if err != nil {
		return "", errors.New("failed to validate JSON encoding: " + err.<PERSON>rror())
	}

	return encoded, nil
}
