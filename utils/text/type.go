package text

import (
	"fmt"
	"regexp"
	"strconv"
)

var IpReg = regexp.MustCompile(`^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)

func IsIp(str string) bool {
	return IpReg.MatchString(str)
}

func ToString(s any) string {
	switch v := s.(type) {
	case nil:
		return ""
	case string:
		return v
	case []byte:
		return string(v)
	case fmt.Stringer:
		return v.String()
	case int:
		return strconv.Itoa(v)
	case uint:
		return strconv.FormatUint(uint64(v), 10)
	case bool:
		return strconv.FormatBool(v)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case int64:
		return strconv.FormatInt(v, 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int16:
		return strconv.FormatInt(int64(v), 10)
	case int8:
		return strconv.FormatInt(int64(v), 10)
	case uint64:
		return strconv.FormatUint(v, 10)
	case uint32:
		return strconv.FormatUint(uint64(v), 10)
	case uint16:
		return strconv.FormatUint(uint64(v), 10)
	case uint8:
		return strconv.FormatUint(uint64(v), 10)
	case error:
		return v.Error()
	default:
		return fmt.Sprint(v)
	}
}

// JSONScalarToString converts an interface coming from json to string
func JSONScalarToString(s interface{}) (string, error) {
	switch v := s.(type) {
	case string:
		return ToString(v), nil
	case float64:
		return ToString(v), nil
	case nil:
		return ToString(v), nil
	case bool:
		return ToString(v), nil
	default:
		return "", fmt.Errorf("cannot convert type to string: %v", v)
	}
}
