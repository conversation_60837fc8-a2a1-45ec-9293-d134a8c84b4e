package text

import (
	"bytes"
	"strings"
)

// StrContainsSlice 判读字符串中是否有slice中的任意元素
func StrContainsSlice(str string, slice []string) bool {
	for _, sub := range slice {
		if strings.Contains(strings.ToLower(str), strings.ToLower(sub)) {
			return true
		}
	}
	return false
}

func LowerStrContains(str, subStr string) bool {
	return strings.Contains(strings.ToLower(str), strings.ToLower(subStr))
}

func StringHasPrefix(data, sub string) bool {
	return strings.HasPrefix(strings.ToLower(data), strings.ToLower(sub))
}

func LowerBytesContain(byte, subByte []byte) bool {
	return bytes.Contains(bytes.ToLower(byte), bytes.ToLower(subByte))
}
