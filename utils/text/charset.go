// Package spider coping from https://github.com/gogf/gf/blob/master/encoding/gcharset/gcharset.go
package text

import (
	"bytes"
	"io"
	"unicode/utf8"

	"golang.org/x/net/html/charset"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/ianaindex"
	"golang.org/x/text/transform"
)

var LowerChars = []rune("abcdefghijklmnopqrstuvwxyz")

// Alias for charsets.
var charsetAlias = map[string]string{
	"HZGB2312": "HZ-GB-2312",
	"hzgb2312": "HZ-GB-2312",
	"GB2312":   "HZ-GB-2312",
	"gb2312":   "HZ-GB-2312",
}

// Supported returns whether charset `charset` is supported.
func Supported(charset string) bool {
	en, err := getEncoding(charset)
	if err != nil {
		return false
	}

	return en != nil
}

// Convert converts `src` charset encoding from `srcCharset` to `dstCharset`,
// and returns the converted string.
// It returns `src` as `dst` if it fails converting.
func Convert(dstCharset string, srcCharset string, src string) (dst string, err error) {
	if dstCharset == srcCharset {
		return src, nil
	}
	// nolint
	dst = src
	// Converting `src` to UTF-8.
	if srcCharset != "UTF-8" {
		en, err := getEncoding(srcCharset)
		if err != nil {
			return "", err
		}

		tmp, err := io.ReadAll(
			transform.NewReader(bytes.NewReader([]byte(src)), en.NewDecoder()),
		)
		if err != nil {
			return "", err
		}
		src = string(tmp)
	}
	// Do the converting from UTF-8 to `dstCharset`.
	if dstCharset != "UTF-8" {
		en, err := getEncoding(dstCharset)
		if err != nil {
			return "", err
		}

		tmp, err := io.ReadAll(
			transform.NewReader(bytes.NewReader([]byte(src)), en.NewEncoder()),
		)
		if err != nil {
			return "", err
		}
		dst = string(tmp)
	} else {
		dst = src
	}
	return dst, nil
}

// ToUTF8 converts `src` charset encoding from `srcCharset` to UTF-8 ,
// and returns the converted string.
func ToUTF8(srcCharset string, src string) (dst string, err error) {
	return Convert("UTF-8", srcCharset, src)
}

// UTF8To converts `src` charset encoding from UTF-8 to `dstCharset`,
// and returns the converted string.
func UTF8To(dstCharset string, src string) (dst string, err error) {
	return Convert(dstCharset, "UTF-8", src)
}

// getEncoding returns the encoding.Encoding interface object for `charset`.
// It returns nil if `charset` is not supported.
func getEncoding(charset string) (encoding.Encoding, error) {
	if c, ok := charsetAlias[charset]; ok {
		charset = c
	}
	enc, err := ianaindex.MIB.Encoding(charset)
	if err != nil {
		return nil, err
	}
	return enc, nil
}

func String2UTF8(str string) ([]byte, error) {
	var result []byte
	if !utf8.ValidString(str) {
		reader, err := charset.NewReaderLabel("utf-8", bytes.NewReader([]byte(str)))
		if err != nil {
			return nil, err
		}
		result, err = io.ReadAll(io.LimitReader(reader, 1024*1024))
		if err != nil {
			return nil, err
		}
		return result, nil
	} else {
		result = []byte(str)
	}
	return result, nil
}

func Pkcs5padding(ciphertext []byte, blockSize int, after int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padText...)
}
