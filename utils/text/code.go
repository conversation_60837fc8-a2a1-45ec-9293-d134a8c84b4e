package text

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"regexp"
)

const (
	BASE64Type = "base64"
	MD5Type    = "md5"
	SHA256Type = "sha256"
)

var (
	BASE64Regex      = regexp.MustCompile(`^[a-zA-Z0-9+/=]{4,}$`)
	MD5Regex         = regexp.MustCompile(`^[0-9a-z]{32}$`)
	SHA256Regex      = regexp.MustCompile(`^[0-9a-z]{64}$`)
	LowerLetterChars = []rune("abcdefghijklmnopqrstuvwxyz") //nolint:gochecknoglobals // need indeed
	UpperLetterChars = []rune("ABCDEFGHIJKLMNOPQRSTUVWXYZ") //nolint:gochecknoglobals // need indeed
)

// EncodeStr 根据编码类型对字符串进行编码
func EncodeStr(str, encodeType string) string {
	switch encodeType {
	case "":
		return str
	case BASE64Type:
		return base64.StdEncoding.EncodeToString([]byte(str))
	case SHA256Type:
		hasher := sha256.New()
		hasher.Write([]byte(str))
		newStr := fmt.Sprintf("%x", hasher.Sum(nil))
		return newStr
	case MD5Type:
		hasher := md5.New()
		hasher.Write([]byte(str))
		newStr := fmt.Sprintf("%x", hasher.Sum(nil))
		return newStr
	}
	return str
}

// GetEncodeType 判断字符串可能是哪种编码
func GetEncodeType(s string) string {
	isBase64, _ := Base64Decode(s)
	if isBase64 {
		return BASE64Type
	} else if MD5Regex.MatchString(s) {
		return MD5Type
	} else if SHA256Regex.MatchString(s) {
		return SHA256Type
	} else {
		return ""
	}
}

// Base64Decode 字符串是base64编码的
func Base64Decode(s string) (bool, []byte) {
	strByte, err := base64.StdEncoding.DecodeString(s)
	if err == nil {
		for i, b := range string(strByte) {
			if i == 15 { // 最多判断字符串的前15位字符
				break
			}
			// 65533为unicode的无效字符编码，如果解码后的字符存在无效字符则证明不是base64编码的
			if b > 65532 {
				return false, nil
			}
		}
		return true, strByte
	}

	return false, nil
}
