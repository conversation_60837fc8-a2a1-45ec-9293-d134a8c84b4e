package oobutils

import (
	"context"
	"log/slog"
	"time"

	"github.acme.red/pictor/dnslog/pkg/client"

	"github.acme.red/intelli-sec/npoc"
)

// ExtractOOBDetails 从interactions中提取 OOB 细节
func ExtractOOBDetails(ctx context.Context, url *client.URL) []*npoc.OOBDetail {
	oobDetail := make([]*npoc.OOBDetail, 0)
	limit := 5
	interactions := url.GetInteractions()
	if len(interactions) < limit {
		limit = len(interactions)
	}

	for _, interaction := range interactions[:limit] {
		detail := &npoc.OOBDetail{
			Timestamp:     interaction.Timestamp,
			RemoteAddress: interaction.RemoteAddress,
			Protocol:      interaction.Protocol,
			FullId:        interaction.FullID,
		}

		switch interaction.Protocol {
		case client.LogTypeDNS, client.LogTypeHTTP, client.LogTypeLDAP:
			detail.Request = interaction.RawRequest
			oobDetail = append(oobDetail, detail)
		case client.LogTypeRMI:
			detail.Request = url.URL()
			oobDetail = append(oobDetail, detail)
		default:
			slog.WarnContext(ctx, "invalid OOB protocol", "interaction", interaction)
		}
	}
	return oobDetail
}

// WaitForOOBTrigger 等待oob触发
func WaitForOOBTrigger(ctx context.Context, oobURL *client.URL) bool {
	deadline := oobURL.Deadline()
	durationToWait := time.Until(deadline)

	timer := time.NewTimer(durationToWait)
	defer timer.Stop()

	select {
	case <-timer.C:
		return oobURL.Triggered()
	case <-ctx.Done():
		return false
	}
}
