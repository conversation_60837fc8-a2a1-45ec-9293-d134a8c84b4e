package npoc

import (
	"errors"
	"fmt"
)

var (
	ErrExist    = errors.New("exist")
	ErrNotExist = errors.New("not exist")
	ErrSkip     = errors.New("skip")
)

type PoCError struct {
	PoC string
	Err error
}

func (e *PoCError) Error() string {
	return fmt.Sprintf("poc: %s, err: %v", e.PoC, e.Err)
}

func (e *PoCError) Unwrap() error {
	return e.Err
}

type ProtocolError struct {
	Protocol string
	Err      error
}

func (e *ProtocolError) Error() string {
	return fmt.Sprintf("protocol: %s, err: %v", e.Protocol, e.Err)
}

func (e *ProtocolError) Unwrap() error {
	return e.Err
}
