---
- name: Deploy npoc
  hosts: agent
  remote_user: root
  roles:
    - docker
  tasks:
    - name: Upload binary tools
      ansible.builtin.copy:
        src: "files/binary-tools"
        dest: "/opt/npoc/binary-tools"
    - name: Upload npoc binary
      ansible.builtin.copy:
        src: "files/npoc-agent"
        dest: "/opt/npoc/npoc-agent"
    - name: Docker compose up
      community.docker.docker_compose_v2:
        profiles: "{{ profiles }}"
        pull: "always"
        remove_orphans: true
        wait: true
        wait_timeout: 60
        project_src: "/opt/npoc"
        recreate: "always"