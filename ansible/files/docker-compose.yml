volumes:
  otel: { }
services:
  agent:
    image: '${ITS_DOCKER_TAG_PREFIX}/npoc'
    container_name: npoc-agent
    mem_limit: ${ITS_MEM_LIMIT:-0}
    cpus: ${ITS_CPU_LIMIT:-0}
    restart: unless-stopped
    init: true
    command:
      - "./npoc-agent"
    environment:
      # 调度服务器地址
      - SCHEDULER_ADDRESS=${ITS_SCHEDULER_ADDRESS}
      # 是否需要禁用证书校验
      - SCHEDULER_SECURE=${ITS_SCHEDULER_SECURE}
      # 调度服务器token
      - NPOC_SCHEDULER_TOKEN=${ITS_SCHEDULER_TOKEN}
      # dnslog地址
      - NPOC_OOB_HOST=${ITS_OOB_HOST}
      # dnslog api url
      - NPOC_OOB_URL=${ITS_OOB_URL}
      # dnslog token
      - NPOC_OOB_TOKEN=${ITS_OOB_TOKEN}
      # 日志输出到控制台
      - NPOC_LOG_OUT_CONSOLE=${ITS_LOG_OUT_CONSOLE}
      # 扫描器每秒的请求数量限制
      - NPOC_TASK_REQUEST_PARALLEL=${ITS_NPOC_TASK_REQUEST_PARALLEL}
      # 扫描器的任务并发数量限制
      - NPOC_TASK_PARALLEL=${ITS_NPOC_TASK_PARALLEL}
      # 扫描器的日志记录等级
      - NPOC_LOG_LEVEL=${ITS_LOG_LEVEL}
      # 当前应用场景
      - NPOC_APP_ENV=${ITS_NPOC_APP_ENV}
    ports:
      - "127.0.0.1:6363:6363"
    volumes:
      - ./logs:/opt/npoc/logs
      - ./binary-tools:/opt/npoc/binary-tools
      - ./npoc-agent:/opt/npoc/npoc-agent:ro
      - ./oss:/opt/npoc/oss
    tmpfs:
      - /tmp:exec,size=2G
  otel:
    restart: unless-stopped
    network_mode: host
    user: "0:0"  #  Error: cannot start pipelines: storage client: open /var/lib/otelcol/file_storage/receiver_filelog_app: permission denied
    image: "otel/opentelemetry-collector-contrib:0.113.0" # 这个是自己打的一个镜像，正常使用官方提供的镜像也没有太大问题，官方的镜像需要注意是否有日志读取的权限
    volumes:
      - ./logs:/opt/npoc/logs # 日志目录映射
      - ./app.yaml:/etc/otelcol-contrib/config.yaml
      - otel:/var/lib/otelcol/file_storage/logs # 保存日志处理状态，防止重复消费
    environment:
      APP_NAME: ${ITS_OTEL_APP_NAME}
      DEPLOY_ENV: ${ITS_OTEL_DEPLOY_ENV}
      INSTANCE_ID: ${ITS_OTEL_INSTANCE_ID}
      GROUP_ID: ${ITS_GROUP_ID}
      APP_ENV_INSTANCE_ID: ${ITS_APP_ENV_INSTANCE_ID}
      METRIC_ENDPOINT: 127.0.0.1:6363
      NODE_EXPORTER_ENDPOINT: 127.0.0.1:9100
      OTLP_ENDPOINT: ${ITS_OTLP_ENDPOINT}
      OTLP_AUTH_USERNAME: ${ITS_OTLP_AUTH_USERNAME}
      OTLP_AUTH_PASSWORD: ${ITS_OTLP_AUTH_PASSWORD}

  node_exporter:
    image: quay.io/prometheus/node-exporter:v1.8.2
    command:
      - "--path.rootfs=/host"
      - "--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|run)($|/)" # wsl需要配置--no-collector.filesystem
      - "--web.listen-address=127.0.0.1:9100"
    network_mode: host
    pid: host
    restart: unless-stopped
    volumes:
      - "/:/host:ro,rslave"