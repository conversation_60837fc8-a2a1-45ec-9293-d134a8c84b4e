all:
  vars:
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o ProxyCommand="ssh -W %h:%p -o StrictHostKeyChecking=no -i files/profiles/pocscan_prod/id_ed25519 root@**********"'
    ITS_DOCKER_TAG_PREFIX: harbor.0xsec.cc/its
    ITS_SCHEDULER_ADDRESS: ************:7070
    ITS_SCHEDULER_SECURE: true
    ITS_OTLP_ENDPOINT: otel.0xsec.cc:443
    ITS_OTLP_AUTH_USERNAME: dev
    ITS_OTLP_AUTH_PASSWORD: "(newpc*mA,h02}/+%jt{JO7bW3^sF'q)"
    ITS_GROUP_ID: intelli-sec
    ITS_OTEL_APP_NAME: npoc
    ITS_OTEL_DEPLOY_ENV: pocscan_prod
    ITS_OOB_HOST: b.bxdnso.cc
    ITS_OOB_TOKEN: aic8doo8Zee0eikuchoo4YiCoh8ieK4O
    ITS_OOB_URL: http://b.bxdnso.cc:8080
    ITS_LOG_OUT_CONSOLE: false
    ITS_LOG_LEVEL: debug
    ITS_NPOC_APP_ENV: pocscan
    docker_registry_url: harbor.0xsec.cc
    docker_registry_username: robot$its+ci-push
    docker_registry_password: 30VmRNYiwzEtskBJ4R4JS0L64kepOGMC
    profiles:
      - pocscan_prod

agent:
  vars:
    ITS_CPU_LIMIT: '{{ (0.9 * (ansible_processor_vcpus | default(1))) | float }}'
    ITS_MEM_LIMIT: '{{ (0.9 * (ansible_memtotal_mb | default(2048))) | int }}MB'
  hosts:
    agent1:
      # 8核16G20M
      ansible_host: ***************
      ITS_OTEL_INSTANCE_ID: "agent1"
      ITS_SCHEDULER_TOKEN: 019739d2-54c0-7c22-a7f5-c8f58eab7280
      ITS_NPOC_TASK_REQUEST_PARALLEL: 2
      ITS_NPOC_TASK_PARALLEL: 1200
      ITS_APP_ENV_INSTANCE_ID: npoc-pocscan_prod-agent1
    agent2:
      # 8核16G20M
      ansible_host: *************
      ITS_OTEL_INSTANCE_ID: "agent2"
      ITS_SCHEDULER_TOKEN: 01973a3e-bd76-70bc-9596-a09f5c7b39b4
      ITS_NPOC_TASK_REQUEST_PARALLEL: 2
      ITS_NPOC_TASK_PARALLEL: 1200
      ITS_APP_ENV_INSTANCE_ID: npoc-pocscan_prod-agent2
    agent3:
      # 8核8G20M
      ansible_host: ***************
      ITS_OTEL_INSTANCE_ID: "agent3"
      ITS_SCHEDULER_TOKEN: 01973a3f-17ed-7eb7-af65-3bb4368a24c0
      ITS_NPOC_TASK_REQUEST_PARALLEL: 2
      ITS_NPOC_TASK_PARALLEL: 1200
      ITS_APP_ENV_INSTANCE_ID: npoc-pocscan_prod-agent3
