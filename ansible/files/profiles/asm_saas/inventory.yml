all:
  vars:
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o ProxyCommand="ssh -W %h:%p -o StrictHostKeyChecking=no -i files/profiles/asm_saas/id_ed25519 root@**********"'
    ITS_DOCKER_TAG_PREFIX: harbor.0xsec.cc/its
    ITS_SCHEDULER_ADDRESS: scheduler.0xsec.cc
    ITS_SCHEDULER_SECURE: false
    ITS_OTLP_ENDPOINT: otel.0xsec.cc:443
    ITS_OTLP_AUTH_USERNAME: dev
    ITS_OTLP_AUTH_PASSWORD: "(newpc*mA,h02}/+%jt{JO7bW3^sF'q)"
    ITS_GROUP_ID: intelli-sec
    ITS_OTEL_APP_NAME: npoc
    ITS_OTEL_DEPLOY_ENV: asm_saas
    ITS_OOB_HOST: b.bxdnso.cc
    ITS_OOB_TOKEN: aic8doo8Zee0eikuchoo4YiCoh8ieK4O
    ITS_OOB_URL: http://b.bxdnso.cc:8080
    ITS_NPOC_APP_ENV: asm
    ITS_LOG_OUT_CONSOLE: false
    ITS_LOG_LEVEL: debug
    docker_registry_url: harbor.0xsec.cc
    docker_registry_username: robot$its+ci-push
    docker_registry_password: 30VmRNYiwzEtskBJ4R4JS0L64kepOGMC
    profiles:
      - asm_saas

agent:
  vars:
    ITS_CPU_LIMIT: '{{ (0.9 * (ansible_processor_vcpus | default(1))) | float }}'
    ITS_MEM_LIMIT: '{{ (0.9 * (ansible_memtotal_mb | default(2048))) | int }}MB'
  hosts:
    # poc扫描节点
    agent1:
      # 8核16G 100M
      ansible_host: **************
      ITS_OTEL_INSTANCE_ID: "agent1"
      ITS_SCHEDULER_TOKEN: 01968634-111a-7e28-8524-ea6484d27456
      ITS_NPOC_TASK_REQUEST_PARALLEL: 25
      ITS_NPOC_TASK_PARALLEL: 100
      ITS_APP_ENV_INSTANCE_ID: npoc-asm_saas-agent1
    agent2:
      # 8核16G 100M
      ansible_host: ************
      ITS_OTEL_INSTANCE_ID: "agent2"
      ITS_SCHEDULER_TOKEN: 0196d27b-93a1-7c62-801e-778135eae52b
      ITS_NPOC_TASK_REQUEST_PARALLEL: 25
      ITS_NPOC_TASK_PARALLEL: 100
      ITS_APP_ENV_INSTANCE_ID: npoc-asm_saas-agent2
    # 通用漏洞扫描节点
    agent3:
      # 8核16G 1000M
      ansible_host: *************
      ITS_OTEL_INSTANCE_ID: "agent3"
      ITS_SCHEDULER_TOKEN: 0196d27b-2801-70da-bc4e-5ace1e48487d
      ITS_NPOC_TASK_REQUEST_PARALLEL: 25
      ITS_NPOC_TASK_PARALLEL: 150
      ITS_APP_ENV_INSTANCE_ID: npoc-asm_saas-agent3