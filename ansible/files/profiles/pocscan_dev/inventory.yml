all:
  vars:
    ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
    ITS_DOCKER_TAG_PREFIX: harbor.0xsec.cc/its
    ITS_SCHEDULER_ADDRESS: scheduler-poc.i.insec.cc
    ITS_SCHEDULER_SECURE: false
    ITS_OTLP_ENDPOINT: otel.0xsec.cc:443
    ITS_OTLP_AUTH_USERNAME: dev
    ITS_OTLP_AUTH_PASSWORD: "(newpc*mA,h02}/+%jt{JO7bW3^sF'q)"
    ITS_OTEL_APP_NAME: npoc
    ITS_OTEL_DEPLOY_ENV: pocscan_dev
    ITS_OOB_HOST: dnsx.cc
    ITS_GROUP_ID: intelli-sec
    ITS_OOB_TOKEN: wzPkZlQcthJQv2IyLW7eXCAfAOQteb0E
    ITS_OOB_URL: http://dnsx.cc:8080
    ITS_LOG_OUT_CONSOLE: false
    ITS_LOG_LEVEL: debug
    ITS_NPOC_APP_ENV: pocscan
    docker_registry_url: harbor.0xsec.cc
    docker_registry_username: robot$its+ci-push
    docker_registry_password: 30VmRNYiwzEtskBJ4R4JS0L64kepOGMC
    profiles:
      - pocscan_dev

agent:
  vars:
    ITS_CPU_LIMIT: '{{ (0.9 * (ansible_processor_vcpus | default(1))) | float }}'
    ITS_MEM_LIMIT: '{{ (0.9 * (ansible_memtotal_mb | default(2048))) | int }}MB'
  hosts:
    agent1:
      ansible_host: ***********
      ITS_OTEL_INSTANCE_ID: "agent1"
      ITS_SCHEDULER_TOKEN: 019714fb-1484-70ac-ab52-2f9c47edf29f
      ITS_NPOC_TASK_REQUEST_PARALLEL: 2
      ITS_NPOC_TASK_PARALLEL: 500
      ITS_APP_ENV_INSTANCE_ID: npoc-pocscan_dev-agent1