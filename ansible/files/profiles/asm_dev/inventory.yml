all:
  vars:
    ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
    ITS_DOCKER_TAG_PREFIX: harbor.0xsec.cc/its
    ITS_SCHEDULER_ADDRESS: scheduler.i.insec.cc
    ITS_SCHEDULER_SECURE: false
    ITS_OTLP_ENDPOINT: otel.0xsec.cc:443
    ITS_OTLP_AUTH_USERNAME: dev
    ITS_OTLP_AUTH_PASSWORD: "(newpc*mA,h02}/+%jt{JO7bW3^sF'q)"
    ITS_OTEL_APP_NAME: npoc
    ITS_OTEL_DEPLOY_ENV: asm_dev
    ITS_OOB_HOST: dnsx.cc
    ITS_GROUP_ID: intelli-sec
    ITS_OOB_TOKEN: wzPkZlQcthJQv2IyLW7eXCAfAOQteb0E
    ITS_OOB_URL: http://dnsx.cc:8080
    ITS_NPOC_APP_ENV: asm
    ITS_LOG_OUT_CONSOLE: false
    ITS_LOG_LEVEL: debug
    docker_registry_url: harbor.0xsec.cc
    docker_registry_username: robot$its+ci-push
    docker_registry_password: 30VmRNYiwzEtskBJ4R4JS0L64kepOGMC
    profiles:
      - asm_dev

agent:
  vars:
    ITS_CPU_LIMIT: '{{ (0.9 * (ansible_processor_vcpus | default(1))) | float }}'
    ITS_MEM_LIMIT: '{{ (0.9 * (ansible_memtotal_mb | default(2048))) | int }}MB'
  hosts:
    agent1:
      ansible_host: ***********
      ITS_OTEL_INSTANCE_ID: "agent1"
      ITS_SCHEDULER_TOKEN: 01967733-6f75-724d-b792-9cbf7331d15c
      ITS_NPOC_TASK_REQUEST_PARALLEL: 25
      ITS_NPOC_TASK_PARALLEL: 80
      ITS_APP_ENV_INSTANCE_ID: npoc-asm_dev-agent1
    agent2:
      ansible_host: ***********
      ITS_OTEL_INSTANCE_ID: "agent2"
      ITS_SCHEDULER_TOKEN: 0196a952-738d-7328-91f7-fd1004efa638
      ITS_NPOC_TASK_REQUEST_PARALLEL: 25
      ITS_NPOC_TASK_PARALLEL: 100
      ITS_APP_ENV_INSTANCE_ID: npoc-asm_dev-agent2
    agent3:
      ansible_host: ***********
      ITS_OTEL_INSTANCE_ID: "agent3"
      ITS_SCHEDULER_TOKEN: 0196a952-8bcd-71ad-a8e2-3547b382c68e
      ITS_NPOC_TASK_REQUEST_PARALLEL: 25
      ITS_NPOC_TASK_PARALLEL: 100
      ITS_APP_ENV_INSTANCE_ID: npoc-asm_dev-agent3