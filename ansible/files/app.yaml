receivers:
  # log 采集器
  filelog/app:
    include: [/opt/npoc/logs/*.log]
    storage: file_storage
    operators:
      - type: json_parser
        parse_from: body
        severity:
          parse_from: attributes.level
        timestamp:
          parse_from: attributes.time
          layout_type: gotime
          layout: 2006-01-02T15:04:05.999999999Z07:00
      - type: add
        field: attributes["data_stream.dataset"]
        value: ${env:APP_NAME} # 1
      - type: add
        field: attributes["data_stream.namespace"]
        value: ${env:DEPLOY_ENV} # 2

  # metrics 采集器
  prometheus/app:
    config:
      scrape_configs:
        - job_name: "${env:DEPLOY_ENV}"
          scrape_interval: 5s
          static_configs:
            - targets: ["${env:METRIC_ENDPOINT}"] # 4
          relabel_configs:
            - target_label: instance
              replacement: ${env:APP_ENV_INSTANCE_ID} # 3

  prometheus/node_exporter:
    config:
      scrape_configs:
        - job_name: ${env:GROUP_ID}
          scrape_interval: 10s
          static_configs:
            - targets: ["${env:NODE_EXPORTER_ENDPOINT}"] # 5
          relabel_configs:
            - target_label: instance
              replacement: ${env:APP_ENV_INSTANCE_ID}
            - target_label: component
              replacement: 'node_exporter'

processors:
  batch:

exporters:
  otlp:
    endpoint: ${env:OTLP_ENDPOINT} # 6
    auth:
      authenticator: basicauth

extensions:
  health_check:
  basicauth:
    client_auth:
      username: ${env:OTLP_AUTH_USERNAME} # 7
      password: ${env:OTLP_AUTH_PASSWORD} # 8
  file_storage:
    directory: /var/lib/otelcol/file_storage

service:
  extensions: [health_check, file_storage, basicauth]
  pipelines:
    logs/app:
      receivers: [filelog/app]
      processors: [batch]
      exporters: [otlp]
    metrics/app:
      receivers: [ prometheus/app ]
      processors: [ batch ]
      exporters: [ otlp ]
    metrics/node_exporter:
      receivers: [ prometheus/node_exporter ]
      processors: [ batch ]
      exporters: [ otlp ]