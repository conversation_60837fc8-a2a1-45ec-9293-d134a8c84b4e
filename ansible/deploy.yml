---
- name: Deploy npoc
  hosts: agent
  remote_user: root
  roles:
    - docker
  tasks:
  - name: Login asm registry
    when: docker_registry_url is defined and docker_registry_url | length > 0
    community.docker.docker_login:
      registry_url: "{{ docker_registry_url }}"
      username: "{{ docker_registry_username }}"
      password: "{{ docker_registry_password }}"
      state: present
  - name: Create deploy dir
    ansible.builtin.file:
      path: "/opt/npoc"
      state: "directory"
  - name: Create logs dir
    ansible.builtin.file:
      path: "/opt/npoc/logs"
      state: "directory"
  - name: Upload docker compose file
    ansible.builtin.copy:
      src: "files/docker-compose.yml"
      dest: "/opt/npoc/docker-compose.yml"
  - name: Upload binary tools
    ansible.builtin.copy:
      src: "files/binary-tools"
      dest: "/opt/npoc/"
      mode: preserve
  - name: Upload app.yaml tools
    ansible.builtin.copy:
      src: "files/app.yaml"
      dest: "/opt/npoc/app.yaml"
  - name: Upload npoc binary
    ansible.builtin.copy:
      src: "files/npoc-agent"
      dest: "/opt/npoc/npoc-agent"
      mode: preserve
  - name: Generate .env file
    ansible.builtin.template:
      src: "templates/env.j2"
      dest: "/opt/npoc/.env"
  - name: Docker compose up
    community.docker.docker_compose_v2:
      profiles: "{{ profiles }}"
      pull: "always"
      remove_orphans: true
      wait: true
      wait_timeout: 60
      project_src: "/opt/npoc"
      recreate: "always"
  - name: Prune docker images
    community.docker.docker_prune:
      images: true