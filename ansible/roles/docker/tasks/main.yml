---
- name: Check docker and compose
  ansible.builtin.command: docker compose version
  ignore_errors: true
  failed_when: false
  register: check_docker

- name: Install docker
  when: check_docker.rc != 0
  block:
    - name: Get docker install script
      ansible.builtin.get_url:
        url: https://get.docker.com
        dest: /tmp/get-docker.sh
        mode: "0755"

    - name: Install docker
      ansible.builtin.shell: /tmp/get-docker.sh
