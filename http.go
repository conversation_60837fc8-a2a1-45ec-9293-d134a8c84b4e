package npoc

import (
	"context"
	"log/slog"
	"runtime"
	"slices"

	"github.acme.red/intelli-sec/npoc/pkg/httpv"
	"github.acme.red/intelli-sec/npoc/pkg/sync"
	"github.acme.red/intelli-sec/npoc/utils"
	"github.acme.red/intelli-sec/npoc/utils/detector"
)

const ProtocolHTTP = "http"

func (np *NPoC) executeHTTPPoC(ctx context.Context, poc PoC, task Task, result *TaskResult) error {
	httpPoC, ok := poc.(HTTPPoC)
	if !ok {
		return nil
	}
	httpContext := &HTTPContext{
		Context: ctx,
		np:      np,
		task:    task.(*HTTPTask),
		result:  result,
	}
	if task.(*HTTPTask).Client == nil {
		runtime.Breakpoint()
	}
	return httpPoC.Execute(httpContext)
}

type HTTPFollow struct {
	*httpv.Request
	*httpv.Response
	Payload string
}

type VulnerabilityHTTP struct {
	Follows []HTTPFollow
}

type HTTPTask struct {
	Client            *httpv.Client
	Request           *httpv.Request
	Response          *httpv.Response
	Env               map[string]string
	FingerPrint       []httpv.FingerprintInfo
	WebLoginBruteDict *WebLoginBruteDict
	TaskCache         *sync.RWMutexMap[string, any]
	FullScan          bool
	Language          string
}
type WebLoginBruteDict struct {
	UseDefaultDict  bool
	CustomUsers     []string
	CustomPasswords []string
	UserAndPassword []string
}

func (t *HTTPTask) TaskProtocol() string {
	return ProtocolHTTP
}

// func (t *HTTPTask) CloneRequest() *httpv.Request {
// 	req := &httpv.Request{
// 		Method: t.Request.Method,
// 		URL:    t.Request.URL,
// 		Header: t.Request.Header.Clone(),
// 		Body:   make([]byte, len(t.Request.Body)),
// 	}
// 	copy(req.Body, t.Request.Body)
// 	return req
// }

type HTTPPoC interface {
	PoC
	Execute(c *HTTPContext) error
}

type HTTPContext struct {
	context.Context
	np     *NPoC
	task   *HTTPTask
	result *TaskResult
}

func (c *HTTPContext) Task() *HTTPTask {
	return c.task
}

func (c *HTTPContext) WithRequestResponse(req *httpv.Request, resp *httpv.Response) *HTTPContext {
	newTask := &HTTPTask{
		Request:     req,
		Response:    resp,
		FingerPrint: c.Task().FingerPrint,
		Client:      c.Task().Client,
	}
	return &HTTPContext{
		Context: c.Context,
		np:      c.np,
		task:    newTask,
		result:  c.result,
	}
}

func (c *HTTPContext) OutputVulnerability(vulnerability *Vulnerability) {
	c.result.VulnMu.Lock()
	if vulnerability == nil {
		// 获取调用栈信息
		buf := make([]byte, 1024)
		n := runtime.Stack(buf, true)
		slog.ErrorContext(c.Context, "vulnerability is nil", "stack trace", buf[:n])
	} else {
		c.result.Vulnerabilities = append(c.result.Vulnerabilities, vulnerability)
	}
	c.result.VulnMu.Unlock()
}

func (c *HTTPContext) OutputPoCError(err *PoCError) {
	c.result.ErrorMu.Lock()
	c.result.PoCErrors = append(c.result.PoCErrors, err)
	c.result.ErrorMu.Unlock()
	if utils.IsTimeoutError(err.Err) {
		c.Task().Client.HostErrCounter.Add(1)
	}
}

func (c *HTTPContext) GetResult() *TaskResult {
	return c.result
}

func CreateNewTestHTTPContext(ctx context.Context, req *httpv.Request, resp *httpv.Response, client *httpv.Client) *HTTPContext {
	tr := &TaskResult{}
	taskCache := sync.NewRWMutexMap[string, any](100)
	htask := &HTTPTask{
		Request:     req,
		Response:    resp,
		Client:      client,
		TaskCache:   taskCache,
		FullScan:    true,
		FingerPrint: []httpv.FingerprintInfo{},
	}
	return &HTTPContext{
		Context: ctx,
		np:      nil,
		task:    htask,
		result:  tr,
	}
}

func (c *HTTPContext) GetLanguage() (languages []string) {
	for _, fp := range c.Task().FingerPrint {
		if slices.Contains(detector.AllLanguages, fp.Name) {
			languages = append(languages, fp.Name)
		}
	}
	if len(languages) == 0 {
		return []string{detector.LangUnknown}
	}
	return languages
}
