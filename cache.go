package npoc

import "sync"

func NewSizedCache[T any](maxSize int) *SizedCache[T] {
	return &SizedCache[T]{
		m:    make(map[string]T),
		keys: make([]string, maxSize),
	}
}

type SizedCache[T any] struct {
	rw         sync.RWMutex
	m          map[string]T
	keys       []string
	nextKeyIdx int
}

func (c *SizedCache[T]) Get(key string) (T, bool) {
	c.rw.RLock()
	v, ok := c.m[key]
	c.rw.RUnlock()
	return v, ok
}

func (c *SizedCache[T]) Set(key string, value T) {
	c.rw.Lock()
	delete(c.m, c.keys[c.nextKeyIdx%len(c.keys)])
	c.m[key] = value
	c.nextKeyIdx++
	c.rw.Unlock()
}
